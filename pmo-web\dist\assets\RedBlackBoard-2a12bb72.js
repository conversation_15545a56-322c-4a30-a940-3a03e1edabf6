import{E as w,J as ft,_ as Ee,c as f,r as We,p as ge,o as Ne,K as Ve,d as v,L as ze,e as d,f as B,B as Le,g as s,h as e,k as W,M as gt,N as V,w as a,j as $,G as ce,t as h,O as Ke,y as yt,P as Ge,Q as Je,R as bt,F as _e,D as ke,S as Se,T as wt,U as ht,V as Qe,m as Ue,i as kt,W as Te,X as xt,Y as $t,u as Vt,H as Ct,Z as Dt,$ as jt,a0 as Ut,a1 as It,a2 as Bt,A as Mt,v as St}from"./index-76121fa4.js";import{u as Ie,w as Xe,F as Ze}from"./xlsx-32f1569e.js";import{g as Tt,u as Et,c as Nt,d as zt}from"./project-0b1ee259.js";import{g as Lt,a as Pt}from"./options-85143662.js";import{g as He,a as At,u as Rt}from"./team-e147e167.js";function Ft(re="您的登录已过期，请重新登录",ie=!0){const _=parseInt(sessionStorage.getItem("last_token_message_time")||"0"),le=Date.now();if(le-_<5e3?console.log("短时间内已显示过token无效消息，跳过重复提示"):(w.warning(re),sessionStorage.setItem("last_token_message_time",le.toString())),localStorage.removeItem("token"),localStorage.removeItem("user"),!ie)return;const F=window.location.pathname+window.location.search;if(sessionStorage.getItem("is_redirecting_to_login")==="true"){console.log("已经在跳转到登录页面，避免重复跳转");return}sessionStorage.setItem("is_redirecting_to_login","true"),setTimeout(()=>{window.location.pathname!=="/login"&&(window.location.href=`/login?redirect=${encodeURIComponent(F)}`),setTimeout(()=>{sessionStorage.removeItem("is_redirecting_to_login")},2e3)},1e3)}function Ot(re={}){return ft({url:"/dashboard/redblack",method:"get",params:re})}const Yt={class:"project-list-dialog"},qt={class:"project-list-content"},Wt={class:"close-button-container"},Ht={class:"stats-header"},Kt={class:"stats-container"},Gt={class:"stat-card"},Jt={class:"stat-icon project-icon"},Qt={class:"stat-content"},Xt={class:"stat-value"},Zt={class:"stat-card"},el={class:"stat-icon budget-icon"},tl={class:"stat-content"},ll={class:"stat-value"},al={class:"stat-card"},ol={class:"stat-icon delayed-icon"},sl={class:"stat-content"},nl={class:"stat-value"},rl={class:"table-header"},il={class:"table-title"},ul={class:"table-actions"},dl={class:"search-box"},cl={style:{"background-color":"#fff",padding:"2px 0"}},pl={style:{"background-color":"#fff",padding:"2px 0"}},_l={key:1},ml={style:{"background-color":"#fff",padding:"2px 0"}},vl={key:1},fl=["onDblclick"],gl={key:0},yl={key:1},bl=["onDblclick"],wl={key:3},hl={key:1},kl={style:{"background-color":"#fff",padding:"2px 0"}},xl={key:0,style:{display:"flex",gap:"5px"}},$l={__name:"ProjectListDialog",props:{entity:{type:String,required:!1,default:""},category:{type:String,default:"全部"},highlightType:{type:String,default:""},title:{type:String,default:""}},emits:["close"],setup(re,{emit:ie}){const _=re,le=ie,F=()=>{le("close")},L=f(!1),C=f([]),c=f([]),K=f(!1),E=f(""),j=f(""),M=f("all"),S=f(!1),R=f(null),N=f(null),P=We({value:""}),A=f({}),Q=f({}),z=f(!1);f(!1);const Z=f([{prop:"excellence_level",label:"优先级",width:"120px",visible:!0},{prop:"project_category",label:"任务分类",width:"150px",visible:!0},{prop:"investment_type",label:"投资类型",width:"100px",visible:!0},{prop:"responsible_department",label:"负责部门",width:"120px",visible:!0},{prop:"is_hardware",label:"是否硬件",width:"100px",visible:!0},{prop:"is_non_indigenous_innovation",label:"是否信创",width:"100px",visible:!0},{prop:"is_project_established",label:"已立项",width:"80px",visible:!0},{prop:"project_establishment_year",label:"立项年份",width:"100px",visible:!0},{prop:"annual_investment_plan",label:"年度投资(万)",width:"120px",visible:!0},{prop:"project_planned_total_investment",label:"总投资(万)",width:"120px",visible:!0},{prop:"budget",label:"预算(万)",width:"100px",visible:!0},{prop:"construction_content",label:"建设内容",width:"200px",visible:!1},{prop:"project_overview",label:"项目概述",width:"200px",visible:!1},{prop:"business_research_time",label:"调研时间",width:"120px",visible:!0},{prop:"solution_time",label:"方案时间",width:"120px",visible:!0},{prop:"project_establishment_time",label:"立项时间",width:"120px",visible:!0},{prop:"project_procurement_time",label:"采购时间",width:"120px",visible:!0},{prop:"project_implementation_time",label:"实施时间",width:"120px",visible:!0},{prop:"project_acceptance_time",label:"验收时间",width:"120px",visible:!0},{prop:"next_steps",label:"下一步计划",width:"200px",visible:!1},{prop:"issues_to_be_coordinated_resolved",label:"待解决问题",width:"200px",visible:!1},{prop:"remarks",label:"备注",width:"150px",visible:!1},{prop:"estimated_operating_revenue",label:"预计营收",width:"120px",visible:!0},{prop:"delayed",label:"是否逾期",width:"100px",visible:!0},{prop:"delay_days",label:"逾期天数",width:"100px",visible:!0}]),be=["excellence_level","project_category","investment_type","is_hardware","is_non_indigenous_innovation","is_project_established","project_establishment_year","current_progress","investment_entity","responsible_department"],g=["business_research_time","solution_time","project_establishment_time","project_procurement_time","project_implementation_time","project_acceptance_time"],r=["construction_content","project_overview","next_steps","issues_to_be_coordinated_resolved","remarks"],m=We({excellence_level:["紧急且重要","不紧急但重要","紧急但不重要","不紧急且不重要"],project_category:["业务赋能与客户体验类","技术支撑与数据治理类","基础设施与安全保障类","管理提升类","其他"],investment_type:["新建","续建","改造"],is_hardware:["硬件","非硬件"],is_non_indigenous_innovation:["信创","非信创"],is_project_established:["是","否"],project_establishment_year:["2024年","2025年","2026年"],current_progress:["未启动","业务调研","解决方案","项目立项","任务采购","项目实施","项目验收","项目结项","已完成"],investment_entity:["集团","集团战略部","集团法规部","集团财务部","集团风控部","集团保全部","集团审计部","集团办公室","集团人力部","集团协同部","财险","资管","金租","汽租","商租","小贷","保理","担保","不动产","征信","金服","外部主体"],responsible_department:[]}),ae=ge(()=>C.value.length?C.value.reduce((t,i)=>{const b=parseFloat(i.annual_investment_plan||0);return t+(isNaN(b)?0:b)},0).toFixed(2):0),X=ge(()=>C.value.length?C.value.filter(l=>l.delayed).length:0),I=ge(()=>Z.value.filter(l=>l.visible&&l.prop!=="project_name_1")),k=ge(()=>Object.keys(A.value).length>0),n=async()=>{K.value=!1,E.value="",L.value=!0;let l=0;const t=2;for(w({message:"正在加载项目数据，可能需要一些时间，请耐心等待...",type:"info",duration:5e3});l<=t;)try{const i={};_.entity&&(i.entity=_.entity),_.category==="开工"?i.implementation=!0:_.category==="逾期"&&(i.delayed=!0),console.log(`请求项目列表参数 (尝试 ${l+1}/${t+1}):`,i);const b=await Tt(i);if(console.log("项目列表响应:",b),b.code===200){C.value=b.data||[],c.value=[...C.value],console.log(`获取到 ${C.value.length} 个项目`);break}else w({message:b.message||"获取项目列表失败",type:"error"}),l++,l<=t?(console.log(`将在1秒后重试 (${l}/${t})`),await new Promise(q=>setTimeout(q,1e3))):b.code===401?(K.value=!0,E.value="登录已过期，请重新登录"):w.error(b.message||"获取项目列表失败")}catch(i){console.error("获取项目列表错误:",i),l++,l<=t?(console.log(`将在1秒后重试 (${l}/${t})`),await new Promise(b=>setTimeout(b,1e3))):i.response&&i.response.status===401?(K.value=!0,E.value="登录已过期，请重新登录"):w.error("获取项目列表失败，请刷新页面重试")}L.value=!1},D=l=>l&&{未启动:"info",业务调研:"info",解决方案:"warning",项目立项:"warning",任务采购:"warning",项目实施:"primary",项目验收:"success",项目结项:"success",已完成:"success"}[l]||"info",G=()=>{const l=Ie.json_to_sheet(C.value.map(ue=>{const ye={项目编号:ue.project_code,项目名称:ue.project_name,投资主体:ue.investment_entity,当前阶段:ue.current_progress,任务名称1:ue.project_name_1||""};return Z.value.forEach(H=>{H.prop!=="project_code"&&H.prop!=="project_name"&&H.prop!=="investment_entity"&&H.prop!=="current_progress"&&H.prop!=="project_name_1"&&(ye[H.label]=Y(ue[H.prop],H.prop))}),ye})),t=Ie.book_new();Ie.book_append_sheet(t,l,"项目列表");const i=Xe(t,{bookType:"xlsx",type:"array"}),b=new Blob([i],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),q=`${_.entity||"所有项目"}_${new Date().toISOString().split("T")[0]}.xlsx`;Ze.saveAs(b,q)},te=l=>{const t=Z.value.find(i=>i.prop===l);t&&(t.visible=!t.visible)},Y=(l,t)=>l==null?"":pe(t)?oe(l):l,oe=l=>{if(!l)return"";try{const t=new Date(l);return isNaN(t.getTime())?l:t.toISOString().split("T")[0]}catch{return l}},ee=l=>be.includes(l),pe=l=>g.includes(l),we=l=>r.includes(l),ve=l=>l?(l==="responsible_department"&&m.responsible_department.length===0&&(console.log("负责部门选项为空，立即尝试加载真实数据"),se()),(!m[l]||m[l].length===0)&&(console.warn(`字段 ${l} 没有选项数据或选项为空`),l==="investment_entity"?(console.log("尝试重新加载投资主体选项"),U()):l==="responsible_department"&&(console.log("尝试重新加载负责部门选项"),se())),m[l]||[]):[],p=l=>{R.value=l,N.value={prop:"current_progress",label:"当前阶段"},P.value=l.current_progress,S.value=!0},o=l=>{R.value=l,N.value={prop:"investment_entity",label:"投资主体"},P.value=l.investment_entity,S.value=!0},T=async()=>{var q;if(console.log("保存编辑开始",{currentRow:R.value?R.value.project_code:null,currentColumn:(q=N.value)==null?void 0:q.prop,editFormValue:P.value}),!R.value||!N.value){console.error("保存编辑失败：缺少当前行或列信息");return}const l=R.value.project_code,t=N.value.prop,i=P.value,b=R.value[t];if(console.log("保存编辑详情",{projectCode:l,fieldName:t,newValue:i,originalValue:b}),b===i){console.log("值未变化，不需要保存"),S.value=!1;return}Me(t)&&i&&!m[t].includes(i)&&(console.log(`添加新的${N.value.label}选项: ${i}`),m[t].push(i)),S.value=!1,R.value[t]=i,Q.value[l]||(Q.value[l]={}),t in Q.value[l]||(Q.value[l][t]=b),A.value[l]||(A.value[l]={}),A.value[l][t]=i,console.log(`已添加修改: 项目=${l}, 字段=${t}, 新值=${i}`),w({message:'修改已添加，点击"保存修改"按钮保存所有更改',type:"success",duration:2e3})},me=l=>!0,J=async()=>{if(!k.value){w.warning("没有需要保存的修改");return}try{z.value=!0,w({message:"正在保存修改，请稍候...",type:"info",duration:2e3});let l=0,t=0;const i=Object.keys(A.value).map(async b=>{var q;try{const ue=A.value[b];console.log(`保存项目 ${b} 的修改:`,ue);const ye=await Et(b,ue);return ye.code===200&&((q=ye.data)!=null&&q.updated)?(l++,console.log(`项目 ${b} 保存成功`),!0):(t++,console.error(`保存项目 ${b} 失败:`,ye),!1)}catch(ue){return t++,console.error(`保存项目 ${b} 出错:`,ue),!1}});await Promise.all(i),t===0?(w.success(`已成功保存 ${l} 个项目的修改`),A.value={},Q.value={},setTimeout(()=>{y()},100)):(w.warning(`成功保存了 ${l} 个项目，但有 ${t} 个项目保存失败`),l>0&&setTimeout(()=>{y()},100))}catch(l){console.error("批量保存出错:",l),w.error("批量保存失败，请重试")}finally{z.value=!1}},fe=()=>{if(k.value){for(const l in A.value){const t=C.value.find(i=>i.project_code===l);if(t)for(const i in A.value[l])Q.value[l]&&i in Q.value[l]&&(t[i]=Q.value[l][i])}A.value={},Q.value={},w.success("已取消所有修改")}},y=async()=>{try{const l=document.querySelector(".el-table__body-wrapper"),t=l?l.scrollTop:0,i=j.value;L.value=!0,await n(),i&&(j.value=i,De()),setTimeout(()=>{const b=document.querySelector(".el-table__body-wrapper");b&&(b.scrollTop=t)},50)}catch(l){console.error("刷新数据出错:",l),w.error("刷新数据失败，请手动刷新页面")}finally{L.value=!1}};Ne(async()=>{try{try{console.log("组件挂载时优先加载负责部门数据"),await se(),console.log("负责部门数据加载完成")}catch(l){console.error("负责部门数据加载失败:",l),w.warning("负责部门数据加载失败，将使用默认数据")}try{console.log("组件挂载时加载其他选项数据"),U().catch(l=>{console.error("加载投资主体选项失败:",l)})}catch(l){console.error("加载其他选项数据失败:",l)}await n();try{m.responsible_department.length<=1&&(console.log("项目列表加载后再次尝试加载负责部门数据"),await se())}catch(l){console.error("二次加载负责部门数据失败:",l)}}catch(l){console.error("项目列表组件挂载失败:",l)}});const U=async()=>{var l;try{console.log("开始加载投资主体选项");const t=await Lt();if(console.log("投资主体选项API响应:",t),t.code===200){const i=((l=t.data)==null?void 0:l.entities)||[];return i.length>0?(m.investment_entity=i,console.log(`成功加载了 ${m.investment_entity.length} 个投资主体选项:`,m.investment_entity)):console.warn("API返回的投资主体选项为空，使用默认选项"),m.investment_entity}else return console.error("获取投资主体选项失败:",t.message),w.warning("获取投资主体选项失败，将使用默认选项"),m.investment_entity}catch(t){return console.error("加载投资主体选项出错:",t),w.warning("加载投资主体选项出错，将使用默认选项"),m.investment_entity}},se=async()=>{var l;try{console.log("开始加载负责部门选项");const t=await Pt();if(console.log("负责部门选项API响应:",t),t.code===200){const i=((l=t.data)==null?void 0:l.departments)||[];return i.length>0?(m.responsible_department=i,console.log(`成功加载了 ${m.responsible_department.length} 个负责部门选项:`,m.responsible_department)):(console.warn("API返回的负责部门选项为空，请检查数据库"),m.responsible_department=["信息技术部"]),m.responsible_department}else return console.error("获取负责部门选项失败:",t.message),w.warning("获取负责部门选项失败，请检查网络连接"),m.responsible_department.length===0&&(m.responsible_department=["信息技术部"]),m.responsible_department}catch(t){return console.error("加载负责部门选项出错:",t),w.warning("加载负责部门选项出错，请检查网络连接"),m.responsible_department.length===0&&(m.responsible_department=["信息技术部"]),m.responsible_department}},Ce=async(l,t)=>{if(!["project_code","project_name"].includes(t.property)){if(me(t.property),R.value=l,N.value=Z.value.find(i=>i.prop===t.property)||{prop:t.property,label:t.label||t.property},P.value=l[t.property],be.includes(t.property)){if(t.property==="investment_entity")try{await U()}catch(i){console.error("加载投资主体选项失败:",i)}else if(t.property==="responsible_department")try{await se()}catch(i){console.error("加载负责部门选项失败:",i)}}S.value=!0}},Be=()=>{const l={project_code:Pe(),project_name:"",investment_entity:_.entity||"",current_progress:"未启动",isNew:!0};C.value.unshift(l),c.value.unshift({...l}),j.value="",w({message:"请填写项目信息后点击保存",type:"info",duration:3e3})},Pe=()=>{const l=new Date,t=l.getFullYear().toString().slice(2),i=(l.getMonth()+1).toString().padStart(2,"0"),b=Date.now().toString().slice(-6);return`P${t}${i}${b}`},O=({row:l})=>l.delayed?{color:"#F56C6C",fontWeight:"500"}:{},De=()=>{if(!j.value){C.value=[...c.value];return}const l=j.value.toLowerCase(),t=c.value.filter(i=>{const b=q=>q==null?!1:String(q).toLowerCase().includes(l);return M.value==="all"?b(i.project_code)||b(i.project_name)||b(i.investment_entity)||b(i.current_progress)||b(i.project_name_1):b(i[M.value])});C.value=t};Ve(j,()=>{De()}),Ve(M,()=>{j.value&&De()});const et=async l=>{z.value=!0;try{const t={...l};delete t.isNew,delete t.isEditing,console.log("准备保存项目数据:",t);const i=await Nt(t);if(i.code===200){C.value=C.value.filter(b=>b!==l),c.value=c.value.filter(b=>b!==l),L.value=!0;try{await n(),w({message:"新增项目成功",type:"success"})}catch(b){console.error("刷新项目列表错误:",b),w({message:"项目已保存，但无法刷新列表",type:"warning"})}finally{L.value=!1}}else w({message:i.message||"新增项目失败",type:"error"})}catch(t){console.error("新增项目错误:",t),w({message:t.message||"新增项目失败",type:"error"})}finally{z.value=!1}},tt=l=>{C.value=C.value.filter(t=>t!==l),c.value=c.value.filter(t=>t!==l),w({message:"已取消新增项目",type:"info"})},lt=l=>{Ue.confirm(`确定要删除项目 "${l.project_name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=await zt(l.project_code);t.code===200?(w({type:"success",message:"项目删除成功"}),C.value=C.value.filter(i=>i.project_code!==l.project_code),c.value=c.value.filter(i=>i.project_code!==l.project_code)):w({type:"error",message:t.message||"删除失败"})}catch(t){console.error("删除项目错误:",t),w({type:"error",message:t.message||"删除项目失败"})}}).catch(()=>{w({type:"info",message:"已取消删除"})})},at=({row:l})=>l.delayed?"delayed-row":"",ot=()=>{Ft("请重新登录后再试",!0)},st=async(l,t,i)=>{if(R.value=l,N.value={prop:t,label:i||t},P.value=l[t],t==="investment_entity")try{await U()}catch(b){console.error("加载投资主体选项失败:",b)}else if(t==="responsible_department")try{console.log("双击负责部门字段，加载真实数据"),await se(),console.log("负责部门数据加载完成，共有选项:",m.responsible_department.length)}catch(b){console.error("加载负责部门选项失败:",b),w.warning("加载负责部门选项失败，将使用现有数据")}S.value=!0},Me=l=>["investment_entity","responsible_department"].includes(l);return(l,t)=>{var Fe;const i=v("el-button"),b=v("el-alert"),q=v("el-icon"),ue=v("el-tooltip"),ye=v("el-card"),H=v("el-option"),je=v("el-select"),xe=v("el-input"),nt=v("el-checkbox"),rt=v("el-dropdown-item"),it=v("el-dropdown-menu"),ut=v("el-dropdown"),he=v("el-table-column"),Ae=v("el-tag"),Re=v("el-date-picker"),dt=v("el-table"),ct=v("el-empty"),pt=v("el-form-item"),_t=v("el-form"),mt=v("el-dialog"),vt=ze("loading");return d(),B("div",Yt,[Le((d(),B("div",qt,[s("div",Wt,[e(i,{type:"danger",size:"small",icon:W(gt),circle:"",onClick:F},null,8,["icon"])]),K.value?(d(),V(b,{key:0,title:E.value||"加载项目列表失败",type:"error",closable:!1,"show-icon":""},{default:a(()=>[t[11]||(t[11]=s("p",null,"请尝试重新登录后再试",-1)),e(i,{type:"primary",size:"small",onClick:ot},{default:a(()=>t[10]||(t[10]=[$("重新登录")])),_:1,__:[10]})]),_:1},8,["title"])):ce("",!0),K.value?ce("",!0):(d(),V(ye,{key:1,class:"stats-card",shadow:"never"},{header:a(()=>[s("div",Ht,[s("span",null,h(_.title||"项目数据概览"),1),e(ue,{content:"显示当前筛选条件下的项目统计信息",placement:"top"},{default:a(()=>[e(q,{class:"stats-icon"},{default:a(()=>[e(W(Ke))]),_:1})]),_:1})])]),default:a(()=>[s("div",Kt,[s("div",Gt,[s("div",Jt,[e(q,null,{default:a(()=>[e(W(yt))]),_:1})]),s("div",Qt,[t[12]||(t[12]=s("div",{class:"stat-title"},"项目总数",-1)),s("div",Xt,h(C.value.length),1)])]),s("div",Zt,[s("div",el,[e(q,null,{default:a(()=>[e(W(Ge))]),_:1})]),s("div",tl,[t[13]||(t[13]=s("div",{class:"stat-title"},"总投资金额",-1)),s("div",ll,h(ae.value)+"万",1)])]),s("div",al,[s("div",ol,[e(q,null,{default:a(()=>[e(W(Je))]),_:1})]),s("div",sl,[t[14]||(t[14]=s("div",{class:"stat-title"},"逾期项目",-1)),s("div",nl,h(X.value)+"个",1)])])])]),_:1})),K.value?ce("",!0):(d(),V(ye,{key:2,class:"project-table-card",shadow:"never"},{header:a(()=>[s("div",rl,[s("div",il,h(_.title||(_.entity?`${_.entity} - 项目列表`:"所有项目列表")),1),s("div",ul,[s("div",dl,[e(xe,{modelValue:j.value,"onUpdate:modelValue":t[1]||(t[1]=u=>j.value=u),placeholder:"搜索项目...",clearable:"",size:"small",style:{width:"220px"},onInput:De},{prefix:a(()=>[e(q,null,{default:a(()=>[e(W(bt))]),_:1})]),append:a(()=>[e(je,{modelValue:M.value,"onUpdate:modelValue":t[0]||(t[0]=u=>M.value=u),placeholder:"搜索字段",size:"small",style:{width:"110px"}},{default:a(()=>[e(H,{label:"全部字段",value:"all"}),e(H,{label:"项目编号",value:"project_code"}),e(H,{label:"项目名称",value:"project_name"}),e(H,{label:"投资主体",value:"investment_entity"}),e(H,{label:"当前阶段",value:"current_progress"}),e(H,{label:"任务名称1",value:"project_name_1"})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),e(ut,{onCommand:te,trigger:"click"},{dropdown:a(()=>[e(it,null,{default:a(()=>[(d(!0),B(_e,null,ke(Z.value,u=>(d(),V(rt,{key:u.prop,command:u.prop},{default:a(()=>[e(nt,{modelValue:u.visible,"onUpdate:modelValue":x=>u.visible=x,onClick:t[2]||(t[2]=Se(()=>{},["stop"]))},{default:a(()=>[$(h(u.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["command"]))),128))]),_:1})]),default:a(()=>[e(i,{size:"small"},{default:a(()=>[t[15]||(t[15]=$(" 显示/隐藏列 ")),e(q,{class:"el-icon--right"},{default:a(()=>[e(W(wt))]),_:1})]),_:1,__:[15]})]),_:1}),e(i,{type:"success",size:"small",onClick:Be},{default:a(()=>[e(q,null,{default:a(()=>[e(W(ht))]),_:1}),t[16]||(t[16]=$("新增项目 "))]),_:1,__:[16]}),e(i,{type:"primary",size:"small",onClick:G,icon:W(Qe)},{default:a(()=>t[17]||(t[17]=[$("导出Excel")])),_:1,__:[17]},8,["icon"]),e(i,{type:"success",size:"small",onClick:J,disabled:!k.value||z.value,loading:z.value},{default:a(()=>[$(h(z.value?"保存中...":"保存修改"),1)]),_:1},8,["disabled","loading"]),e(i,{type:"warning",size:"small",onClick:fe,disabled:!k.value||z.value},{default:a(()=>t[18]||(t[18]=[$(" 取消修改 ")])),_:1,__:[18]},8,["disabled"])])])]),default:a(()=>[e(dt,{data:C.value,border:"",stripe:"","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",fontWeight:"bold",fontSize:"13px"},"cell-style":O,"row-class-name":at,style:{width:"100%"},onCellDblclick:Ce,size:"small",height:"calc(100vh - 300px)","row-style":{height:"36px"}},{default:a(()=>[e(he,{type:"index",label:"序号",width:"60",fixed:"left"},{header:a(()=>t[19]||(t[19]=[s("div",{style:{"background-color":"#f5f7fa",padding:"0 8px"}},"序号",-1)])),default:a(u=>[s("div",cl,h(u.$index+1),1)]),_:1}),e(he,{prop:"project_code",label:"项目编号",width:"100",fixed:"left"},{header:a(()=>t[20]||(t[20]=[s("div",{style:{"background-color":"#f5f7fa",padding:"0 8px"}},"项目编号",-1)])),default:a(u=>[s("div",pl,[u.row.isNew?(d(),V(xe,{key:0,modelValue:u.row.project_code,"onUpdate:modelValue":x=>u.row.project_code=x,placeholder:"请输入项目编号",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):(d(),B("span",_l,h(u.row.project_code),1))])]),_:1}),e(he,{prop:"project_name",label:"项目名称",width:"150",fixed:"left"},{header:a(()=>t[21]||(t[21]=[s("div",{style:{"background-color":"#f5f7fa",padding:"0 8px"}},"项目名称",-1)])),default:a(u=>[s("div",ml,[u.row.isNew?(d(),V(xe,{key:0,modelValue:u.row.project_name,"onUpdate:modelValue":x=>u.row.project_name=x,placeholder:"请输入项目名称",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):(d(),B("span",vl,h(u.row.project_name),1))])]),_:1}),e(he,{prop:"investment_entity",label:"投资主体",width:"100"},{default:a(u=>[u.row.isNew?(d(),V(je,{key:0,modelValue:u.row.investment_entity,"onUpdate:modelValue":x=>u.row.investment_entity=x,placeholder:"请选择或输入",size:"small",filterable:"","allow-create":"","default-first-option":"",style:{width:"100%"}},{default:a(()=>[(d(!0),B(_e,null,ke(m.investment_entity,x=>(d(),V(H,{key:x,label:x,value:x},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):(d(),B("span",{key:1,onDblclick:Se(x=>o(u.row),["stop"])},h(u.row.investment_entity),41,fl))]),_:1}),e(he,{prop:"current_progress",label:"当前阶段",width:"100"},{default:a(u=>[u.row.isNew?(d(),V(je,{key:0,modelValue:u.row.current_progress,"onUpdate:modelValue":x=>u.row.current_progress=x,placeholder:"请选择阶段",size:"small",style:{width:"100%"}},{default:a(()=>[(d(!0),B(_e,null,ke(m.current_progress,x=>(d(),V(H,{key:x,label:x,value:x},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):(d(),V(Ae,{key:1,type:D(u.row.current_progress),size:"small",onDblclick:Se(x=>p(u.row),["stop"])},{default:a(()=>[$(h(u.row.current_progress),1)]),_:2},1032,["type","onDblclick"]))]),_:1}),(d(!0),B(_e,null,ke(I.value,u=>(d(),V(he,{key:u.prop,prop:u.prop,label:u.label,width:u.width,"show-overflow-tooltip":!0},{default:a(x=>[x.row.isNew?(d(),B(_e,{key:0},[pe(u.prop)?(d(),V(Re,{key:0,modelValue:x.row[u.prop],"onUpdate:modelValue":de=>x.row[u.prop]=de,type:"date",placeholder:"选择日期",size:"small",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):ee(u.prop)?(d(),V(je,{key:1,modelValue:x.row[u.prop],"onUpdate:modelValue":de=>x.row[u.prop]=de,placeholder:Me(u.prop)?"请选择或输入":"请选择",size:"small",filterable:Me(u.prop),"allow-create":Me(u.prop),"default-first-option":Me(u.prop),style:{width:"100%"}},{default:a(()=>[(d(!0),B(_e,null,ke(ve(u.prop),de=>(d(),V(H,{key:de,label:de,value:de},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","filterable","allow-create","default-first-option"])):(d(),V(xe,{key:2,modelValue:x.row[u.prop],"onUpdate:modelValue":de=>x.row[u.prop]=de,placeholder:"请输入",size:"small"},null,8,["modelValue","onUpdate:modelValue"]))],64)):(d(),B(_e,{key:1},[pe(u.prop)?(d(),B("span",gl,h(oe(x.row[u.prop])),1)):u.prop==="delayed"?(d(),B("span",yl,[e(Ae,{type:x.row.delayed?"danger":"success",size:"small"},{default:a(()=>[$(h(x.row.delayed?"是":"否"),1)]),_:2},1032,["type"])])):ee(u.prop)?(d(),B("span",{key:2,onDblclick:Se(de=>st(x.row,u.prop,u.label),["stop"])},h(Y(x.row[u.prop],u.prop)),41,bl)):(d(),B("span",wl,h(Y(x.row[u.prop],u.prop)),1))],64))]),_:2},1032,["prop","label","width"]))),128)),e(he,{prop:"project_name_1",label:"任务名称1",width:"150"},{default:a(u=>[u.row.isNew?(d(),V(xe,{key:0,modelValue:u.row.project_name_1,"onUpdate:modelValue":x=>u.row.project_name_1=x,placeholder:"请输入任务名称1",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):(d(),B("span",hl,h(u.row.project_name_1),1))]),_:1}),e(he,{label:"操作",width:"100",fixed:"right"},{header:a(()=>t[22]||(t[22]=[s("div",{style:{"background-color":"#f5f7fa",padding:"0 8px"}},"操作",-1)])),default:a(u=>[s("div",kl,[u.row.isNew?(d(),B("div",xl,[e(i,{type:"primary",link:"",size:"small",onClick:x=>et(u.row)},{default:a(()=>t[23]||(t[23]=[$("保存")])),_:2,__:[23]},1032,["onClick"]),e(i,{type:"danger",link:"",size:"small",onClick:x=>tt(u.row)},{default:a(()=>t[24]||(t[24]=[$("取消")])),_:2,__:[24]},1032,["onClick"])])):(d(),V(i,{key:1,type:"danger",link:"",size:"small",onClick:x=>lt(u.row)},{default:a(()=>t[25]||(t[25]=[$("删除")])),_:2,__:[25]},1032,["onClick"]))])]),_:1})]),_:1},8,["data"])]),_:1})),!L.value&&!K.value&&C.value.length===0?(d(),V(ct,{key:3,description:"暂无项目数据"})):ce("",!0)])),[[vt,L.value]]),e(mt,{modelValue:S.value,"onUpdate:modelValue":t[9]||(t[9]=u=>S.value=u),title:`编辑${((Fe=N.value)==null?void 0:Fe.label)||""}`,width:"500px","destroy-on-close":""},{footer:a(()=>[e(i,{onClick:t[8]||(t[8]=u=>S.value=!1)},{default:a(()=>t[26]||(t[26]=[$("取消")])),_:1,__:[26]}),e(i,{type:"primary",onClick:T},{default:a(()=>t[27]||(t[27]=[$("确定")])),_:1,__:[27]})]),default:a(()=>[e(_t,{model:P,"label-position":"top"},{default:a(()=>{var u;return[e(pt,{label:((u=N.value)==null?void 0:u.label)||""},{default:a(()=>{var x,de,Oe,Ye,qe;return[ee((x=N.value)==null?void 0:x.prop)?(d(),B(_e,{key:0},[((de=N.value)==null?void 0:de.prop)==="investment_entity"||((Oe=N.value)==null?void 0:Oe.prop)==="responsible_department"?(d(),V(je,{key:0,modelValue:P.value,"onUpdate:modelValue":t[3]||(t[3]=ne=>P.value=ne),placeholder:"请选择或输入",filterable:"","allow-create":"","default-first-option":"",style:{width:"100%"}},{default:a(()=>{var ne;return[(d(!0),B(_e,null,ke(ve((ne=N.value)==null?void 0:ne.prop),$e=>(d(),V(H,{key:$e,label:$e,value:$e},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])):(d(),V(je,{key:1,modelValue:P.value,"onUpdate:modelValue":t[4]||(t[4]=ne=>P.value=ne),placeholder:"请选择",style:{width:"100%"}},{default:a(()=>{var ne;return[(d(!0),B(_e,null,ke(ve((ne=N.value)==null?void 0:ne.prop),$e=>(d(),V(H,{key:$e,label:$e,value:$e},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"]))],64)):pe((Ye=N.value)==null?void 0:Ye.prop)?(d(),V(Re,{key:1,modelValue:P.value,"onUpdate:modelValue":t[5]||(t[5]=ne=>P.value=ne),type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue"])):we((qe=N.value)==null?void 0:qe.prop)?(d(),V(xe,{key:2,modelValue:P.value,"onUpdate:modelValue":t[6]||(t[6]=ne=>P.value=ne),type:"textarea",rows:4,placeholder:"请输入内容"},null,8,["modelValue"])):(d(),V(xe,{key:3,modelValue:P.value,"onUpdate:modelValue":t[7]||(t[7]=ne=>P.value=ne),placeholder:"请输入内容"},null,8,["modelValue"]))]}),_:1},8,["label"])]}),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Vl=Ee($l,[["__scopeId","data-v-1fc18950"]]);const Cl={class:"dialog-footer"},Dl={style:{"margin-bottom":"15px"}},jl={style:{"margin-top":"15px",display:"flex","justify-content":"center"}},Ul={key:0,style:{"margin-top":"15px",color:"#909399","font-size":"12px"}},Il={key:1,style:{"margin-top":"15px",color:"#909399","font-size":"12px"}},Bl={__name:"MemberEditDialog",props:{modelValue:{type:Boolean,default:!1},member:{type:Object,default:()=>({})},entity:{type:String,required:!0},isEdit:{type:Boolean,default:!1}},emits:["update:modelValue","save","cancel"],setup(re,{emit:ie}){const _=re,le=ie,F=f(_.modelValue);Ve(()=>_.modelValue,k=>{F.value=k}),Ve(F,k=>{le("update:modelValue",k)});const L=f(null),C=f(!1),c=f({id:void 0,NAME:"",UserID:"",staff_category:2,start_time:new Date().toISOString().split("T")[0],end_time:"",status:"active",company_name:"",department_name:"",LaborCost:0,investment_entity:_.entity}),K={NAME:[{required:!0,message:"请输入姓名",trigger:"blur"}],staff_category:[{required:!0,message:"请选择员工类别",trigger:"change"}],start_time:[{required:!0,message:"请选择开始时间",trigger:"change"}]},E=f(!1),j=f(""),M=f([]),S=f(1),R=f(20),N=ge(()=>{const k=(S.value-1)*R.value,n=k+R.value;return Q.value.slice(k,n)}),P=k=>{S.value=k},A=k=>{R.value=k,(S.value-1)*R.value>=Q.value.length&&(S.value=1)},Q=ge(()=>M.value),z=f(!1),Z=()=>{_.member&&(c.value=Te(_.member),c.value.start_time&&typeof c.value.start_time=="string"&&(c.value.start_time=c.value.start_time.split(" ")[0]),c.value.end_time&&typeof c.value.end_time=="string"&&(c.value.end_time=c.value.end_time.split(" ")[0]),c.value.investment_entity=_.entity)};Ve(()=>c.value.status,k=>{k==="expired"&&!c.value.end_time?c.value.end_time=new Date().toISOString().split("T")[0]:k==="active"&&(c.value.end_time="")});const be=async()=>{E.value=!0,z.value=!0;try{await g()}catch(k){console.error("加载用户数据失败:",k),w.error("加载用户数据失败，请稍后重试")}finally{z.value=!1}},g=async()=>{var k;if(!z.value){z.value=!0;try{console.log("加载所有用户");const n=await He("");if(console.log("API返回数据:",n),n.code===200&&((k=n.data)!=null&&k.users)){if(M.value=n.data.users.sort((D,G)=>{const te=D.UserID||"",Y=G.UserID||"",oe=te.match(/\d+/),ee=Y.match(/\d+/);return oe&&ee?parseInt(oe[0])-parseInt(ee[0]):te.localeCompare(Y)}),console.log(`成功获取到 ${M.value.length} 个用户，已按编号排序`),M.value.length>0){const D=M.value[0];console.log("第一个用户数据示例:",{UserID:D.UserID,name:D.name,company_name:D.company_name,department_name:D.department_name})}}else console.warn("API返回异常:",n),w.warning(n.message||"获取用户数据失败"),M.value=[]}catch(n){console.error("加载用户错误:",n),w.error("加载用户失败，请稍后重试"),M.value=[]}finally{z.value=!1}}},r=async()=>{var k;if(!z.value){z.value=!0;try{if(!j.value.trim()){await g();return}console.log("开始搜索用户:",j.value);const n=await He(j.value);if(console.log("API返回数据:",n),n.code===200&&((k=n.data)!=null&&k.users)){if(M.value=n.data.users.sort((D,G)=>{const te=D.UserID||"",Y=G.UserID||"",oe=te.match(/\d+/),ee=Y.match(/\d+/);return oe&&ee?parseInt(oe[0])-parseInt(ee[0]):te.localeCompare(Y)}),console.log(`成功获取到 ${M.value.length} 个用户，已按编号排序`),M.value.length>0){const D=M.value[0];console.log("第一个用户数据示例:",{UserID:D.UserID,name:D.name,company_name:D.company_name,department_name:D.department_name})}}else console.warn("API返回异常:",n),w.warning(n.message||"获取用户数据失败"),M.value=[]}catch(n){console.error("搜索用户错误:",n),w.error("搜索用户失败，请稍后重试"),M.value=[]}finally{z.value=!1}}},m=k=>{c.value.UserID=k.UserID,c.value.NAME=k.name||c.value.NAME,c.value.company_name=k.company_name||c.value.company_name,c.value.department_name=k.department_name||c.value.department_name,c.value.LaborCost=k.LaborCost||c.value.LaborCost,E.value=!1},ae=()=>{L.value.validate(async k=>{if(!k){w.warning("请填写必填字段");return}C.value=!0;try{const n=Te(c.value);typeof n.staff_category=="string"&&(n.staff_category=parseInt(n.staff_category)),n.investment_entity=_.entity,le("save",n)}catch(n){console.error("保存成员错误:",n),w.error(n.message||"保存失败")}finally{C.value=!1}})},X=()=>{le("cancel"),F.value=!1},I=()=>{var k;(k=L.value)==null||k.resetFields()};return Ne(()=>{Z()}),(k,n)=>{const D=v("el-input"),G=v("el-form-item"),te=v("el-button"),Y=v("el-option"),oe=v("el-select"),ee=v("el-date-picker"),pe=v("el-radio"),we=v("el-radio-group"),ve=v("el-input-number"),p=v("el-form"),o=v("el-table-column"),T=v("el-table"),me=v("el-pagination"),J=v("el-dialog"),fe=ze("loading");return d(),V(J,{modelValue:F.value,"onUpdate:modelValue":n[13]||(n[13]=y=>F.value=y),title:re.isEdit?"编辑成员":"添加成员",width:"500px","destroy-on-close":"",onClosed:I},{footer:a(()=>[s("div",Cl,[e(te,{onClick:X},{default:a(()=>n[17]||(n[17]=[$("取消")])),_:1,__:[17]}),e(te,{type:"primary",onClick:ae,loading:C.value},{default:a(()=>n[18]||(n[18]=[$("保存")])),_:1,__:[18]},8,["loading"])])]),default:a(()=>[e(p,{ref_key:"formRef",ref:L,model:c.value,rules:K,"label-width":"100px",style:{"max-height":"60vh","overflow-y":"auto"}},{default:a(()=>[e(G,{label:"姓名",prop:"NAME"},{default:a(()=>[e(D,{modelValue:c.value.NAME,"onUpdate:modelValue":n[0]||(n[0]=y=>c.value.NAME=y),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),e(G,{label:"用户ID",prop:"UserID"},{default:a(()=>[e(D,{modelValue:c.value.UserID,"onUpdate:modelValue":n[1]||(n[1]=y=>c.value.UserID=y),placeholder:"请输入用户ID"},null,8,["modelValue"]),e(te,{type:"primary",link:"",style:{"margin-left":"8px"},onClick:be},{default:a(()=>n[14]||(n[14]=[$(" 选择用户 ")])),_:1,__:[14]})]),_:1}),e(G,{label:"员工类别",prop:"staff_category"},{default:a(()=>[e(oe,{modelValue:c.value.staff_category,"onUpdate:modelValue":n[2]||(n[2]=y=>c.value.staff_category=y),placeholder:"请选择员工类别",style:{width:"100%"}},{default:a(()=>[e(Y,{label:"团队负责人",value:1}),e(Y,{label:"团队成员",value:2}),e(Y,{label:"见习",value:3}),e(Y,{label:"客户方人员",value:4}),e(Y,{label:"临时人员",value:5})]),_:1},8,["modelValue"])]),_:1}),e(G,{label:"开始时间",prop:"start_time"},{default:a(()=>[e(ee,{modelValue:c.value.start_time,"onUpdate:modelValue":n[3]||(n[3]=y=>c.value.start_time=y),type:"date",placeholder:"选择开始时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e(G,{label:"结束时间",prop:"end_time"},{default:a(()=>[e(ee,{modelValue:c.value.end_time,"onUpdate:modelValue":n[4]||(n[4]=y=>c.value.end_time=y),type:"date",placeholder:"选择结束时间",style:{width:"100%"},"value-format":"YYYY-MM-DD",disabled:c.value.status==="active"},null,8,["modelValue","disabled"])]),_:1}),e(G,{label:"状态",prop:"status"},{default:a(()=>[e(we,{modelValue:c.value.status,"onUpdate:modelValue":n[5]||(n[5]=y=>c.value.status=y)},{default:a(()=>[e(pe,{label:"active"},{default:a(()=>n[15]||(n[15]=[$("活跃")])),_:1,__:[15]}),e(pe,{label:"expired"},{default:a(()=>n[16]||(n[16]=[$("已过期")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),e(G,{label:"公司",prop:"company_name"},{default:a(()=>[e(D,{modelValue:c.value.company_name,"onUpdate:modelValue":n[6]||(n[6]=y=>c.value.company_name=y),placeholder:"请输入公司名称"},null,8,["modelValue"])]),_:1}),e(G,{label:"部门",prop:"department_name"},{default:a(()=>[e(D,{modelValue:c.value.department_name,"onUpdate:modelValue":n[7]||(n[7]=y=>c.value.department_name=y),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1}),e(G,{label:"人工费",prop:"LaborCost"},{default:a(()=>[e(ve,{modelValue:c.value.LaborCost,"onUpdate:modelValue":n[8]||(n[8]=y=>c.value.LaborCost=y),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),e(J,{modelValue:E.value,"onUpdate:modelValue":n[12]||(n[12]=y=>E.value=y),title:"选择用户",width:"800px","append-to-body":""},{default:a(()=>[s("div",Dl,[e(D,{modelValue:j.value,"onUpdate:modelValue":n[9]||(n[9]=y=>j.value=y),placeholder:"搜索用户名、ID、公司或部门",clearable:"",style:{width:"300px"},onKeyup:kt(r,["enter"])},null,8,["modelValue"]),e(te,{type:"primary",onClick:r,style:{"margin-left":"10px"},loading:z.value},{default:a(()=>n[19]||(n[19]=[$("搜索")])),_:1,__:[19]},8,["loading"])]),Le((d(),V(T,{data:N.value,border:"",stripe:"",height:"400px",onRowClick:m,style:{width:"100%"}},{default:a(()=>[e(o,{prop:"UserID",label:"用户ID",width:"120",sortable:""}),e(o,{prop:"name",label:"姓名",width:"100"}),e(o,{prop:"company_name",label:"公司",width:"150"}),e(o,{prop:"department_name",label:"部门",width:"150"}),e(o,{prop:"LaborCost",label:"人工费",width:"100"},{default:a(y=>[$(h(y.row.LaborCost?y.row.LaborCost:"0"),1)]),_:1})]),_:1},8,["data"])),[[fe,z.value]]),s("div",jl,[M.value.length>0?(d(),V(me,{key:0,"current-page":S.value,"onUpdate:currentPage":n[10]||(n[10]=y=>S.value=y),"page-size":R.value,"onUpdate:pageSize":n[11]||(n[11]=y=>R.value=y),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:M.value.length,onSizeChange:A,onCurrentChange:P},null,8,["current-page","page-size","total"])):ce("",!0)]),M.value.length>0?(d(),B("div",Ul," 共找到 "+h(M.value.length)+" 个用户，点击选择 ",1)):z.value?ce("",!0):(d(),B("div",Il," 未找到匹配的用户，请修改搜索条件 "))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])}}},Ml=Ee(Bl,[["__scopeId","data-v-28ce257b"]]);const Sl={class:"team-management-dialog"},Tl={class:"control-panel"},El={class:"search-box"},Nl={class:"filter-box"},zl={class:"stats-panel"},Ll={class:"stat-item"},Pl={class:"stat-value"},Al={class:"stat-item"},Rl={class:"stat-value"},Fl={class:"stat-item"},Ol={class:"stat-value"},Yl={class:"stat-item"},ql={class:"stat-value"},Wl={class:"stat-item"},Hl={class:"stat-value"},Kl={class:"stat-item"},Gl={class:"stat-value"},Jl={class:"dialog-footer",style:{"margin-top":"15px"}},Ql={class:"left-buttons"},Xl={class:"right-buttons"},Zl={key:1,class:"status-bar"},ea={__name:"TeamManagementDialog",props:{entity:{type:String,required:!0},modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","refresh"],setup(re,{emit:ie}){const _=re,le=ie,F=f(_.modelValue);Ve(()=>_.modelValue,p=>{F.value=p,p&&r()}),Ve(F,p=>{le("update:modelValue",p)});const L=f(!1),C=f(!1),c=f(!1),K=f(""),E=f(""),j=f([]),M=f([]),S=f(!1),R=f(""),N=f("all"),P=f("all"),A=f(!1),Q=f(null),z=f(!1),Z=f([]),be=ge(()=>Z.value.length>0),g=ge(()=>{let p=[...j.value];if(R.value){const o=R.value.toLowerCase();p=p.filter(T=>T.NAME&&T.NAME.toLowerCase().includes(o)||T.UserID&&T.UserID.toLowerCase().includes(o)||T.company_name&&T.company_name.toLowerCase().includes(o)||T.department_name&&T.department_name.toLowerCase().includes(o))}return N.value!=="all"&&(p=p.filter(o=>o.staff_category===parseInt(N.value)||o.staff_category===parseFloat(N.value))),P.value!=="all"&&(p=p.filter(o=>o.status===P.value)),p}),r=async()=>{var p;if(_.entity){L.value=!0,c.value=!1,K.value="",E.value="正在加载团队成员数据...";try{console.log("请求团队成员参数:",_.entity);const o=await At(_.entity);o.code===200&&((p=o.data)!=null&&p.members)?(j.value=o.data.members.map(T=>{const me=new Date;let J="active";return T.end_time&&(J=new Date(T.end_time)>me?"active":"expired"),{...T,status:J}}),M.value=Te(j.value),S.value=!1,E.value=`已加载 ${j.value.length} 个团队成员`):(c.value=!0,K.value=o.message||"获取团队成员失败",E.value="加载失败")}catch(o){console.error("获取团队成员错误:",o),c.value=!0,K.value=o.message||"获取团队成员时发生错误",E.value="加载失败"}finally{L.value=!1}}},m=p=>({1:"团队负责人",2:"团队成员",3:"见习",4:"客户方人员",5:"临时人员"})[p]||"未知",ae=p=>({1:"danger",2:"primary",3:"warning",4:"success",5:"info"})[p]||"",X=({row:p})=>p.staff_category===1?"leader-row":p.status==="expired"?"expired-row":"",I=p=>j.value.filter(o=>o.staff_category===p).length,k=()=>{},n=()=>{},D=()=>{},G=p=>{Y(p)},te=()=>{Q.value={NAME:"",UserID:"",staff_category:2,start_time:new Date().toISOString().split("T")[0],end_time:"",status:"active",investment_entity:_.entity},z.value=!1,A.value=!0},Y=p=>{Q.value=Te(p),z.value=!0,A.value=!0},oe=p=>{Ue.confirm(`确定要删除成员 "${p.NAME}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const o=j.value.findIndex(T=>T.id===p.id);o!==-1&&(j.value.splice(o,1),S.value=!0,E.value=`已标记删除成员 "${p.NAME}"，点击保存按钮完成删除操作`)}).catch(()=>{})},ee=()=>{Z.value.length!==0&&(Z.value.map(p=>p.NAME).join("、"),Ue.confirm(`确定要删除选中的 ${Z.value.length} 个成员吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const p=Z.value.map(o=>o.id);j.value=j.value.filter(o=>!p.includes(o.id)),S.value=!0,E.value=`已标记删除 ${Z.value.length} 个成员，点击保存按钮完成删除操作`,Z.value=[]}).catch(()=>{}))},pe=p=>{if(z.value){const o=j.value.findIndex(T=>T.id===p.id);o!==-1&&(j.value[o]=p,S.value=!0,E.value=`已修改成员 "${p.NAME}"，点击保存按钮保存所有更改`)}else j.value.push(p),S.value=!0,E.value=`已添加成员 "${p.NAME}"，点击保存按钮保存所有更改`;A.value=!1},we=async()=>{if(!S.value){w.info("没有需要保存的更改");return}try{await Ue.confirm("确定要保存对团队成员的所有更改吗？","保存确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}catch{return}C.value=!0,E.value="正在保存更改...";try{const p=new Set(M.value.map(U=>U.id).filter(U=>U!=null)),o=new Set(j.value.map(U=>U.id).filter(U=>U!=null)),T=j.value.filter(U=>!U.id||U.id&&!p.has(U.id)).map(U=>{const{status:se,id:Ce,...Be}=U;return Be}),me=j.value.filter(U=>U.id&&p.has(U.id)).map(U=>{const{status:se,...Ce}=U;return Ce}),J=M.value.filter(U=>U.id&&!o.has(U.id)).map(U=>({id:U.id})),fe={add:T,update:me,delete:J};console.log("保存团队成员操作:",fe);const y=await Rt(_.entity,fe);if(y.code===200)w.success("团队成员更新成功"),await r(),le("refresh");else throw new Error(y.message||"保存失败")}catch(p){console.error("保存团队成员错误:",p),w.error(p.message||"保存团队成员时发生错误"),E.value="保存失败"}finally{C.value=!1}},ve=()=>{S.value?Ue.confirm("有未保存的更改，确定要关闭吗？","关闭确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{F.value=!1}).catch(()=>{}):F.value=!1};return Ne(()=>{F.value&&r()}),(p,o)=>{const T=v("el-alert"),me=v("el-input"),J=v("el-option"),fe=v("el-select"),y=v("el-table-column"),U=v("el-tag"),se=v("el-button"),Ce=v("el-table"),Be=v("el-dialog"),Pe=ze("loading");return d(),V(Be,{modelValue:F.value,"onUpdate:modelValue":o[6]||(o[6]=O=>F.value=O),title:`${re.entity} - 团队成员管理`,width:"80%","destroy-on-close":"",onClosed:ve},{default:a(()=>[Le((d(),B("div",Sl,[c.value?(d(),V(T,{key:0,title:K.value||"加载团队成员失败",type:"error",closable:!1,"show-icon":"",style:{"margin-bottom":"15px"}},{default:a(()=>o[7]||(o[7]=[s("p",null,"请稍后重试或联系管理员",-1)])),_:1},8,["title"])):ce("",!0),s("div",Tl,[s("div",El,[e(me,{modelValue:R.value,"onUpdate:modelValue":o[0]||(o[0]=O=>R.value=O),placeholder:"搜索成员",clearable:"","prefix-icon":"Search",onInput:k},null,8,["modelValue"])]),s("div",Nl,[o[8]||(o[8]=s("span",{class:"filter-label"},"按类别筛选:",-1)),e(fe,{modelValue:N.value,"onUpdate:modelValue":o[1]||(o[1]=O=>N.value=O),placeholder:"全部",onChange:n},{default:a(()=>[e(J,{label:"全部",value:"all"}),e(J,{label:"团队负责人",value:"1"}),e(J,{label:"团队成员",value:"2"}),e(J,{label:"见习",value:"3"}),e(J,{label:"客户方人员",value:"4"}),e(J,{label:"临时人员",value:"5"})]),_:1},8,["modelValue"]),o[9]||(o[9]=s("span",{class:"filter-label",style:{"margin-left":"15px"}},"按状态筛选:",-1)),e(fe,{modelValue:P.value,"onUpdate:modelValue":o[2]||(o[2]=O=>P.value=O),placeholder:"全部",onChange:D},{default:a(()=>[e(J,{label:"全部",value:"all"}),e(J,{label:"活跃",value:"active"}),e(J,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])])]),s("div",zl,[s("div",Ll,[o[10]||(o[10]=s("span",{class:"stat-label"},"总人数:",-1)),s("span",Pl,h(g.value.length),1)]),s("div",Al,[o[11]||(o[11]=s("span",{class:"stat-label"},"团队负责人:",-1)),s("span",Rl,h(I(1)),1)]),s("div",Fl,[o[12]||(o[12]=s("span",{class:"stat-label"},"团队成员:",-1)),s("span",Ol,h(I(2)),1)]),s("div",Yl,[o[13]||(o[13]=s("span",{class:"stat-label"},"见习:",-1)),s("span",ql,h(I(3)),1)]),s("div",Wl,[o[14]||(o[14]=s("span",{class:"stat-label"},"客户方人员:",-1)),s("span",Hl,h(I(4)),1)]),s("div",Kl,[o[15]||(o[15]=s("span",{class:"stat-label"},"临时人员:",-1)),s("span",Gl,h(I(5)),1)])]),e(Ce,{data:g.value,border:"",stripe:"",style:{width:"100%","margin-top":"15px"},height:"400px","row-class-name":X,onRowDblclick:G},{default:a(()=>[e(y,{prop:"id",label:"ID",width:"60"}),e(y,{prop:"NAME",label:"姓名",width:"100"}),e(y,{prop:"UserID",label:"用户ID",width:"120"}),e(y,{label:"员工类别",width:"120"},{default:a(O=>[e(U,{type:ae(O.row.staff_category)},{default:a(()=>[$(h(m(O.row.staff_category)),1)]),_:2},1032,["type"])]),_:1}),e(y,{prop:"start_time",label:"开始时间",width:"120"}),e(y,{prop:"end_time",label:"结束时间",width:"120"}),e(y,{label:"状态",width:"100"},{default:a(O=>[e(U,{type:O.row.status==="active"?"success":"info"},{default:a(()=>[$(h(O.row.status==="active"?"活跃":"已过期"),1)]),_:2},1032,["type"])]),_:1}),e(y,{prop:"company_name",label:"公司",width:"150"}),e(y,{prop:"department_name",label:"部门",width:"150"}),e(y,{prop:"LaborCost",label:"人工费",width:"100"}),e(y,{label:"操作",width:"150",fixed:"right"},{default:a(O=>[e(se,{type:"primary",size:"small",onClick:De=>Y(O.row),icon:W(xt),circle:""},null,8,["onClick","icon"]),e(se,{type:"danger",size:"small",onClick:De=>oe(O.row),icon:W($t),circle:""},null,8,["onClick","icon"])]),_:1})]),_:1},8,["data"]),s("div",Jl,[s("div",Ql,[e(se,{type:"primary",onClick:te,disabled:L.value||C.value},{default:a(()=>o[16]||(o[16]=[$(" 添加成员 ")])),_:1,__:[16]},8,["disabled"]),e(se,{type:"danger",onClick:ee,disabled:!be.value||L.value||C.value},{default:a(()=>o[17]||(o[17]=[$(" 删除选中 ")])),_:1,__:[17]},8,["disabled"])]),s("div",Xl,[e(se,{onClick:o[3]||(o[3]=O=>F.value=!1),disabled:C.value},{default:a(()=>o[18]||(o[18]=[$("取消")])),_:1,__:[18]},8,["disabled"]),e(se,{type:"success",onClick:we,loading:C.value,disabled:!S.value||L.value},{default:a(()=>o[19]||(o[19]=[$(" 保存 ")])),_:1,__:[19]},8,["loading","disabled"])])]),E.value?(d(),B("div",Zl,h(E.value),1)):ce("",!0)])),[[Pe,L.value]]),A.value?(d(),V(Ml,{key:0,modelValue:A.value,"onUpdate:modelValue":o[4]||(o[4]=O=>A.value=O),member:Q.value,entity:re.entity,"is-edit":z.value,onSave:pe,onCancel:o[5]||(o[5]=O=>A.value=!1)},null,8,["modelValue","member","entity","is-edit"])):ce("",!0)]),_:1},8,["modelValue","title"])}}},ta=Ee(ea,[["__scopeId","data-v-72511384"]]);const la={class:"red-black-board"},aa={class:"dashboard-header"},oa={class:"action-section"},sa={class:"dashboard-content"},na={class:"stats-overview compact"},ra={class:"stat-card total-card"},ia={class:"stat-icon"},ua={class:"stat-info"},da={class:"stat-desc"},ca={class:"stat-card implementation-card"},pa={class:"stat-icon"},_a={class:"stat-info"},ma={class:"stat-value"},va={class:"stat-desc"},fa={class:"stat-card rate-card"},ga={class:"stat-icon"},ya={class:"stat-info"},ba={class:"stat-value"},wa={class:"progress-container"},ha={class:"progress-bg"},ka={class:"stat-card delayed-card"},xa={class:"stat-icon"},$a={class:"stat-info"},Va={class:"stat-value"},Ca={class:"stat-desc"},Da={class:"stat-card delayed-line-card"},ja={class:"stat-icon"},Ua={class:"stat-info"},Ia={class:"stat-value"},Ba={class:"criteria-section"},Ma={class:"criteria-item"},Sa={class:"criteria-item"},Ta={class:"board-table-section"},Ea={class:"table-header"},Na={class:"entity-cell"},za={key:1},La={class:"team-members-cell"},Pa={key:1,class:"no-members"},Aa={__name:"RedBlackBoard",setup(re){Vt();const ie=f(!1),_=f(null),le=f(!1),F=f(!1),L=f(""),C=f(""),c=f(!1),K=f(""),E=f(""),j=ge(()=>{if(!_.value||!_.value.stats)return 0;const g=_.value.stats.completionRate;if(g==null)return 0;const r=parseFloat(g);return isNaN(r)?0:r.toFixed(2)}),M=ge(()=>{if(!_.value)return[];const g=[];return _.value.progressList&&_.value.progressList.forEach((r,m)=>{g.push({number:m+1,entity:r.entity,type:"正常",totalBudget:`${r.totalBudget}万 (${r.totalCount||0}个)`,implementationBudget:`${r.implementationBudget}万 (${r.implementationCount||0}个)`,delayedBudget:"0万 (0个)",yearlyWorkingHours:`${r.workingHours||0}人次 (${r.laborCost||0}万元)`,teamMembers:N(r.teamMembers),isRed:_.value.redEntities.includes(r.entity),isBlack:!1,rawData:r})}),_.value.delayedList&&_.value.delayedList.forEach((r,m)=>{var ae;g.push({number:((ae=_.value.progressList)==null?void 0:ae.length)+m+1||m+1,entity:r.entity,type:"逾期",totalBudget:`${r.totalBudget||0}万 (${r.totalCount||0}个)`,implementationBudget:`${r.implementationBudget||0}万 (${r.implementationCount||0}个)`,delayedBudget:`${r.delayedBudget||0}万 (${r.delayedCount||0}个)`,yearlyWorkingHours:`${r.workingHours||0}人次 (${r.laborCost||0}万元)`,teamMembers:N(r.teamMembers),isRed:!1,isBlack:_.value.blackEntities.includes(r.entity),rawData:r})}),g}),S=async()=>{ie.value=!0;try{const g=le.value?{software_only:!0}:{},r=await Ot(g);r.code===200?(_.value=r.data,console.log("红黑榜数据:",_.value)):w({message:r.message||"获取红黑榜数据失败",type:"error"})}catch(g){w({message:g.message||"获取红黑榜数据时发生错误",type:"error"})}finally{ie.value=!1}},R=()=>{S()},N=g=>{if(!g||!Array.isArray(g)||g.length===0)return"无";const r=g.filter(I=>I.staff_category===1||I.staff_category==="1"),m=g.filter(I=>I.staff_category===2||I.staff_category==="2"),ae=g.filter(I=>I.staff_category===3||I.staff_category==="3"),X=[];return r.length>0&&X.push(`负责人: ${r.map(I=>I.NAME).join(", ")}`),m.length>0&&X.push(`成员: ${m.map(I=>I.NAME).join(", ")}`),ae.length>0&&X.push(`见习: ${ae.map(I=>I.NAME).join(", ")}`),X.join(" | ")},P=({row:g})=>g.isRed?"red-row":g.isBlack?"black-row":"";function A(g,r){Ue.confirm(`确定要查看${g.entity||"所有"}${r}项目列表吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{try{w({message:"正在加载项目数据，请耐心等待...",type:"info",duration:3e3}),L.value=g.entity||"",C.value=r,g.isRed?(K.value=`${g.entity} - ${r}项目 (红榜)`,E.value="red"):g.isBlack?(K.value=`${g.entity} - ${r}项目 (黑榜)`,E.value="black"):(K.value=`${g.entity||"所有"} - ${r}项目`,E.value=""),c.value=!0}catch(m){console.error("显示项目列表对话框出错:",m),w.error("加载项目列表失败，请稍后再试")}}).catch(()=>{})}const Q=()=>{c.value=!1,L.value="",C.value="",E.value=""};function z(){try{A({entity:"",type:"正常"},"全部")}catch(g){console.error("导航到投资详情页面出错:",g),w.error("加载投资详情失败，请稍后再试")}}const Z=g=>{L.value=g.entity,F.value=!0},be=()=>{if(!_.value){w({type:"warning",message:"没有数据可导出"});return}try{const g=Ie.book_new(),m=[["序号","投资主体","类型","总投资","开工金额","逾期金额","当年人次合计(人工费)","ITBP团队成员","是否红榜","是否黑榜"]];M.value.forEach(D=>{m.push([D.number,D.entity,D.type,D.totalBudget,D.implementationBudget,D.delayedBudget,D.yearlyWorkingHours,D.teamMembers,D.isRed?"是":"否",D.isBlack?"是":"否"])});const ae=Ie.aoa_to_sheet(m);Ie.book_append_sheet(g,ae,"红黑榜");const I=`条线项目进度榜_${new Date().toISOString().split("T")[0]}.xlsx`,k=Xe(g,{bookType:"xlsx",type:"array"}),n=new Blob([k],{type:"application/octet-stream"});Ze.saveAs(n,I),w({type:"success",message:"导出成功"})}catch(g){console.error("导出Excel失败:",g),w({type:"error",message:"导出失败: "+g.message})}};return Ne(()=>{S()}),(g,r)=>{var Y,oe,ee,pe,we,ve,p;const m=v("el-icon"),ae=v("el-checkbox"),X=v("el-button"),I=v("el-tag"),k=v("el-tooltip"),n=v("el-table-column"),D=v("el-table"),G=v("el-empty"),te=ze("loading");return d(),B("div",la,[s("div",aa,[r[5]||(r[5]=s("div",{class:"title-section"},[s("h2",{class:"main-title"},"条线项目进度榜"),s("p",{class:"subtitle"},"项目按投资类型分组展示，红色标记表示推进优秀，黑色标记表示逾期严重")],-1)),s("div",oa,[e(ae,{modelValue:le.value,"onUpdate:modelValue":r[0]||(r[0]=o=>le.value=o),onChange:R},{default:a(()=>[e(m,null,{default:a(()=>[e(W(Dt))]),_:1}),r[2]||(r[2]=$("只统计软件 "))]),_:1,__:[2]},8,["modelValue"]),e(X,{type:"primary",size:"small",onClick:be,icon:W(Qe)},{default:a(()=>r[3]||(r[3]=[$("导出Excel")])),_:1,__:[3]},8,["icon"]),e(X,{type:"primary",size:"small",onClick:S,icon:W(jt)},{default:a(()=>r[4]||(r[4]=[$("刷新数据")])),_:1,__:[4]},8,["icon"])])]),Le((d(),B("div",sa,[!ie.value&&_.value?(d(),B(_e,{key:0},[s("div",na,[s("div",ra,[s("div",ia,[e(m,null,{default:a(()=>[e(W(Ge))]),_:1})]),s("div",ua,[s("span",{class:"stat-value",style:{cursor:"pointer",color:"#409EFF"},onClick:z},h(((Y=_.value.stats)==null?void 0:Y.totalBudget)||0)+"万",1),r[6]||(r[6]=s("span",{class:"stat-label"},"总投资金额",-1)),s("span",da,h(((oe=_.value.stats)==null?void 0:oe.totalCount)||0)+"个项目",1)])]),s("div",ca,[s("div",pa,[e(m,null,{default:a(()=>[e(W(Ut))]),_:1})]),s("div",_a,[s("span",ma,h(((ee=_.value.stats)==null?void 0:ee.implementationBudget)||0)+"万",1),r[7]||(r[7]=s("span",{class:"stat-label"},"开工金额",-1)),s("span",va,h(((pe=_.value.stats)==null?void 0:pe.implementationCount)||0)+"个项目",1)])]),s("div",fa,[s("div",ga,[e(m,null,{default:a(()=>[e(W(It))]),_:1})]),s("div",ya,[s("span",ba,h(j.value)+"%",1),r[8]||(r[8]=s("span",{class:"stat-label"},"开工率",-1)),s("div",wa,[s("div",ha,[s("div",{class:"progress-bar",style:Ct({width:`${j.value}%`})},null,4)])])])]),s("div",ka,[s("div",xa,[e(m,null,{default:a(()=>[e(W(Je))]),_:1})]),s("div",$a,[s("span",Va,h(((we=_.value.stats)==null?void 0:we.delayedBudget)||0)+"万",1),r[9]||(r[9]=s("span",{class:"stat-label"},"逾期金额",-1)),s("span",Ca,h(((ve=_.value.stats)==null?void 0:ve.delayedCount)||0)+"个项目",1)])]),s("div",Da,[s("div",ja,[e(m,null,{default:a(()=>[e(W(Bt))]),_:1})]),s("div",Ua,[s("span",Ia,h(((p=_.value.delayedList)==null?void 0:p.length)||0)+"个",1),r[10]||(r[10]=s("span",{class:"stat-label"},"逾期条线",-1))])])]),s("div",Ba,[s("div",Ma,[e(I,{type:"danger",effect:"dark",class:"criteria-tag"},{default:a(()=>r[11]||(r[11]=[$("红榜")])),_:1,__:[11]}),s("span",null,h(_.value.redListCriteria||"无逾期任务，按开工金额从高到低排序"),1)]),s("div",Sa,[e(I,{type:"info",effect:"dark",class:"criteria-tag"},{default:a(()=>r[12]||(r[12]=[$("黑榜")])),_:1,__:[12]}),s("span",null,h(_.value.blackListCriteria||"存在逾期任务，按逾期金额从小到大排序"),1)])]),s("div",Ta,[s("div",Ea,[r[13]||(r[13]=s("h3",null,"红黑榜",-1)),e(k,{content:"红榜表示推进优秀，黑榜表示逾期严重",placement:"top"},{default:a(()=>[e(m,null,{default:a(()=>[e(W(Ke))]),_:1})]),_:1})]),e(D,{data:M.value,border:"","row-class-name":P,"header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",fontWeight:"bold",fontSize:"12px",padding:"8px 0"},"cell-style":{fontSize:"12px",padding:"4px 0"},class:"board-table",size:"small"},{default:a(()=>[e(n,{prop:"number",label:"序号",width:"50",align:"center"}),e(n,{prop:"entity",label:"投资主体","min-width":"120"},{default:a(o=>[s("div",Na,[s("div",{class:Mt(["entity-name",{"red-text":o.row.isRed,"black-text":o.row.isBlack}])},[$(h(o.row.entity)+" ",1),o.row.isRed?(d(),V(I,{key:0,type:"danger",size:"small",effect:"plain",class:"entity-tag"},{default:a(()=>r[14]||(r[14]=[$("红榜")])),_:1,__:[14]})):ce("",!0),o.row.isBlack?(d(),V(I,{key:1,type:"info",size:"small",effect:"plain",class:"entity-tag"},{default:a(()=>r[15]||(r[15]=[$("黑榜")])),_:1,__:[15]})):ce("",!0)],2)])]),_:1}),e(n,{prop:"type",label:"类型",width:"60"},{default:a(o=>[e(I,{type:o.row.type==="逾期"?"danger":"success",size:"small"},{default:a(()=>[$(h(o.row.type),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"totalBudget",label:"总投资",width:"130"},{default:a(o=>[e(X,{type:"primary",link:"",onClick:T=>A(o.row,"全部")},{default:a(()=>[$(h(o.row.totalBudget),1)]),_:2},1032,["onClick"])]),_:1}),e(n,{prop:"implementationBudget",label:"开工金额",width:"130"},{default:a(o=>[e(X,{type:"primary",link:"",onClick:T=>A(o.row,"开工")},{default:a(()=>[$(h(o.row.implementationBudget),1)]),_:2},1032,["onClick"])]),_:1}),e(n,{prop:"delayedBudget",label:"逾期金额",width:"130"},{default:a(o=>[o.row.type==="逾期"?(d(),V(X,{key:0,type:"danger",link:"",onClick:T=>A(o.row,"逾期")},{default:a(()=>[$(h(o.row.delayedBudget),1)]),_:2},1032,["onClick"])):(d(),B("span",za,h(o.row.delayedBudget),1))]),_:1}),e(n,{prop:"yearlyWorkingHours",label:"当年人次合计(人工费)","min-width":"160"}),e(n,{prop:"teamMembers",label:"ITBP团队成员","min-width":"180"},{default:a(o=>[s("div",La,[o.row.teamMembers!=="无"?(d(!0),B(_e,{key:0},ke(o.row.teamMembers.split(" | "),(T,me)=>(d(),B("div",{key:me,class:"team-member-group"},h(T),1))),128)):(d(),B("span",Pa,"无团队成员"))])]),_:1}),e(n,{label:"操作",width:"90"},{default:a(o=>[e(X,{type:"primary",link:"",size:"small",onClick:T=>Z(o.row)},{default:a(()=>[e(m,null,{default:a(()=>[e(W(St))]),_:1}),r[16]||(r[16]=$("团队管理 "))]),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])],64)):!ie.value&&!_.value?(d(),V(G,{key:1,description:"暂无数据"})):ce("",!0)])),[[te,ie.value]]),c.value?(d(),V(Vl,{key:0,entity:L.value,category:C.value,"highlight-type":E.value,title:K.value,onClose:Q},null,8,["entity","category","highlight-type","title"])):ce("",!0),e(ta,{modelValue:F.value,"onUpdate:modelValue":r[1]||(r[1]=o=>F.value=o),entity:L.value,onRefresh:S},null,8,["modelValue","entity"])])}}},Wa=Ee(Aa,[["__scopeId","data-v-98f4e14a"]]);export{Wa as default};
