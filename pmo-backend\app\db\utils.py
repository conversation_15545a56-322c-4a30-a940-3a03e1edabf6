from typing import Dict, List, Any, Optional, Union
from app.db.connection import get_db_connection, db_transaction
from app.core.logger import get_logger
from app.core.config import settings

logger = get_logger(__name__)

def execute_query(query: str, params: Optional[Union[Dict, List, tuple]] = None) -> List[Dict]:
    """执行查询并返回结果"""
    try:
        with db_transaction() as conn:
            if settings.DB_TYPE.lower() == 'mysql':
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    # 处理datetime字段
                    return _process_datetime_fields(rows)
            elif settings.DB_TYPE.lower() == 'sqlite':
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                rows = cursor.fetchall()
                # 将sqlite3.Row对象转换为字典
                result = []
                for row in rows:
                    result.append({key: row[key] for key in row.keys()})
                return _process_datetime_fields(result)
    except Exception as e:
        logger.error(f"执行查询错误: {str(e)}, 查询: {query}, 参数: {params}")
        raise e

def _process_datetime_fields(rows: List[Dict]) -> List[Dict]:
    """处理datetime字段，将其转换为字符串"""
    from datetime import datetime

    if not rows:
        return rows

    processed_rows = []
    for row in rows:
        processed_row = {}
        for key, value in row.items():
            if isinstance(value, datetime):
                processed_row[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            else:
                processed_row[key] = value
        processed_rows.append(processed_row)

    return processed_rows

def execute_update(query: str, params: Optional[Union[Dict, List, tuple]] = None) -> int:
    """执行更新操作并返回影响的行数"""
    try:
        with db_transaction() as conn:
            if settings.DB_TYPE.lower() == 'mysql':
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(query, params)
                    return affected_rows
            elif settings.DB_TYPE.lower() == 'sqlite':
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.rowcount
    except Exception as e:
        logger.error(f"执行更新错误: {str(e)}, 查询: {query}, 参数: {params}")
        raise e

def execute_insert(query: str, params: Optional[Union[Dict, List, tuple]] = None) -> int:
    """执行插入操作并返回最后插入的ID"""
    try:
        with db_transaction() as conn:
            if settings.DB_TYPE.lower() == 'mysql':
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    return cursor.lastrowid
            elif settings.DB_TYPE.lower() == 'sqlite':
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.lastrowid
    except Exception as e:
        logger.error(f"执行插入错误: {str(e)}, 查询: {query}, 参数: {params}")
        raise e 