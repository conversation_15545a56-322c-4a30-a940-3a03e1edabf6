#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务模型 - 参考禅道zt_task表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Task:
    """任务模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.parent: int = 0
        self.project: int = 0
        self.module: int = 0
        self.design: int = 0
        self.story: int = 0
        self.storyVersion: int = 1
        self.designVersion: int = 1
        self.fromBug: int = 0
        self.fromIssue: int = 0
        self.feedback: int = 0
        self.name: str = ""
        self.type: str = "devel"
        self.mode: str = ""
        self.pri: int = 3
        self.estimate: float = 0
        self.consumed: float = 0
        self.left: float = 0
        self.deadline: Optional[date] = None
        self.status: str = "wait"
        self.subStatus: str = ""
        self.color: str = ""
        self.mailto: Optional[str] = None
        self.desc: Optional[str] = None
        self.version: int = 1
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.assignedTo: str = ""
        self.assignedDate: Optional[datetime] = None
        self.estStarted: Optional[date] = None
        self.realStarted: Optional[datetime] = None
        self.finishedBy: str = ""
        self.finishedDate: Optional[datetime] = None
        self.finishedList: Optional[str] = None
        self.canceledBy: str = ""
        self.canceledDate: Optional[datetime] = None
        self.closedBy: str = ""
        self.closedDate: Optional[datetime] = None
        self.planDuration: int = 0
        self.realDuration: int = 0
        self.closedReason: str = ""
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.activatedDate: Optional[datetime] = None
        self.order: int = 0
        self.repo: int = 0
        self.mr: int = 0
        self.entry: str = ""
        self.codeLines: str = ""
        self.v1: str = ""
        self.v2: str = ""
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, task_id: int) -> Optional['Task']:
        """根据ID获取任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, p.name as project_name, s.title as story_title
                FROM zt_task t
                LEFT JOIN zt_project p ON t.project = p.id
                LEFT JOIN zt_story s ON t.story = s.id
                WHERE t.id = %s AND t.deleted = '0'
            """, (task_id,))
            
            row = cursor.fetchone()
            if row:
                task = cls()
                task._load_from_dict(row)
                return task
            return None
            
        except Exception as e:
            print(f"获取任务失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_tasks(cls, page: int = 1, page_size: int = 20, project_id: int = 0, 
                     status: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取任务列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["t.deleted = '0'"]
            params = []
            
            if project_id > 0:
                where_conditions.append("t.project = %s")
                params.append(project_id)
            
            if status:
                where_conditions.append("t.status = %s")
                params.append(status)
            
            if keyword:
                where_conditions.append("(t.name LIKE %s OR t.`desc` LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_task t WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT t.id, t.name, t.type, t.pri, t.status, t.assignedTo, 
                       t.estimate, t.consumed, t.`left`, t.openedBy, t.openedDate,
                       t.deadline, t.project, p.name as project_name
                FROM zt_task t
                LEFT JOIN zt_project p ON t.project = p.id
                WHERE {where_clause}
                ORDER BY t.`order` ASC, t.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            tasks = cursor.fetchall()
            
            return {
                "tasks": tasks,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取任务列表失败: {e}")
            return {"tasks": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入任务
            cursor.execute("""
                INSERT INTO zt_task (
                    vision, parent, project, module, design, story, storyVersion,
                    fromBug, fromIssue, feedback, name, type, mode, pri, estimate,
                    `left`, deadline, status, subStatus, color, mailto, `desc`,
                    openedBy, openedDate, assignedTo, estStarted, planDuration,
                    `order`
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s
                )
            """, (
                self.vision, self.parent, self.project, self.module, self.design,
                self.story, self.storyVersion, self.fromBug, self.fromIssue,
                self.feedback, self.name, self.type, self.mode, self.pri,
                self.estimate, self.left, self.deadline, self.status, self.subStatus,
                self.color, self.mailto, self.desc, self.openedBy, self.assignedTo,
                self.estStarted, self.planDuration, self.order
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新任务信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET 
                    name = %s, type = %s, pri = %s, estimate = %s, `left` = %s,
                    deadline = %s, status = %s, assignedTo = %s, `desc` = %s,
                    lastEditedBy = %s, lastEditedDate = NOW(), `order` = %s
                WHERE id = %s
            """, (
                self.name, self.type, self.pri, self.estimate, self.left,
                self.deadline, self.status, self.assignedTo, self.desc,
                self.lastEditedBy, self.order, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def assign(self, assignedTo: str, assignedBy: str) -> bool:
        """指派任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET 
                    assignedTo = %s, assignedDate = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (assignedTo, assignedBy, self.id))
            
            self.assignedTo = assignedTo
            self.assignedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"指派任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def start(self, startedBy: str) -> bool:
        """开始任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET 
                    status = 'doing', realStarted = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (startedBy, self.id))
            
            self.status = 'doing'
            self.realStarted = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"开始任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def finish(self, finishedBy: str, consumed: float = 0, left: float = 0) -> bool:
        """完成任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET 
                    status = 'done', finishedBy = %s, finishedDate = NOW(),
                    consumed = %s, `left` = %s,
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (finishedBy, consumed, left, finishedBy, self.id))
            
            self.status = 'done'
            self.finishedBy = finishedBy
            self.finishedDate = datetime.now()
            self.consumed = consumed
            self.left = left
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"完成任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def close(self, closedBy: str, closedReason: str = "") -> bool:
        """关闭任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_task SET 
                    status = 'closed', closedBy = %s, closedDate = NOW(),
                    closedReason = %s, lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (closedBy, closedReason, closedBy, self.id))
            
            self.status = 'closed'
            self.closedBy = closedBy
            self.closedDate = datetime.now()
            self.closedReason = closedReason
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"关闭任务失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "vision": self.vision,
            "parent": self.parent,
            "project": self.project,
            "module": self.module,
            "story": self.story,
            "name": self.name,
            "type": self.type,
            "pri": self.pri,
            "estimate": self.estimate,
            "consumed": self.consumed,
            "left": self.left,
            "deadline": self.deadline.isoformat() if self.deadline else None,
            "status": self.status,
            "subStatus": self.subStatus,
            "color": self.color,
            "desc": self.desc,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "assignedTo": self.assignedTo,
            "assignedDate": self.assignedDate.isoformat() if self.assignedDate else None,
            "estStarted": self.estStarted.isoformat() if self.estStarted else None,
            "realStarted": self.realStarted.isoformat() if self.realStarted else None,
            "finishedBy": self.finishedBy,
            "finishedDate": self.finishedDate.isoformat() if self.finishedDate else None,
            "closedBy": self.closedBy,
            "closedDate": self.closedDate.isoformat() if self.closedDate else None,
            "closedReason": self.closedReason,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "order": self.order,
            "deleted": self.deleted
        }


class Team:
    """团队成员模型类"""

    def __init__(self):
        self.id: Optional[int] = None
        self.root: int = 0
        self.type: str = "project"
        self.account: str = ""
        self.role: str = ""
        self.limited: str = "no"
        self.joinDate: Optional[date] = None
        self.days: float = 0
        self.hours: float = 7
        self.estimate: float = 0
        self.consumed: float = 0
        self.left: float = 0
        self.order: int = 0

    @classmethod
    def add_member(cls, project_id: int, account: str, role: str = "", days: float = 22, hours: float = 8) -> bool:
        """添加团队成员"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # 检查成员是否已存在
            cursor.execute("""
                SELECT id FROM zt_team
                WHERE root = %s AND type = 'project' AND account = %s
            """, (project_id, account))

            if cursor.fetchone():
                raise ValueError(f"用户 {account} 已是项目成员")

            cursor.execute("""
                INSERT INTO zt_team (root, type, account, role, joinDate, days, hours)
                VALUES (%s, 'project', %s, %s, CURDATE(), %s, %s)
            """, (project_id, account, role, days, hours))

            conn.commit()
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"添加团队成员失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def remove_member(cls, project_id: int, account: str) -> bool:
        """移除团队成员"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("""
                DELETE FROM zt_team
                WHERE root = %s AND type = 'project' AND account = %s
            """, (project_id, account))

            conn.commit()
            return True

        except Exception as e:
            if conn:
                conn.rollback()
            print(f"移除团队成员失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)
