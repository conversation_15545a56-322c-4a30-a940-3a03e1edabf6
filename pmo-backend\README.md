# PMO系统后端

## 项目介绍

PMO系统后端是一个基于FastAPI的RESTful API服务，用于支持PMO系统的Web应用、桌面客户端和移动应用。本项目采用了直接迁移云函数代码的方式，保留了原有的业务逻辑，同时提供了统一的API接口。

## 技术栈

- Python 3.9+
- FastAPI
- PyMySQL
- SQLite (可选)
- JWT认证

## 目录结构

```
pmo-backend/
├── .env                     # 环境变量配置
├── main.py                  # 应用入口
├── requirements.txt         # 依赖管理
├── app/                     # 应用核心
│   ├── api/                 # API路由
│   ├── core/                # 核心功能
│   ├── db/                  # 数据库
│   ├── cloud_functions/     # 迁移的云函数代码
│   └── utils/               # 工具函数
└── tests/                   # 测试
```

## 安装步骤

1. 克隆项目

```bash
git clone https://github.com/your-username/pmo-system.git
cd pmo-system/pmo-backend
```

2. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

4. 配置环境变量

复制`.env.example`文件为`.env`，并根据实际情况修改配置：

```bash
cp .env.example .env
```

5. 运行应用

```bash
python main.py
```

应用将在 http://localhost:8000 上运行，API文档可在 http://localhost:8000/api/v1/docs 访问。

## API接口

系统提供以下主要API接口：

- 认证API: `/api/v1/auth/`
- 项目API: `/api/v1/projects/`
- 用户API: `/api/v1/users/`
- 仪表盘API: `/api/v1/dashboard/`
- 工时API: `/api/v1/timesheet/`
- 报告API: `/api/v1/reports/`
- 选项API: `/api/v1/options/`
- 团队API: `/api/v1/teams/`

详细的API文档可以在运行应用后访问 http://localhost:8000/api/v1/docs 查看。

## 开发说明

### 云函数迁移

本项目采用了直接迁移云函数代码的方式，云函数代码位于`app/cloud_functions/`目录下，每个云函数都有自己的目录和入口文件。API路由层负责将HTTP请求转换为云函数可以处理的格式，并将云函数的响应转换为HTTP响应。

### 添加新功能

1. 在`app/cloud_functions/`目录下添加新的云函数代码
2. 在`app/api/endpoints/`目录下创建对应的API路由
3. 在`app/api/api.py`中注册新的路由

### 测试

运行单元测试：

```bash
pytest
```

运行特定测试：

```bash
pytest tests/test_api/test_projects.py
```

## 部署

### Docker部署

1. 构建Docker镜像

```bash
docker build -t pmo-backend .
```

2. 运行Docker容器

```bash
docker run -d -p 8000:8000 --name pmo-backend pmo-backend
```

### 传统部署

1. 安装依赖

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

2. 使用Gunicorn运行（生产环境）

```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

## 许可证

[MIT](LICENSE) 