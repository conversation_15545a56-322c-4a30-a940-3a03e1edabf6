# 🧠 PMO动态知识库使用指南

## 📋 概述

PMO动态知识库是一个智能化的知识管理系统，具备以下特性：

- 🔄 **实时更新**：自动监控数据库变化，实时更新知识内容
- 🤖 **AI驱动**：集成智能助手，支持自然语言查询
- 🔍 **语义搜索**：基于向量数据库的语义搜索能力
- 📊 **多维度数据**：整合项目、用户、督办、统计等多种数据

## 🚀 快速开始

### 1. 启动系统

```bash
# 启动PMO系统（知识库会自动启动）
cd pmo-backend
python main.py

# 前端
cd pmo-web
npm run dev
```

### 2. 访问智能助手

- **基础版AI助手**：http://localhost:3000/ai-assistant
- **增强版智能助手**：http://localhost:3000/intelligent-assistant
- **向量知识库管理**：http://localhost:3000/vector-knowledge

## 🎯 核心功能

### 1. 动态监控

#### 数据库监控
- 监控11个核心数据表
- 30秒检查一次变化
- 自动检测数据更新

#### 文件监控
- 监控项目档案目录
- 实时检测文件变化
- 自动更新文档内容

#### 缓存管理
- 智能缓存失效
- 按需更新数据
- 提升查询性能

### 2. 智能问答

#### 自然语言查询
```
用户：金租的项目有哪些？
助手：根据最新数据，金租共有15个项目，包括：
1. 金融租赁管理系统 - 实施阶段
2. 风险控制平台 - 验收阶段
...
```

#### 查询类型识别
- **项目查询**：项目列表、状态、进度
- **统计分析**：数量统计、金额汇总
- **人员信息**：用户权限、部门分布
- **督办管理**：督办事项、进展情况

#### 实体提取
- **投资主体**：金租、商租、汽租、集团
- **项目状态**：立项、采购、实施、验收、结项
- **时间表达**：今年、本月、最近

### 3. 向量搜索

#### 语义理解
```
查询："逾期的项目情况"
理解：查找状态为逾期或延期的项目信息
结果：返回相关项目列表和详细信息
```

#### 相似内容发现
- 找到语义相关的文档
- 发现潜在关联信息
- 提供上下文补充

## 📊 使用场景

### 1. 项目管理场景

#### 查询示例
```
Q: "金租今年新增了多少个项目？"
A: 根据2025年数据，金租新增项目8个，总投资金额2.5亿元...

Q: "实施阶段的项目有哪些问题？"
A: 当前实施阶段项目12个，主要问题包括：
   1. 进度延期：3个项目
   2. 预算超支：1个项目
   ...
```

### 2. 统计分析场景

#### 查询示例
```
Q: "各投资主体的项目分布情况"
A: 项目分布统计：
   • 金租：15个项目（占比35%）
   • 商租：12个项目（占比28%）
   • 汽租：10个项目（占比23%）
   ...
```

### 3. 督办管理场景

#### 查询示例
```
Q: "当前有哪些督办事项需要关注？"
A: 当前督办事项5个：
   1. 【紧急】系统安全评估 - 截止日期：本周五
   2. 【重要】项目验收准备 - 进度：80%
   ...
```

## 🔧 高级功能

### 1. 知识库管理

#### 手动刷新
```bash
# API调用
curl -X POST http://localhost:8000/api/intelligent-assistant/refresh-knowledge

# 前端操作
智能助手页面 -> 刷新知识库按钮
```

#### 状态监控
```bash
# 获取知识库状态
curl http://localhost:8000/api/intelligent-assistant/knowledge-status
```

### 2. 向量索引管理

#### 增量索引
- 适用于日常数据更新
- 只处理变化的数据
- 速度快，资源消耗少

#### 完全重建
- 适用于大量数据变更
- 重新构建所有向量
- 确保数据一致性

### 3. 查询优化

#### 提高查询效果的技巧
1. **使用具体关键词**
   - ❌ "项目情况"
   - ✅ "金租的实施阶段项目"

2. **指定时间范围**
   - ❌ "项目统计"
   - ✅ "今年新增项目统计"

3. **明确查询意图**
   - ❌ "用户"
   - ✅ "有权限访问系统的用户列表"

## 🛠️ 配置说明

### 1. 监控配置

```python
# 监控的数据库表
monitored_tables = [
    'project_account_book',  # 项目台账
    'users',                 # 用户信息
    'project_hours',         # 工时数据
    'supervision_items',     # 督办事项
    # ... 更多表
]

# 监控的文件目录
monitored_directories = [
    'project_archive_materials',  # 项目档案
    'temp_files',                # 临时文件
    'knowledge_export'           # 知识导出
]
```

### 2. 缓存策略

```python
# 缓存失效规则
cache_invalidation_rules = {
    'project_account_book': ['red_black_board', 'project_summary'],
    'users': ['user_data'],
    'supervision_items': ['supervision_data'],
    # ... 更多规则
}
```

### 3. 向量配置

```python
# 向量模型配置
embedding_config = {
    'model_name': 'shibing624/text2vec-base-chinese',
    'vector_size': 768,
    'chunk_size': 500,
    'overlap': 50
}
```

## 📈 性能优化

### 1. 监控频率调整

```python
# 根据数据变化频率调整检查间隔
check_interval = 30  # 秒，可根据需要调整
```

### 2. 缓存策略优化

```python
# 设置缓存过期时间
cache_expire_time = 3600  # 1小时
```

### 3. 向量索引优化

```python
# 批量处理大小
batch_size = 100

# 相似度阈值
similarity_threshold = 0.7
```

## 🚨 故障排除

### 1. 动态监控问题

#### 监控未启动
```bash
# 检查监控状态
curl http://localhost:8000/api/dynamic-knowledge/status

# 重启监控
curl -X POST http://localhost:8000/api/dynamic-knowledge/restart
```

#### 数据库连接问题
```bash
# 检查数据库连接
python -c "from app.db.utils import execute_query; print(execute_query('SELECT 1'))"
```

### 2. 向量搜索问题

#### Qdrant连接失败
```bash
# 检查Qdrant服务
docker ps | grep qdrant

# 重启Qdrant
docker-compose -f docker-compose.qdrant.yml restart
```

#### 索引失败
```bash
# 检查索引状态
curl http://localhost:8000/api/vector-knowledge/status

# 重新索引
curl -X POST http://localhost:8000/api/vector-knowledge/reindex
```

### 3. 智能助手问题

#### 查询无结果
1. 检查知识库是否有数据
2. 降低相似度阈值
3. 使用更具体的关键词

#### 响应慢
1. 检查数据库性能
2. 优化查询语句
3. 增加缓存时间

## 💡 最佳实践

### 1. 日常维护

- **每日检查**：监控状态、错误日志
- **每周优化**：清理缓存、更新索引
- **每月分析**：查询统计、性能分析

### 2. 查询技巧

- **分步查询**：复杂问题分解为简单查询
- **关键词组合**：使用多个相关关键词
- **上下文利用**：结合历史对话内容

### 3. 系统优化

- **定期备份**：知识库数据和配置
- **性能监控**：响应时间、资源使用
- **用户反馈**：收集使用体验，持续改进

## 🎉 总结

PMO动态知识库为项目管理提供了强大的智能化支持：

1. ✅ **实时性**：数据变化立即反映到知识库
2. ✅ **智能性**：自然语言查询，语义理解
3. ✅ **全面性**：覆盖项目管理各个方面
4. ✅ **易用性**：简单配置，开箱即用

通过合理使用这些功能，可以大大提升项目管理的效率和质量！
