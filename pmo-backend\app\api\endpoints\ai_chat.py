#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天API - WrenAI集成的自然语言查询接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime
import logging

from app.services.professional_ai_service import ProfessionalAIService
from app.core.ai_client import ai_client

# 初始化专业AI服务
professional_ai_service = ProfessionalAIService()

logger = logging.getLogger(__name__)

router = APIRouter()

# 请求模型
class ChatRequest(BaseModel):
    question: str = Field(..., description="用户问题", min_length=1, max_length=500)
    user_id: Optional[str] = Field(default="anonymous", description="用户ID")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    stream: Optional[bool] = Field(default=False, description="是否流式响应")

class QuickQuestionRequest(BaseModel):
    question: str = Field(..., description="快捷问题")
    user_id: Optional[str] = Field(default="anonymous", description="用户ID")

class DatabaseSummaryRequest(BaseModel):
    include_sample_data: Optional[bool] = Field(default=False, description="是否包含样本数据")

# 响应模型
class ChatResponse(BaseModel):
    success: bool
    question: str
    answer: str
    sql: Optional[str] = None
    explanation: Optional[str] = None
    data: Optional[List[Dict[str, Any]]] = None
    chart_data: Optional[Dict[str, Any]] = None
    insights: Optional[List[str]] = None
    timestamp: str
    error: Optional[str] = None

class QuickQuestionsResponse(BaseModel):
    questions: List[str]
    categories: Dict[str, List[str]]

class DatabaseSummaryResponse(BaseModel):
    tables: int
    table_info: Dict[str, Any]
    sample_questions: List[str]

@router.post("/ask", response_model=ChatResponse, summary="AI问答")
async def ask_question(request: ChatRequest, background_tasks: BackgroundTasks):
    """
    AI智能问答接口
    
    支持自然语言查询PMO系统数据，自动生成SQL并返回结果、图表和洞察分析
    """
    try:
        logger.info(f"🤖 收到AI问答请求: {request.question[:100]}...")
        
        # 验证问题长度
        if len(request.question.strip()) < 2:
            raise HTTPException(status_code=400, detail="问题太短，请提供更详细的描述")
        
        # 如果是流式响应
        if request.stream:
            return StreamingResponse(
                stream_chat_response(request),
                media_type="text/plain"
            )
        
        # 调用专业AI服务
        result = await professional_ai_service.process_question(
            question=request.question,
            user_id=request.user_id or "anonymous"
        )

        # 记录用户问题（后台任务）
        background_tasks.add_task(
            log_user_question,
            request.question,
            request.user_id,
            result.get("success", False)
        )

        # 构建响应数据，确保包含所有必需字段
        response_data = {
            "success": result.get("success", False),
            "question": request.question,
            "answer": result.get("answer", "抱歉，没有收到有效回复"),
            "sql": result.get("sql"),
            "explanation": None,
            "data": result.get("data"),
            "chart_data": result.get("chart_config"),
            "insights": None,
            "timestamp": result.get("timestamp", ""),
            "error": result.get("error")
        }

        return ChatResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ AI问答处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI服务异常: {str(e)}")

@router.get("/quick-questions", response_model=QuickQuestionsResponse, summary="获取快捷问题")
async def get_quick_questions():
    """
    获取预设的快捷问题列表
    
    返回按类别分组的常用问题，用户可以直接点击使用
    """
    try:
        questions = professional_ai_service.get_quick_questions()
        
        # 按类别分组
        categories = {
            "项目管理": [
                "本月完成了多少个项目？",
                "哪些项目进度延迟了？",
                "项目平均工期是多少？"
            ],
            "投资分析": [
                "投资效率最高的项目是哪个？",
                "本年度投资完成情况如何？",
                "哪个项目类别投资最多？"
            ],
            "团队效率": [
                "团队工时分布情况如何？",
                "各投资主体的项目分布情况"
            ],
            "风险监控": [
                "有哪些高风险项目？",
                "督办事项完成率是多少？"
            ]
        }
        
        return QuickQuestionsResponse(
            questions=questions,
            categories=categories
        )
        
    except Exception as e:
        logger.error(f"❌ 获取快捷问题失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取快捷问题失败")

@router.post("/quick-ask", response_model=ChatResponse, summary="快捷问答")
async def quick_ask(request: QuickQuestionRequest):
    """
    快捷问题问答
    
    针对预设问题的快速响应，优化了性能和准确性
    """
    try:
        # 验证是否为预设问题
        quick_questions = professional_ai_service.get_quick_questions()
        if request.question not in quick_questions:
            logger.warning(f"⚠️ 非预设快捷问题: {request.question}")

        # 调用标准问答接口
        result = await professional_ai_service.process_question(
            question=request.question,
            user_id=request.user_id or "anonymous"
        )

        # 构建响应数据
        response_data = {
            "success": result.get("success", False),
            "question": request.question,
            "answer": result.get("answer", "抱歉，没有收到有效回复"),
            "sql": result.get("sql"),
            "explanation": None,
            "data": result.get("data"),
            "chart_data": result.get("chart_config"),
            "insights": None,
            "timestamp": result.get("timestamp", ""),
            "error": result.get("error")
        }

        return ChatResponse(**response_data)
        
    except Exception as e:
        logger.error(f"❌ 快捷问答失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"快捷问答失败: {str(e)}")

@router.get("/database-summary", response_model=DatabaseSummaryResponse, summary="数据库概览")
async def get_database_summary(include_sample_data: bool = False):
    """
    获取数据库结构概览
    
    返回数据库表结构信息和建议的示例问题
    """
    try:
        summary = professional_ai_service.get_database_summary()
        
        # 生成示例问题
        sample_questions = [
            "显示所有项目的基本信息",
            "统计各投资主体的项目数量",
            "分析项目投资完成情况",
            "查看最近一个月的工时记录",
            "显示进度延迟的项目"
        ]
        
        return DatabaseSummaryResponse(
            tables=summary.get("tables", 0),
            table_info=summary.get("table_info", {}),
            sample_questions=sample_questions
        )
        
    except Exception as e:
        logger.error(f"❌ 获取数据库概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取数据库概览失败")

@router.post("/analyze-project", summary="项目智能分析")
async def analyze_project(project_code: str):
    """
    项目智能分析
    
    对指定项目进行全面的AI分析，包括进度、投资、风险等
    """
    try:
        if not project_code:
            raise HTTPException(status_code=400, detail="项目编号不能为空")
        
        # 构建分析问题
        question = f"请全面分析项目编号为 {project_code} 的项目情况，包括进度、投资、工时等各方面数据"
        
        result = await professional_ai_service.process_question(
            question=question,
            user_id="system_analysis"
        )

        # 构建响应数据
        response_data = {
            "success": result.get("success", False),
            "question": question,
            "answer": result.get("answer", "抱歉，没有收到有效回复"),
            "sql": result.get("sql"),
            "explanation": None,
            "data": result.get("data"),
            "chart_data": result.get("chart_config"),
            "insights": None,
            "timestamp": result.get("timestamp", ""),
            "error": result.get("error")
        }

        return ChatResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 项目分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"项目分析失败: {str(e)}")

@router.get("/health", summary="AI服务健康检查")
async def health_check():
    """
    AI服务健康检查

    检查AI客户端和WrenAI服务的运行状态
    """
    try:
        # 简单检查AI客户端是否可用（不实际调用）
        ai_status = "healthy"  # AI客户端总是可用，因为有mock模式

        # 测试数据库连接
        try:
            db_summary = professional_ai_service.get_database_summary()
            db_status = "healthy" if "error" not in str(db_summary) else "degraded"
        except Exception as e:
            logger.warning(f"数据库健康检查失败: {str(e)}")
            db_status = "degraded"

        return {
            "status": "healthy" if ai_status == "healthy" and db_status == "healthy" else "degraded",
            "ai_client": ai_status,
            "database": db_status,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 流式响应生成器
async def stream_chat_response(request: ChatRequest):
    """生成流式聊天响应"""
    try:
        yield "data: " + json.dumps({"type": "start", "message": "开始处理您的问题..."}) + "\n\n"
        
        # 模拟处理步骤
        steps = [
            "正在分析问题...",
            "正在生成SQL查询...",
            "正在执行数据查询...",
            "正在生成回答...",
            "正在准备图表数据..."
        ]
        
        for i, step in enumerate(steps):
            yield "data: " + json.dumps({
                "type": "progress", 
                "message": step,
                "progress": (i + 1) * 20
            }) + "\n\n"
            await asyncio.sleep(0.5)
        
        # 获取最终结果
        result = await professional_ai_service.process_question(
            question=request.question,
            user_id=request.user_id or "anonymous"
        )

        # 构建响应数据
        response_data = {
            "success": result.get("success", False),
            "question": request.question,
            "answer": result.get("answer", "抱歉，没有收到有效回复"),
            "sql": result.get("sql"),
            "explanation": None,
            "data": result.get("data"),
            "chart_data": result.get("chart_config"),
            "insights": None,
            "timestamp": result.get("timestamp", ""),
            "error": result.get("error")
        }
        
        yield "data: " + json.dumps({
            "type": "result",
            "data": result
        }) + "\n\n"
        
        yield "data: " + json.dumps({"type": "end"}) + "\n\n"
        
    except Exception as e:
        yield "data: " + json.dumps({
            "type": "error",
            "message": f"处理失败: {str(e)}"
        }) + "\n\n"

# 后台任务
async def log_user_question(question: str, user_id: str, success: bool):
    """记录用户问题日志"""
    try:
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "question": question[:200],  # 限制长度
            "success": success
        }
        
        # 这里可以保存到数据库或日志文件
        logger.info(f"📝 用户问题记录: {json.dumps(log_data, ensure_ascii=False)}")
        
    except Exception as e:
        logger.error(f"❌ 记录用户问题失败: {str(e)}")

# 添加路由标签和描述
router.tags = ["AI智能问答"]
