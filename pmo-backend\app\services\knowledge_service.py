"""
PMO知识库服务
复用现有业务逻辑，构建智能知识库
"""

import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
from app.core.logger import get_logger

logger = get_logger(__name__)

class PMOKnowledgeService:
    """PMO知识库服务"""
    
    def __init__(self):
        self.knowledge_cache = {}
        self.last_update = {}
        self.update_intervals = {
            'knowledge_base': 900,       # 完整知识库：15分钟
            'red_black_board': 900,      # 红黑榜：15分钟
            'entity_stats': 600,         # 投资主体统计：10分钟
            'project_summary': 300,      # 项目汇总：5分钟
            'labor_costs': 1800,         # 人工费统计：30分钟
            'recent_activities': 180     # 最新动态：3分钟
        }
        self.max_cache_size = 50 * 1024 * 1024  # 最大缓存50MB
        self.cache_cleanup_interval = 3600       # 每小时清理一次缓存
        
    def get_knowledge_base(self, force_refresh: bool = False) -> str:
        """获取完整的知识库文本 - 已禁用以提升启动速度"""
        try:
            logger.info("知识库构建已禁用（提升启动速度）")
            return "知识库功能已禁用以提升系统启动速度。"
            self._build_complete_knowledge_base()
            self.last_update['knowledge_base'] = time.time()
            logger.info("PMO知识库构建完成")

            return self.knowledge_cache.get('formatted_knowledge', '')

        except Exception as e:
            logger.error(f"获取知识库失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

            # 如果构建失败，尝试返回缓存的版本
            cached_knowledge = self.knowledge_cache.get('formatted_knowledge', '')
            if cached_knowledge and len(cached_knowledge) > 1000:  # 确保不是简化版本
                logger.info("构建失败，使用缓存的完整知识库")
                return cached_knowledge

            # 如果没有有效的缓存，抛出异常而不是返回简化版本
            raise Exception(f"知识库服务不可用，无法提供完整数据: {str(e)}")
    
    def _need_update(self, cache_key: str) -> bool:
        """检查是否需要更新缓存"""
        if cache_key not in self.last_update:
            return True

        interval = self.update_intervals.get(cache_key, 600)
        return time.time() - self.last_update[cache_key] > interval

    def _cleanup_cache_if_needed(self):
        """如果缓存过大，进行清理"""
        try:
            # 计算当前缓存大小
            cache_size = self._calculate_cache_size()

            if cache_size > self.max_cache_size:
                logger.info(f"缓存大小 {cache_size/1024/1024:.1f}MB 超过限制，开始清理...")

                # 清理策略：保留核心数据，清理详细数据
                keys_to_keep = ['formatted_knowledge', 'entity_stats', 'red_black_board']
                keys_to_remove = [k for k in self.knowledge_cache.keys() if k not in keys_to_keep]

                for key in keys_to_remove:
                    if key in self.knowledge_cache:
                        del self.knowledge_cache[key]
                        if key in self.last_update:
                            del self.last_update[key]

                new_size = self._calculate_cache_size()
                logger.info(f"缓存清理完成，大小从 {cache_size/1024/1024:.1f}MB 减少到 {new_size/1024/1024:.1f}MB")

        except Exception as e:
            logger.error(f"缓存清理失败: {str(e)}")

    def _calculate_cache_size(self) -> int:
        """计算缓存大小（字节）"""
        total_size = 0
        try:
            for key, value in self.knowledge_cache.items():
                if isinstance(value, str):
                    total_size += len(value.encode('utf-8'))
                elif isinstance(value, (dict, list)):
                    import json
                    from datetime import date, datetime

                    def json_serializer(obj):
                        """JSON序列化器，处理日期对象"""
                        if isinstance(obj, (date, datetime)):
                            return obj.isoformat()
                        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

                    total_size += len(json.dumps(value, ensure_ascii=False, default=json_serializer).encode('utf-8'))
                else:
                    total_size += len(str(value).encode('utf-8'))
        except Exception as e:
            logger.error(f"计算缓存大小失败: {str(e)}")

        return total_size

    def clear_cache(self):
        """手动清空所有缓存"""
        logger.info("手动清空知识库缓存")
        self.knowledge_cache.clear()
        self.last_update.clear()

    def warmup_knowledge_base(self):
        """预热知识库 - 已禁用以提升启动速度"""
        try:
            logger.info("知识库预热已禁用（提升启动速度）")
            return True
        except Exception as e:
            logger.error(f"知识库预热失败: {str(e)}")
            return False
    
    def _build_complete_knowledge_base(self):
        """构建完整的知识库"""
        try:
            # 检查并清理缓存
            self._cleanup_cache_if_needed()

            # 1. 获取红黑榜数据（复用现有逻辑）
            red_black_data = self._get_red_black_board_data()
            self.knowledge_cache['red_black_board'] = red_black_data

            # 2. 构建投资主体统计
            entity_stats = self._build_entity_statistics(red_black_data)
            self.knowledge_cache['entity_stats'] = entity_stats

            # 3. 获取项目汇总信息
            project_summary = self._get_project_summary()
            self.knowledge_cache['project_summary'] = project_summary

            # 4. 获取用户和权限数据
            user_data = self._get_user_and_permission_data()
            self.knowledge_cache['user_data'] = user_data

            # 5. 获取项目详细数据
            project_details = self._get_project_details_data()
            self.knowledge_cache['project_details'] = project_details

            # 6. 获取督办管理数据
            supervision_data = self._get_supervision_data()
            self.knowledge_cache['supervision_data'] = supervision_data

            # 7. 获取变更历史数据
            change_history = self._get_change_history_data()
            self.knowledge_cache['change_history'] = change_history

            # 8. 获取文档内容数据
            document_content = self._get_document_content_data()
            self.knowledge_cache['document_content'] = document_content

            # 9. 获取额外详细数据
            additional_data = self._get_additional_data()
            self.knowledge_cache['additional_data'] = additional_data

            # 10. 格式化为AI可用的知识文本
            formatted_knowledge = self._format_knowledge_for_ai()
            self.knowledge_cache['formatted_knowledge'] = formatted_knowledge

            # 11. 记录缓存大小
            cache_size = self._calculate_cache_size()
            logger.info(f"知识库构建完成，缓存大小: {cache_size/1024/1024:.1f}MB")

        except Exception as e:
            logger.error(f"构建知识库失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 不使用简化版本，直接抛出异常
            raise Exception(f"知识库构建失败，无法提供服务: {str(e)}")
    
    def _get_red_black_board_data(self) -> Dict[str, Any]:
        """获取红黑榜数据（复用现有云函数）"""
        try:
            from app.cloud_functions.getRedBlackBoard.index import main_handler
            
            event = {"queryString": {}}
            result = main_handler(event, None)
            
            if result.get("statusCode") == 200:
                body = json.loads(result.get("body", "{}"))
                data = body.get("data", {})
                logger.info(f"红黑榜数据获取成功，包含 {len(data.get('progressList', []))} 个进度榜实体")
                return data
            else:
                logger.error(f"红黑榜数据获取失败: {result}")
                return {}
                
        except Exception as e:
            logger.error(f"获取红黑榜数据异常: {str(e)}")
            return {}
    
    def _build_entity_statistics(self, red_black_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建投资主体统计信息"""
        try:
            entity_stats = {}
            
            # 合并进度榜和逾期榜数据
            progress_list = red_black_data.get('progressList', [])
            delayed_list = red_black_data.get('delayedList', [])
            all_entities = progress_list + delayed_list
            
            for entity in all_entities:
                entity_name = entity.get('entity', '')
                if entity_name:
                    entity_stats[entity_name] = {
                        'total_projects': entity.get('totalCount', 0),
                        'total_budget': entity.get('totalBudget', 0),
                        'implementation_projects': entity.get('implementationCount', 0),
                        'implementation_budget': entity.get('implementationBudget', 0),
                        'delayed_projects': entity.get('delayedCount', 0),
                        'delayed_budget': entity.get('delayedBudget', 0),
                        'completion_rate': entity.get('completionRate', 0),
                        'working_hours': entity.get('workingHours', 0),
                        'labor_cost': entity.get('laborCost', 0),
                        'status': '正常' if entity.get('delayedCount', 0) == 0 else '有逾期',
                        'rank_type': 'red' if entity in progress_list else 'black'
                    }
            
            logger.info(f"投资主体统计构建完成，包含 {len(entity_stats)} 个实体")
            return entity_stats
            
        except Exception as e:
            logger.error(f"构建投资主体统计失败: {str(e)}")
            return {}
    
    def _get_project_summary(self) -> Dict[str, Any]:
        """获取项目汇总信息"""
        try:
            from app.cloud_functions.getProjectList.index import main_handler
            
            # 获取所有项目
            event = {"queryString": {"entity": "", "implementation": False, "delayed": False}}
            result = main_handler(event, None)
            
            if result.get("statusCode") == 200:
                body = json.loads(result.get("body", "{}"))
                projects = body.get("data", [])
                
                # 统计项目状态分布
                status_distribution = {}
                total_projects = len(projects)
                
                for project in projects:
                    status = project.get('status', '未知')
                    status_distribution[status] = status_distribution.get(status, 0) + 1
                
                summary = {
                    'total_projects': total_projects,
                    'status_distribution': status_distribution,
                    'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                logger.info(f"项目汇总获取成功，总计 {total_projects} 个项目")
                return summary
            else:
                logger.error(f"获取项目汇总失败: {result}")
                return {}
                
        except Exception as e:
            logger.error(f"获取项目汇总异常: {str(e)}")
            return {}

    def _get_user_and_permission_data(self) -> Dict[str, Any]:
        """获取用户和权限数据 - 包含所有详细信息"""
        try:
            from app.db.utils import execute_query

            # 获取所有用户数据（不限制数量）
            users = execute_query("SELECT * FROM users")

            # 获取所有ITBP权限数据
            itbp_data = execute_query("SELECT * FROM itbp")

            # 按投资主体分组ITBP团队成员
            itbp_teams = {}
            for itbp in itbp_data:
                entity = itbp.get('investment_entity', '未知')
                if entity not in itbp_teams:
                    itbp_teams[entity] = []
                itbp_teams[entity].append(itbp)

            # 获取所有登录记录（不限制数量）
            recent_logins = execute_query("""
                SELECT UserID, name, login_time
                FROM user_logins
                ORDER BY id DESC
            """)

            # 构建用户详细信息映射
            user_details = {}
            for user in users:
                user_id = user.get('UserID')
                if user_id:
                    # 获取该用户的ITBP权限
                    user_itbp = [itbp for itbp in itbp_data if itbp.get('UserID') == user_id]
                    user_details[user_id] = {
                        'basic_info': user,
                        'itbp_permissions': user_itbp,
                        'service_lines': list(set([itbp.get('investment_entity') for itbp in user_itbp if itbp.get('investment_entity')]))
                    }

            logger.info(f"用户数据获取成功: {len(users)} 个用户, {len(itbp_data)} 个权限记录, {len(itbp_teams)} 个团队")

            return {
                "users": users,
                "itbp_permissions": itbp_data,
                "itbp_teams": itbp_teams,
                "user_details": user_details,
                "recent_logins": recent_logins,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取用户权限数据失败: {str(e)}")
            return {}

    def _get_project_details_data(self) -> Dict[str, Any]:
        """获取项目详细数据 - 包含所有项目的完整信息"""
        try:
            from app.db.utils import execute_query
            from app.cloud_functions.getProjectList.index import main_handler

            # 1. 获取所有项目的基本信息（通过云函数）
            event = {"queryString": {"entity": "", "implementation": False, "delayed": False}}
            result = main_handler(event, None)

            all_projects = []
            if result.get("statusCode") == 200:
                body = json.loads(result.get("body", "{}"))
                all_projects = body.get("data", [])

            # 2. 获取所有项目工时数据（不限制数量）
            project_hours = execute_query("SELECT * FROM project_hours")

            # 3. 获取所有项目规划数据
            project_plans = execute_query("SELECT * FROM project_plan")

            # 4. 获取所有周报数据
            weekly_reports = execute_query("SELECT * FROM weekly_reports")

            # 5. 获取所有历史项目数据
            old_projects = execute_query("SELECT * FROM old_project_account_book")

            # 6. 按项目编号组织数据
            projects_by_code = {}
            for project in all_projects:
                code = project.get('project_code')
                if code:
                    projects_by_code[code] = {
                        'basic_info': project,
                        'hours': [h for h in project_hours if h.get('project_code') == code],
                        'plans': [p for p in project_plans if p.get('project_code') == code],
                        'reports': [r for r in weekly_reports if r.get('project_code') == code]
                    }

            # 7. 按投资主体分组项目
            projects_by_entity = {}
            for project in all_projects:
                entity = project.get('investment_entity', '未知')
                if entity not in projects_by_entity:
                    projects_by_entity[entity] = []
                projects_by_entity[entity].append(project)

            logger.info(f"项目详细数据获取成功: {len(all_projects)} 个项目, {len(project_hours)} 工时记录, {len(project_plans)} 规划, {len(weekly_reports)} 周报")

            return {
                "all_projects": all_projects,
                "projects_by_code": projects_by_code,
                "projects_by_entity": projects_by_entity,
                "project_hours": project_hours,
                "project_plans": project_plans,
                "weekly_reports": weekly_reports,
                "old_projects": old_projects,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取项目详细数据失败: {str(e)}")
            return {}

    def _get_supervision_data(self) -> Dict[str, Any]:
        """获取督办管理数据"""
        try:
            from app.db.utils import execute_query

            # 获取督办事项
            supervision_items = execute_query("SELECT * FROM supervision_items")

            # 获取进展详情
            progress_details = execute_query("SELECT * FROM progress_details")

            # 获取公司信息
            companies = execute_query("SELECT * FROM companies")

            # 获取公司进展
            company_progress = execute_query("SELECT * FROM company_progress")

            logger.info(f"督办数据获取成功: {len(supervision_items)} 督办事项, {len(progress_details)} 进展详情")

            return {
                "supervision_items": supervision_items,
                "progress_details": progress_details,
                "companies": companies,
                "company_progress": company_progress,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取督办数据失败: {str(e)}")
            return {}

    def _get_change_history_data(self) -> Dict[str, Any]:
        """获取变更历史数据"""
        try:
            from app.db.utils import execute_query

            # 获取所有变更记录（不限制数量）
            recent_changes = execute_query("""
                SELECT * FROM change_log
                ORDER BY id DESC
            """)

            logger.info(f"变更历史数据获取成功: {len(recent_changes)} 条记录")

            return {
                "recent_changes": recent_changes,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取变更历史数据失败: {str(e)}")
            return {}

    def _get_additional_data(self) -> Dict[str, Any]:
        """获取额外的详细数据"""
        try:
            from app.db.utils import execute_query

            # 获取项目档案文件信息
            try:
                archive_files = execute_query("SELECT * FROM project_archive_files")
            except:
                archive_files = []

            # 获取档案清单
            try:
                archive_checklist = execute_query("SELECT * FROM archive_checklist")
            except:
                archive_checklist = []

            # 获取档案任务
            try:
                archive_tasks = execute_query("SELECT * FROM archive_tasks")
            except:
                archive_tasks = []

            # 获取项目成员信息（如果存在）
            try:
                project_members = execute_query("SELECT * FROM project_members")
            except:
                project_members = []

            # 获取项目分类信息（如果存在）
            try:
                project_categories = execute_query("SELECT * FROM project_categories")
            except:
                project_categories = []

            # 获取所有系统日志（不限制数量）
            try:
                system_logs = execute_query("SELECT * FROM system_logs ORDER BY id DESC")
            except:
                system_logs = []

            # 获取项目状态历史
            try:
                project_status_history = execute_query("SELECT * FROM project_status_history ORDER BY change_time DESC")
            except:
                project_status_history = []

            # 获取通知信息
            try:
                notifications = execute_query("SELECT * FROM notifications ORDER BY created_time DESC")
            except:
                notifications = []

            # 获取文件上传记录
            try:
                file_uploads = execute_query("SELECT * FROM file_uploads ORDER BY upload_time DESC")
            except:
                file_uploads = []

            # 获取项目文档
            try:
                project_documents = execute_query("SELECT * FROM project_documents")
            except:
                project_documents = []

            # 获取项目里程碑
            try:
                project_milestones = execute_query("SELECT * FROM project_milestones")
            except:
                project_milestones = []

            # 获取项目风险
            try:
                project_risks = execute_query("SELECT * FROM project_risks")
            except:
                project_risks = []

            # 获取项目预算
            try:
                project_budgets = execute_query("SELECT * FROM project_budgets")
            except:
                project_budgets = []

            # 获取项目合同
            try:
                project_contracts = execute_query("SELECT * FROM project_contracts")
            except:
                project_contracts = []

            # 获取项目交付物
            try:
                project_deliverables = execute_query("SELECT * FROM project_deliverables")
            except:
                project_deliverables = []

            logger.info(f"额外数据获取成功: {len(archive_files)} 档案文件, {len(archive_checklist)} 档案清单项, {len(system_logs)} 系统日志, {len(project_members)} 项目成员, {len(project_status_history)} 状态历史")

            return {
                "archive_files": archive_files,
                "archive_checklist": archive_checklist,
                "archive_tasks": archive_tasks,
                "project_members": project_members,
                "project_categories": project_categories,
                "system_logs": system_logs,
                "project_status_history": project_status_history,
                "notifications": notifications,
                "file_uploads": file_uploads,
                "project_documents": project_documents,
                "project_milestones": project_milestones,
                "project_risks": project_risks,
                "project_budgets": project_budgets,
                "project_contracts": project_contracts,
                "project_deliverables": project_deliverables,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取额外数据失败: {str(e)}")
            return {}

    def _get_document_content_data(self) -> Dict[str, Any]:
        """获取文档内容数据"""
        try:
            import os
            from pathlib import Path

            document_contents = []
            archive_dir = Path("project_archive_materials")

            if archive_dir.exists():
                # 遍历所有项目档案目录
                for project_dir in archive_dir.iterdir():
                    if project_dir.is_dir():
                        project_code = project_dir.name

                        # 查找解析后的文档内容
                        for root, dirs, files in os.walk(project_dir):
                            for file in files:
                                if file.endswith('_解析内容.md'):
                                    file_path = Path(root) / file
                                    try:
                                        with open(file_path, 'r', encoding='utf-8') as f:
                                            content = f.read()

                                        document_contents.append({
                                            "project_code": project_code,
                                            "file_name": file,
                                            "file_path": str(file_path),
                                            "content": content[:2000],  # 限制长度
                                            "content_length": len(content),
                                            "last_modified": file_path.stat().st_mtime
                                        })

                                    except Exception as e:
                                        logger.warning(f"读取文档内容失败 {file_path}: {str(e)}")

            logger.info(f"文档内容数据获取成功: {len(document_contents)} 个文档")

            return {
                "document_contents": document_contents,
                "update_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取文档内容数据失败: {str(e)}")
            return {}

    def _format_knowledge_for_ai(self) -> str:
        """格式化知识库为AI可用的文本"""
        try:
            red_black_data = self.knowledge_cache.get('red_black_board', {})
            entity_stats = self.knowledge_cache.get('entity_stats', {})
            project_summary = self.knowledge_cache.get('project_summary', {})
            user_data = self.knowledge_cache.get('user_data', {})
            project_details = self.knowledge_cache.get('project_details', {})
            supervision_data = self.knowledge_cache.get('supervision_data', {})
            change_history = self.knowledge_cache.get('change_history', {})
            document_content = self.knowledge_cache.get('document_content', {})
            additional_data = self.knowledge_cache.get('additional_data', {})

            # 获取统计数据 - 修复数据结构访问问题
            stats = red_black_data.get('stats', {}) if red_black_data else {}
            red_entities = red_black_data.get('redEntities', []) if red_black_data else []
            black_entities = red_black_data.get('blackEntities', []) if red_black_data else []

            # 如果stats为空，尝试从entity_stats计算总体统计
            if not stats and entity_stats:
                total_projects = sum(s.get('total_projects', 0) for s in entity_stats.values() if s)
                total_budget = sum(s.get('total_budget', 0) for s in entity_stats.values() if s)
                implementation_projects = sum(s.get('implementation_projects', 0) for s in entity_stats.values() if s)
                implementation_budget = sum(s.get('implementation_budget', 0) for s in entity_stats.values() if s)
                delayed_projects = sum(s.get('delayed_projects', 0) for s in entity_stats.values() if s)
                delayed_budget = sum(s.get('delayed_budget', 0) for s in entity_stats.values() if s)
                working_hours = sum(s.get('working_hours', 0) for s in entity_stats.values() if s)
                labor_cost = sum(s.get('labor_cost', 0) for s in entity_stats.values() if s)
                completion_rate = round((implementation_budget / total_budget * 100) if total_budget > 0 else 0, 1)

                stats = {
                    'totalCount': total_projects,
                    'totalBudget': total_budget,
                    'implementationCount': implementation_projects,
                    'implementationBudget': implementation_budget,
                    'delayedCount': delayed_projects,
                    'delayedBudget': delayed_budget,
                    'completionRate': completion_rate,
                    'workingHours': working_hours,
                    'laborCost': labor_cost
                }
            
            knowledge_text = f"""# PMO项目管理系统知识库
数据更新时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 总体概况
- 项目总数：{stats.get('totalCount', 0)}个
- 总预算：{stats.get('totalBudget', 0)}万元
- 实施项目数：{stats.get('implementationCount', 0)}个
- 实施预算：{stats.get('implementationBudget', 0)}万元
- 逾期项目数：{stats.get('delayedCount', 0)}个
- 逾期预算：{stats.get('delayedBudget', 0)}万元
- 总体开工率：{stats.get('completionRate', 0)}%
- 当年人次合计：{stats.get('workingHours', 0)}人次
- 人工费合计：{stats.get('laborCost', 0)}万元

## 红黑榜排名
### 红榜（按进度推进，无逾期项目）
{', '.join(red_entities) if red_entities else '暂无'}

### 黑榜（逾期推进）
{', '.join(black_entities) if black_entities else '暂无'}

## 各投资主体详细统计"""
            
            # 添加投资主体详细信息
            for entity_name, stats in entity_stats.items():
                if not stats or not isinstance(stats, dict):
                    continue

                knowledge_text += f"""
### {entity_name}
- 项目总数：{stats.get('total_projects', 0)}个
- 总预算：{stats.get('total_budget', 0)}万元
- 实施项目：{stats.get('implementation_projects', 0)}个（预算{stats.get('implementation_budget', 0)}万元）
- 逾期项目：{stats.get('delayed_projects', 0)}个（预算{stats.get('delayed_budget', 0)}万元）
- 开工率：{stats.get('completion_rate', 0)}%
- 当年工时：{stats.get('working_hours', 0)}人次
- 人工费：{stats.get('labor_cost', 0)}万元
- 状态：{stats.get('status', '未知')}
- 榜单：{'红榜' if stats.get('rank_type') == 'red' else '黑榜'}"""
            
            # 添加项目状态分布
            status_dist = project_summary.get('status_distribution', {})
            if status_dist:
                knowledge_text += f"""

## 项目状态分布
"""
                for status, count in status_dist.items():
                    knowledge_text += f"- {status}：{count}个\n"
            
            # 添加用户和权限信息
            users = user_data.get('users', []) if user_data else []
            itbp_permissions = user_data.get('itbp_permissions', []) if user_data else []
            itbp_teams = user_data.get('itbp_teams', {}) if user_data else {}

            if users:
                knowledge_text += f"""

## 用户和权限信息
### 用户统计
- 总用户数：{len(users)}个
- 活跃用户：{len([u for u in users if u.get('is_disabled') != 'Y'])}个

### 权限分配统计
- ITBP权限记录：{len(itbp_permissions)}条
"""
                # 统计各投资主体的权限分配
                entity_permissions = {}
                for perm in itbp_permissions:
                    entity = perm.get('investment_entity', '未知')
                    entity_permissions[entity] = entity_permissions.get(entity, 0) + 1

                if entity_permissions:
                    knowledge_text += "### 各投资主体权限分配\n"
                    for entity, count in sorted(entity_permissions.items()):
                        knowledge_text += f"- {entity}：{count}人\n"

            # 添加ITBP团队详细信息（提前到重要位置）
            if itbp_teams:
                knowledge_text += f"""

## ITBP团队详细信息
"""
                for entity, members in itbp_teams.items():
                    if entity and members:  # 确保实体名称和成员都存在
                        knowledge_text += f"""
### {entity}团队（{len(members)}人）
"""
                        for member in members:
                            name = member.get('NAME', '')
                            user_id = member.get('UserID', '')
                            category = member.get('staff_category', '')
                            role = member.get('role', '')
                            start_time = member.get('start_time', '')
                            end_time = member.get('end_time', '')

                            knowledge_text += f"- {name} ({user_id})"
                            if category:
                                knowledge_text += f" - {category}"
                            if role:
                                knowledge_text += f" - {role}"
                            if start_time:
                                knowledge_text += f" - 开始：{start_time}"
                            if end_time:
                                knowledge_text += f" - 结束：{end_time}"
                            knowledge_text += "\n"

            # 添加项目详细信息
            project_hours = project_details.get('project_hours', []) if project_details else []
            project_plans = project_details.get('project_plans', []) if project_details else []
            weekly_reports = project_details.get('weekly_reports', []) if project_details else []
            if project_hours or project_plans or weekly_reports:
                knowledge_text += f"""

## 项目详细信息
### 工时记录
- 工时记录总数：{len(project_hours)}条
"""
                if project_hours:
                    # 统计工时最多的项目
                    project_hour_stats = {}
                    user_hour_stats = {}
                    monthly_stats = {}

                    for hour in project_hours:
                        project_code = hour.get('project_code', '未知')
                        user_id = hour.get('UserID', '未知')
                        working_hours = hour.get('working_hours', 0)
                        month = hour.get('month', '未知')

                        # 项目工时统计
                        project_hour_stats[project_code] = project_hour_stats.get(project_code, 0) + working_hours

                        # 用户工时统计
                        user_hour_stats[user_id] = user_hour_stats.get(user_id, 0) + working_hours

                        # 月度工时统计
                        monthly_stats[month] = monthly_stats.get(month, 0) + working_hours

                    # 显示工时最多的项目
                    top_projects = sorted(project_hour_stats.items(), key=lambda x: x[1], reverse=True)[:10]
                    if top_projects:
                        knowledge_text += "#### 工时投入最多的项目（前10名）\n"
                        for project_code, total_hours in top_projects:
                            knowledge_text += f"- {project_code}：{total_hours}小时\n"

                    # 显示工时最多的用户
                    top_users = sorted(user_hour_stats.items(), key=lambda x: x[1], reverse=True)[:10]
                    if top_users:
                        knowledge_text += "\n#### 工时投入最多的用户（前10名）\n"
                        for user_id, total_hours in top_users:
                            knowledge_text += f"- {user_id}：{total_hours}小时\n"

                    # 显示月度工时分布
                    monthly_sorted = sorted(monthly_stats.items(), key=lambda x: x[0], reverse=True)[:12]
                    if monthly_sorted:
                        knowledge_text += "\n#### 月度工时分布（最近12个月）\n"
                        for month, total_hours in monthly_sorted:
                            knowledge_text += f"- {month}：{total_hours}小时\n"

                    # 显示详细的工时记录（最新100条）
                    knowledge_text += f"\n#### 详细工时记录（最新100条，共{len(project_hours)}条）\n"
                    sorted_hours = sorted(project_hours, key=lambda x: str(x.get('month', '')), reverse=True)
                    for hour in sorted_hours[:100]:
                        project_code = hour.get('project_code', '未知')
                        user_id = hour.get('UserID', '未知')
                        working_hours = hour.get('working_hours', 0)
                        month = hour.get('month', '未知')
                        labor_cost = hour.get('LaborCost', 0)
                        knowledge_text += f"- {month}: {user_id} 在 {project_code} 投入 {working_hours}小时"
                        if labor_cost:
                            knowledge_text += f"，人工费 {labor_cost}元"
                        knowledge_text += "\n"

                knowledge_text += f"""
### 项目规划
- 规划项目数：{len(project_plans)}个
"""
                if project_plans:
                    # 显示最新的项目规划
                    recent_plans = sorted(project_plans, key=lambda x: x.get('created_time', ''), reverse=True)[:5]
                    knowledge_text += "#### 最新项目规划（前5个）\n"
                    for plan in recent_plans:
                        project_code = plan.get('project_code', '未知')
                        plan_content = plan.get('plan_content', '')
                        created_time = plan.get('created_time', '')
                        knowledge_text += f"- {project_code} ({created_time}): {plan_content[:100]}{'...' if len(plan_content) > 100 else ''}\n"

                knowledge_text += f"""
### 周报记录
- 周报记录数：{len(weekly_reports)}条
"""
                if weekly_reports:
                    # 显示最新的周报
                    recent_reports = sorted(weekly_reports, key=lambda x: x.get('report_date', ''), reverse=True)[:5]
                    knowledge_text += "#### 最新周报记录（前5个）\n"
                    for report in recent_reports:
                        project_code = report.get('project_code', '未知')
                        report_content = report.get('report_content', '')
                        report_date = report.get('report_date', '')
                        knowledge_text += f"- {project_code} ({report_date}): {report_content[:100]}{'...' if len(report_content) > 100 else ''}\n"

            # 添加督办管理信息
            supervision_items = supervision_data.get('supervision_items', []) if supervision_data else []
            companies = supervision_data.get('companies', []) if supervision_data else []
            if supervision_items:
                knowledge_text += f"""

## 督办管理信息
### 督办事项
- 督办事项总数：{len(supervision_items)}个
- 涉及公司数：{len(companies)}个
"""
                # 统计督办事项的工作维度
                work_dimensions = {}
                for item in supervision_items:
                    dimension = item.get('work_dimension', '未分类')
                    work_dimensions[dimension] = work_dimensions.get(dimension, 0) + 1

                if work_dimensions:
                    knowledge_text += "#### 督办事项分类\n"
                    for dimension, count in work_dimensions.items():
                        knowledge_text += f"- {dimension}：{count}项\n"

            # 添加变更历史信息
            recent_changes = change_history.get('recent_changes', []) if change_history else []
            if recent_changes:
                knowledge_text += f"""

## 系统变更历史
### 最近变更记录
- 变更记录数：{len(recent_changes)}条
"""
                # 统计变更类型
                change_types = {}
                user_changes = {}
                project_changes = {}

                for change in recent_changes:
                    change_name = change.get('name', '未知')
                    user_id = change.get('UserID', '未知')
                    project_code = change.get('project_code', '')

                    change_types[change_name] = change_types.get(change_name, 0) + 1
                    user_changes[user_id] = user_changes.get(user_id, 0) + 1

                    if project_code:
                        project_changes[project_code] = project_changes.get(project_code, 0) + 1

                if change_types:
                    knowledge_text += "#### 主要变更类型\n"
                    for change_type, count in sorted(change_types.items(), key=lambda x: x[1], reverse=True)[:15]:
                        knowledge_text += f"- {change_type}：{count}次\n"

                # 显示变更最多的用户
                if user_changes:
                    top_users = sorted(user_changes.items(), key=lambda x: x[1], reverse=True)[:10]
                    knowledge_text += "\n#### 变更最多的用户（前10名）\n"
                    for user_id, count in top_users:
                        knowledge_text += f"- {user_id}：{count}次变更\n"

                # 显示变更最多的项目
                if project_changes:
                    top_projects = sorted(project_changes.items(), key=lambda x: x[1], reverse=True)[:10]
                    knowledge_text += "\n#### 变更最多的项目（前10名）\n"
                    for project_code, count in top_projects:
                        knowledge_text += f"- {project_code}：{count}次变更\n"

                # 显示最近的详细变更记录（显示更多记录）
                knowledge_text += f"\n#### 最近变更详情（前50条，共{len(recent_changes)}条）\n"
                for change in recent_changes[:50]:
                    change_name = change.get('name', '未知')
                    user_id = change.get('UserID', '未知')
                    project_code = change.get('project_code', '')
                    change_time = change.get('change_time', '')
                    old_value = change.get('old_value', '')
                    new_value = change.get('new_value', '')
                    table_name = change.get('table_name', '')
                    field_name = change.get('field_name', '')

                    knowledge_text += f"- {change_time}: {user_id} 在项目 {project_code} 的 {table_name}.{field_name} 进行了 {change_name}\n"
                    if old_value and new_value:
                        knowledge_text += f"  * 从 '{old_value[:80]}{'...' if len(old_value) > 80 else ''}' 改为 '{new_value[:80]}{'...' if len(new_value) > 80 else ''}'\n"
                    elif new_value:
                        knowledge_text += f"  * 新值：'{new_value[:80]}{'...' if len(new_value) > 80 else ''}'\n"

            # 添加文档内容信息
            document_contents = document_content.get('document_contents', []) if document_content else []
            if document_contents:
                knowledge_text += f"""

## 项目文档内容
### 文档统计
- 已解析文档数：{len(document_contents)}个
"""
                # 按项目分组统计文档
                project_docs = {}
                for doc in document_contents:
                    project_code = doc.get('project_code', '未知')
                    if project_code not in project_docs:
                        project_docs[project_code] = []
                    project_docs[project_code].append(doc)

                if project_docs:
                    knowledge_text += "#### 各项目文档分布\n"
                    for project_code, docs in sorted(project_docs.items()):
                        knowledge_text += f"- {project_code}：{len(docs)}个文档\n"

                # 显示部分文档内容摘要
                knowledge_text += "\n#### 重要文档内容摘要\n"
                for doc in document_contents[:5]:  # 只显示前5个文档的摘要
                    project_code = doc.get('project_code', '未知')
                    file_name = doc.get('file_name', '未知文件')
                    content = doc.get('content', '')

                    # 提取文档关键信息
                    if content:
                        # 简单提取前200字符作为摘要
                        summary = content.replace('\n', ' ').strip()[:200]
                        if len(content) > 200:
                            summary += "..."
                        knowledge_text += f"**{project_code} - {file_name}**\n{summary}\n\n"

            # 添加详细的用户信息
            users = user_data.get('users', [])
            itbp_teams = user_data.get('itbp_teams', {})
            user_details = user_data.get('user_details', {})

            if users:
                knowledge_text += f"""

## 详细用户信息
### 所有用户列表（{len(users)}个用户）
"""
                for user in users:
                    user_id = user.get('UserID', '')
                    name = user.get('name', '')
                    username = user.get('username', '')
                    role = user.get('role', '')
                    department = user.get('department_name', '')
                    company = user.get('company_name', '')
                    labor_cost = user.get('LaborCost', 0)

                    # 获取用户的服务条线
                    user_detail = user_details.get(user_id, {})
                    service_lines = user_detail.get('service_lines', [])

                    knowledge_text += f"""
#### {name} ({username})
- 用户ID：{user_id}
- 角色：{role}
- 部门：{department}
- 公司：{company}
- 年薪：{labor_cost}万元
- 服务条线：{', '.join(service_lines) if service_lines else '无'}
"""

            # ITBP团队详细信息已在前面添加，此处删除重复

            # 添加详细的项目信息
            all_projects = project_details.get('all_projects', [])
            projects_by_entity = project_details.get('projects_by_entity', {})
            projects_by_code = project_details.get('projects_by_code', {})

            if all_projects:
                knowledge_text += f"""

## 详细项目信息
### 所有项目列表（{len(all_projects)}个项目）
"""
                # 按投资主体分组显示项目
                for entity, projects in projects_by_entity.items():
                    knowledge_text += f"""
#### {entity}（{len(projects)}个项目）
"""
                    for project in projects:
                        project_name = project.get('project_name', '')
                        project_code = project.get('project_code', '')
                        status = project.get('status', '')
                        budget = project.get('budget', 0)
                        current_stage = project.get('current_stage', '')
                        manager = project.get('project_manager', '')
                        investment_type = project.get('investment_type', '')
                        category = project.get('project_category', '')
                        overview = project.get('project_overview', '') or ''
                        current_progress = project.get('current_progress', '') or ''
                        next_steps = project.get('next_steps', '') or ''
                        issues = project.get('issues_to_be_coordinated_resolved', '') or ''

                        knowledge_text += f"""
**{project_name}** ({project_code})
- 状态：{status}
- 当前阶段：{current_stage}
- 预算：{budget}万元
- 项目经理：{manager}
- 投资类型：{investment_type}
- 项目分类：{category}
- 项目概述：{overview[:100] if overview else ''}{'...' if overview and len(overview) > 100 else ''}
- 当前进展：{current_progress[:100] if current_progress else ''}{'...' if current_progress and len(current_progress) > 100 else ''}
- 下一步工作：{next_steps[:100] if next_steps else ''}{'...' if next_steps and len(next_steps) > 100 else ''}
- 待协调解决问题：{issues[:100] if issues else ''}{'...' if issues and len(issues) > 100 else ''}
"""

                        # 添加项目的工时、规划等详细信息
                        project_detail = projects_by_code.get(project_code, {})
                        hours = project_detail.get('hours', [])
                        plans = project_detail.get('plans', [])
                        reports = project_detail.get('reports', [])

                        if hours:
                            total_hours = sum([h.get('working_hours', 0) for h in hours])
                            knowledge_text += f"- 总工时：{total_hours}小时\n"

                            # 显示最近的工时记录
                            recent_hours = sorted(hours, key=lambda x: x.get('month', ''), reverse=True)[:3]
                            if recent_hours:
                                knowledge_text += "- 最近工时记录：\n"
                                for hour in recent_hours:
                                    user_id = hour.get('UserID', '')
                                    working_hours = hour.get('working_hours', 0)
                                    month = hour.get('month', '')
                                    knowledge_text += f"  * {month}: {user_id} - {working_hours}小时\n"

                        if plans:
                            knowledge_text += f"- 项目规划：{len(plans)}个\n"
                            # 显示最新的规划
                            if plans:
                                latest_plan = plans[0]
                                plan_content = latest_plan.get('plan_content', '')
                                knowledge_text += f"  * 最新规划：{plan_content[:100]}{'...' if len(plan_content) > 100 else ''}\n"

                        if reports:
                            knowledge_text += f"- 周报记录：{len(reports)}条\n"
                            # 显示最新的周报
                            if reports:
                                latest_report = reports[0]
                                report_content = latest_report.get('report_content', '')
                                knowledge_text += f"  * 最新周报：{report_content[:100]}{'...' if len(report_content) > 100 else ''}\n"

            # 添加额外数据信息
            archive_files = additional_data.get('archive_files', [])
            archive_checklist = additional_data.get('archive_checklist', [])
            system_logs = additional_data.get('system_logs', [])

            if archive_files or archive_checklist or system_logs:
                knowledge_text += f"""

## 档案和系统信息
### 项目档案文件
- 档案文件数：{len(archive_files)}个
- 档案清单项：{len(archive_checklist)}个

### 系统日志
- 系统日志记录：{len(system_logs)}条
"""

            # 添加业务规则说明
            knowledge_text += """

## 业务规则说明
### 项目阶段（共9个阶段）
1. 未启动
2. 业务调研
3. 解决方案
4. 项目立项
5. 任务采购
6. 项目实施
7. 项目验收
8. 项目结项
9. 已完成

### 关键指标计算
- **开工率** = 实施预算 / 总预算 × 100%
- **实施阶段项目**：包括任务采购、项目实施、项目验收、项目结项、已完成
- **人工费** = Σ(用户年薪 / 12 × 月工时)
- **逾期判断**：当前阶段超过计划完成时间
- **红榜标准**：无逾期项目，按开工金额排序
- **黑榜标准**：有逾期项目，按逾期金额排序

### 投资主体列表
汽租、商租、金租、征信、资管、集团战略部、集团财务部、小贷、集团、不动产、担保、集团保全部、集团风控部、集团办公室、集团人力部、集团协同部、金服、外部主体、集团法规部、集团审计部、保理、财险
"""

            # 添加额外的底层数据信息
            additional_data = self.knowledge_cache.get('additional_data', {})
            if additional_data:
                knowledge_text += f"""

## 系统底层数据统计
### 档案管理数据
"""
                archive_files = additional_data.get('archive_files', [])
                archive_checklist = additional_data.get('archive_checklist', [])
                archive_tasks = additional_data.get('archive_tasks', [])
                project_members = additional_data.get('project_members', [])
                system_logs = additional_data.get('system_logs', [])

                knowledge_text += f"- 项目档案文件：{len(archive_files)}个\n"
                knowledge_text += f"- 档案清单项：{len(archive_checklist)}项\n"
                knowledge_text += f"- 档案任务：{len(archive_tasks)}个\n"
                knowledge_text += f"- 项目成员：{len(project_members)}人\n"
                knowledge_text += f"- 系统日志：{len(system_logs)}条\n"

                # 显示最新的档案文件
                if archive_files:
                    knowledge_text += f"\n#### 最新档案文件（前10个）\n"
                    for file_info in archive_files[:10]:
                        file_name = file_info.get('file_name', '')
                        project_code = file_info.get('project_code', '')
                        upload_time = file_info.get('upload_time', '')
                        knowledge_text += f"- {project_code}: {file_name} ({upload_time})\n"

                # 显示项目成员信息
                if project_members:
                    knowledge_text += f"\n#### 项目成员信息（前20个）\n"
                    for member in project_members[:20]:
                        member_name = member.get('member_name', '')
                        project_code = member.get('project_code', '')
                        role = member.get('role', '')
                        knowledge_text += f"- {project_code}: {member_name} - {role}\n"

                # 显示最新系统日志
                if system_logs:
                    knowledge_text += f"\n#### 最新系统日志（前20条）\n"
                    for log in system_logs[:20]:
                        log_time = log.get('log_time', '')
                        log_level = log.get('log_level', '')
                        log_message = log.get('message', '')
                        user_id = log.get('user_id', '')
                        knowledge_text += f"- {log_time} [{log_level}] {user_id}: {log_message[:100]}{'...' if len(log_message) > 100 else ''}\n"

                # 添加其他底层数据统计
                project_status_history = additional_data.get('project_status_history', [])
                notifications = additional_data.get('notifications', [])
                file_uploads = additional_data.get('file_uploads', [])

                if project_status_history or notifications or file_uploads:
                    knowledge_text += f"""

### 其他系统数据
- 项目状态历史：{len(project_status_history)}条
- 系统通知：{len(notifications)}条
- 文件上传记录：{len(file_uploads)}条
"""

            return knowledge_text
            
        except Exception as e:
            logger.error(f"格式化知识库失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 不使用简化版本，直接抛出异常
            raise Exception(f"知识库格式化失败，无法提供服务: {str(e)}")

    def _test_format_step_by_step(self):
        """逐步测试格式化函数的每一步"""
        print("🔍 开始逐步测试格式化函数...")

        # 获取缓存数据
        red_black_data = self.knowledge_cache.get('red_black_board', {})
        entity_stats = self.knowledge_cache.get('entity_stats', {})
        project_summary = self.knowledge_cache.get('project_summary', {})
        user_data = self.knowledge_cache.get('user_data', {})
        project_details = self.knowledge_cache.get('project_details', {})
        supervision_data = self.knowledge_cache.get('supervision_data', {})
        change_history = self.knowledge_cache.get('change_history', {})
        document_content = self.knowledge_cache.get('document_content', {})
        additional_data = self.knowledge_cache.get('additional_data', {})

        print(f"缓存数据类型检查:")
        print(f"  - red_black_data: {type(red_black_data)}")
        print(f"  - entity_stats: {type(entity_stats)}")
        print(f"  - project_summary: {type(project_summary)}")
        print(f"  - user_data: {type(user_data)}")
        print(f"  - project_details: {type(project_details)}")
        print(f"  - supervision_data: {type(supervision_data)}")
        print(f"  - change_history: {type(change_history)}")
        print(f"  - document_content: {type(document_content)}")
        print(f"  - additional_data: {type(additional_data)}")

        # 逐步执行格式化函数的每一行
        try:
            print("\n步骤1: 获取统计数据...")
            stats = red_black_data.get('stats', {}) if red_black_data else {}
            print(f"stats: {type(stats)} -> {stats}")

            red_entities = red_black_data.get('redEntities', []) if red_black_data else []
            print(f"red_entities: {type(red_entities)} -> {red_entities}")

            black_entities = red_black_data.get('blackEntities', []) if red_black_data else []
            print(f"black_entities: {type(black_entities)} -> {black_entities}")

        except Exception as e:
            print(f"❌ 步骤1失败: {e}")
            raise

        try:
            print("\n步骤2: 计算总体统计...")
            if not stats and entity_stats:
                total_projects = sum(s.get('total_projects', 0) for s in entity_stats.values() if s)
                print(f"total_projects计算成功: {total_projects}")

                total_budget = sum(s.get('total_budget', 0) for s in entity_stats.values() if s)
                print(f"total_budget计算成功: {total_budget}")

        except Exception as e:
            print(f"❌ 步骤2失败: {e}")
            raise

        print("✅ 逐步测试完成")



# 全局知识库服务实例
pmo_knowledge_service = PMOKnowledgeService()
