#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件存储管理服务
"""

import os
import shutil
import hashlib
import uuid
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from app.core.logger import get_logger

logger = get_logger(__name__)

class FileStorageService:
    """文件存储服务"""
    
    def __init__(self, base_storage_path: str = "project_archive_materials"):
        self.base_storage_path = base_storage_path
        self.temp_storage_path = "temp_files"
        
        # 确保基础目录存在
        os.makedirs(self.base_storage_path, exist_ok=True)
        os.makedirs(self.temp_storage_path, exist_ok=True)
    
    def save_temp_file(self, task_id: str, filename: str, file_content: bytes) -> str:
        """保存临时文件"""
        try:
            # 创建任务临时目录
            task_temp_dir = os.path.join(self.temp_storage_path, task_id)
            os.makedirs(task_temp_dir, exist_ok=True)

            # 生成安全的文件名
            safe_filename = self._sanitize_filename(filename)
            temp_file_path = os.path.join(task_temp_dir, safe_filename)

            # 保存文件
            with open(temp_file_path, 'wb') as f:
                f.write(file_content)

            logger.info(f"临时文件保存成功: {temp_file_path}")
            return temp_file_path

        except Exception as e:
            logger.error(f"保存临时文件失败 {filename}: {str(e)}")
            raise e

    def save_temp_parsed_content(self, task_id: str, filename: str, parsed_content: str) -> str:
        """保存临时解析内容（markdown格式）"""
        try:
            # 创建任务临时目录
            task_temp_dir = os.path.join(self.temp_storage_path, task_id)
            os.makedirs(task_temp_dir, exist_ok=True)

            # 生成解析内容文件名（原文件名 + _解析内容.md）
            base_name = os.path.splitext(filename)[0]
            safe_base_name = self._sanitize_filename(base_name)
            parsed_filename = f"{safe_base_name}_解析内容.md"
            parsed_file_path = os.path.join(task_temp_dir, parsed_filename)

            # 保存解析内容
            with open(parsed_file_path, 'w', encoding='utf-8') as f:
                f.write(parsed_content)

            logger.info(f"临时解析内容保存成功: {parsed_file_path}")
            return parsed_file_path

        except Exception as e:
            logger.error(f"保存临时解析内容失败 {filename}: {str(e)}")
            raise e
    
    def move_to_archive(
        self, 
        temp_file_path: str, 
        project_code: str, 
        classification: Dict,
        suggested_filename: str
    ) -> str:
        """将临时文件移动到档案目录"""
        try:
            # 生成档案存储路径
            archive_path = self._get_archive_path(project_code, classification)
            os.makedirs(archive_path, exist_ok=True)
            
            # 生成最终文件路径
            safe_filename = self._sanitize_filename(suggested_filename)
            final_file_path = os.path.join(archive_path, safe_filename)
            
            # 处理文件名冲突
            final_file_path = self._handle_filename_conflict(final_file_path)
            
            # 移动文件
            shutil.move(temp_file_path, final_file_path)
            
            logger.info(f"文件移动到档案目录: {final_file_path}")
            return final_file_path
            
        except Exception as e:
            logger.error(f"移动文件到档案目录失败: {str(e)}")
            raise e

    def move_to_archive_with_content(
        self,
        temp_file_path: str,
        temp_parsed_content_path: str,
        project_code: str,
        classification: Dict,
        suggested_filename: str
    ) -> Tuple[str, str]:
        """将临时文件和解析内容一起移动到档案目录"""
        try:
            # 生成档案存储路径
            archive_path = self._get_archive_path(project_code, classification)
            os.makedirs(archive_path, exist_ok=True)

            # 移动原始文件
            safe_filename = self._sanitize_filename(suggested_filename)
            final_file_path = os.path.join(archive_path, safe_filename)
            final_file_path = self._handle_filename_conflict(final_file_path)
            shutil.move(temp_file_path, final_file_path)

            # 移动解析内容文件
            parsed_content_moved = None
            if temp_parsed_content_path and os.path.exists(temp_parsed_content_path):
                # 生成解析内容的最终文件名
                base_name = os.path.splitext(safe_filename)[0]
                parsed_filename = f"{base_name}_解析内容.md"
                final_parsed_path = os.path.join(archive_path, parsed_filename)
                final_parsed_path = self._handle_filename_conflict(final_parsed_path)
                shutil.move(temp_parsed_content_path, final_parsed_path)
                parsed_content_moved = final_parsed_path
                logger.info(f"解析内容移动到档案目录: {final_parsed_path}")

            logger.info(f"文件移动到档案目录: {final_file_path}")
            return final_file_path, parsed_content_moved

        except Exception as e:
            logger.error(f"移动文件和解析内容到档案目录失败: {str(e)}")
            raise e

    def save_archive_file(
        self, 
        file_content: bytes, 
        project_code: str, 
        classification: Dict,
        suggested_filename: str
    ) -> str:
        """直接保存文件到档案目录"""
        try:
            # 生成档案存储路径
            archive_path = self._get_archive_path(project_code, classification)
            os.makedirs(archive_path, exist_ok=True)
            
            # 生成最终文件路径
            safe_filename = self._sanitize_filename(suggested_filename)
            final_file_path = os.path.join(archive_path, safe_filename)
            
            # 处理文件名冲突
            final_file_path = self._handle_filename_conflict(final_file_path)
            
            # 保存文件
            with open(final_file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"文件保存到档案目录: {final_file_path}")
            return final_file_path
            
        except Exception as e:
            logger.error(f"保存文件到档案目录失败: {str(e)}")
            raise e
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件删除成功: {file_path}")
                
                # 尝试删除空目录
                self._cleanup_empty_directories(os.path.dirname(file_path))
                return True
            else:
                logger.warning(f"文件不存在: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {str(e)}")
            return False
    
    def cleanup_temp_files(self, task_id: str) -> bool:
        """清理临时文件"""
        try:
            task_temp_dir = os.path.join(self.temp_storage_path, task_id)
            if os.path.exists(task_temp_dir):
                shutil.rmtree(task_temp_dir)
                logger.info(f"临时文件清理成功: {task_temp_dir}")
                return True
            return True
            
        except Exception as e:
            logger.error(f"清理临时文件失败 {task_id}: {str(e)}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """获取文件信息"""
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "exists": True
            }
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {str(e)}")
            return None
    
    def calculate_file_hash(self, file_path: str) -> Optional[str]:
        """计算文件MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
            
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {str(e)}")
            return None
    
    def _get_archive_path(self, project_code: str, classification: Dict) -> str:
        """生成档案存储路径"""
        main_process = classification.get("main_process", "未分类")
        sub_process = classification.get("sub_process", "未分类")
        
        # 清理路径中的特殊字符
        clean_main = self._sanitize_path_component(main_process)
        clean_sub = self._sanitize_path_component(sub_process)
        clean_project = self._sanitize_path_component(project_code)
        
        return os.path.join(self.base_storage_path, clean_project, clean_main, clean_sub)
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        # 替换Windows和Linux中的非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        
        # 移除前后空格和点
        filename = filename.strip(' .')
        
        # 确保文件名不为空
        if not filename:
            filename = f"unnamed_file_{uuid.uuid4().hex[:8]}"
        
        # 限制文件名长度
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename
    
    def _sanitize_path_component(self, component: str) -> str:
        """清理路径组件中的非法字符"""
        # 替换路径中的非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            component = component.replace(char, '_')
        
        # 移除前后空格和点
        component = component.strip(' .')
        
        # 确保组件不为空
        if not component:
            component = "unknown"
        
        return component
    
    def _handle_filename_conflict(self, file_path: str) -> str:
        """处理文件名冲突"""
        if not os.path.exists(file_path):
            return file_path
        
        # 分离文件名和扩展名
        directory = os.path.dirname(file_path)
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        
        # 添加数字后缀
        counter = 1
        while True:
            new_filename = f"{name}_{counter}{ext}"
            new_file_path = os.path.join(directory, new_filename)
            if not os.path.exists(new_file_path):
                return new_file_path
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                new_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"
                return os.path.join(directory, new_filename)
    
    def _cleanup_empty_directories(self, directory: str) -> None:
        """清理空目录"""
        try:
            # 不要删除基础存储目录
            if directory == self.base_storage_path or not directory.startswith(self.base_storage_path):
                return
            
            # 如果目录为空，删除它
            if os.path.exists(directory) and not os.listdir(directory):
                os.rmdir(directory)
                logger.info(f"删除空目录: {directory}")
                
                # 递归检查父目录
                parent_dir = os.path.dirname(directory)
                self._cleanup_empty_directories(parent_dir)
                
        except Exception as e:
            logger.debug(f"清理空目录失败 {directory}: {str(e)}")
    
    def get_project_archive_structure(self, project_code: str) -> Dict:
        """获取项目档案目录结构"""
        try:
            project_path = os.path.join(self.base_storage_path, project_code)
            if not os.path.exists(project_path):
                return {"structure": {}, "total_files": 0, "total_size": 0}
            
            structure = {}
            total_files = 0
            total_size = 0
            
            for root, dirs, files in os.walk(project_path):
                # 计算相对路径
                rel_path = os.path.relpath(root, project_path)
                if rel_path == ".":
                    rel_path = ""
                
                # 构建目录结构
                current_level = structure
                if rel_path:
                    path_parts = rel_path.split(os.sep)
                    for part in path_parts:
                        if part not in current_level:
                            current_level[part] = {"files": [], "subdirs": {}}
                        current_level = current_level[part]["subdirs"]
                
                # 添加文件信息
                file_list = current_level.get("files", []) if rel_path else structure.setdefault("files", [])
                
                for file in files:
                    file_path = os.path.join(root, file)
                    file_stat = os.stat(file_path)
                    file_info = {
                        "name": file,
                        "size": file_stat.st_size,
                        "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    }
                    file_list.append(file_info)
                    total_files += 1
                    total_size += file_stat.st_size
            
            return {
                "structure": structure,
                "total_files": total_files,
                "total_size": total_size
            }
            
        except Exception as e:
            logger.error(f"获取项目档案结构失败 {project_code}: {str(e)}")
            return {"structure": {}, "total_files": 0, "total_size": 0, "error": str(e)}
