#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知和消息模型 - 参考禅道zt_message、zt_notify表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
import json
from app.core.database import get_db_connection, close_db_connection

class Message:
    """站内消息模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.from_user: str = ""
        self.to_user: str = ""
        self.title: str = ""
        self.content: Optional[str] = None
        self.type: str = "message"
        self.status: str = "unread"
        self.date: Optional[datetime] = None
        self.readDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def create_message(cls, from_user: str, to_user: str, title: str, content: str = "", msg_type: str = "message") -> bool:
        """创建站内消息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_message (`from`, `to`, title, content, `type`) 
                VALUES (%s, %s, %s, %s, %s)
            """, (from_user, to_user, title, content, msg_type))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"创建消息失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_user_messages(cls, account: str, status: str = "", msg_type: str = "", limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户消息列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            where_conditions = ["`to` = %s", "deleted = '0'"]
            params = [account]
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            if msg_type:
                where_conditions.append("`type` = %s")
                params.append(msg_type)
            
            where_clause = " AND ".join(where_conditions)
            params.extend([limit, offset])
            
            cursor.execute(f"""
                SELECT * FROM zt_message 
                WHERE {where_clause}
                ORDER BY date DESC
                LIMIT %s OFFSET %s
            """, params)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取用户消息失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_message_by_id(cls, message_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取消息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_message WHERE id = %s AND deleted = '0'
            """, (message_id,))
            
            return cursor.fetchone()
            
        except Exception as e:
            print(f"获取消息失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def mark_as_read(cls, message_id: int, account: str) -> bool:
        """标记消息为已读"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_message 
                SET status = 'read', readDate = NOW() 
                WHERE id = %s AND `to` = %s
            """, (message_id, account))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"标记消息已读失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def delete_message(cls, message_id: int, account: str) -> bool:
        """删除消息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_message 
                SET deleted = '1' 
                WHERE id = %s AND `to` = %s
            """, (message_id, account))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"删除消息失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_unread_count(cls, account: str) -> int:
        """获取未读消息数量"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_message 
                WHERE `to` = %s AND status = 'unread' AND deleted = '0'
            """, (account,))
            
            result = cursor.fetchone()
            return result['count'] if result else 0
            
        except Exception as e:
            print(f"获取未读消息数量失败: {e}")
            return 0
        finally:
            if conn:
                close_db_connection(conn)


class Notification:
    """通知模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.objectType: str = ""
        self.objectID: int = 0
        self.action: str = ""
        self.toList: Optional[str] = None
        self.ccList: Optional[str] = None
        self.subject: str = ""
        self.data: Optional[str] = None
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.sendTime: Optional[datetime] = None
        self.status: str = "wait"
        self.read: str = "0"
        self.deleted: str = "0"

    @classmethod
    def create_notification(cls, object_type: str, object_id: int, action: str, to_list: str, 
                          subject: str, data: str = "", created_by: str = "", cc_list: str = "") -> bool:
        """创建通知"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_notify (objectType, objectID, action, toList, ccList, subject, data, createdBy) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (object_type, object_id, action, to_list, cc_list, subject, data, created_by))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"创建通知失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_notifications(cls, status: str = "", limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """获取通知列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            where_conditions = ["deleted = '0'"]
            params = []
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            params.extend([limit, offset])
            
            cursor.execute(f"""
                SELECT * FROM zt_notify 
                WHERE {where_clause}
                ORDER BY createdDate DESC
                LIMIT %s OFFSET %s
            """, params)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取通知列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def update_notification_status(cls, notification_id: int, status: str) -> bool:
        """更新通知状态"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_notify 
                SET status = %s, sendTime = NOW() 
                WHERE id = %s
            """, (status, notification_id))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"更新通知状态失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)


class MailTemplate:
    """邮件模板模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.code: str = ""
        self.subject: str = ""
        self.content: Optional[str] = None
        self.type: str = "html"
        self.desc: Optional[str] = None
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_template_by_code(cls, code: str) -> Optional[Dict[str, Any]]:
        """根据代码获取邮件模板"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_mailtemplate WHERE code = %s AND deleted = '0'
            """, (code,))
            
            return cursor.fetchone()
            
        except Exception as e:
            print(f"获取邮件模板失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_templates(cls) -> List[Dict[str, Any]]:
        """获取所有邮件模板"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_mailtemplate 
                WHERE deleted = '0'
                ORDER BY id ASC
            """)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取邮件模板列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def render_template(cls, code: str, variables: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """渲染邮件模板"""
        template = cls.get_template_by_code(code)
        if not template:
            return None
        
        try:
            subject = template['subject']
            content = template['content']
            
            # 简单的变量替换
            for key, value in variables.items():
                placeholder = "{" + key + "}"
                subject = subject.replace(placeholder, str(value))
                content = content.replace(placeholder, str(value))
            
            return {
                "subject": subject,
                "content": content,
                "type": template['type']
            }
            
        except Exception as e:
            print(f"渲染邮件模板失败: {e}")
            return None


class Announcement:
    """系统公告模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.title: str = ""
        self.content: Optional[str] = None
        self.type: str = "info"
        self.priority: int = 1
        self.startDate: Optional[date] = None
        self.endDate: Optional[date] = None
        self.targetUsers: Optional[str] = None
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.status: str = "active"
        self.deleted: str = "0"

    @classmethod
    def get_active_announcements(cls, account: str = "") -> List[Dict[str, Any]]:
        """获取有效的系统公告"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_announcement 
                WHERE status = 'active' 
                AND deleted = '0'
                AND (startDate IS NULL OR startDate <= CURDATE())
                AND (endDate IS NULL OR endDate >= CURDATE())
                AND (targetUsers = 'all' OR targetUsers LIKE %s)
                ORDER BY priority DESC, createdDate DESC
            """, (f"%{account}%",))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取系统公告失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def create_announcement(cls, title: str, content: str, ann_type: str = "info", 
                          priority: int = 1, start_date: str = None, end_date: str = None,
                          target_users: str = "all", created_by: str = "") -> bool:
        """创建系统公告"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_announcement (title, content, `type`, priority, startDate, endDate, targetUsers, createdBy) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (title, content, ann_type, priority, start_date, end_date, target_users, created_by))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"创建系统公告失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_announcements(cls, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """获取所有公告"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_announcement 
                WHERE deleted = '0'
                ORDER BY createdDate DESC
                LIMIT %s OFFSET %s
            """, (limit, offset))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取公告列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)
