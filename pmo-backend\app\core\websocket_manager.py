"""
WebSocket连接管理器
用于处理实时数据同步
"""

import json
import asyncio
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 按房间分组的连接
        self.rooms: Dict[str, Set[str]] = {}
        # 连接的用户信息
        self.connection_users: Dict[str, dict] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str, user_info: dict = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_users[connection_id] = user_info or {}
        
        logger.info(f"WebSocket连接已建立: {connection_id}")
        
        # 发送连接成功消息
        await self.send_personal_message({
            "type": "connected",
            "message": "WebSocket连接已建立",
            "connection_id": connection_id
        }, connection_id)
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        if connection_id in self.connection_users:
            del self.connection_users[connection_id]
        
        # 从所有房间中移除
        for room_connections in self.rooms.values():
            room_connections.discard(connection_id)
        
        logger.info(f"WebSocket连接已断开: {connection_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送个人消息失败 {connection_id}: {e}")
                self.disconnect(connection_id)
    
    async def broadcast_to_room(self, message: dict, room: str):
        """向房间内所有连接广播消息"""
        if room not in self.rooms:
            return
        
        disconnected_connections = []
        
        for connection_id in self.rooms[room].copy():
            try:
                websocket = self.active_connections.get(connection_id)
                if websocket:
                    await websocket.send_text(json.dumps(message))
                else:
                    disconnected_connections.append(connection_id)
            except Exception as e:
                logger.error(f"广播消息失败 {connection_id}: {e}")
                disconnected_connections.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected_connections:
            self.disconnect(connection_id)
    
    async def broadcast_to_all(self, message: dict):
        """向所有连接广播消息"""
        disconnected_connections = []
        
        for connection_id, websocket in self.active_connections.copy().items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"广播消息失败 {connection_id}: {e}")
                disconnected_connections.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected_connections:
            self.disconnect(connection_id)
    
    def join_room(self, connection_id: str, room: str):
        """加入房间"""
        if room not in self.rooms:
            self.rooms[room] = set()
        
        self.rooms[room].add(connection_id)
        logger.info(f"连接 {connection_id} 加入房间 {room}")
    
    def leave_room(self, connection_id: str, room: str):
        """离开房间"""
        if room in self.rooms:
            self.rooms[room].discard(connection_id)
            
            # 如果房间为空，删除房间
            if not self.rooms[room]:
                del self.rooms[room]
        
        logger.info(f"连接 {connection_id} 离开房间 {room}")
    
    def get_room_connections(self, room: str) -> List[str]:
        """获取房间内的所有连接"""
        return list(self.rooms.get(room, set()))
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
    
    def get_room_count(self) -> int:
        """获取房间数"""
        return len(self.rooms)
    
    async def send_ping(self):
        """发送心跳包"""
        ping_message = {
            "type": "ping",
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast_to_all(ping_message)

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()

class PlanningWebSocketManager:
    """项目规划专用WebSocket管理器"""
    
    def __init__(self, manager: WebSocketManager):
        self.manager = manager
        self.planning_room = "planning"
    
    async def connect_planning(self, websocket: WebSocket, connection_id: str, user_info: dict = None):
        """连接到项目规划房间"""
        await self.manager.connect(websocket, connection_id, user_info)
        self.manager.join_room(connection_id, self.planning_room)
    
    def disconnect_planning(self, connection_id: str):
        """从项目规划房间断开"""
        self.manager.leave_room(connection_id, self.planning_room)
        self.manager.disconnect(connection_id)
    
    async def notify_project_created(self, project_data: dict):
        """通知项目创建"""
        message = {
            "type": "project_created",
            "data": project_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.manager.broadcast_to_room(message, self.planning_room)
    
    async def notify_project_updated(self, project_data: dict):
        """通知项目更新"""
        message = {
            "type": "project_updated",
            "data": project_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.manager.broadcast_to_room(message, self.planning_room)
    
    async def notify_project_deleted(self, project_data: dict):
        """通知项目删除"""
        message = {
            "type": "project_deleted",
            "data": project_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.manager.broadcast_to_room(message, self.planning_room)
    
    async def notify_stats_updated(self, stats_data: dict):
        """通知统计数据更新"""
        message = {
            "type": "stats_updated",
            "data": stats_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.manager.broadcast_to_room(message, self.planning_room)
    
    async def notify_batch_operation(self, operation_data: dict):
        """通知批量操作"""
        message = {
            "type": "batch_operation",
            "data": operation_data,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.manager.broadcast_to_room(message, self.planning_room)

# 全局项目规划WebSocket管理器实例
planning_ws_manager = PlanningWebSocketManager(websocket_manager)
