#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
动态知识库管理API
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
import json

from app.core.logger import get_logger
from app.services.dynamic_knowledge_service import dynamic_knowledge_service

logger = get_logger(__name__)
router = APIRouter()

@router.post("/start-monitoring")
async def start_monitoring():
    """启动动态知识库监控"""
    try:
        dynamic_knowledge_service.start_monitoring()
        return {
            "code": 200,
            "message": "动态知识库监控已启动",
            "data": {
                "status": "monitoring",
                "monitored_tables": dynamic_knowledge_service.monitored_tables,
                "monitored_directories": dynamic_knowledge_service.monitored_directories
            }
        }
    except Exception as e:
        logger.error(f"启动动态知识库监控失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动监控失败: {str(e)}")

@router.post("/stop-monitoring")
async def stop_monitoring():
    """停止动态知识库监控"""
    try:
        dynamic_knowledge_service.stop_monitoring()
        return {
            "code": 200,
            "message": "动态知识库监控已停止",
            "data": {
                "status": "stopped"
            }
        }
    except Exception as e:
        logger.error(f"停止动态知识库监控失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止监控失败: {str(e)}")

@router.get("/monitoring-status")
async def get_monitoring_status():
    """获取动态知识库监控状态"""
    try:
        status = dynamic_knowledge_service.get_monitoring_status()
        return {
            "code": 200,
            "message": "获取监控状态成功",
            "data": status
        }
    except Exception as e:
        logger.error(f"获取监控状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/force-refresh")
async def force_refresh_knowledge():
    """强制刷新知识库"""
    try:
        # 清除所有缓存
        dynamic_knowledge_service.invalidate_cache([
            'red_black_board', 'entity_stats', 'project_summary',
            'user_data', 'project_details', 'supervision_data',
            'change_history', 'document_content', 'formatted_knowledge'
        ])
        
        # 重新构建知识库
        knowledge_content = dynamic_knowledge_service.get_knowledge_base(force_refresh=True)
        
        return {
            "code": 200,
            "message": "知识库强制刷新成功",
            "data": {
                "content_length": len(knowledge_content),
                "sections_count": knowledge_content.count('##'),
                "refresh_time": "刚刚"
            }
        }
    except Exception as e:
        logger.error(f"强制刷新知识库失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新失败: {str(e)}")

@router.get("/knowledge-content")
async def get_knowledge_content():
    """获取当前知识库内容"""
    try:
        knowledge_content = dynamic_knowledge_service.get_knowledge_base()
        
        return {
            "code": 200,
            "message": "获取知识库内容成功",
            "data": {
                "content": knowledge_content,
                "content_length": len(knowledge_content),
                "sections_count": knowledge_content.count('##'),
                "last_update": dynamic_knowledge_service.base_knowledge_service.last_update.get('knowledge_base', 0)
            }
        }
    except Exception as e:
        logger.error(f"获取知识库内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取内容失败: {str(e)}")

@router.post("/invalidate-cache")
async def invalidate_cache(cache_keys: list = None):
    """使指定缓存失效"""
    try:
        if cache_keys is None:
            # 默认清除所有缓存
            cache_keys = [
                'red_black_board', 'entity_stats', 'project_summary',
                'user_data', 'project_details', 'supervision_data',
                'change_history', 'document_content', 'formatted_knowledge'
            ]
        
        dynamic_knowledge_service.invalidate_cache(cache_keys)
        
        return {
            "code": 200,
            "message": "缓存清除成功",
            "data": {
                "invalidated_keys": cache_keys
            }
        }
    except Exception as e:
        logger.error(f"清除缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/cache-status")
async def get_cache_status():
    """获取缓存状态"""
    try:
        cache_info = {
            "cached_items": list(dynamic_knowledge_service.base_knowledge_service.knowledge_cache.keys()),
            "last_updates": dynamic_knowledge_service.base_knowledge_service.last_update,
            "cache_sizes": {}
        }
        
        # 计算各缓存项的大小
        for key, value in dynamic_knowledge_service.base_knowledge_service.knowledge_cache.items():
            try:
                cache_info["cache_sizes"][key] = len(str(value))
            except:
                cache_info["cache_sizes"][key] = 0
        
        return {
            "code": 200,
            "message": "获取缓存状态成功",
            "data": cache_info
        }
    except Exception as e:
        logger.error(f"获取缓存状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缓存状态失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        status = dynamic_knowledge_service.get_monitoring_status()
        
        return {
            "code": 200,
            "message": "动态知识库服务正常",
            "data": {
                "service_status": "healthy",
                "monitoring_active": status["is_monitoring"],
                "cached_items_count": len(status["cache_status"]["cached_items"]),
                "monitored_tables_count": len(status["monitored_tables"]),
                "monitored_directories_count": len(status["monitored_directories"])
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "code": 500,
            "message": f"服务异常: {str(e)}",
            "data": {
                "service_status": "unhealthy"
            }
        }
