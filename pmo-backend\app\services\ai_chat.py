"""
AI聊天服务模块
处理与大模型的交互，包括文件内容分析和问答
"""

import asyncio
import json
import time
from typing import Dict, List, Optional
import httpx
import aiohttp

from app.core.logger import get_logger

logger = get_logger(__name__)

class AIChatService:
    """AI聊天服务类"""
    
    def __init__(self, api_url: str, api_key: str, model: str, backup_urls: List[str] = None):
        """初始化AI聊天服务"""
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.backup_urls = backup_urls or [api_url]
        self.current_api_index = 0
    
    def get_current_api_url(self) -> str:
        """获取当前API地址"""
        if not self.backup_urls:
            return self.api_url
        return self.backup_urls[self.current_api_index % len(self.backup_urls)]
    
    def switch_to_next_api(self) -> str:
        """切换到下一个API地址"""
        if len(self.backup_urls) > 1:
            self.current_api_index = (self.current_api_index + 1) % len(self.backup_urls)
            new_api_url = self.get_current_api_url()
            logger.warning(f"切换到备用API地址: {new_api_url}")
            return new_api_url
        return self.get_current_api_url()
    
    async def chat_completion(self, messages: List[Dict], temperature: float = 0.7, max_tokens: int = 1000) -> Dict:
        """通用的聊天完成接口"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                current_api_url = self.get_current_api_url()

                # 构建请求数据
                request_data = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens
                }

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                async with aiohttp.ClientSession() as session:
                    # 检查API URL是否已经包含完整路径
                    if "/v1/chat/completions" in current_api_url:
                        api_endpoint = current_api_url
                    else:
                        api_endpoint = f"{current_api_url}/v1/chat/completions"

                    async with session.post(
                        api_endpoint,
                        json=request_data,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=60)
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            return {
                                "success": True,
                                "content": result["choices"][0]["message"]["content"],
                                "usage": result.get("usage", {}),
                                "model": result.get("model", self.model)
                            }
                        else:
                            error_text = await response.text()
                            logger.error(f"API请求失败: {response.status}, {error_text}")

            except Exception as e:
                logger.error(f"AI聊天请求失败 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1

                if retry_count < max_retries:
                    # 尝试切换到下一个API地址
                    self.switch_to_next_api()
                    await asyncio.sleep(1)  # 等待1秒后重试
                    continue

                return {
                    "success": False,
                    "error": f"聊天请求失败: {str(e)}"
                }

        return {
            "success": False,
            "error": "已达到最大重试次数"
        }

    async def generate_file_answer(self, query: str) -> str:
        """为文件内容生成AI回答"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                api_url = self.get_current_api_url()
                
                messages = [
                    {
                        "role": "user",
                        "content": query
                    }
                ]
                
                request_body = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 4000
                }
                
                # 检查API URL是否已经包含完整路径
                if "/v1/chat/completions" in api_url:
                    api_endpoint = api_url
                else:
                    api_endpoint = f"{api_url}/v1/chat/completions"

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                async with aiohttp.ClientSession() as session:
                    logger.info(f"发送AI请求到: {api_endpoint} (尝试 {retry_count + 1}/{max_retries})")

                    async with session.post(
                        api_endpoint,
                        json=request_body,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=300)
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                            if content:
                                return content
                            else:
                                logger.warning("AI返回空内容，尝试重试")
                                retry_count += 1
                                continue
                        else:
                            error_text = await response.text()
                            logger.error(f"AI API错误: {response.status} - {error_text}")
                            # 对于401错误也尝试切换API
                            if (response.status >= 500 or response.status == 401) and retry_count < max_retries - 1:
                                # 服务器错误或认证错误，尝试切换API
                                self.switch_to_next_api()
                                retry_count += 1
                                await asyncio.sleep(2)
                                continue
                            return f"AI回答生成失败: API错误 {response.status}"
                        
            except (httpx.TimeoutException, httpx.ConnectError, httpx.RemoteProtocolError) as e:
                logger.error(f"网络连接错误 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                if retry_count < max_retries - 1:
                    # 网络错误，尝试切换API
                    self.switch_to_next_api()
                    retry_count += 1
                    await asyncio.sleep(2)
                    continue
                return f"AI回答生成失败: 网络连接超时，请稍后重试"
            except Exception as e:
                logger.error(f"生成AI回答失败: {str(e)}")
                if retry_count < max_retries - 1:
                    retry_count += 1
                    await asyncio.sleep(1)
                    continue
                return f"AI回答生成失败: {str(e)}"
        
        return "AI回答生成失败: 已达到最大重试次数"
    
    def build_file_context(self, file_contents: List[Dict], query: str) -> str:
        """构建包含文件内容的提示，并控制长度"""
        files_context_parts = []
        total_length = 0
        max_context_length = 30000  # 限制上下文长度，为AI回答预留空间
        
        for fc in file_contents:
            file_part = f"文件名: {fc['filename']}\n内容:\n{fc['content']}"
            
            # 检查是否会超过长度限制
            if total_length + len(file_part) > max_context_length:
                # 截断内容
                remaining_length = max_context_length - total_length - 100  # 预留一些空间
                if remaining_length > 500:  # 如果剩余空间足够，添加截断的内容
                    truncated_content = fc['content'][:remaining_length]
                    file_part = f"文件名: {fc['filename']}\n内容:\n{truncated_content}...[内容已截断]"
                    files_context_parts.append(file_part)
                break
            
            files_context_parts.append(file_part)
            total_length += len(file_part)
        
        files_context = "\n\n".join(files_context_parts)
        
        enhanced_query = f"""用户问题: {query}

相关文件内容:
{files_context}

请基于上述文件内容回答用户的问题。如果文件中包含表格数据（如项目规划表），请特别注意数据的结构和关联关系。如果内容被截断，请基于可见部分进行分析。"""
        
        return enhanced_query
    
    async def chat_with_files(self, query: str, file_contents: List[Dict]) -> Dict:
        """带文件的聊天功能"""
        try:
            # 构建包含文件内容的提示
            enhanced_query = self.build_file_context(file_contents, query)
            
            # 调用AI模型
            response_text = await self.generate_file_answer(enhanced_query)
            
            return {
                "success": True,
                "answer": response_text,
                "files_processed": len(file_contents),
                "query": query
            }
            
        except Exception as e:
            logger.error(f"带文件聊天处理失败: {str(e)}")
            return {
                "success": False,
                "error": f"处理失败: {str(e)}",
                "files_processed": 0,
                "query": query
            }
    
    async def analyze_single_file(self, file_content: str, filename: str, query: str = None) -> Dict:
        """分析单个文件内容"""
        try:
            if not query:
                query = "请分析这个文件的内容，总结主要信息。"
            
            enhanced_query = f"""文件名: {filename}
文件内容:
{file_content[:5000]}  # 限制内容长度

用户问题: {query}

请基于上述文件内容回答问题。"""
            
            response_text = await self.generate_file_answer(enhanced_query)
            
            return {
                "success": True,
                "filename": filename,
                "analysis": response_text,
                "content_preview": file_content[:500]  # 返回内容预览
            }
            
        except Exception as e:
            logger.error(f"文件分析失败: {str(e)}")
            return {
                "success": False,
                "filename": filename,
                "error": f"分析失败: {str(e)}"
            }
    
    def validate_file_content(self, file_content: str, filename: str) -> Dict:
        """验证文件内容"""
        if not file_content or len(file_content.strip()) == 0:
            return {
                "valid": False,
                "error": "文件内容为空"
            }
        
        if len(file_content) > 100000:  # 100KB文本限制
            return {
                "valid": True,
                "warning": "文件内容较大，可能会被截断",
                "truncated": True
            }
        
        return {
            "valid": True,
            "size": len(file_content)
        }
    
    def format_response(self, response_data: Dict) -> Dict:
        """格式化响应数据"""
        return {
            "code": 200 if response_data.get("success", False) else 500,
            "message": "成功" if response_data.get("success", False) else "失败",
            "data": response_data
        }
