|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  | 取值规则 |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 序号 | 代码 | 名称 | 类型 | 字典 | 说明 | 业务系统是否包含 | 是否必填 | 类型/长度 | 说明 | 业务系统是否包含 | 数据源1 | 数据源2 | 数据源2 | 数据源2 | 备注 | 提问 | 取值单据 | 取值字段 | 取值字段备注 | 字段默认格式 | 默认值 | 取值规则备注 |
| 1 | ctif_id | 客户号 | varchar(32) |  | 必填，reference (t_stan_org) busi_reg_no |  |  |  |  | 是 | rt_pvp_zjtf_app.cus_id | cus_com.inner_cus_id ,cus_com.cus_id | rt_pvp_zjtf_app.cus_id | rt_pvp_zjtf_app.cus_id | 租赁-资金投放申请信息 |  |  |  |  |  |  |  |
| 2 | ctnm | 客户名称 | varchar(128) |  | 必填 |  |  |  |  | 是 | rt_pvp_zjtf_app.cus_name | cus_com.cus_name | rt_pvp_zjtf_app.cus_name | rt_pvp_zjtf_app.cus_name |  |  |  |  |  |  |  |  |
| 3 | ctif_tp | 主体类型 | varchar(10) | 1:个人
2:机构 | 必填 |  |  |  |  | 否 | 通过cus_base.cus_type识别是"个人"还是"机构" | 2 | 通过cus_base.cus_type识别是"个人"还是"机构" | 通过cus_base.cus_type识别是"个人"还是"机构" |  |  |  |  |  |  |  |  |
| 4 | acc_type1 | 账户类型 | varchar(2) | 11:支付账户
12:银行账户 | 必填 |  |  |  |  | 否 | 12 | 12 ?? | 11或12 | 12 |  | 支付账号和银行账号怎么定义, 支付账号是付款账号吗,银行账号是收款账号吗 |  |  |  |  |  |  |
| 5 | act_cd | 账户号 | varchar(64) |  | 必填 |  |  |  |  | 是 | rt_pvp_zjtf_app.ls_rpt_acct_no(承租人还款账户) | cus_com.bas_acc_no | rt_pvp_zjtf_app.lessor_acct_no(出租方账户) | 
rt_pvp_zjtf_app.rt_recv_acct_no(租赁本金收款账户) |  |  |  |  |  |  |  |  |
| 6 | bank_acc_name | 账户名称 | varchar(80) |  | 账号对应账户名称 |  |  |  |  | 是 | rt_pvp_zjtf_app.ls_rpt_acct_name(承租人还款账户) | 无 | rt_pvp_zjtf_app.lessor_acct_name(出租方账户) | 
rt_pvp_zjtf_app.rt_recv_acct_name(租赁本金收款账户) |  |  |  |  |  |  |  |  |
| 7 | cabm | 账号开户机构名称 | varchar(64) |  | 必填 |  |  |  |  | 是 | rt_pvp_zjtf_app.ls_rpt_acct_ob(承租人还款账户) | cus_com.bas_acc_bank | rt_pvp_zjtf_app.lessor_acct_ob(出租方账户) | 
rt_pvp_zjtf_app.rt_recv_acct_ob(租赁本金收款账户) |  |  |  |  |  |  |  |  |
| 8 | country | 开户机构所在地 | varchar(6) | 参见省市代码数据字典 | 开户机构行政区划代码 |  |  |  |  | 是 | 通过"账号开户机构名称"关联s_bank_serno表，取s_bank_serno.area_code | s_bank_serno.area_code | 通过"账号开户机构名称"关联s_bank_serno表，取s_bank_serno.area_code | 通过"账号开户机构名称"关联s_bank_serno表，取s_bank_serno.area_code |  |  |  |  |  |  |  |  |
| 10 | statement_type | 结算类型 | char(1)/char(3) | 0:D0,T0
1:D1,T1
9:其它 | D、T表示结算日，如D0,T0 表示当天结算 |  |  |  |  | 否 | 无 | 无 | 无 | 无 |  |  |  |  |  |  |  |  |
| 11 | mer_unit | 管理机构 | varchar(10) |  | 必填，所属分支代码（分公司）用于数据过滤，实施时根据实际情况配置 |  |  |  |  | 否 | 无 | 无 | 无 | 无 |  |  |  |  |  |  |  |  |
| 12 | remark | 备注 | varchar(512) |  |  |  |  |  |  | 否 | 无 | 无 | 无 | 无 |  |  |  |  |  |  |  |  |
| 13 | data_updt | 数据更新日期 | varchar(8) |  | 必填，数据内容更新日期 |  |  |  |  | 否 | 更新时间 | 更新时间 | 更新时间 | 更新时间 |  |  |  |  |  |  |  |  |
| 14 | data_transfer_dt | 数据传输日期 | varchar(8) |  | 必填，推送完成的数据日期 |  |  |  |  | 否 | 更新时间 | 更新时间 | 更新时间 | 更新时间 |  |  |  |  |  |  |  |  |
|  | 一个主体存在一个或多个银行账户（卡）信息，如存在多个，则分为多条填写 |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |