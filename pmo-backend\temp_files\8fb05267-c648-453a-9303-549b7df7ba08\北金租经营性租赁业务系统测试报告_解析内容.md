# Word文档内容


北金租经营性租赁业务系统测试报告

编写人员：     覃业凤

编制时间： 2024年6月25日

## 1概述

### 1.1关于本文档

### 1.2目的

本测试报告为北金租经营性租赁业务系统项目的测试报告，主要用于总结测试阶段的测试以及分析测试结果，描述系统是否符合需求。

### 1.3项目背景

北金租经营性租赁业务系统是一款金融租赁系统软件，针对个人和公司，提供经营性租赁物租赁等业务。系统界面简洁明了，操作直观简单，充分满足了经营性租赁的业务管理需求。

### 1.4参考资料

## 2测试概要

### 2.1测试版本

北金租经营性租赁业务系统V1.0

### 2.2测试范围

所有模块：

### 2.3测试环境

### 2.4测试策略

### 2.5测试用例设计方法

## 3测试组织

### 3.1测试人员

### 3.2测试时间

2024年5月10日--2024年6月25日

### 3.3测试类型

#### 3.3.1功能测试

测试已通过，详见测试用例

## 4测试用例执行状况

说明：测试用例执行完毕，发现有效缺陷636个，已修复633个，遗留2个轻微问题。

## 5测试总结

### 5.2缺陷数量汇总图

### 5.1缺陷状态汇总图

### 5.3缺陷收敛趋势图

本阶段测试用例执行通过率为99%，有2个轻微问题


| 项目名称 | 北金租经营性租赁业务系统 |
| --- | --- |
| 主题 | 测试报告 |
| 版本 | V1.0 |
| 密级 | 机密 |
| 适用对象 | 甲方项目经理，项目经理、测试经理、测试人员、其他质量管理人员和需要阅读本报告的高层经理和其他相关人员 |



| 文档 | 可用 | 已通过审核 |
| --- | --- | --- |
| 需求文档 | 是 | 是 |
| 用户使用说明 | 是 | 是 |
| 测试用例 | 是 | 是 |



| PC端模块 | PC端模块 |
| --- | --- |
| 客户管理 | 自然人登记、法人登记、黑名单管理、客户交接、客户财务报表 |
| 租前管理 | 项目立项、项目申报、项目交接单、项目报价、采购订单 |
| 租中管理 | 租赁合同、合同变更、合同签约、用印申请、租赁物交付单、抵质押物登记 |
| 租后管理 | 还款计划、还款计划变更、合同结清、租赁物设备档案、租后巡检 |
| 收付款管理 | 交易流水单、租金收款单、费用应收单、费用付款单、费用收款单、费用退款单 |
| 资料管理 | 资料配置、资料归档、资料查询 |
| 财务管理 | 经营性租赁收入计提、经营性租赁租金增值税计提 |



| 电脑类型 | 笔记本电脑 |
| --- | --- |
| CPU | 酷睿i5 4核2.6Hz |
| 内存 | 8GB |
| 硬盘 | 256G |
| 操作系统 | Windows 7 旗舰版 |
| 数据库 | Oracle |



| 测试类型 | 是否采用 |
| --- | --- |
| 界面测试 | 采用 |
| 功能测试 | 采用 |
| 文档测试 | 采用 |
| 兼容性测试 | 采用 |
| 安全性测试 | 采用 |
| 接口测试 | 采用 |



| 方法名称 | 描述 |
| --- | --- |
| 等价类划分 | 属于典型的黑盒测试方法，根据程序对数据的要求，将程序输入域划分为若干个部分，判断数据的有效性，分为有效等价类和无效等价类两种。 |
| 边界值 | 基于等价类的基础之上，在边界越容易出错的原则，核心是在输入范围的两个最值的基础上加1和减1，得出在范围内的有效等价类和范围外的无效等价类，用于输入值是数值的情况。 |
| 测试大纲法 | 找出所有窗口和窗口之间的输入动作和窗口之间的联系，编写测试用例。 |
| 状态转换图 | 分析需求、画出状态、画出状态树，编写测试用例。 |
| 因果图 | 明确找出输入的条件、明确找出输入的条件、明确输入条件和输出结果，在编写测试用例。 |
| 判定表 | 需求分析，确定条件桩（输入条件）和动作桩（输出结果）、设计和优化判定表、填写动作项、根据判定表中输出的结果，进行判定表的合并、编写测试用例 |
| 正交表 | 把测试的程序中控件的个数以及每个控件的取值个数选取合适一个，然后在把控件取的值列举出来，把取的值进行编号（根据公式:Ln(mk)或n=k*（m-1）+1
），把控件取的值在排列表中，根据排列好的表编写测试用例。 |
| 场景法 | 模拟用户操作软件场景：一个场景可能对应多条测试用例，或者一条用例可能对应多个场景，描述程序基本流以及各项备选流，根据基本流和各项备选流生成不同的场景，对每个场景生成测试用例。 |



| 姓名 | 职位 | 任务 |
| --- | --- | --- |
| 覃业凤 | 测试工程师 | 1.参与评审测试计划
2. 负责客户管理模块、租中模块、租后模块、收付款模块等的测试用例编写和执行 |
| 张宝宜 | 测试工程师 | 1.参与评审测试计划
2.负责租前模块、租中模块、租后模块、收付款模块等的测试用例编写和执行 |



|  | 共计 | 测试人员 | 用例数 |
| --- | --- | --- | --- |
| 用例总数 | 352条 | 覃业凤
张宝宜 | 160条 |
| 发现缺陷数 | 636个 | 覃业凤
张宝宜 | 340 |
| 修复缺陷数 | 633个 | 覃业凤
张宝宜 | 已修复 |
