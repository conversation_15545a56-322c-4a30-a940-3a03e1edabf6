#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知和消息API - 第九个功能模块：通知和消息
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from app.models.user import User
from app.models.notification import Message, Notification, MailTemplate, Announcement
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/notification", tags=["通知和消息"])

# 请求模型
class MessageCreateRequest(BaseModel):
    to_user: str
    title: str
    content: str = ""
    type: str = "message"

class NotificationCreateRequest(BaseModel):
    object_type: str
    object_id: int
    action: str
    to_list: str
    subject: str
    data: str = ""
    cc_list: str = ""

class AnnouncementCreateRequest(BaseModel):
    title: str
    content: str
    type: str = "info"
    priority: int = 1
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    target_users: str = "all"

# 站内消息API
@router.get("/messages")
async def get_messages(
    status: str = Query("", description="消息状态"),
    type: str = Query("", description="消息类型"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取用户消息列表"""
    try:
        offset = (page - 1) * page_size
        messages = Message.get_user_messages(
            account=current_user.account,
            status=status,
            msg_type=type,
            limit=page_size,
            offset=offset
        )
        
        return success_response({
            "messages": messages,
            "page": page,
            "page_size": page_size,
            "total": len(messages)
        }, "获取消息列表成功")
    except Exception as e:
        return error_response(f"获取消息列表失败: {str(e)}")

@router.get("/messages/{message_id}")
async def get_message(
    message_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取消息详情"""
    try:
        message = Message.get_message_by_id(message_id)
        if not message:
            return error_response("消息不存在", status_code=404)
        
        # 检查权限
        if message['to'] != current_user.account:
            return error_response("权限不足", status_code=403)
        
        return success_response(message, "获取消息详情成功")
    except Exception as e:
        return error_response(f"获取消息详情失败: {str(e)}")

@router.post("/messages")
async def create_message(
    request: MessageCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """发送站内消息"""
    try:
        success = Message.create_message(
            from_user=current_user.account,
            to_user=request.to_user,
            title=request.title,
            content=request.content,
            msg_type=request.type
        )
        
        if success:
            return success_response(None, "发送消息成功")
        else:
            return error_response("发送消息失败")
    except Exception as e:
        return error_response(f"发送消息失败: {str(e)}")

@router.put("/messages/{message_id}/read")
async def mark_message_read(
    message_id: int,
    current_user: User = Depends(get_current_user)
):
    """标记消息为已读"""
    try:
        success = Message.mark_as_read(message_id, current_user.account)
        
        if success:
            return success_response(None, "标记已读成功")
        else:
            return error_response("标记已读失败")
    except Exception as e:
        return error_response(f"标记已读失败: {str(e)}")

@router.delete("/messages/{message_id}")
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除消息"""
    try:
        success = Message.delete_message(message_id, current_user.account)
        
        if success:
            return success_response(None, "删除消息成功")
        else:
            return error_response("删除消息失败")
    except Exception as e:
        return error_response(f"删除消息失败: {str(e)}")

@router.get("/messages/unread/count")
async def get_unread_count(current_user: User = Depends(get_current_user)):
    """获取未读消息数量"""
    try:
        count = Message.get_unread_count(current_user.account)
        return success_response({"count": count}, "获取未读消息数量成功")
    except Exception as e:
        return error_response(f"获取未读消息数量失败: {str(e)}")

# 通知管理API
@router.get("/notifications")
async def get_notifications(
    status: str = Query("", description="通知状态"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取通知列表"""
    try:
        # 只有管理员可以查看所有通知
        if current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        offset = (page - 1) * page_size
        notifications = Notification.get_notifications(
            status=status,
            limit=page_size,
            offset=offset
        )
        
        return success_response({
            "notifications": notifications,
            "page": page,
            "page_size": page_size,
            "total": len(notifications)
        }, "获取通知列表成功")
    except Exception as e:
        return error_response(f"获取通知列表失败: {str(e)}")

@router.post("/notifications")
async def create_notification(
    request: NotificationCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建通知"""
    try:
        success = Notification.create_notification(
            object_type=request.object_type,
            object_id=request.object_id,
            action=request.action,
            to_list=request.to_list,
            subject=request.subject,
            data=request.data,
            created_by=current_user.account,
            cc_list=request.cc_list
        )
        
        if success:
            return success_response(None, "创建通知成功")
        else:
            return error_response("创建通知失败")
    except Exception as e:
        return error_response(f"创建通知失败: {str(e)}")

@router.put("/notifications/{notification_id}/status")
async def update_notification_status(
    notification_id: int,
    status: str,
    current_user: User = Depends(get_current_user)
):
    """更新通知状态"""
    try:
        # 只有管理员可以更新通知状态
        if current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        success = Notification.update_notification_status(notification_id, status)
        
        if success:
            return success_response(None, "更新通知状态成功")
        else:
            return error_response("更新通知状态失败")
    except Exception as e:
        return error_response(f"更新通知状态失败: {str(e)}")

# 邮件模板API
@router.get("/mail-templates")
async def get_mail_templates(current_user: User = Depends(get_current_user)):
    """获取邮件模板列表"""
    try:
        templates = MailTemplate.get_all_templates()
        return success_response(templates, "获取邮件模板列表成功")
    except Exception as e:
        return error_response(f"获取邮件模板列表失败: {str(e)}")

@router.get("/mail-templates/{code}")
async def get_mail_template(
    code: str,
    current_user: User = Depends(get_current_user)
):
    """获取邮件模板详情"""
    try:
        template = MailTemplate.get_template_by_code(code)
        if not template:
            return error_response("邮件模板不存在", status_code=404)
        
        return success_response(template, "获取邮件模板成功")
    except Exception as e:
        return error_response(f"获取邮件模板失败: {str(e)}")

@router.post("/mail-templates/{code}/render")
async def render_mail_template(
    code: str,
    variables: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """渲染邮件模板"""
    try:
        rendered = MailTemplate.render_template(code, variables)
        if not rendered:
            return error_response("邮件模板不存在或渲染失败", status_code=404)
        
        return success_response(rendered, "渲染邮件模板成功")
    except Exception as e:
        return error_response(f"渲染邮件模板失败: {str(e)}")

# 系统公告API
@router.get("/announcements")
async def get_announcements(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    current_user: User = Depends(get_current_user)
):
    """获取系统公告列表"""
    try:
        offset = (page - 1) * page_size
        announcements = Announcement.get_all_announcements(
            limit=page_size,
            offset=offset
        )
        
        return success_response({
            "announcements": announcements,
            "page": page,
            "page_size": page_size,
            "total": len(announcements)
        }, "获取公告列表成功")
    except Exception as e:
        return error_response(f"获取公告列表失败: {str(e)}")

@router.get("/announcements/active")
async def get_active_announcements(current_user: User = Depends(get_current_user)):
    """获取有效的系统公告"""
    try:
        announcements = Announcement.get_active_announcements(current_user.account)
        return success_response(announcements, "获取有效公告成功")
    except Exception as e:
        return error_response(f"获取有效公告失败: {str(e)}")

@router.post("/announcements")
async def create_announcement(
    request: AnnouncementCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建系统公告"""
    try:
        # 只有管理员可以创建公告
        if current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        success = Announcement.create_announcement(
            title=request.title,
            content=request.content,
            ann_type=request.type,
            priority=request.priority,
            start_date=request.start_date,
            end_date=request.end_date,
            target_users=request.target_users,
            created_by=current_user.account
        )
        
        if success:
            return success_response(None, "创建公告成功")
        else:
            return error_response("创建公告失败")
    except Exception as e:
        return error_response(f"创建公告失败: {str(e)}")

# 通知助手API
@router.post("/send-task-notification")
async def send_task_notification(
    task_id: int,
    assigned_to: str,
    action: str = "assigned",
    current_user: User = Depends(get_current_user)
):
    """发送任务通知"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        # 获取任务信息
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, p.name as project_name 
                FROM zt_task t
                LEFT JOIN zt_project p ON t.project = p.id
                WHERE t.id = %s
            """, (task_id,))
            
            task = cursor.fetchone()
            if not task:
                return error_response("任务不存在", status_code=404)
            
            # 创建站内消息
            if action == "assigned":
                title = f"任务分配通知：{task['name']}"
                content = f"您有一个新的任务需要处理：{task['name']}，项目：{task['project_name']}"
            elif action == "finished":
                title = f"任务完成通知：{task['name']}"
                content = f"任务'{task['name']}'已完成，项目：{task['project_name']}"
            else:
                title = f"任务更新通知：{task['name']}"
                content = f"任务'{task['name']}'状态已更新，项目：{task['project_name']}"
            
            Message.create_message(
                from_user="system",
                to_user=assigned_to,
                title=title,
                content=content,
                msg_type="task"
            )
            
            # 创建通知记录
            Notification.create_notification(
                object_type="task",
                object_id=task_id,
                action=action,
                to_list=assigned_to,
                subject=title,
                data=f"task_id:{task_id}",
                created_by=current_user.account
            )
            
            return success_response(None, "发送任务通知成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"发送任务通知失败: {str(e)}")

@router.post("/send-bug-notification")
async def send_bug_notification(
    bug_id: int,
    assigned_to: str,
    action: str = "assigned",
    current_user: User = Depends(get_current_user)
):
    """发送缺陷通知"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        # 获取缺陷信息
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT b.*, p.name as product_name 
                FROM zt_bug b
                LEFT JOIN zt_product p ON b.product = p.id
                WHERE b.id = %s
            """, (bug_id,))
            
            bug = cursor.fetchone()
            if not bug:
                return error_response("缺陷不存在", status_code=404)
            
            # 创建站内消息
            if action == "assigned":
                title = f"缺陷指派通知：{bug['title']}"
                content = f"您有一个新的缺陷需要处理：{bug['title']}，产品：{bug['product_name']}"
            elif action == "resolved":
                title = f"缺陷解决通知：{bug['title']}"
                content = f"缺陷'{bug['title']}'已解决，产品：{bug['product_name']}"
            else:
                title = f"缺陷更新通知：{bug['title']}"
                content = f"缺陷'{bug['title']}'状态已更新，产品：{bug['product_name']}"
            
            Message.create_message(
                from_user="system",
                to_user=assigned_to,
                title=title,
                content=content,
                msg_type="bug"
            )
            
            # 创建通知记录
            Notification.create_notification(
                object_type="bug",
                object_id=bug_id,
                action=action,
                to_list=assigned_to,
                subject=title,
                data=f"bug_id:{bug_id}",
                created_by=current_user.account
            )
            
            return success_response(None, "发送缺陷通知成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"发送缺陷通知失败: {str(e)}")
