#!/usr/bin/env python3
"""
测试文档解析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_document_parser():
    """测试文档解析器初始化"""
    try:
        print("🔧 初始化DocumentParser...")
        parser = DocumentParser()
        print("✅ DocumentParser初始化成功")
        
        # 测试一个简单的文本文件
        test_content = "这是一个测试文档内容".encode('utf-8')
        result = parser.parse_document(test_content, "test.txt", "text/plain")
        print(f"📄 测试文本解析结果: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DocumentParser初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_document_parser()
    if success:
        print("✅ 文档解析器测试通过")
    else:
        print("❌ 文档解析器测试失败")
        sys.exit(1)
