#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from pydantic import BaseModel
import logging
from app.db.connection import get_db_connection
from app.core.security import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()

# Pydantic 模型
class ChecklistItemCreate(BaseModel):
    main_process: str
    sub_process: str
    output_name: str
    content_description: Optional[str] = None
    keywords: Optional[str] = None
    is_required: int = 1
    sort_order: int = 0

class ChecklistItemUpdate(BaseModel):
    main_process: Optional[str] = None
    sub_process: Optional[str] = None
    output_name: Optional[str] = None
    content_description: Optional[str] = None
    keywords: Optional[str] = None
    is_required: Optional[int] = None
    sort_order: Optional[int] = None

class ChecklistItemResponse(BaseModel):
    id: int
    main_process: str
    sub_process: str
    output_name: str
    content_description: Optional[str]
    keywords: Optional[str]
    is_required: int
    sort_order: int
    created_at: str
    updated_at: str
    created_by: Optional[str]
    updated_by: Optional[str]

# 档案清单管理API
@router.get("/checklist")
async def get_checklist(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    main_process: Optional[str] = Query(None, description="主流程筛选"),
    sub_process: Optional[str] = Query(None, description="子流程筛选"),
    current_user = Depends(get_current_user)
):
    """获取档案清单列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if main_process:
                where_conditions.append("main_process LIKE %s")
                params.append(f"%{main_process}%")
            
            if sub_process:
                where_conditions.append("sub_process LIKE %s")
                params.append(f"%{sub_process}%")
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM project_archive_checklist {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * size
            data_sql = f"""
                SELECT id, main_process, sub_process, output_name, content_description, 
                       keywords, is_required, sort_order, created_at, updated_at, 
                       created_by, updated_by
                FROM project_archive_checklist 
                {where_clause}
                ORDER BY sort_order ASC, id ASC
                LIMIT %s OFFSET %s
            """
            cursor.execute(data_sql, params + [size, offset])
            items = cursor.fetchall()
            
            # 格式化时间
            for item in items:
                if item['created_at']:
                    item['created_at'] = item['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if item['updated_at']:
                    item['updated_at'] = item['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "获取档案清单成功",
            "data": {
                "items": items,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        }
        
    except Exception as e:
        logger.error(f"获取档案清单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取档案清单失败: {str(e)}")

@router.post("/checklist")
async def create_checklist_item(
    item: ChecklistItemCreate,
    current_user = Depends(get_current_user)
):
    """创建档案清单项"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查是否已存在相同的清单项
            cursor.execute("""
                SELECT COUNT(*) as count FROM project_archive_checklist 
                WHERE main_process = %s AND sub_process = %s AND output_name = %s
            """, (item.main_process, item.sub_process, item.output_name))
            
            if cursor.fetchone()['count'] > 0:
                raise HTTPException(status_code=400, detail="相同的档案清单项已存在")

            # 如果排序值为0或未指定，自动分配一个合适的排序值
            sort_order = item.sort_order
            if sort_order <= 0:
                cursor.execute("SELECT GREATEST(COALESCE(MAX(sort_order), 0), 0) + 10 as next_order FROM project_archive_checklist")
                result = cursor.fetchone()
                sort_order = result['next_order']

            # 插入新记录
            cursor.execute("""
                INSERT INTO project_archive_checklist 
                (main_process, sub_process, output_name, content_description, keywords, 
                 is_required, sort_order, created_by) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                item.main_process, item.sub_process, item.output_name,
                item.content_description, item.keywords, item.is_required,
                sort_order, current_user.get('username', 'system')
            ))
            
            new_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "创建档案清单项成功",
            "data": {"id": new_id}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建档案清单项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建档案清单项失败: {str(e)}")

@router.put("/checklist/{item_id}")
async def update_checklist_item(
    item_id: int,
    item: ChecklistItemUpdate,
    current_user = Depends(get_current_user)
):
    """更新档案清单项"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查记录是否存在
            cursor.execute("SELECT id FROM project_archive_checklist WHERE id = %s", (item_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="档案清单项不存在")
            
            # 构建更新语句
            update_fields = []
            params = []
            
            for field, value in item.dict(exclude_unset=True).items():
                if value is not None:
                    update_fields.append(f"{field} = %s")
                    params.append(value)
            
            if not update_fields:
                raise HTTPException(status_code=400, detail="没有提供要更新的字段")
            
            update_fields.append("updated_by = %s")
            params.append(current_user.get('username', 'system'))
            params.append(item_id)
            
            update_sql = f"""
                UPDATE project_archive_checklist 
                SET {', '.join(update_fields)}
                WHERE id = %s
            """
            cursor.execute(update_sql, params)
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "更新档案清单项成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新档案清单项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新档案清单项失败: {str(e)}")

@router.delete("/checklist/{item_id}")
async def delete_checklist_item(
    item_id: int,
    current_user = Depends(get_current_user)
):
    """删除档案清单项"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查记录是否存在
            cursor.execute("SELECT id FROM project_archive_checklist WHERE id = %s", (item_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="档案清单项不存在")
            
            # 检查是否有关联的文件
            cursor.execute("""
                SELECT COUNT(*) as count FROM project_archive_files 
                WHERE main_process = (
                    SELECT main_process FROM project_archive_checklist WHERE id = %s
                ) AND sub_process = (
                    SELECT sub_process FROM project_archive_checklist WHERE id = %s
                ) AND output_name = (
                    SELECT output_name FROM project_archive_checklist WHERE id = %s
                )
            """, (item_id, item_id, item_id))
            
            file_count = cursor.fetchone()['count']
            if file_count > 0:
                raise HTTPException(
                    status_code=400, 
                    detail=f"无法删除，该清单项关联了 {file_count} 个文件"
                )
            
            # 删除记录
            cursor.execute("DELETE FROM project_archive_checklist WHERE id = %s", (item_id,))
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "删除档案清单项成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除档案清单项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除档案清单项失败: {str(e)}")

# 档案文件管理API
@router.get("/files")
async def get_archive_files(
    project_code: Optional[str] = Query(None, description="项目编号"),
    main_process: Optional[str] = Query(None, description="主流程"),
    is_confirmed: Optional[int] = Query(None, description="确认状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_current_user)
):
    """获取档案文件列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if project_code:
                where_conditions.append("project_code = %s")
                params.append(project_code)
            
            if main_process:
                where_conditions.append("main_process = %s")
                params.append(main_process)
            
            if is_confirmed is not None:
                where_conditions.append("is_confirmed = %s")
                params.append(is_confirmed)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM project_archive_files {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * size
            data_sql = f"""
                SELECT id, project_code, file_name, file_path, file_size, file_type,
                       main_process, sub_process, output_name, classification_confidence,
                       is_confirmed, upload_time, uploaded_by, confirmed_by, confirmed_time
                FROM project_archive_files 
                {where_clause}
                ORDER BY upload_time DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(data_sql, params + [size, offset])
            files = cursor.fetchall()
            
            # 格式化时间
            for file in files:
                if file['upload_time']:
                    file['upload_time'] = file['upload_time'].strftime('%Y-%m-%d %H:%M:%S')
                if file['confirmed_time']:
                    file['confirmed_time'] = file['confirmed_time'].strftime('%Y-%m-%d %H:%M:%S')
        
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "获取档案文件成功",
            "data": {
                "items": files,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        }
        
    except Exception as e:
        logger.error(f"获取档案文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取档案文件失败: {str(e)}")

# 分类任务管理API
@router.get("/tasks")
async def get_classification_tasks(
    project_code: Optional[str] = Query(None, description="项目编号"),
    status: Optional[str] = Query(None, description="任务状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user = Depends(get_current_user)
):
    """获取分类任务列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if project_code:
                where_conditions.append("project_code = %s")
                params.append(project_code)
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM file_classification_tasks {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * size
            data_sql = f"""
                SELECT id, task_id, project_code, total_files, processed_files, status,
                       error_message, created_by, created_at, completed_at
                FROM file_classification_tasks 
                {where_clause}
                ORDER BY created_at DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(data_sql, params + [size, offset])
            tasks = cursor.fetchall()
            
            # 格式化时间
            for task in tasks:
                if task['created_at']:
                    task['created_at'] = task['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                if task['completed_at']:
                    task['completed_at'] = task['completed_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "获取分类任务成功",
            "data": {
                "items": tasks,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        }
        
    except Exception as e:
        logger.error(f"获取分类任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类任务失败: {str(e)}")

@router.delete("/tasks/{task_id}")
async def delete_classification_task(
    task_id: str,
    current_user = Depends(get_current_user)
):
    """删除分类任务"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查任务是否存在
            cursor.execute("SELECT task_id FROM file_classification_tasks WHERE task_id = %s", (task_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="分类任务不存在")
            
            # 删除任务
            cursor.execute("DELETE FROM file_classification_tasks WHERE task_id = %s", (task_id,))
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "删除分类任务成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除分类任务失败: {str(e)}")

@router.delete("/tasks/completed")
async def clear_completed_tasks(
    current_user = Depends(get_current_user)
):
    """清理已完成的分类任务"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 删除已完成的任务
            cursor.execute("DELETE FROM file_classification_tasks WHERE status = 'completed'")
            deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": f"清理完成，删除了 {deleted_count} 个已完成任务"
        }
        
    except Exception as e:
        logger.error(f"清理已完成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理已完成任务失败: {str(e)}")
