import{J as a}from"./index-76121fa4.js";function o(e){return a({url:"/team/members",method:"get",params:{entity:e,action:"getMembers"}})}function m(e){return a({url:"/team/members",method:"get",params:{action:"getUsers",search:e}})}function n(e,r){return a({url:"/team/members",method:"post",data:{entity:e,action:"updateMembers",operations:r}})}function u(e,r){return console.log(`调用updateUser API: userData=${JSON.stringify(e)}, updatedBy=${r}`),a({url:"/team/members",method:"post",data:{action:"updateUser",userData:e,updatedBy:r}}).then(t=>(console.log("updateUser API响应:",t),t)).catch(t=>{throw console.error("updateUser API错误:",t),t})}function c(e,r){return console.log(`调用deleteUser API: userId=${e}, adminUserId=${r}`),a({url:"/team/members",method:"post",data:{action:"deleteUser",userId:e,adminUserId:r}}).then(t=>(console.log("deleteUser API响应:",t),t)).catch(t=>{throw console.error("deleteUser API错误:",t),t})}export{o as a,u as b,c as d,m as g,n as u};
