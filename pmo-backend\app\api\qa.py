#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理API - 第五个功能模块：缺陷和测试用例管理
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import date
from app.models.user import User
from app.models.bug import Bug
from app.models.testcase import TestCase, TestTask
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/qa", tags=["测试管理"])

# 请求模型
class BugCreateRequest(BaseModel):
    product: int
    title: str
    severity: int = 3
    pri: int = 3
    type: str = "codeerror"
    os: str = ""
    browser: str = ""
    hardware: str = ""
    found: str = ""
    steps: str = ""
    assignedTo: str = ""
    keywords: str = ""
    module: int = 0
    story: int = 0
    task: int = 0

class BugUpdateRequest(BaseModel):
    title: Optional[str] = None
    severity: Optional[int] = None
    pri: Optional[int] = None
    type: Optional[str] = None
    steps: Optional[str] = None
    status: Optional[str] = None
    assignedTo: Optional[str] = None
    keywords: Optional[str] = None

class BugAssignRequest(BaseModel):
    assignedTo: str

class BugResolveRequest(BaseModel):
    resolution: str = ""
    resolvedBuild: str = ""

class CaseStepRequest(BaseModel):
    desc: str
    expect: str

class CaseCreateRequest(BaseModel):
    product: int
    title: str
    precondition: str = ""
    pri: int = 3
    type: str = "feature"
    stage: str = ""
    method: str = "manual"
    keywords: str = ""
    module: int = 0
    story: int = 0
    steps: List[CaseStepRequest] = []
    order: int = 0

class CaseUpdateRequest(BaseModel):
    title: Optional[str] = None
    precondition: Optional[str] = None
    pri: Optional[int] = None
    type: Optional[str] = None
    stage: Optional[str] = None
    method: Optional[str] = None
    status: Optional[str] = None
    keywords: Optional[str] = None
    steps: Optional[List[CaseStepRequest]] = None
    order: Optional[int] = None

class TestTaskCreateRequest(BaseModel):
    name: str
    product: int
    build: str = ""
    owner: str = ""
    pri: int = 3
    begin: Optional[date] = None
    end: Optional[date] = None
    desc: str = ""

# 权限检查
def check_qa_permission(current_user: User = Depends(get_current_user)):
    """检查测试权限"""
    if current_user.role not in ["admin", "qd", "tester"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员、测试经理或测试员权限"
        )
    return current_user

# 缺陷管理API
@router.get("/bugs")
async def get_bugs(
    page: int = 1,
    page_size: int = 20,
    product_id: int = 0,
    status: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取缺陷列表"""
    try:
        result = Bug.get_all_bugs(
            page=page, page_size=page_size, 
            product_id=product_id, status=status, keyword=keyword
        )
        return success_response(result, "获取缺陷列表成功")
    except Exception as e:
        return error_response(f"获取缺陷列表失败: {str(e)}")

@router.get("/bugs/{bug_id}")
async def get_bug(bug_id: int, current_user: User = Depends(get_current_user)):
    """获取缺陷详情"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        return success_response(bug.to_dict(), "获取缺陷详情成功")
    except Exception as e:
        return error_response(f"获取缺陷详情失败: {str(e)}")

@router.post("/bugs")
async def create_bug(
    request: BugCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建缺陷"""
    try:
        bug = Bug()
        bug.product = request.product
        bug.title = request.title
        bug.severity = request.severity
        bug.pri = request.pri
        bug.type = request.type
        bug.os = request.os
        bug.browser = request.browser
        bug.hardware = request.hardware
        bug.found = request.found
        bug.steps = request.steps
        bug.assignedTo = request.assignedTo
        bug.keywords = request.keywords
        bug.module = request.module
        bug.story = request.story
        bug.task = request.task
        bug.openedBy = current_user.account
        bug.status = "active"
        
        if bug.create():
            return success_response(bug.to_dict(), "创建缺陷成功")
        else:
            return error_response("创建缺陷失败")
            
    except Exception as e:
        return error_response(f"创建缺陷失败: {str(e)}")

@router.put("/bugs/{bug_id}")
async def update_bug(
    bug_id: int,
    request: BugUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新缺陷信息"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        # 更新缺陷信息
        if request.title is not None:
            bug.title = request.title
        if request.severity is not None:
            bug.severity = request.severity
        if request.pri is not None:
            bug.pri = request.pri
        if request.type is not None:
            bug.type = request.type
        if request.steps is not None:
            bug.steps = request.steps
        if request.status is not None:
            bug.status = request.status
        if request.assignedTo is not None:
            bug.assignedTo = request.assignedTo
        if request.keywords is not None:
            bug.keywords = request.keywords
        
        bug.lastEditedBy = current_user.account
        
        if bug.update():
            return success_response(bug.to_dict(), "更新缺陷信息成功")
        else:
            return error_response("更新缺陷信息失败")
            
    except Exception as e:
        return error_response(f"更新缺陷信息失败: {str(e)}")

@router.delete("/bugs/{bug_id}")
async def delete_bug(
    bug_id: int,
    current_user: User = Depends(check_qa_permission)
):
    """删除缺陷"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        if bug.delete():
            return success_response(None, "删除缺陷成功")
        else:
            return error_response("删除缺陷失败")
            
    except Exception as e:
        return error_response(f"删除缺陷失败: {str(e)}")

@router.post("/bugs/{bug_id}/assign")
async def assign_bug(
    bug_id: int,
    request: BugAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """指派缺陷"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        # 检查被指派用户是否存在
        assignee = User.get_by_account(request.assignedTo)
        if not assignee:
            return error_response("被指派用户不存在", status_code=404)
        
        if bug.assign(request.assignedTo, current_user.account):
            return success_response(bug.to_dict(), "指派缺陷成功")
        else:
            return error_response("指派缺陷失败")
            
    except Exception as e:
        return error_response(f"指派缺陷失败: {str(e)}")

@router.post("/bugs/{bug_id}/resolve")
async def resolve_bug(
    bug_id: int,
    request: BugResolveRequest,
    current_user: User = Depends(get_current_user)
):
    """解决缺陷"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        if bug.resolve(current_user.account, request.resolution, request.resolvedBuild):
            return success_response(bug.to_dict(), "解决缺陷成功")
        else:
            return error_response("解决缺陷失败")
            
    except Exception as e:
        return error_response(f"解决缺陷失败: {str(e)}")

@router.post("/bugs/{bug_id}/close")
async def close_bug(
    bug_id: int,
    current_user: User = Depends(get_current_user)
):
    """关闭缺陷"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        if bug.close(current_user.account):
            return success_response(bug.to_dict(), "关闭缺陷成功")
        else:
            return error_response("关闭缺陷失败")
            
    except Exception as e:
        return error_response(f"关闭缺陷失败: {str(e)}")

@router.post("/bugs/{bug_id}/activate")
async def activate_bug(
    bug_id: int,
    current_user: User = Depends(get_current_user)
):
    """激活缺陷"""
    try:
        bug = Bug.get_by_id(bug_id)
        if not bug:
            return error_response("缺陷不存在", status_code=404)
        
        if bug.activate(current_user.account):
            return success_response(bug.to_dict(), "激活缺陷成功")
        else:
            return error_response("激活缺陷失败")
            
    except Exception as e:
        return error_response(f"激活缺陷失败: {str(e)}")

# 测试用例管理API
@router.get("/cases")
async def get_cases(
    page: int = 1,
    page_size: int = 20,
    product_id: int = 0,
    status: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取测试用例列表"""
    try:
        result = TestCase.get_all_cases(
            page=page, page_size=page_size, 
            product_id=product_id, status=status, keyword=keyword
        )
        return success_response(result, "获取测试用例列表成功")
    except Exception as e:
        return error_response(f"获取测试用例列表失败: {str(e)}")

@router.get("/cases/{case_id}")
async def get_case(case_id: int, current_user: User = Depends(get_current_user)):
    """获取测试用例详情"""
    try:
        case = TestCase.get_by_id(case_id)
        if not case:
            return error_response("测试用例不存在", status_code=404)
        
        return success_response(case.to_dict(include_steps=True), "获取测试用例详情成功")
    except Exception as e:
        return error_response(f"获取测试用例详情失败: {str(e)}")

@router.post("/cases")
async def create_case(
    request: CaseCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建测试用例"""
    try:
        case = TestCase()
        case.product = request.product
        case.title = request.title
        case.precondition = request.precondition
        case.pri = request.pri
        case.type = request.type
        case.stage = request.stage
        case.method = request.method
        case.keywords = request.keywords
        case.module = request.module
        case.story = request.story
        case.order = request.order
        case.openedBy = current_user.account
        case.status = "normal"
        
        # 转换步骤格式
        steps = [{"desc": step.desc, "expect": step.expect} for step in request.steps]
        
        if case.create(steps=steps):
            return success_response(case.to_dict(include_steps=True), "创建测试用例成功")
        else:
            return error_response("创建测试用例失败")
            
    except Exception as e:
        return error_response(f"创建测试用例失败: {str(e)}")

@router.put("/cases/{case_id}")
async def update_case(
    case_id: int,
    request: CaseUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新测试用例信息"""
    try:
        case = TestCase.get_by_id(case_id)
        if not case:
            return error_response("测试用例不存在", status_code=404)
        
        # 更新测试用例信息
        if request.title is not None:
            case.title = request.title
        if request.precondition is not None:
            case.precondition = request.precondition
        if request.pri is not None:
            case.pri = request.pri
        if request.type is not None:
            case.type = request.type
        if request.stage is not None:
            case.stage = request.stage
        if request.method is not None:
            case.method = request.method
        if request.status is not None:
            case.status = request.status
        if request.keywords is not None:
            case.keywords = request.keywords
        if request.order is not None:
            case.order = request.order
        
        case.lastEditedBy = current_user.account
        
        # 转换步骤格式
        steps = None
        if request.steps is not None:
            steps = [{"desc": step.desc, "expect": step.expect} for step in request.steps]
        
        if case.update(steps=steps):
            return success_response(case.to_dict(include_steps=True), "更新测试用例信息成功")
        else:
            return error_response("更新测试用例信息失败")
            
    except Exception as e:
        return error_response(f"更新测试用例信息失败: {str(e)}")

@router.delete("/cases/{case_id}")
async def delete_case(
    case_id: int,
    current_user: User = Depends(check_qa_permission)
):
    """删除测试用例"""
    try:
        case = TestCase.get_by_id(case_id)
        if not case:
            return error_response("测试用例不存在", status_code=404)
        
        if case.delete():
            return success_response(None, "删除测试用例成功")
        else:
            return error_response("删除测试用例失败")
            
    except Exception as e:
        return error_response(f"删除测试用例失败: {str(e)}")

@router.post("/cases/{case_id}/review")
async def review_case(
    case_id: int,
    current_user: User = Depends(check_qa_permission)
):
    """评审测试用例"""
    try:
        case = TestCase.get_by_id(case_id)
        if not case:
            return error_response("测试用例不存在", status_code=404)
        
        if case.review(current_user.account):
            return success_response(case.to_dict(), "评审测试用例成功")
        else:
            return error_response("评审测试用例失败")
            
    except Exception as e:
        return error_response(f"评审测试用例失败: {str(e)}")

# 测试任务管理API
@router.get("/testtasks")
async def get_test_tasks(
    page: int = 1,
    page_size: int = 20,
    product_id: int = 0,
    status: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取测试任务列表"""
    try:
        result = TestTask.get_all_tasks(
            page=page, page_size=page_size, 
            product_id=product_id, status=status
        )
        return success_response(result, "获取测试任务列表成功")
    except Exception as e:
        return error_response(f"获取测试任务列表失败: {str(e)}")

@router.get("/testtasks/{task_id}")
async def get_test_task(task_id: int, current_user: User = Depends(get_current_user)):
    """获取测试任务详情"""
    try:
        task = TestTask.get_by_id(task_id)
        if not task:
            return error_response("测试任务不存在", status_code=404)
        
        return success_response(task.to_dict(), "获取测试任务详情成功")
    except Exception as e:
        return error_response(f"获取测试任务详情失败: {str(e)}")
