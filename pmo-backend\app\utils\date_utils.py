from datetime import datetime, timedelta
import time

def get_current_timestamp() -> int:
    """获取当前时间戳（秒）"""
    return int(time.time())

def get_current_datetime() -> datetime:
    """获取当前日期时间"""
    return datetime.now()

def format_datetime(dt: datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    return dt.strftime(fmt)

def parse_datetime(dt_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """解析日期时间字符串"""
    return datetime.strptime(dt_str, fmt)

def get_date_range(days: int, end_date: datetime = None) -> tuple:
    """
    获取日期范围
    
    Args:
        days: 天数
        end_date: 结束日期，默认为当前日期
        
    Returns:
        (开始日期, 结束日期)
    """
    if end_date is None:
        end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    return start_date, end_date

def get_week_start_end(dt: datetime = None) -> tuple:
    """
    获取指定日期所在周的开始和结束日期
    
    Args:
        dt: 日期，默认为当前日期
        
    Returns:
        (周开始日期, 周结束日期)
    """
    if dt is None:
        dt = datetime.now()
    start = dt - timedelta(days=dt.weekday())
    end = start + timedelta(days=6)
    return start.replace(hour=0, minute=0, second=0, microsecond=0), end.replace(hour=23, minute=59, second=59, microsecond=999999)

def get_month_start_end(dt: datetime = None) -> tuple:
    """
    获取指定日期所在月的开始和结束日期
    
    Args:
        dt: 日期，默认为当前日期
        
    Returns:
        (月开始日期, 月结束日期)
    """
    if dt is None:
        dt = datetime.now()
    start = dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # 获取下个月的第一天，然后减去1秒
    if dt.month == 12:
        end = datetime(dt.year + 1, 1, 1, 23, 59, 59, 999999) - timedelta(days=1)
    else:
        end = datetime(dt.year, dt.month + 1, 1, 23, 59, 59, 999999) - timedelta(days=1)
    
    return start, end 