"""
权限管理模块
完全参考Python桌面版本的权限管理逻辑
"""
from typing import List, Dict, Any, Optional
from app.core.logger import get_logger

logger = get_logger(__name__)

class PermissionManager:
    """权限管理器"""
    
    # 用户角色定义（完全参考Python版本）
    ROLES = {
        "SUPER_ADMIN": 1,      # 超级管理员 - 1类人员
        "DEPARTMENT_ADMIN": 2,  # 部门管理员 - 2类人员  
        "PROJECT_ADMIN": 3,     # 项目管理员 - 3类人员
        "NORMAL_USER": 4,       # 普通用户
        "TEMP_USER": 5         # 临时用户
    }
    
    @staticmethod
    def has_project_access(user_data: Dict[str, Any], entity: str) -> bool:
        """
        检查用户是否有权限访问项目
        完全参考Python版本的实现
        """
        if not user_data or not entity:
            return False
            
        # 超级管理员可以访问所有项目
        if user_data.get('role') == PermissionManager.ROLES["SUPER_ADMIN"]:
            return True
            
        # 检查用户的条线权限
        user_service_lines = user_data.get('service_lines', [])
        return entity in user_service_lines
    
    @staticmethod
    def can_edit_project_field(user_data: Dict[str, Any], entity: str, field_name: str) -> bool:
        """
        检查用户是否能编辑项目的特定字段
        完全参考Python版本的实现
        """
        if not user_data:
            return False
            
        user_role = user_data.get('role')
        
        # 超级管理员可以编辑所有字段
        if user_role == PermissionManager.ROLES["SUPER_ADMIN"]:
            return True
            
        # 临时用户没有任何编辑权限
        if user_role == PermissionManager.ROLES["TEMP_USER"]:
            return False
            
        # 检查用户是否有访问该条线的权限
        has_access = PermissionManager.has_project_access(user_data, entity)
        if not has_access:
            return False
            
        # 对于部门管理员、项目管理员和普通用户的受限字段（黑名单模式）
        if user_role in [PermissionManager.ROLES["DEPARTMENT_ADMIN"], 
                        PermissionManager.ROLES["PROJECT_ADMIN"],
                        PermissionManager.ROLES["NORMAL_USER"]]:
            restricted_fields = [
                'project_code',             # 项目编码
                'annual_investment_plan',   # 年度投资(万)
                'project_planned_total_investment',  # 总投资(万)
                'budget',                   # 预算(万)
                'business_research_time',   # 调研时间
                'solution_time',            # 方案时间
                'project_establishment_time', # 立项时间
                'project_procurement_time',  # 采购时间
                'project_implementation_time', # 实施时间
                'project_acceptance_time'   # 验收时间
            ]
            return field_name not in restricted_fields
        
        return False  # 其他情况默认不允许编辑
    
    @staticmethod
    def filter_projects_by_permission(projects: List[Dict[str, Any]], user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        根据用户权限过滤项目列表
        完全参考Python版本的实现
        """
        if not user_data:
            return []
        
        user_role = user_data.get('role')
        
        # 超级管理员可以看到所有项目
        if user_role == PermissionManager.ROLES["SUPER_ADMIN"]:
            logger.info(f"超级管理员用户，返回所有 {len(projects)} 个项目")
            return projects

        # 部门管理员可以看到所有项目（但编辑权限受限）
        if user_role == PermissionManager.ROLES["DEPARTMENT_ADMIN"]:
            logger.info(f"部门管理员用户，返回所有 {len(projects)} 个项目")
            return projects
        
        # 获取用户的条线权限
        user_service_lines = user_data.get('service_lines', [])

        # 添加调试日志
        logger.info(f"权限过滤 - 用户数据: {user_data}")
        logger.info(f"权限过滤 - 用户条线权限: {user_service_lines}")

        if not user_service_lines:
            logger.info("用户没有任何条线权限，返回空列表")
            return []
        
        # 根据条线权限过滤项目
        filtered_projects = []
        for project in projects:
            entity = project.get('investment_entity', '')
            if entity in user_service_lines:
                filtered_projects.append(project)
        
        logger.info(f"用户条线权限: {user_service_lines}, 过滤后项目数量: {len(filtered_projects)}/{len(projects)}")
        return filtered_projects
    
    @staticmethod
    def get_user_service_lines(user_data: Dict[str, Any]) -> List[str]:
        """
        获取用户的服务条线列表
        完全参考Python版本的实现
        """
        if not user_data:
            return []
        
        # 从用户数据中直接获取条线信息
        service_lines = user_data.get('service_lines', [])
        
        # 如果没有条线信息，尝试从其他字段获取
        if not service_lines:
            # 可以从部门信息推断条线
            department = user_data.get('department_name', '')
            if department:
                # 这里可以根据部门映射到条线
                # 暂时返回空列表，后续可以扩展
                pass
        
        return service_lines
    
    @staticmethod
    def can_add_project(user_data: Dict[str, Any]) -> bool:
        """检查用户是否可以新增项目"""
        if not user_data:
            return False
        
        user_role = user_data.get('role')
        return user_role in [
            PermissionManager.ROLES["SUPER_ADMIN"],
            PermissionManager.ROLES["DEPARTMENT_ADMIN"],
            PermissionManager.ROLES["PROJECT_ADMIN"]
        ]
    
    @staticmethod
    def can_delete_project(user_data: Dict[str, Any]) -> bool:
        """检查用户是否可以删除项目"""
        if not user_data:
            return False
        
        user_role = user_data.get('role')
        return user_role in [
            PermissionManager.ROLES["SUPER_ADMIN"],
            PermissionManager.ROLES["DEPARTMENT_ADMIN"]
        ]
    
    @staticmethod
    def can_batch_operation(user_data: Dict[str, Any]) -> bool:
        """检查用户是否可以批量操作"""
        if not user_data:
            return False
        
        user_role = user_data.get('role')
        return user_role in [
            PermissionManager.ROLES["SUPER_ADMIN"],
            PermissionManager.ROLES["DEPARTMENT_ADMIN"],
            PermissionManager.ROLES["PROJECT_ADMIN"]
        ]
    
    @staticmethod
    def can_export_data(user_data: Dict[str, Any]) -> bool:
        """检查用户是否可以导出数据"""
        if not user_data:
            return False
        
        user_role = user_data.get('role')
        # 所有角色都可以导出数据，除了临时用户
        return user_role != PermissionManager.ROLES["TEMP_USER"]
