from fastapi import APIRouter, Query, Depends, HTTPException, WebSocket, WebSocketDisconnect
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import pymysql
from app.core.database import get_db_connection
from app.core.security import get_current_user
from app.core.websocket_manager import planning_ws_manager
import json
import uuid
import asyncio
from datetime import datetime

router = APIRouter()

# 辅助函数
async def update_category_stats():
    """更新分类统计数据并通知WebSocket客户端"""
    try:
        # 这里可以实现统计数据的计算和通知逻辑
        # 暂时留空，后续可以扩展
        pass
    except Exception as e:
        print(f"更新统计数据失败: {e}")

class ProjectPlanModel(BaseModel):
    project_id: Optional[str] = None
    category_level1: str
    category_level2: str
    project_name: str
    summary: Optional[str] = None
    milestone_plan: Optional[str] = None
    priority: Optional[str] = None
    budget_2026: Optional[float] = None
    budget_2027: Optional[float] = None
    budget_2028: Optional[float] = None
    budget_2029: Optional[float] = None
    budget_2030: Optional[float] = None
    total_budget: Optional[float] = None

class CategoryStructureResponse(BaseModel):
    structure: Dict[str, Dict[str, List[Dict]]]
    stats: Dict[str, Dict[str, Any]]

class CategoryInfo(BaseModel):
    name: str
    count: int
    subcategories: List[Dict[str, Any]]

class PlanningOverviewResponse(BaseModel):
    categories: List[CategoryInfo]
    total_projects: int
    total_budget: float
    priority_distribution: Dict[str, int]

@router.get("/planning/overview")
async def get_planning_overview(current_user: dict = Depends(get_current_user)):
    """获取规划概览信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取分类统计
            cursor.execute("""
                SELECT category_level1, category_level2, COUNT(*) as count,
                       SUM(CASE WHEN total_budget = '' OR total_budget IS NULL THEN 0 ELSE CAST(total_budget AS DECIMAL(10,2)) END) as total_budget
                FROM project_plan
                GROUP BY category_level1, category_level2
                ORDER BY category_level1, category_level2
            """)
            category_stats = cursor.fetchall()

            # 获取优先级分布
            cursor.execute("""
                SELECT priority, COUNT(*) as count
                FROM project_plan
                GROUP BY priority
            """)
            priority_stats = cursor.fetchall()

            # 获取总统计
            cursor.execute("""
                SELECT COUNT(*) as total_projects,
                       SUM(CASE WHEN total_budget = '' OR total_budget IS NULL THEN 0 ELSE CAST(total_budget AS DECIMAL(10,2)) END) as total_budget
                FROM project_plan
            """)
            total_stats = cursor.fetchone()

        # 构建分类信息
        categories_dict = {}
        for stat in category_stats:
            level1 = stat['category_level1']
            level2 = stat['category_level2']

            if level1 not in categories_dict:
                categories_dict[level1] = {
                    'name': level1,
                    'count': 0,
                    'subcategories': []
                }

            categories_dict[level1]['count'] += stat['count']
            categories_dict[level1]['subcategories'].append({
                'name': level2,
                'count': stat['count'],
                'budget': float(stat['total_budget'] or 0)
            })

        categories = list(categories_dict.values())

        # 构建优先级分布
        priority_distribution = {}
        for stat in priority_stats:
            priority_distribution[stat['priority'] or '未设置'] = stat['count']

        conn.close()

        return {
            "code": 200,
            "message": "获取概览数据成功",
            "data": {
                "categories": categories,
                "total_projects": total_stats['total_projects'],
                "total_budget": float(total_stats['total_budget'] or 0),
                "priority_distribution": priority_distribution
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览数据失败: {str(e)}")

@router.get("/planning/categories")
async def get_planning_categories(current_user: dict = Depends(get_current_user)):
    """获取规划分类结构和统计信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取所有项目数据
            cursor.execute("""
                SELECT project_id, category_level1, category_level2, project_name,
                       summary, milestone_plan, priority, budget_2026, budget_2027,
                       budget_2028, budget_2029, budget_2030, total_budget
                FROM project_plan
                ORDER BY category_level1, category_level2, project_id
            """)
            projects = cursor.fetchall()

        # 构建分类结构和统计信息
        structure = {}
        stats = {}

        for project in projects:
            level1 = project['category_level1']
            level2 = project['category_level2']

            # 初始化结构
            if level1 not in structure:
                structure[level1] = {}
                stats[level1] = {'total': 0, 'priorities': {}, 'budget': 0}

            if level2 not in structure[level1]:
                structure[level1][level2] = []
                stats[level1][level2] = {'total': 0, 'priorities': {}, 'budget': 0}

            # 添加项目到结构中
            project_dict = dict(project)
            structure[level1][level2].append(project_dict)

            # 更新统计信息
            stats[level1]['total'] += 1
            stats[level1][level2]['total'] += 1

            # 处理预算字段（字符串转数字）
            budget_str = project['total_budget'] or '0'
            try:
                budget = float(budget_str) if budget_str and budget_str != '' else 0
            except:
                budget = 0
            stats[level1]['budget'] += budget
            stats[level1][level2]['budget'] += budget

            priority = project['priority'] or '未设置'
            stats[level1]['priorities'][priority] = stats[level1]['priorities'].get(priority, 0) + 1
            stats[level1][level2]['priorities'][priority] = stats[level1][level2]['priorities'].get(priority, 0) + 1

        conn.close()

        return {
            "code": 200,
            "message": "获取分类数据成功",
            "data": {
                "structure": structure,
                "stats": stats
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类数据失败: {str(e)}")

@router.get("/planning/projects")
async def get_planning_projects(
    category_level1: Optional[str] = Query(None, alias="category"),
    category_level2: Optional[str] = Query(None, alias="subcategory"),
    priority: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=10000),  # 增加最大限制以支持获取所有项目
    current_user: dict = Depends(get_current_user)
):
    """获取规划项目列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if category_level1:
                where_conditions.append("category_level1 = %s")
                params.append(category_level1)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            if priority:
                where_conditions.append("priority = %s")
                params.append(priority)

            if search:
                where_conditions.append("(project_name LIKE %s OR summary LIKE %s)")
                params.extend([f"%{search}%", f"%{search}%"])

            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM project_plan {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            offset = (page - 1) * page_size
            data_sql = f"""
                SELECT project_id, category_level1, category_level2, project_name,
                       summary, milestone_plan, priority, budget_2026, budget_2027,
                       budget_2028, budget_2029, budget_2030, total_budget
                FROM project_plan {where_clause}
                ORDER BY project_id
                LIMIT %s OFFSET %s
            """
            cursor.execute(data_sql, params + [page_size, offset])
            projects = cursor.fetchall()

            # 处理项目数据
            processed_projects = []
            for project in projects:
                project_dict = dict(project)
                # 添加默认字段
                project_dict['tags'] = []
                project_dict['status'] = 'active'
                project_dict['display_order'] = 0
                project_dict['color_code'] = None
                project_dict['responsible_person'] = None
                project_dict['created_at'] = None
                project_dict['updated_at'] = None
                processed_projects.append(project_dict)

        conn.close()

        return {
            "code": 200,
            "message": "获取项目列表成功",
            "data": {
                "projects": processed_projects,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.post("/planning/projects")
async def create_planning_project(
    project: ProjectPlanModel,
    current_user: dict = Depends(get_current_user)
):
    """创建规划项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 生成项目ID
            if not project.project_id:
                # 根据分类生成ID前缀
                prefix_map = {
                    'AI²层': 'AI',
                    '9大智慧管理体系': 'M',
                    '3大AI技术底座': 'T',
                    'N个业务场景': 'N'
                }
                prefix = prefix_map.get(project.category_level1, 'P')

                # 获取该前缀下的最大序号
                cursor.execute(
                    "SELECT project_id FROM project_plan WHERE project_id LIKE %s ORDER BY project_id DESC LIMIT 1",
                    (f"{prefix}-%")
                )
                last_project = cursor.fetchone()

                if last_project:
                    try:
                        last_num = int(last_project['project_id'].split('-')[1])
                        new_num = last_num + 1
                    except:
                        new_num = 1
                else:
                    new_num = 1

                project.project_id = f"{prefix}-{new_num}"

            # 计算总预算
            if not project.total_budget:
                budget_fields = [project.budget_2026, project.budget_2027, project.budget_2028,
                               project.budget_2029, project.budget_2030]
                project.total_budget = sum(b for b in budget_fields if b is not None)

            # 插入数据
            sql = """
                INSERT INTO project_plan (
                    project_id, category_level1, category_level2, project_name,
                    summary, milestone_plan, priority, budget_2026, budget_2027,
                    budget_2028, budget_2029, budget_2030, total_budget
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            cursor.execute(sql, (
                project.project_id, project.category_level1, project.category_level2,
                project.project_name, project.summary, project.milestone_plan,
                project.priority, project.budget_2026, project.budget_2027,
                project.budget_2028, project.budget_2029, project.budget_2030,
                project.total_budget
            ))
            conn.commit()

            # 获取创建的项目数据
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM project_plan WHERE project_id = %s
                """, (project.project_id,))
                created_project = cursor.fetchone()

                # 通过WebSocket通知项目创建
                if created_project:
                    project_dict = dict(created_project)

                    # 异步通知
                    asyncio.create_task(planning_ws_manager.notify_project_created(project_dict))

                    # 更新统计数据
                    asyncio.create_task(update_category_stats())

        conn.close()

        return {
            "code": 200,
            "message": "项目创建成功",
            "data": {
                "project_id": project.project_id
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建项目失败: {str(e)}")

@router.put("/planning/projects/{project_id}")
async def update_planning_project(
    project_id: str,
    project: ProjectPlanModel,
    current_user: dict = Depends(get_current_user)
):
    """更新规划项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目是否存在
            cursor.execute("SELECT project_id FROM project_plan WHERE project_id = %s", (project_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="项目不存在")

            # 计算总预算
            if not project.total_budget:
                budget_fields = [project.budget_2026, project.budget_2027, project.budget_2028,
                               project.budget_2029, project.budget_2030]
                project.total_budget = sum(b for b in budget_fields if b is not None)

            # 更新数据
            sql = """
                UPDATE project_plan SET
                    category_level1 = %s, category_level2 = %s, project_name = %s,
                    summary = %s, milestone_plan = %s, priority = %s,
                    budget_2026 = %s, budget_2027 = %s, budget_2028 = %s,
                    budget_2029 = %s, budget_2030 = %s, total_budget = %s
                WHERE project_id = %s
            """
            cursor.execute(sql, (
                project.category_level1, project.category_level2, project.project_name,
                project.summary, project.milestone_plan, project.priority,
                project.budget_2026, project.budget_2027, project.budget_2028,
                project.budget_2029, project.budget_2030, project.total_budget,
                project_id
            ))
            conn.commit()

        conn.close()

        return {
            "code": 200,
            "message": "项目更新成功",
            "data": {}
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")

@router.delete("/planning/projects/{project_id}")
async def delete_planning_project(
    project_id: str,
    current_user: dict = Depends(get_current_user)
):
    """删除规划项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目是否存在
            cursor.execute("SELECT project_id FROM project_plan WHERE project_id = %s", (project_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="项目不存在")
            
            # 删除项目
            cursor.execute("DELETE FROM project_plan WHERE project_id = %s", (project_id,))
            conn.commit()
        
        conn.close()

        return {
            "code": 200,
            "message": "项目删除成功",
            "data": {}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")

@router.get("/planning/projects/{project_id}")
async def get_planning_project_detail(
    project_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取项目详情"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT project_id, category_level1, category_level2, project_name,
                       summary, milestone_plan, priority, budget_2026, budget_2027,
                       budget_2028, budget_2029, budget_2030, total_budget,
                       created_at, updated_at
                FROM project_plan
                WHERE project_id = %s
            """, (project_id,))
            project = cursor.fetchone()

        conn.close()

        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        # 处理项目数据
        project_dict = dict(project)

        return {
            "code": 200,
            "message": "获取项目详情成功",
            "data": project_dict
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.get("/planning/subcategories/{category_level1}")
async def get_planning_subcategories(
    category_level1: str,
    current_user: dict = Depends(get_current_user)
):
    """获取指定一级分类下的所有二级分类"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT category_level2,
                       COUNT(*) as project_count,
                       SUM(COALESCE(total_budget, 0)) as total_budget
                FROM project_plan
                WHERE category_level1 = %s AND status = 'active'
                GROUP BY category_level2
                ORDER BY category_level2
            """, (category_level1,))
            subcategories = cursor.fetchall()

        conn.close()

        return {
            "code": 200,
            "message": "获取二级分类成功",
            "data": {
                "category": category_level1,
                "subcategories": subcategories
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取二级分类失败: {str(e)}")

@router.get("/planning/statistics")
async def get_planning_statistics(
    current_user: dict = Depends(get_current_user)
):
    """获取规划统计信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取按优先级分组的统计
            cursor.execute("""
                SELECT priority, COUNT(*) as count, SUM(COALESCE(total_budget, 0)) as budget
                FROM project_plan
                WHERE status = 'active'
                GROUP BY priority
            """)
            priority_stats = cursor.fetchall()

            # 获取按年份分组的预算统计
            cursor.execute("""
                SELECT
                    SUM(COALESCE(budget_2026, 0)) as budget_2026,
                    SUM(COALESCE(budget_2027, 0)) as budget_2027,
                    SUM(COALESCE(budget_2028, 0)) as budget_2028,
                    SUM(COALESCE(budget_2029, 0)) as budget_2029,
                    SUM(COALESCE(budget_2030, 0)) as budget_2030,
                    SUM(COALESCE(total_budget, 0)) as total_budget
                FROM project_plan
                WHERE status = 'active'
            """)
            budget_stats = cursor.fetchone()

        conn.close()

        # 处理优先级统计
        priority_data = {}
        for stat in priority_stats:
            priority = stat['priority'] or '未设置'
            priority_data[priority] = {
                'count': stat['count'],
                'budget': float(stat['budget'] or 0)
            }

        # 处理预算统计
        budget_data = {
            'by_year': {
                '2026': float(budget_stats['budget_2026'] or 0),
                '2027': float(budget_stats['budget_2027'] or 0),
                '2028': float(budget_stats['budget_2028'] or 0),
                '2029': float(budget_stats['budget_2029'] or 0),
                '2030': float(budget_stats['budget_2030'] or 0)
            },
            'total': float(budget_stats['total_budget'] or 0)
        }

        return {
            "code": 200,
            "message": "获取统计信息成功",
            "data": {
                'priority': priority_data,
                'budget': budget_data
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/planning/projects/batch")
async def batch_update_planning_projects(
    batch_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """批量更新项目"""
    try:
        action = batch_data.get('action')
        project_ids = batch_data.get('projectIds', [])
        data = batch_data.get('data', {})

        if not action or not project_ids:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        conn = get_db_connection()
        with conn.cursor() as cursor:
            if action == 'priority':
                # 批量设置优先级
                priority = data.get('priority')
                if not priority:
                    raise HTTPException(status_code=400, detail="缺少优先级参数")

                cursor.execute("""
                    UPDATE project_plan
                    SET priority = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE project_id IN ({})
                """.format(','.join(['%s'] * len(project_ids))),
                [priority] + project_ids)

            elif action == 'responsible':
                # 批量设置负责人
                responsible_person = data.get('responsible_person')
                if not responsible_person:
                    raise HTTPException(status_code=400, detail="缺少负责人参数")

                cursor.execute("""
                    UPDATE project_plan
                    SET responsible_person = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE project_id IN ({})
                """.format(','.join(['%s'] * len(project_ids))),
                [responsible_person] + project_ids)

            elif action == 'addTag':
                # 批量添加标签
                tags_to_add = data.get('tags', [])
                if not tags_to_add:
                    raise HTTPException(status_code=400, detail="缺少标签参数")

                # 获取现有标签并合并
                for project_id in project_ids:
                    cursor.execute("SELECT tags FROM project_plan WHERE project_id = %s", (project_id,))
                    result = cursor.fetchone()
                    if result:
                        existing_tags = []
                        if result['tags']:
                            try:
                                existing_tags = json.loads(result['tags'])
                            except:
                                existing_tags = []

                        # 合并标签（去重）
                        new_tags = list(set(existing_tags + tags_to_add))

                        cursor.execute("""
                            UPDATE project_plan
                            SET tags = %s, updated_at = CURRENT_TIMESTAMP
                            WHERE project_id = %s
                        """, (json.dumps(new_tags), project_id))

            elif action == 'removeTag':
                # 批量移除标签
                tags_to_remove = data.get('tags', [])
                if not tags_to_remove:
                    raise HTTPException(status_code=400, detail="缺少标签参数")

                # 从现有标签中移除指定标签
                for project_id in project_ids:
                    cursor.execute("SELECT tags FROM project_plan WHERE project_id = %s", (project_id,))
                    result = cursor.fetchone()
                    if result:
                        existing_tags = []
                        if result['tags']:
                            try:
                                existing_tags = json.loads(result['tags'])
                            except:
                                existing_tags = []

                        # 移除标签
                        new_tags = [tag for tag in existing_tags if tag not in tags_to_remove]

                        cursor.execute("""
                            UPDATE project_plan
                            SET tags = %s, updated_at = CURRENT_TIMESTAMP
                            WHERE project_id = %s
                        """, (json.dumps(new_tags), project_id))

            elif action == 'delete':
                # 批量删除
                cursor.execute("""
                    DELETE FROM project_plan
                    WHERE project_id IN ({})
                """.format(','.join(['%s'] * len(project_ids))), project_ids)

            else:
                raise HTTPException(status_code=400, detail="不支持的操作类型")

            conn.commit()

        conn.close()

        return {
            "code": 200,
            "message": f"批量操作成功，影响 {len(project_ids)} 个项目",
            "data": {}
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")

@router.get("/planning/projects/export")
async def export_planning_projects(
    category_level1: Optional[str] = None,
    category_level2: Optional[str] = None,
    priority: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """导出项目数据"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = ["status = 'active'"]
            params = []

            if category_level1:
                where_conditions.append("category_level1 = %s")
                params.append(category_level1)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            if priority:
                where_conditions.append("priority = %s")
                params.append(priority)

            where_clause = "WHERE " + " AND ".join(where_conditions)

            # 查询数据
            sql = f"""
                SELECT project_id, category_level1, category_level2, project_name,
                       summary, milestone_plan, priority, responsible_person,
                       budget_2026, budget_2027, budget_2028, budget_2029, budget_2030,
                       total_budget, tags, created_at, updated_at
                FROM project_plan {where_clause}
                ORDER BY category_level1, category_level2, project_id
            """
            cursor.execute(sql, params)
            projects = cursor.fetchall()

        conn.close()

        # 处理数据格式
        for project in projects:
            if project.get('tags'):
                try:
                    project['tags'] = json.loads(project['tags'])
                except:
                    project['tags'] = []
            else:
                project['tags'] = []

        return {
            "code": 200,
            "message": "导出数据成功",
            "data": {
                "projects": projects,
                "total": len(projects),
                "export_time": datetime.now().isoformat()
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出数据失败: {str(e)}")

@router.get("/planning/overview")
async def get_planning_overview(current_user: dict = Depends(get_current_user)):
    """获取项目规划概览信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取基本统计信息
            cursor.execute("""
                SELECT
                    COUNT(*) as total_projects,
                    SUM(IFNULL(total_budget, 0)) as total_budget,
                    COUNT(CASE WHEN priority = '紧急且重要' THEN 1 END) as urgent_important,
                    COUNT(CASE WHEN priority = '重要不紧急' THEN 1 END) as important_not_urgent,
                    COUNT(CASE WHEN priority = '紧急不重要' THEN 1 END) as urgent_not_important,
                    COUNT(CASE WHEN priority = '不紧急不重要' THEN 1 END) as not_urgent_not_important
                FROM project_plan
                WHERE status = 'active'
            """)
            overview = cursor.fetchone()

        conn.close()

        return {
            "code": 200,
            "message": "获取概览信息成功",
            "data": {
                "total_projects": overview['total_projects'] or 0,
                "total_budget": float(overview['total_budget'] or 0),
                "priority_distribution": {
                    "urgent_important": overview['urgent_important'] or 0,
                    "important_not_urgent": overview['important_not_urgent'] or 0,
                    "urgent_not_important": overview['urgent_not_important'] or 0,
                    "not_urgent_not_important": overview['not_urgent_not_important'] or 0
                }
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概览信息失败: {str(e)}")

@router.get("/planning/categories")
async def get_planning_categories(current_user: dict = Depends(get_current_user)):
    """获取项目规划分类结构和统计信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取分类结构和统计信息
            cursor.execute("""
                SELECT
                    category_level1,
                    category_level2,
                    COUNT(*) as project_count,
                    SUM(IFNULL(total_budget, 0)) as total_budget
                FROM project_plan
                WHERE status = 'active'
                GROUP BY category_level1, category_level2
                ORDER BY category_level1, category_level2
            """)
            categories = cursor.fetchall()

        conn.close()

        # 构建分类结构
        structure = {}
        stats = {}

        for category in categories:
            level1 = category['category_level1']
            level2 = category['category_level2']

            if level1 not in structure:
                structure[level1] = {}
                stats[level1] = {}

            structure[level1][level2] = True
            stats[level1][level2] = {
                'total': category['project_count'],
                'budget': float(category['total_budget'] or 0)
            }

        return {
            "code": 200,
            "message": "获取分类信息成功",
            "data": {
                "structure": structure,
                "stats": stats
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类信息失败: {str(e)}")

@router.websocket("/planning/ws")
async def planning_websocket_endpoint(websocket: WebSocket, token: str = Query(None)):
    """项目规划WebSocket端点"""
    connection_id = str(uuid.uuid4())

    try:
        # 简化token验证，暂时允许所有连接
        user_info = {"user_id": "anonymous", "username": "匿名用户"}
        if token:
            # 如果有token，可以在这里验证
            user_info = {"user_id": "authenticated", "username": "认证用户"}

        # 建立WebSocket连接
        await planning_ws_manager.connect_planning(websocket, connection_id, user_info)

        # 监听消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                # 处理不同类型的消息
                message_type = message.get("type")

                if message_type == "pong":
                    # 心跳响应，不需要处理
                    continue
                elif message_type == "subscribe":
                    # 订阅特定分类的更新
                    category = message.get("category")
                    subcategory = message.get("subcategory")
                    # TODO: 实现分类订阅逻辑
                    pass
                elif message_type == "unsubscribe":
                    # 取消订阅
                    # TODO: 实现取消订阅逻辑
                    pass
                else:
                    # 未知消息类型
                    await planning_ws_manager.manager.send_personal_message({
                        "type": "error",
                        "message": f"未知消息类型: {message_type}"
                    }, connection_id)

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await planning_ws_manager.manager.send_personal_message({
                    "type": "error",
                    "message": "消息格式错误"
                }, connection_id)
            except Exception as e:
                await planning_ws_manager.manager.send_personal_message({
                    "type": "error",
                    "message": f"处理消息失败: {str(e)}"
                }, connection_id)

    except Exception as e:
        print(f"WebSocket连接错误: {e}")
    finally:
        # 断开连接
        planning_ws_manager.disconnect_planning(connection_id)