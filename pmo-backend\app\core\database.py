#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接管理
"""

import os
import pymysql
import logging
from pymysql.cursors import DictCursor
from datetime import datetime, timedelta
import random

logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', '3306')),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'kanban2'),
    'charset': 'utf8mb4',
    'cursorclass': DictCursor,
    'connect_timeout': 5,
    'read_timeout': 10,
    'write_timeout': 10
}

# 是否使用模拟数据
USE_MOCK_DATA = os.environ.get('USE_MOCK_DATA', 'false').lower() == 'true'
FORCE_REAL_DB = os.environ.get('FORCE_REAL_DB', 'false').lower() == 'true'

def get_db_connection():
    """获取数据库连接"""
    if USE_MOCK_DATA and not FORCE_REAL_DB:
        logger.info("🔄 使用模拟数据模式")
        return MockConnection()

    try:
        conn = pymysql.connect(**DB_CONFIG)
        logger.info("✅ 数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {str(e)}")
        if FORCE_REAL_DB:
            raise Exception(f"强制使用真实数据库但连接失败: {str(e)}")
        logger.info("🔄 切换到模拟数据模式")
        return MockConnection()

def close_db_connection(conn):
    """关闭数据库连接"""
    if conn and hasattr(conn, 'close'):
        conn.close()

class MockCursor:
    """模拟数据库游标"""

    def __init__(self):
        self.description = None
        self._results = []
        self._mock_data = self._generate_mock_data()

    def _generate_mock_data(self):
        """生成模拟数据"""
        return {
            'projects': [
                {
                    'project_code': 'PRJ001',
                    'project_name': '智慧城市建设项目',
                    'investment_entity': '市政府',
                    'project_category': '基础设施',
                    'current_progress': '设计阶段',
                    'planned_start_date': '2024-01-15',
                    'actual_start_date': '2024-01-20',
                    'planned_completion_date': '2024-12-31',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'status': 'active',
                    'created_at': '2024-01-01 10:00:00',
                    'updated_at': '2024-08-01 15:30:00'
                },
                {
                    'project_code': 'PRJ002',
                    'project_name': '数字化转型项目',
                    'investment_entity': '科技公司',
                    'project_category': '信息化',
                    'current_progress': '实施阶段',
                    'planned_start_date': '2024-03-01',
                    'actual_start_date': '2024-03-05',
                    'planned_completion_date': '2024-10-31',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'status': 'active',
                    'created_at': '2024-02-15 09:00:00',
                    'updated_at': '2024-08-01 16:45:00'
                },
                {
                    'project_code': 'PRJ003',
                    'project_name': '绿色能源项目',
                    'investment_entity': '能源集团',
                    'project_category': '新能源',
                    'current_progress': '验收阶段',
                    'planned_start_date': '2023-06-01',
                    'actual_start_date': '2023-06-10',
                    'planned_completion_date': '2024-06-30',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'status': 'completed',
                    'created_at': '2023-05-20 14:00:00',
                    'updated_at': '2024-07-15 11:20:00'
                }
            ],
            'project_account_book': [
                {
                    'project_code': 'PRJ001',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'investment_progress': 30.00,
                    'budget_year': 2024,
                    'created_at': '2024-01-01 10:00:00'
                },
                {
                    'project_code': 'PRJ002',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'investment_progress': 73.33,
                    'budget_year': 2024,
                    'created_at': '2024-02-15 09:00:00'
                },
                {
                    'project_code': 'PRJ003',
                    'total_investment': ********.00,
                    'completed_investment': ********.00,
                    'investment_progress': 93.75,
                    'budget_year': 2024,
                    'created_at': '2023-05-20 14:00:00'
                }
            ],
            'project_hours': [
                {
                    'project_code': 'PRJ001',
                    'user_id': 'user001',
                    'work_date': '2024-08-01',
                    'hours': 8.0,
                    'work_content': '需求分析和设计评审',
                    'created_at': '2024-08-01 18:00:00'
                },
                {
                    'project_code': 'PRJ002',
                    'user_id': 'user002',
                    'work_date': '2024-08-01',
                    'hours': 6.5,
                    'work_content': '系统开发和测试',
                    'created_at': '2024-08-01 17:30:00'
                }
            ],
            'supervision_items': [
                {
                    'item_id': 1,
                    'title': '项目进度督办',
                    'content': '检查智慧城市项目进度是否按计划执行',
                    'responsible_person': '张三',
                    'completion_deadline': '2024-08-15',
                    'status': 'pending',
                    'progress_description': '正在跟进中',
                    'created_at': '2024-08-01 09:00:00'
                },
                {
                    'item_id': 2,
                    'title': '投资审核督办',
                    'content': '审核数字化转型项目投资使用情况',
                    'responsible_person': '李四',
                    'completion_deadline': '2024-08-10',
                    'status': 'completed',
                    'progress_description': '已完成审核',
                    'created_at': '2024-07-25 10:30:00'
                }
            ]
        }

    def execute(self, sql, params=None):
        """执行SQL语句"""
        sql_upper = sql.upper().strip()
        logger.info(f"🔍 模拟执行SQL: {sql[:100]}...")

        # 处理不同类型的查询
        if sql_upper.startswith('SELECT COUNT(*)'):
            # 计数查询
            if 'projects' in sql.lower():
                self._results = [{'count': len(self._mock_data['projects'])}]
            elif 'project_account_book' in sql.lower():
                self._results = [{'count': len(self._mock_data['project_account_book'])}]
            elif 'project_hours' in sql.lower():
                self._results = [{'count': len(self._mock_data['project_hours'])}]
            elif 'supervision_items' in sql.lower():
                self._results = [{'count': len(self._mock_data['supervision_items'])}]
            else:
                self._results = [{'count': 0}]

            self.description = [('count',)]

        elif sql_upper.startswith('SELECT 1'):
            # 测试查询
            self._results = [{'test': 1}]
            self.description = [('test',)]

        elif sql_upper.startswith('SHOW TABLES'):
            # 显示表
            self._results = [
                {'Tables_in_kanban2': 'projects'},
                {'Tables_in_kanban2': 'project_account_book'},
                {'Tables_in_kanban2': 'project_hours'},
                {'Tables_in_kanban2': 'supervision_items'}
            ]
            self.description = [('Tables_in_kanban2',)]

        elif 'projects' in sql.lower():
            # 项目查询
            self._results = self._mock_data['projects']
            if self._results:
                self.description = [(key,) for key in self._results[0].keys()]

        elif 'project_account_book' in sql.lower():
            # 账本查询
            self._results = self._mock_data['project_account_book']
            if self._results:
                self.description = [(key,) for key in self._results[0].keys()]

        elif 'project_hours' in sql.lower():
            # 工时查询
            self._results = self._mock_data['project_hours']
            if self._results:
                self.description = [(key,) for key in self._results[0].keys()]

        elif 'supervision_items' in sql.lower():
            # 督办查询
            self._results = self._mock_data['supervision_items']
            if self._results:
                self.description = [(key,) for key in self._results[0].keys()]
        else:
            # 默认返回空结果
            self._results = []
            self.description = []

        return len(self._results)

    def fetchall(self):
        """获取所有结果"""
        return self._results

    def fetchone(self):
        """获取一条结果"""
        return self._results[0] if self._results else None

    def close(self):
        """关闭游标"""
        pass

class MockConnection:
    """模拟数据库连接"""

    def __init__(self):
        logger.info("🎭 使用模拟数据库连接")

    def cursor(self):
        """创建游标"""
        return MockCursor()

    def close(self):
        """关闭连接"""
        pass

    def commit(self):
        """提交事务"""
        pass

    def rollback(self):
        """回滚事务"""
        pass
