#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品管理API - 第三个功能模块：产品和需求管理
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.models.user import User
from app.models.product import Product, ProductLine
from app.models.story import Story
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/product", tags=["产品管理"])

# 请求模型
class ProductCreateRequest(BaseModel):
    name: str
    code: str = ""
    line: int = 0
    type: str = "normal"
    desc: str = ""
    PO: str = ""
    QD: str = ""
    RD: str = ""
    acl: str = "open"
    order: int = 0

class ProductUpdateRequest(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    line: Optional[int] = None
    type: Optional[str] = None
    desc: Optional[str] = None
    PO: Optional[str] = None
    QD: Optional[str] = None
    RD: Optional[str] = None
    acl: Optional[str] = None
    order: Optional[int] = None

class StoryCreateRequest(BaseModel):
    product: int
    title: str
    type: str = "story"
    category: str = "feature"
    pri: int = 3
    estimate: float = 0
    assignedTo: str = ""
    spec: str = ""
    verify: str = ""
    keywords: str = ""
    module: int = 0

class StoryUpdateRequest(BaseModel):
    title: Optional[str] = None
    type: Optional[str] = None
    category: Optional[str] = None
    pri: Optional[int] = None
    estimate: Optional[float] = None
    assignedTo: Optional[str] = None
    status: Optional[str] = None
    stage: Optional[str] = None
    spec: Optional[str] = None
    verify: Optional[str] = None
    keywords: Optional[str] = None
    order: Optional[int] = None

class StoryAssignRequest(BaseModel):
    assignedTo: str

class StoryCloseRequest(BaseModel):
    closedReason: str = ""

# 权限检查
def check_product_permission(current_user: User = Depends(get_current_user)):
    """检查产品权限"""
    if current_user.role not in ["admin", "pm", "po"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员、项目经理或产品经理权限"
        )
    return current_user

# 产品管理API
@router.get("/products")
async def get_products(
    page: int = 1,
    page_size: int = 20,
    keyword: str = "",
    status: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取产品列表"""
    try:
        result = Product.get_all_products(page=page, page_size=page_size, keyword=keyword, status=status)
        return success_response(result, "获取产品列表成功")
    except Exception as e:
        return error_response(f"获取产品列表失败: {str(e)}")

@router.get("/products/{product_id}")
async def get_product(product_id: int, current_user: User = Depends(get_current_user)):
    """获取产品详情"""
    try:
        product = Product.get_by_id(product_id)
        if not product:
            return error_response("产品不存在", status_code=404)
        
        product_data = product.to_dict()
        
        # 获取产品统计信息
        stories = product.get_stories(page_size=1000)  # 获取所有需求用于统计
        product_data['story_count'] = stories['total']
        
        # 按状态统计需求
        status_stats = {}
        for story in stories['stories']:
            status = story['status']
            status_stats[status] = status_stats.get(status, 0) + 1
        product_data['story_stats'] = status_stats
        
        # 获取产品计划和发布
        product_data['plans'] = product.get_plans()
        product_data['releases'] = product.get_releases()
        product_data['modules'] = product.get_modules()
        
        return success_response(product_data, "获取产品详情成功")
    except Exception as e:
        return error_response(f"获取产品详情失败: {str(e)}")

@router.post("/products")
async def create_product(
    request: ProductCreateRequest,
    current_user: User = Depends(check_product_permission)
):
    """创建产品"""
    try:
        product = Product()
        product.name = request.name
        product.code = request.code
        product.line = request.line
        product.type = request.type
        product.desc = request.desc
        product.PO = request.PO
        product.QD = request.QD
        product.RD = request.RD
        product.acl = request.acl
        product.order = request.order
        product.createdBy = current_user.account
        
        if product.create():
            return success_response(product.to_dict(), "创建产品成功")
        else:
            return error_response("创建产品失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"创建产品失败: {str(e)}")

@router.put("/products/{product_id}")
async def update_product(
    product_id: int,
    request: ProductUpdateRequest,
    current_user: User = Depends(check_product_permission)
):
    """更新产品信息"""
    try:
        product = Product.get_by_id(product_id)
        if not product:
            return error_response("产品不存在", status_code=404)
        
        # 更新产品信息
        if request.name is not None:
            product.name = request.name
        if request.code is not None:
            product.code = request.code
        if request.line is not None:
            product.line = request.line
        if request.type is not None:
            product.type = request.type
        if request.desc is not None:
            product.desc = request.desc
        if request.PO is not None:
            product.PO = request.PO
        if request.QD is not None:
            product.QD = request.QD
        if request.RD is not None:
            product.RD = request.RD
        if request.acl is not None:
            product.acl = request.acl
        if request.order is not None:
            product.order = request.order
        
        if product.update():
            return success_response(product.to_dict(), "更新产品信息成功")
        else:
            return error_response("更新产品信息失败")
            
    except Exception as e:
        return error_response(f"更新产品信息失败: {str(e)}")

@router.delete("/products/{product_id}")
async def delete_product(
    product_id: int,
    current_user: User = Depends(check_product_permission)
):
    """删除产品"""
    try:
        product = Product.get_by_id(product_id)
        if not product:
            return error_response("产品不存在", status_code=404)
        
        if product.delete():
            return success_response(None, "删除产品成功")
        else:
            return error_response("删除产品失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"删除产品失败: {str(e)}")

# 产品线管理API
@router.get("/lines")
async def get_product_lines(current_user: User = Depends(get_current_user)):
    """获取产品线列表"""
    try:
        lines = ProductLine.get_all_lines()
        return success_response([line.to_dict() for line in lines], "获取产品线列表成功")
    except Exception as e:
        return error_response(f"获取产品线列表失败: {str(e)}")

# 需求管理API
@router.get("/stories")
async def get_stories(
    page: int = 1,
    page_size: int = 20,
    product_id: int = 0,
    status: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取需求列表"""
    try:
        result = Story.get_all_stories(
            page=page, page_size=page_size, 
            product_id=product_id, status=status, keyword=keyword
        )
        return success_response(result, "获取需求列表成功")
    except Exception as e:
        return error_response(f"获取需求列表失败: {str(e)}")

@router.get("/stories/{story_id}")
async def get_story(story_id: int, current_user: User = Depends(get_current_user)):
    """获取需求详情"""
    try:
        story = Story.get_by_id(story_id)
        if not story:
            return error_response("需求不存在", status_code=404)
        
        return success_response(story.to_dict(include_spec=True), "获取需求详情成功")
    except Exception as e:
        return error_response(f"获取需求详情失败: {str(e)}")

@router.post("/stories")
async def create_story(
    request: StoryCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建需求"""
    try:
        # 检查产品是否存在
        product = Product.get_by_id(request.product)
        if not product:
            return error_response("产品不存在", status_code=404)
        
        story = Story()
        story.product = request.product
        story.title = request.title
        story.type = request.type
        story.category = request.category
        story.pri = request.pri
        story.estimate = request.estimate
        story.assignedTo = request.assignedTo
        story.keywords = request.keywords
        story.module = request.module
        story.openedBy = current_user.account
        story.status = "draft"
        story.stage = "wait"
        
        if story.create(spec=request.spec, verify=request.verify):
            return success_response(story.to_dict(include_spec=True), "创建需求成功")
        else:
            return error_response("创建需求失败")
            
    except Exception as e:
        return error_response(f"创建需求失败: {str(e)}")

@router.put("/stories/{story_id}")
async def update_story(
    story_id: int,
    request: StoryUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新需求信息"""
    try:
        story = Story.get_by_id(story_id)
        if not story:
            return error_response("需求不存在", status_code=404)
        
        # 更新需求信息
        if request.title is not None:
            story.title = request.title
        if request.type is not None:
            story.type = request.type
        if request.category is not None:
            story.category = request.category
        if request.pri is not None:
            story.pri = request.pri
        if request.estimate is not None:
            story.estimate = request.estimate
        if request.assignedTo is not None:
            story.assignedTo = request.assignedTo
        if request.status is not None:
            story.status = request.status
        if request.stage is not None:
            story.stage = request.stage
        if request.keywords is not None:
            story.keywords = request.keywords
        if request.order is not None:
            story.order = request.order
        
        story.lastEditedBy = current_user.account
        
        if story.update(spec=request.spec, verify=request.verify):
            return success_response(story.to_dict(include_spec=True), "更新需求信息成功")
        else:
            return error_response("更新需求信息失败")
            
    except Exception as e:
        return error_response(f"更新需求信息失败: {str(e)}")

@router.delete("/stories/{story_id}")
async def delete_story(
    story_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除需求"""
    try:
        story = Story.get_by_id(story_id)
        if not story:
            return error_response("需求不存在", status_code=404)
        
        if story.delete():
            return success_response(None, "删除需求成功")
        else:
            return error_response("删除需求失败")
            
    except Exception as e:
        return error_response(f"删除需求失败: {str(e)}")

@router.post("/stories/{story_id}/assign")
async def assign_story(
    story_id: int,
    request: StoryAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """指派需求"""
    try:
        story = Story.get_by_id(story_id)
        if not story:
            return error_response("需求不存在", status_code=404)
        
        # 检查被指派用户是否存在
        assignee = User.get_by_account(request.assignedTo)
        if not assignee:
            return error_response("被指派用户不存在", status_code=404)
        
        if story.assign(request.assignedTo, current_user.account):
            return success_response(story.to_dict(), "指派需求成功")
        else:
            return error_response("指派需求失败")
            
    except Exception as e:
        return error_response(f"指派需求失败: {str(e)}")

@router.post("/stories/{story_id}/close")
async def close_story(
    story_id: int,
    request: StoryCloseRequest,
    current_user: User = Depends(get_current_user)
):
    """关闭需求"""
    try:
        story = Story.get_by_id(story_id)
        if not story:
            return error_response("需求不存在", status_code=404)
        
        if story.close(current_user.account, request.closedReason):
            return success_response(story.to_dict(), "关闭需求成功")
        else:
            return error_response("关闭需求失败")
            
    except Exception as e:
        return error_response(f"关闭需求失败: {str(e)}")

@router.get("/products/{product_id}/stories")
async def get_product_stories(
    product_id: int,
    page: int = 1,
    page_size: int = 20,
    status: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取产品下的需求"""
    try:
        product = Product.get_by_id(product_id)
        if not product:
            return error_response("产品不存在", status_code=404)
        
        result = product.get_stories(page=page, page_size=page_size, status=status)
        return success_response(result, "获取产品需求成功")
    except Exception as e:
        return error_response(f"获取产品需求失败: {str(e)}")
