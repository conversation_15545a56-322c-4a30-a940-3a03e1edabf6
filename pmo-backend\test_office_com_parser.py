#!/usr/bin/env python3
"""
测试Office COM解析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_com_availability():
    """测试COM组件可用性"""
    print("🔧 测试COM组件可用性...")
    
    try:
        import win32com.client
        print("✅ win32com模块已安装")
        
        # 尝试创建Word应用程序
        try:
            word = win32com.client.Dispatch("Word.Application")
            word.Visible = False
            print("✅ Word应用程序可用")
            word.Quit()
            return True
        except Exception as e:
            print(f"❌ Word应用程序不可用: {str(e)}")
            return False
            
    except ImportError:
        print("❌ win32com模块未安装")
        return False

def test_powershell_availability():
    """测试PowerShell可用性"""
    print("\n🔧 测试PowerShell可用性...")
    
    import subprocess
    
    try:
        result = subprocess.run(['powershell.exe', '-Command', 'Write-Host "PowerShell测试"'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ PowerShell可用")
            return True
        else:
            print(f"❌ PowerShell不可用: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ PowerShell测试失败: {str(e)}")
        return False

def create_test_doc_content():
    """创建测试用的.doc文件内容"""
    # 这里使用一个简单的.doc文件头和内容
    # 实际应用中应该使用真实的.doc文件
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    test_content = "测试文档内容\n这是一个测试用的Word文档\n包含中文内容".encode('utf-16le')
    
    return ole_header + b'\x00' * 200 + test_content + b'\x00' * 500

def test_office_com_parsing():
    """测试Office COM解析"""
    print("\n🔧 测试Office COM解析...")
    
    parser = DocumentParser()
    
    # 使用模拟的.doc内容
    mock_doc = create_test_doc_content()
    
    try:
        result = parser._parse_with_office_com(mock_doc)
        if result:
            print(f"✅ COM解析成功")
            print(f"解析结果: {result[:200]}...")
        else:
            print("❌ COM解析失败或返回空内容")
    except Exception as e:
        print(f"❌ COM解析异常: {str(e)}")

def test_powershell_parsing():
    """测试PowerShell解析"""
    print("\n🔧 测试PowerShell解析...")
    
    parser = DocumentParser()
    
    # 使用模拟的.doc内容
    mock_doc = create_test_doc_content()
    
    try:
        result = parser._parse_with_powershell_script(mock_doc)
        if result:
            print(f"✅ PowerShell解析成功")
            print(f"解析结果: {result[:200]}...")
        else:
            print("❌ PowerShell解析失败或返回空内容")
    except Exception as e:
        print(f"❌ PowerShell解析异常: {str(e)}")

def test_complete_doc_parsing():
    """测试完整的.doc文件解析流程"""
    print("\n🔧 测试完整的.doc文件解析流程...")
    
    parser = DocumentParser()
    
    # 使用模拟的.doc内容
    mock_doc = create_test_doc_content()
    
    try:
        result = parser._parse_old_doc_file(mock_doc)
        print(f"完整解析结果:\n{result}")
        
        # 检查是否使用了COM/PowerShell解析
        if "Word文档内容" in result:
            print("✅ 解析成功，返回了格式化的内容")
        else:
            print("❌ 解析失败或格式不正确")
            
    except Exception as e:
        print(f"❌ 完整解析异常: {str(e)}")

def test_powershell_script_creation():
    """测试PowerShell脚本创建"""
    print("\n🔧 测试PowerShell脚本创建...")
    
    parser = DocumentParser()
    
    try:
        ps_script = parser._create_powershell_doc_parser()
        if ps_script and len(ps_script) > 100:
            print("✅ PowerShell脚本创建成功")
            print(f"脚本长度: {len(ps_script)} 字符")
            print("脚本开头:")
            print(ps_script[:200] + "...")
        else:
            print("❌ PowerShell脚本创建失败")
    except Exception as e:
        print(f"❌ PowerShell脚本创建异常: {str(e)}")

def test_office_text_cleaning():
    """测试Office文本清理功能"""
    print("\n🔧 测试Office文本清理功能...")
    
    parser = DocumentParser()
    
    # 模拟从Office提取的原始文本
    raw_text = "标题\r\n\r\n正文内容\x07表格内容\x0c分页内容\r\n\r\n结尾"
    
    try:
        cleaned_text = parser._clean_office_extracted_text(raw_text)
        print(f"原始文本: {repr(raw_text)}")
        print(f"清理后文本: {cleaned_text}")
        
        if '\r' not in cleaned_text and '\x07' not in cleaned_text:
            print("✅ 文本清理成功")
        else:
            print("❌ 文本清理不完整")
            
    except Exception as e:
        print(f"❌ 文本清理异常: {str(e)}")

if __name__ == "__main__":
    print("🔧 测试Office COM/.doc文件解析器")
    print("=" * 60)
    
    try:
        # 测试环境可用性
        com_available = test_com_availability()
        ps_available = test_powershell_availability()
        
        # 测试脚本创建
        test_powershell_script_creation()
        
        # 测试文本清理
        test_office_text_cleaning()
        
        # 如果环境可用，测试解析功能
        if com_available:
            test_office_com_parsing()
        
        if ps_available:
            test_powershell_parsing()
        
        # 测试完整流程
        test_complete_doc_parsing()
        
        print("\n✅ 所有测试完成")
        
        if com_available or ps_available:
            print("💡 现在可以重新启动后端并测试实际的.doc文件")
            print("\n🚀 新解析器的优势:")
            print("1. 直接调用Microsoft Word进行解析")
            print("2. 完美支持.doc格式")
            print("3. 自动处理编码和格式问题")
            print("4. 提取干净的文本内容")
        else:
            print("⚠️  需要安装Microsoft Word才能使用COM解析")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
