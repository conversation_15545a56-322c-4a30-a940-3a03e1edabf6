from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from app.core.config import settings
from app.core.logger import get_logger

logger = get_logger(__name__)

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2密码Bearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """获取当前用户"""
    import os

    # 测试模式：如果设置了测试环境变量，返回测试用户
    if os.getenv("PMO_TEST_MODE") == "true":
        logger.info("测试模式：使用测试用户")
        return {
            "UserID": "na10000014",
            "username": "TestUser",
            "name": "测试用户",
            "role": 3.0,
            "department_name": "业务二部",
            "company_name": "数字金服",
            "LaborCost": 20.0,
            "service_lines": ["金租", "商租", "汽租"]
        }

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception

        # 获取完整的用户信息，包括service_lines
        from app.api.endpoints.auth import get_user_service_lines
        from app.db.utils import execute_query

        # 查询数据库获取用户信息
        query = """
            SELECT UserID, username, name, role, department_name, company_name, LaborCost
            FROM users
            WHERE username = %s AND (is_disabled IS NULL OR is_disabled = 'N')
        """

        user_result = execute_query(query, (username,))
        if not user_result:
            raise credentials_exception

        user_data = user_result[0]

        # 获取用户的服务条线
        service_lines = get_user_service_lines(user_data.get("UserID", ""))

        # 返回完整的用户信息
        return {
            "UserID": user_data.get("UserID"),
            "username": user_data.get("username"),
            "name": user_data.get("name"),
            "role": user_data.get("role"),
            "department_name": user_data.get("department_name"),
            "company_name": user_data.get("company_name"),
            "LaborCost": user_data.get("LaborCost"),
            "service_lines": service_lines
        }

    except JWTError as e:
        logger.error(f"JWT解码错误: {str(e)}")
        raise credentials_exception

def decode_access_token(token: str):
    """解码访问令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"JWT解码错误: {str(e)}")
        return None 