#!/usr/bin/env python3
"""
测试修复后的.doc文件解析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_chinese_text_extraction():
    """测试中文文本提取功能"""
    print("🔧 测试中文文本提取功能...")
    
    parser = DocumentParser()
    
    # 创建包含中文的测试数据
    test_chinese = "这是一个测试文档内容，包含中文字符。"
    
    # 测试UTF-16LE编码
    utf16_data = test_chinese.encode('utf-16le')
    result = parser._extract_chinese_text_only(utf16_data)
    print(f"UTF-16LE测试结果: {result}")
    
    # 测试GBK编码
    gbk_data = test_chinese.encode('gbk')
    result = parser._extract_chinese_text_only(gbk_data)
    print(f"GBK测试结果: {result}")
    
    # 测试混合数据（模拟OLE文件）
    mixed_data = b'\x00\x00' + utf16_data + b'\x00\x00' + gbk_data + b'\x00\x00'
    result = parser._extract_chinese_text_only(mixed_data)
    print(f"混合数据测试结果: {result}")

def test_docx2txt_cleaning():
    """测试docx2txt输出清理功能"""
    print("\n🔧 测试docx2txt输出清理功能...")
    
    parser = DocumentParser()
    
    # 模拟docx2txt的乱码输出
    messy_output = "蜾梾肛乌躴隯\n\n瘃倏錹绊元脍\n\n正常的中文内容\n\nNormal English text\n\n鰢籀瞎吭爹溩崴旐"
    
    cleaned = parser._clean_docx2txt_output(messy_output)
    print(f"清理前: {messy_output}")
    print(f"清理后: {cleaned}")

def test_simple_extraction():
    """测试简单文本提取功能"""
    print("\n🔧 测试简单文本提取功能...")
    
    parser = DocumentParser()
    
    # 创建测试数据
    test_content = "项目管理文档\n\n需求分析\n\n系统设计\n\nProject Management\n\nRequirement Analysis"
    
    # 测试不同编码
    for encoding in ['utf-16le', 'gbk', 'utf-8']:
        try:
            encoded_data = test_content.encode(encoding)
            result = parser._simple_text_extraction(encoded_data)
            print(f"{encoding}编码测试结果: {result}")
        except Exception as e:
            print(f"{encoding}编码测试失败: {str(e)}")

def test_full_doc_parsing():
    """测试完整的.doc文件解析流程"""
    print("\n🔧 测试完整的.doc文件解析流程...")
    
    parser = DocumentParser()
    
    # 创建模拟的OLE格式.doc文件内容
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'  # OLE文件头
    chinese_content = "项目管理\n需求分析\n系统设计\n测试计划".encode('utf-16le')
    english_content = b"Project Management\nRequirement Analysis\nSystem Design\nTest Plan"
    
    # 组合成模拟的.doc文件
    mock_doc_content = ole_header + b'\x00' * 100 + chinese_content + b'\x00' * 50 + english_content + b'\x00' * 200
    
    result = parser._parse_old_doc_file(mock_doc_content)
    print(f"完整解析结果:\n{result}")

if __name__ == "__main__":
    print("🔧 测试修复后的.doc文件解析器")
    print("=" * 60)
    
    try:
        # 测试各个组件
        test_chinese_text_extraction()
        test_docx2txt_cleaning()
        test_simple_extraction()
        test_full_doc_parsing()
        
        print("\n✅ 所有测试完成")
        print("💡 现在可以重新启动后端并测试实际的.doc文件")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
