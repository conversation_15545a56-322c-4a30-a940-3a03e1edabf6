#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化数据库并添加测试用户数据
"""

import os
import pymysql
import hashlib
import base64
import secrets

# 设置环境变量
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_PORT'] = '3306'
os.environ['DB_USER'] = 'root'
os.environ['DB_PASSWORD'] = ''
os.environ['DB_NAME'] = 'pmo'

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 测试用户数据
TEST_USERS = [
    {
        'UserID': 'U20250001',
        'name': '张三',
        'username': 'zhang<PERSON>',
        'password': '123456',
        'company_name': '总公司',
        'department_name': '技术部',
        'LaborCost': 10000,
        'role': 3,
        'is_disabled': 'N'
    },
    {
        'UserID': 'U20250002',
        'name': '李四',
        'username': 'lisi',
        'password': '123456',
        'company_name': '总公司',
        'department_name': '市场部',
        'LaborCost': 12000,
        'role': 3,
        'is_disabled': 'N'
    },
    {
        'UserID': 'U20250003',
        'name': '王五',
        'username': 'wangwu',
        'password': '123456',
        'company_name': '分公司',
        'department_name': '财务部',
        'LaborCost': 9000,
        'role': 3,
        'is_disabled': 'N'
    },
    {
        'UserID': 'U20250004',
        'name': '赵六',
        'username': 'zhaoliu',
        'password': '123456',
        'company_name': '分公司',
        'department_name': '技术部',
        'LaborCost': 11000,
        'role': 3,
        'is_disabled': 'N'
    },
    {
        'UserID': 'U20250005',
        'name': '钱七',
        'username': 'qianqi',
        'password': '123456',
        'company_name': '总公司',
        'department_name': '人力资源部',
        'LaborCost': 8000,
        'role': 3,
        'is_disabled': 'N'
    }
]

def generate_salt():
    """生成随机盐值"""
    return secrets.token_hex(16)

def hash_password(password, salt=None):
    """
    使用PBKDF2算法哈希密码
    :param password: 明文密码
    :param salt: 盐值，如果为None则生成新的盐值
    :return: salt:hash格式的密码哈希
    """
    if salt is None:
        salt = generate_salt()
    
    key = hashlib.pbkdf2_hmac(
        'sha512', 
        password.encode('utf-8'), 
        salt.encode('utf-8'), 
        1000,
        dklen=64
    )
    
    password_hash = base64.b64encode(key).decode('utf-8')
    return f"{salt}:{password_hash}"

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器
        conn = pymysql.connect(
            host=os.environ['DB_HOST'],
            port=int(os.environ['DB_PORT']),
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD'],
            charset='utf8mb4'
        )
        
        with conn.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {os.environ['DB_NAME']} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 {os.environ['DB_NAME']} 创建成功")
        
        conn.close()
    except Exception as e:
        print(f"创建数据库失败: {str(e)}")
        raise e

def check_table_exists(conn, table_name):
    """检查表是否存在"""
    with conn.cursor() as cursor:
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        return cursor.fetchone() is not None

def create_users_table(conn):
    """创建users表"""
    with conn.cursor() as cursor:
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            UserID VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(50) NOT NULL,
            username VARCHAR(50) NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            openid VARCHAR(100),
            company_name VARCHAR(100),
            department_name VARCHAR(100),
            LaborCost DECIMAL(10,2) DEFAULT 0,
            role INT DEFAULT 3,
            is_disabled ENUM('Y', 'N') DEFAULT 'N',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)
        conn.commit()
        print("users表创建成功")

def add_test_users(conn):
    """添加测试用户数据"""
    with conn.cursor() as cursor:
        for user in TEST_USERS:
            # 检查用户是否已存在
            cursor.execute("SELECT id FROM users WHERE UserID = %s", (user['UserID'],))
            if cursor.fetchone():
                print(f"用户 {user['UserID']} 已存在，跳过")
                continue
            
            # 生成密码哈希
            password_hash = hash_password(user['password'])
            
            # 插入用户数据
            sql = """
            INSERT INTO users (
                UserID, name, username, password_hash, company_name, department_name, LaborCost, role, is_disabled
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            cursor.execute(sql, (
                user['UserID'],
                user['name'],
                user['username'],
                password_hash,
                user['company_name'],
                user['department_name'],
                user['LaborCost'],
                user['role'],
                user['is_disabled']
            ))
            print(f"添加用户: {user['name']} ({user['UserID']})")
        
        conn.commit()

def main():
    """主函数"""
    try:
        # 创建数据库
        create_database()
        
        # 连接数据库
        print("正在连接数据库...")
        conn = pymysql.connect(**DB_CONFIG)
        
        # 检查users表是否存在
        if not check_table_exists(conn, 'users'):
            print("users表不存在，正在创建...")
            create_users_table(conn)
        
        # 添加测试用户数据
        print("正在添加测试用户数据...")
        add_test_users(conn)
        
        print("数据库初始化完成")
        conn.close()
    except Exception as e:
        print(f"初始化数据库失败: {str(e)}")

if __name__ == "__main__":
    main() 