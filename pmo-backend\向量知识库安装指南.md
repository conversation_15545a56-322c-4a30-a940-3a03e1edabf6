# 🚀 PMO向量知识库安装指南

## 📋 概述

向量知识库是PMO系统的高级功能，基于Qdrant向量数据库和中文语义模型，提供：

- ✅ **语义搜索**：理解自然语言查询意图
- ✅ **相似内容发现**：找到语义相关的文档
- ✅ **智能分类**：自动识别文档类型和主题
- ✅ **实时索引**：动态更新知识库内容

## 🛠️ 安装步骤

### 1. 运行安装脚本

```bash
cd pmo-backend
python install_vector_knowledge.py
```

安装脚本会自动：
- 📦 安装Python依赖包
- 🐳 创建Docker Compose配置
- 📝 生成启动脚本
- 🧪 测试安装结果

### 2. 启动Qdrant服务

#### Windows系统
```bash
# 双击运行
start_qdrant.bat

# 或手动运行
docker-compose -f docker-compose.qdrant.yml up -d
```

#### Linux/macOS系统
```bash
# 运行启动脚本
./start_qdrant.sh

# 或手动运行
docker-compose -f docker-compose.qdrant.yml up -d
```

### 3. 验证安装

访问Qdrant Web UI：http://localhost:3000

检查服务状态：
```bash
curl http://localhost:6333/collections
```

### 4. 重启PMO系统

```bash
# 重启后端服务
cd pmo-backend
python main.py

# 重启前端服务
cd pmo-web
npm run dev
```

## 🎯 使用指南

### 访问向量知识库

1. 登录PMO系统：http://localhost:3000
2. 点击侧边栏"向量知识库"菜单
3. 进入智能搜索界面

### 功能使用

#### 🔍 智能搜索
- 输入自然语言查询，如："金租的逾期项目有哪些？"
- 支持文档类型和投资主体过滤
- 可调整相似度阈值和结果数量

#### 📊 系统状态
- 查看Qdrant连接状态
- 监控向量数据量
- 检查缓存模块状态

#### ⚙️ 管理操作
- **增量索引**：添加新数据到向量库
- **完全重建**：清空并重新构建所有向量
- 查看系统详细信息

## 🔧 配置说明

### 环境变量

在 `.env` 文件中配置：

```bash
# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333

# 向量模型配置
EMBEDDING_MODEL=shibing624/text2vec-base-chinese
MODEL_CACHE_DIR=./models
```

### 性能优化

#### 内存配置
- 推荐内存：8GB+
- Qdrant内存：2GB+
- 模型缓存：1GB+

#### 索引策略
- **增量索引**：适用于日常更新
- **完全重建**：适用于大量数据变更
- **定时索引**：可配置自动更新

## 🚨 故障排除

### 常见问题

#### 1. Qdrant连接失败
```bash
# 检查Docker服务
docker ps | grep qdrant

# 重启Qdrant
docker-compose -f docker-compose.qdrant.yml restart
```

#### 2. 模型下载失败
```bash
# 手动下载模型
python -c "
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('shibing624/text2vec-base-chinese')
"
```

#### 3. 索引失败
- 检查数据库连接
- 确认知识库服务正常
- 查看后端日志

#### 4. 搜索结果为空
- 确认已完成索引
- 检查搜索参数设置
- 降低相似度阈值

### 日志查看

```bash
# 后端日志
tail -f pmo-backend/logs/app.log

# Qdrant日志
docker logs qdrant-pmo

# 前端控制台
# 打开浏览器开发者工具查看
```

## 📈 性能监控

### 关键指标

- **向量数量**：索引的文档片段数
- **搜索延迟**：查询响应时间
- **内存使用**：Qdrant和模型内存占用
- **索引速度**：文档处理速度

### 监控命令

```bash
# Qdrant状态
curl http://localhost:6333/collections/pmo_knowledge

# 系统资源
docker stats qdrant-pmo

# API状态
curl http://localhost:8000/api/vector-knowledge/status
```

## 🔄 升级指南

### 更新依赖
```bash
pip install --upgrade qdrant-client sentence-transformers
```

### 更新Qdrant
```bash
docker-compose -f docker-compose.qdrant.yml pull
docker-compose -f docker-compose.qdrant.yml up -d
```

### 重建索引
访问管理界面，点击"完全重建"按钮

## 💡 最佳实践

### 1. 定期维护
- 每周执行增量索引
- 每月检查系统状态
- 定期清理过期数据

### 2. 查询优化
- 使用具体的关键词
- 合理设置相似度阈值
- 利用过滤条件缩小范围

### 3. 性能调优
- 根据数据量调整批处理大小
- 监控内存使用情况
- 优化向量维度设置

## 📞 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 确认环境配置正确
4. 联系技术支持团队

---

## 🎉 安装完成

恭喜！向量知识库已成功安装。现在可以享受智能语义搜索带来的便利了！

**下一步**：
1. 访问 http://localhost:3000/vector-knowledge
2. 执行首次索引
3. 尝试智能搜索功能
