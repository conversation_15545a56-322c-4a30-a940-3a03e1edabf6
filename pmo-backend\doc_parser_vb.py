#!/usr/bin/env python3
"""
使用VB.NET脚本解析.doc文件的Python包装器
"""

import subprocess
import tempfile
import os
import json

def create_vb_doc_parser():
    """创建VB.NET的.doc文件解析器"""
    
    vb_script = '''
Imports System
Imports System.IO
Imports System.Text
Imports Microsoft.Office.Interop.Word

Module DocParser
    Sub Main(args As String())
        If args.Length < 2 Then
            Console.WriteLine("用法: DocParser.exe <输入文件路径> <输出文件路径>")
            Return
        End If
        
        Dim inputFile As String = args(0)
        Dim outputFile As String = args(1)
        
        Try
            ParseDocFile(inputFile, outputFile)
        Catch ex As Exception
            Console.WriteLine("错误: " & ex.Message)
        End Try
    End Sub
    
    Sub ParseDocFile(inputFile As String, outputFile As String)
        Dim wordApp As Application = Nothing
        Dim doc As Document = Nothing
        
        Try
            ' 创建Word应用程序实例
            wordApp = New Application()
            wordApp.Visible = False
            wordApp.DisplayAlerts = WdAlertLevel.wdAlertsNone
            
            ' 打开文档
            doc = wordApp.Documents.Open(inputFile, ReadOnly:=True)
            
            ' 提取文本内容
            Dim content As String = doc.Content.Text
            
            ' 清理文本
            content = CleanText(content)
            
            ' 保存到输出文件
            File.WriteAllText(outputFile, content, Encoding.UTF8)
            
            Console.WriteLine("解析完成")
            
        Catch ex As Exception
            Console.WriteLine("解析失败: " & ex.Message)
        Finally
            ' 清理资源
            If doc IsNot Nothing Then
                doc.Close(SaveChanges:=False)
            End If
            If wordApp IsNot Nothing Then
                wordApp.Quit()
            End If
        End Try
    End Sub
    
    Function CleanText(text As String) As String
        If String.IsNullOrEmpty(text) Then
            Return ""
        End If
        
        ' 替换特殊字符
        text = text.Replace(Chr(13), vbNewLine)  ' 回车符
        text = text.Replace(Chr(7), "")          ' 表格结束符
        text = text.Replace(Chr(12), vbNewLine)  ' 分页符
        
        ' 移除多余的空行
        Dim lines() As String = text.Split({vbNewLine}, StringSplitOptions.None)
        Dim cleanLines As New List(Of String)
        
        For Each line As String In lines
            Dim cleanLine As String = line.Trim()
            If Not String.IsNullOrEmpty(cleanLine) Then
                cleanLines.Add(cleanLine)
            End If
        Next
        
        Return String.Join(vbNewLine, cleanLines)
    End Function
End Module
'''
    
    return vb_script

def create_powershell_doc_parser():
    """创建PowerShell的.doc文件解析器（作为VB.NET的替代方案）"""
    
    ps_script = '''
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$true)]
    [string]$OutputFile
)

try {
    # 创建Word应用程序对象
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $word.DisplayAlerts = 0  # 不显示警告
    
    # 打开文档
    $doc = $word.Documents.Open($InputFile, $false, $true)  # 只读模式
    
    # 提取文本内容
    $content = $doc.Content.Text
    
    # 清理文本
    $content = $content -replace "`r", "`n"  # 替换回车符
    $content = $content -replace "`f", "`n"  # 替换分页符
    $content = $content -replace "[`u0007]", ""  # 移除表格结束符
    
    # 移除多余的空行
    $lines = $content -split "`n" | Where-Object { $_.Trim() -ne "" }
    $cleanContent = $lines -join "`n"
    
    # 保存到输出文件
    [System.IO.File]::WriteAllText($OutputFile, $cleanContent, [System.Text.Encoding]::UTF8)
    
    Write-Host "解析完成"
    
} catch {
    Write-Host "解析失败: $($_.Exception.Message)"
} finally {
    # 清理资源
    if ($doc) {
        $doc.Close([ref]$false)
    }
    if ($word) {
        $word.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    }
}
'''
    
    return ps_script

def parse_doc_with_powershell(file_content: bytes) -> str:
    """使用PowerShell脚本解析.doc文件"""
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_input:
            temp_input.write(file_content)
            temp_input_path = temp_input.name
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w') as temp_output:
            temp_output_path = temp_output.name
        
        # 创建PowerShell脚本文件
        ps_script = create_powershell_doc_parser()
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ps1', mode='w', encoding='utf-8') as ps_file:
            ps_file.write(ps_script)
            ps_script_path = ps_file.name
        
        try:
            # 执行PowerShell脚本
            cmd = [
                'powershell.exe',
                '-ExecutionPolicy', 'Bypass',
                '-File', ps_script_path,
                '-InputFile', temp_input_path,
                '-OutputFile', temp_output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 读取输出文件
                if os.path.exists(temp_output_path):
                    with open(temp_output_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if content and len(content.strip()) > 10:
                        return content.strip()
            
            return ""
            
        finally:
            # 清理临时文件
            for path in [temp_input_path, temp_output_path, ps_script_path]:
                try:
                    if os.path.exists(path):
                        os.unlink(path)
                except:
                    pass
                    
    except Exception as e:
        print(f"PowerShell解析失败: {str(e)}")
        return ""

def create_com_doc_parser():
    """创建基于COM的.doc文件解析器"""
    
    python_script = '''
import win32com.client
import sys
import os

def parse_doc_file(input_file, output_file):
    word = None
    doc = None
    
    try:
        # 创建Word应用程序对象
        word = win32com.client.Dispatch("Word.Application")
        word.Visible = False
        word.DisplayAlerts = 0
        
        # 打开文档
        doc = word.Documents.Open(input_file, ReadOnly=True)
        
        # 提取文本内容
        content = doc.Content.Text
        
        # 清理文本
        content = content.replace('\\r', '\\n')  # 替换回车符
        content = content.replace('\\f', '\\n')  # 替换分页符
        content = content.replace('\\x07', '')   # 移除表格结束符
        
        # 移除多余的空行
        lines = [line.strip() for line in content.split('\\n') if line.strip()]
        clean_content = '\\n'.join(lines)
        
        # 保存到输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(clean_content)
        
        print("解析完成")
        return True
        
    except Exception as e:
        print(f"解析失败: {str(e)}")
        return False
        
    finally:
        # 清理资源
        if doc:
            doc.Close(SaveChanges=False)
        if word:
            word.Quit()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python com_parser.py <输入文件> <输出文件>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    success = parse_doc_file(input_file, output_file)
    sys.exit(0 if success else 1)
'''
    
    return python_script

def parse_doc_with_com(file_content: bytes) -> str:
    """使用COM对象解析.doc文件"""
    try:
        # 检查是否安装了pywin32
        try:
            import win32com.client
        except ImportError:
            print("需要安装pywin32: pip install pywin32")
            return ""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_input:
            temp_input.write(file_content)
            temp_input_path = temp_input.name
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_output:
            temp_output_path = temp_output.name
        
        # 创建COM解析脚本
        com_script = create_com_doc_parser()
        with tempfile.NamedTemporaryFile(delete=False, suffix='.py', mode='w', encoding='utf-8') as py_file:
            py_file.write(com_script)
            py_script_path = py_file.name
        
        try:
            # 执行COM脚本
            cmd = ['python', py_script_path, temp_input_path, temp_output_path]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 读取输出文件
                if os.path.exists(temp_output_path):
                    with open(temp_output_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if content and len(content.strip()) > 10:
                        return content.strip()
            
            return ""
            
        finally:
            # 清理临时文件
            for path in [temp_input_path, temp_output_path, py_script_path]:
                try:
                    if os.path.exists(path):
                        os.unlink(path)
                except:
                    pass
                    
    except Exception as e:
        print(f"COM解析失败: {str(e)}")
        return ""

if __name__ == "__main__":
    print("🔧 VB.NET/.doc文件解析器")
    print("=" * 50)
    
    # 测试文件路径
    test_file = "test.doc"
    
    if os.path.exists(test_file):
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        print("测试PowerShell解析...")
        ps_result = parse_doc_with_powershell(file_content)
        print(f"PowerShell结果: {ps_result[:200]}...")
        
        print("\n测试COM解析...")
        com_result = parse_doc_with_com(file_content)
        print(f"COM结果: {com_result[:200]}...")
    else:
        print("请提供test.doc文件进行测试")
