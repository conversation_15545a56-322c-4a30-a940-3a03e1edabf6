#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取项目变更历史云函数

功能：根据项目编码获取项目的变更历史记录
接口：HTTP GET
参数：
  - project_code：项目编码（必填）

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "history": [
      {
        "field": "字段名",
        "previous_value": "原值",
        "new_value": "新值",
        "name": "修改人",
        "change_time": "修改时间"
      },
      ...
    ]
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def get_project_change_history(project_code):
    """
    获取项目变更历史
    :param project_code: 项目编码
    :return: 变更历史记录列表
    """
    if not project_code:
        raise ValueError("项目编码不能为空")
        
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 查询变更记录
            sql = """
            SELECT field, previous_value, new_value, name, DATE_FORMAT(change_time, '%Y-%m-%d %H:%i:%s') as change_time
            FROM change_log
            WHERE project_code = %s
            ORDER BY change_time DESC
            """
            
            cursor.execute(sql, (project_code,))
            history = cursor.fetchall()
            
            return {"history": history}
                
    except Exception as e:
        logger.error(f"获取项目变更历史错误: {str(e)}")
        raise e
    finally:
        if conn:
            conn.close()

def parse_query_string(event):
    """解析查询参数"""
    if 'queryString' in event:
        return event['queryString']
    elif 'queryStringParameters' in event:
        return event['queryStringParameters'] or {}
    return {}

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析查询参数
        params = parse_query_string(event)
        
        # 获取项目编码
        project_code = params.get('project_code')
        
        if not project_code:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 400,
                    'message': '项目编码不能为空',
                    'data': None
                }, ensure_ascii=False)
            }
        
        # 获取项目变更历史
        result = get_project_change_history(project_code)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 200,
                'message': 'success',
                'data': result
            }, ensure_ascii=False)
        }
    except ValueError as e:
        return {
            'statusCode': 400,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 400,
                'message': str(e),
                'data': None
            }, ensure_ascii=False)
        }
    except Exception as e:
        logger.error(f"处理请求错误: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'获取项目变更历史失败: {str(e)}',
                'data': None
            }, ensure_ascii=False)
        } 