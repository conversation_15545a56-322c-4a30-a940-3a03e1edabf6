#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取数据库选项云函数

功能：获取系统中各种选项列表，如投资主体、负责部门等
接口：HTTP GET
参数：
  - action: 操作类型（必填）
    - getInvestmentEntities: 获取投资主体列表 
    - getResponsibleDepartments: 获取负责部门列表

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "entities": [投资主体列表]  // 当action为getInvestmentEntities时
    或
    "departments": [负责部门列表]  // 当action为getResponsibleDepartments时
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def get_investment_entities():
    """
    获取投资主体列表
    :return: 投资主体列表
    """
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 从Project_Account_Book表中查询所有不重复的投资主体
            sql = """
            SELECT DISTINCT investment_entity 
            FROM Project_Account_Book 
            WHERE investment_entity IS NOT NULL AND investment_entity <> ''
            ORDER BY investment_entity
            """
            cursor.execute(sql)
            entities = [row['investment_entity'] for row in cursor.fetchall()]
            
            # 记录返回的投资主体数量
            logger.info(f"查询结果: 共返回 {len(entities)} 个投资主体")
            
            return {"entities": entities}
    except Exception as e:
        logger.error(f"获取投资主体列表错误: {str(e)}")
        raise e
    finally:
        if conn:
            conn.close()

def get_responsible_departments():
    """
    获取负责部门列表
    :return: 负责部门列表
    """
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 从users表中查询所有不重复的部门
            sql = """
            SELECT DISTINCT department_name 
            FROM users 
            WHERE department_name IS NOT NULL AND department_name <> ''
            ORDER BY department_name
            """
            logger.info(f"执行SQL查询负责部门: {sql}")
            cursor.execute(sql)
            departments = [row['department_name'] for row in cursor.fetchall()]
            
            # 如果从users表中没有获取到足够的部门数据，则从Project_Account_Book表中查询
            if len(departments) <= 1:
                logger.warning(f"从users表获取的部门数量不足: {len(departments)}，尝试从Project_Account_Book表获取")
                backup_sql = """
                SELECT DISTINCT responsible_department 
                FROM Project_Account_Book 
                WHERE responsible_department IS NOT NULL AND responsible_department <> ''
                ORDER BY responsible_department
                """
                logger.info(f"执行备用SQL查询负责部门: {backup_sql}")
                cursor.execute(backup_sql)
                backup_departments = [row['responsible_department'] for row in cursor.fetchall()]
                
                # 合并两个来源的部门列表并去重
                departments = list(set(departments + backup_departments))
            
            # 过滤空值并排序
            departments = [dept for dept in departments if dept and dept.strip()]
            departments.sort()
            
            # 记录返回的部门数量
            logger.info(f"查询结果: 共返回 {len(departments)} 个负责部门: {departments}")
            
            return {"departments": departments}
    except Exception as e:
        logger.error(f"获取负责部门列表错误: {str(e)}")
        raise e
    finally:
        if conn:
            conn.close()

def parse_query_string(event):
    """解析查询字符串"""
    if 'queryString' in event:
        return event['queryString']
    elif 'queryStringParameters' in event and event['queryStringParameters']:
        return event['queryStringParameters']
    return {}

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析请求参数
        params = parse_query_string(event)
        action = params.get('action')
        
        if not action:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 400,
                    'message': '缺少必要参数: action',
                    'data': None
                }, ensure_ascii=False)
            }
        
        # 根据操作类型执行不同的操作
        if action == 'getInvestmentEntities':
            data = get_investment_entities()
            message = "获取投资主体列表成功"
        elif action == 'getResponsibleDepartments':
            data = get_responsible_departments()
            message = "获取负责部门列表成功"
        else:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 400,
                    'message': f'不支持的操作类型: {action}',
                    'data': None
                }, ensure_ascii=False)
            }
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 200,
                'message': message,
                'data': data
            }, ensure_ascii=False)
        }
    except Exception as e:
        logger.error(f"处理请求错误: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, ensure_ascii=False)
        } 