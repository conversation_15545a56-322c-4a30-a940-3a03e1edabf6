#!/usr/bin/env python3
"""
Excel和PPT的VB.NET/COM解析方式 vs 现有方式对比分析
"""

def analyze_current_vs_com_approach():
    """分析当前方式 vs COM方式的区别"""
    
    analysis = {
        "Excel解析对比": {
            "当前方式": {
                "技术栈": ["openpyxl", "xlrd", "CSV解析"],
                "优点": [
                    "轻量级，总共15MB",
                    "不依赖Office软件",
                    "跨平台兼容",
                    "纯Python实现"
                ],
                "缺点": [
                    "复杂公式无法计算",
                    "格式信息丢失",
                    "图表无法解析",
                    "宏和VBA代码无法执行",
                    "某些Excel功能不支持"
                ],
                "支持格式": [".xlsx", ".xls", "CSV"],
                "解析内容": ["纯文本", "数字", "基本表格结构"]
            },
            "VB.NET/COM方式": {
                "技术栈": ["win32com.client", "Excel.Application"],
                "优点": [
                    "完美兼容所有Excel功能",
                    "自动计算公式",
                    "保持格式信息",
                    "支持图表解析",
                    "可执行宏和VBA",
                    "处理复杂Excel文件"
                ],
                "缺点": [
                    "需要安装Microsoft Excel",
                    "仅限Windows环境",
                    "资源占用较大",
                    "启动速度较慢"
                ],
                "支持格式": ["所有Excel支持的格式"],
                "解析内容": ["完整Excel内容", "计算结果", "格式信息", "图表数据"]
            }
        },
        
        "PPT解析对比": {
            "当前方式": {
                "技术栈": ["python-pptx", "oletools", "MonkeyOCR"],
                "优点": [
                    "支持.pptx格式",
                    "集成视觉大模型识别图片",
                    "不依赖Office软件",
                    "跨平台兼容"
                ],
                "缺点": [
                    "老版本.ppt支持有限",
                    "动画效果无法解析",
                    "复杂布局可能错乱",
                    "图片识别依赖外部API",
                    "某些特殊元素无法处理"
                ],
                "支持格式": [".pptx", "部分.ppt"],
                "解析内容": ["文本", "表格", "图片(通过OCR)"]
            },
            "VB.NET/COM方式": {
                "技术栈": ["win32com.client", "PowerPoint.Application"],
                "优点": [
                    "完美支持所有PPT格式",
                    "保持原始布局",
                    "支持动画和过渡效果",
                    "可导出为各种格式",
                    "处理复杂演示文稿",
                    "支持嵌入对象"
                ],
                "缺点": [
                    "需要安装Microsoft PowerPoint",
                    "仅限Windows环境",
                    "资源占用较大",
                    "图片仍需OCR识别"
                ],
                "支持格式": ["所有PowerPoint支持的格式"],
                "解析内容": ["完整PPT内容", "布局信息", "动画数据", "嵌入对象"]
            }
        }
    }
    
    return analysis

def create_excel_com_parser():
    """创建Excel的COM解析器代码"""
    
    excel_com_code = '''
def _parse_excel_with_com(self, file_content: bytes) -> str:
    """使用Excel COM对象解析Excel文件"""
    try:
        import tempfile
        import os
        
        logger.info("尝试使用Excel COM解析Excel文件")
        
        # 检查是否在Windows环境
        if os.name != 'nt':
            logger.info("非Windows环境，跳过Excel COM解析")
            return ""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # 尝试使用win32com
            import win32com.client
            
            excel = None
            workbook = None
            
            try:
                # 创建Excel应用程序对象
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False
                excel.DisplayAlerts = False
                
                # 打开工作簿
                workbook = excel.Workbooks.Open(temp_file_path)
                
                content_lines = []
                
                # 遍历所有工作表
                for sheet_idx in range(1, workbook.Sheets.Count + 1):
                    sheet = workbook.Sheets(sheet_idx)
                    sheet_name = sheet.Name
                    
                    content_lines.append(f"=== 工作表: {sheet_name} ===")
                    
                    # 获取使用区域
                    used_range = sheet.UsedRange
                    if used_range:
                        # 获取数据
                        data = used_range.Value
                        
                        if data:
                            # 处理单行数据
                            if not isinstance(data[0], (list, tuple)):
                                data = [data]
                            
                            # 转换为表格
                            table_data = []
                            for row in data:
                                if isinstance(row, (list, tuple)):
                                    row_values = [str(cell) if cell is not None else '' for cell in row]
                                else:
                                    row_values = [str(row) if row is not None else '']
                                table_data.append(row_values)
                            
                            # 转换为Markdown表格
                            if table_data:
                                markdown_table = self._convert_to_markdown_table(table_data)
                                content_lines.append(markdown_table)
                    
                    content_lines.append("")  # 工作表之间添加空行
                
                if content_lines:
                    logger.info(f"Excel COM解析成功，提取内容长度: {len(''.join(content_lines))}")
                    return '\\n'.join(content_lines)
                
            finally:
                # 清理资源
                if workbook:
                    workbook.Close(SaveChanges=False)
                if excel:
                    excel.Quit()
                    
        except ImportError:
            logger.info("win32com模块未安装")
        except Exception as e:
            logger.info(f"Excel COM解析失败: {str(e)}")
        
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            except:
                pass
        
        return ""
        
    except Exception as e:
        logger.error(f"Excel COM解析失败: {str(e)}")
        return ""
'''
    
    return excel_com_code

def create_ppt_com_parser():
    """创建PPT的COM解析器代码"""
    
    ppt_com_code = '''
def _parse_ppt_with_com(self, file_content: bytes) -> str:
    """使用PowerPoint COM对象解析PPT文件"""
    try:
        import tempfile
        import os
        
        logger.info("尝试使用PowerPoint COM解析PPT文件")
        
        # 检查是否在Windows环境
        if os.name != 'nt':
            logger.info("非Windows环境，跳过PowerPoint COM解析")
            return ""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pptx') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        try:
            # 尝试使用win32com
            import win32com.client
            
            ppt = None
            presentation = None
            
            try:
                # 创建PowerPoint应用程序对象
                ppt = win32com.client.Dispatch("PowerPoint.Application")
                ppt.Visible = False
                
                # 打开演示文稿
                presentation = ppt.Presentations.Open(temp_file_path, ReadOnly=True, WithWindow=False)
                
                content_lines = []
                
                # 遍历所有幻灯片
                for slide_idx in range(1, presentation.Slides.Count + 1):
                    slide = presentation.Slides(slide_idx)
                    
                    content_lines.append(f"=== 幻灯片 {slide_idx} ===")
                    
                    # 遍历幻灯片中的所有形状
                    for shape_idx in range(1, slide.Shapes.Count + 1):
                        shape = slide.Shapes(shape_idx)
                        
                        # 处理文本内容
                        if hasattr(shape, 'TextFrame') and shape.TextFrame.HasText:
                            text_content = shape.TextFrame.TextRange.Text.strip()
                            if text_content:
                                content_lines.append(text_content)
                        
                        # 处理表格
                        if hasattr(shape, 'Table') and shape.HasTable:
                            table = shape.Table
                            for row_idx in range(1, table.Rows.Count + 1):
                                row_values = []
                                for col_idx in range(1, table.Columns.Count + 1):
                                    cell = table.Cell(row_idx, col_idx)
                                    cell_text = cell.Shape.TextFrame.TextRange.Text.strip()
                                    row_values.append(cell_text)
                                
                                if any(val for val in row_values):
                                    row_text = '\\t'.join(row_values)
                                    content_lines.append(row_text)
                        
                        # 处理图片（标记需要进一步处理）
                        if shape.Type == 13:  # msoLinkedPicture or msoPicture
                            content_lines.append(f"[幻灯片{slide_idx}_图片内容_需要OCR识别]")
                    
                    content_lines.append("")  # 幻灯片之间添加空行
                
                if content_lines:
                    logger.info(f"PowerPoint COM解析成功，提取内容长度: {len(''.join(content_lines))}")
                    return '\\n'.join(content_lines)
                
            finally:
                # 清理资源
                if presentation:
                    presentation.Close()
                if ppt:
                    ppt.Quit()
                    
        except ImportError:
            logger.info("win32com模块未安装")
        except Exception as e:
            logger.info(f"PowerPoint COM解析失败: {str(e)}")
        
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            except:
                pass
        
        return ""
        
    except Exception as e:
        logger.error(f"PowerPoint COM解析失败: {str(e)}")
        return ""
'''
    
    return ppt_com_code

def recommend_approach():
    """推荐使用方案"""
    
    recommendations = {
        "Excel解析推荐": {
            "生产环境": "保持当前方式（openpyxl + xlrd）",
            "原因": [
                "轻量级，部署简单",
                "跨平台兼容性好",
                "满足大部分Excel解析需求",
                "不依赖Office软件"
            ],
            "COM方式适用场景": [
                "需要计算复杂公式",
                "处理包含宏的Excel文件",
                "需要保持完整格式信息",
                "Windows环境且已安装Excel"
            ]
        },
        
        "PPT解析推荐": {
            "生产环境": "混合方式：COM优先，python-pptx备用",
            "原因": [
                "COM方式对.ppt/.pptx支持更完整",
                "可以更好地保持布局结构",
                "python-pptx作为跨平台备用方案"
            ],
            "实施策略": [
                "Windows环境优先使用COM解析",
                "COM失败时回退到python-pptx",
                "图片识别继续使用MonkeyOCR"
            ]
        }
    }
    
    return recommendations

if __name__ == "__main__":
    print("📊 Excel和PPT解析方式对比分析")
    print("=" * 60)
    
    # 分析对比
    analysis = analyze_current_vs_com_approach()
    
    print("🔍 Excel解析对比:")
    excel_analysis = analysis["Excel解析对比"]
    print(f"当前方式优点: {excel_analysis['当前方式']['优点']}")
    print(f"COM方式优点: {excel_analysis['VB.NET/COM方式']['优点']}")
    
    print("\n🔍 PPT解析对比:")
    ppt_analysis = analysis["PPT解析对比"]
    print(f"当前方式优点: {ppt_analysis['当前方式']['优点']}")
    print(f"COM方式优点: {ppt_analysis['VB.NET/COM方式']['优点']}")
    
    # 推荐方案
    recommendations = recommend_approach()
    print("\n💡 推荐方案:")
    print(f"Excel: {recommendations['Excel解析推荐']['生产环境']}")
    print(f"PPT: {recommendations['PPT解析推荐']['生产环境']}")
    
    print("\n✅ 分析完成")
