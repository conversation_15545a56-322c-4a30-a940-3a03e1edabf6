import{_ as N,c as d,p as B,o as H,d as M,e as p,f as m,g as l,A as S,t as k,F as P,D as R,G as U,h as w,w as x,i as A,a5 as O,j as q,I as y,E as D}from"./index-76121fa4.js";const F={class:"ai-assistant-container"},K={class:"chat-header"},j={class:"chat-container"},z={key:0,class:"message-content"},G=["innerHTML"],J={key:0,class:"message assistant-message"},Q={class:"input-area"},W={__name:"ai_assistant",setup(X){const _=d(""),o=d([{role:"assistant",content:`您好！我是PMO数据库助手，可以帮您查询项目数据、统计信息和进度报告。

您可以按投资主体/业务类型查询相关项目，例如：
- 查询汽租的所有项目
- 小贷有哪些进行中的项目
- 金租的项目完成情况
- 集团财务相关项目

系统支持的投资主体包括：保险、汽租、商租、金租、征信、资管、小贷、集团、不动产、担保、金服等。

请问有什么可以帮您？`}]),s=d(!1),f=d(null),v=d("connecting"),I=B(()=>{switch(v.value){case"ok":return`数据库连接正常 | 表数量：${$.value}`;case"error":return`数据库连接失败: ${b.value}`;default:return"正在连接数据库..."}}),$=d(0),b=d(""),E=async()=>{try{const t=localStorage.getItem("token"),e=await O.get(`/api/v1/db/status?token=${encodeURIComponent(t)}`);e.data&&e.data.status==="ok"?(v.value="ok",$.value=e.data.tables_count||0):(v.value="error",b.value=e.data.message||"未知错误")}catch(t){v.value="error",b.value=t.message||"连接失败",console.error("API状态检查失败:",t)}},C=async()=>{const t=_.value.trim();if(!(!t||s.value)){o.value.push({role:"user",content:t}),_.value="",await y(),h(),s.value=!0;try{o.value.push({role:"assistant",content:"正在查询数据库，复杂查询可能需要几分钟时间，请耐心等待..."}),await y(),h();const e=localStorage.getItem("token"),r=new EventSource(`/api/v1/db/chat/stream?query=${encodeURIComponent(t)}&token=${encodeURIComponent(e)}`);let c="";r.onmessage=a=>{try{const n=JSON.parse(a.data);n.content&&(c===""?(c=n.content,o.value[o.value.length-1].content=c):(c+=n.content,o.value[o.value.length-1].content=c),y(()=>{h()})),n.error&&(D.error(`错误: ${n.error}`),o.value.push({role:"assistant",content:`抱歉，发生了错误: ${n.error}`}),r.close(),s.value=!1)}catch(n){console.error("解析消息失败:",n)}},r.addEventListener("done",()=>{r.close(),s.value=!1}),r.onerror=a=>{console.error("事件流错误:",a),r.close(),s.value=!1,c===""&&o.value.push({role:"assistant",content:"抱歉，处理您的请求时发生错误，请稍后再试。"})},setTimeout(()=>{s.value&&c===""&&(r.close(),s.value=!1,o.value.push({role:"assistant",content:"抱歉，请求超时，请稍后再试。"}))},48e4)}catch(e){console.error("发送消息错误:",e),s.value=!1,o.value.push({role:"assistant",content:`抱歉，发生了错误: ${e.message}`})}}},T=t=>{if(!t)return"";let e=t.replace(/```sql([\s\S]*?)```/g,'<div class="code-block"><pre>$1</pre></div>');e=e.replace(/```([\s\S]*?)```/g,'<div class="code-block"><pre>$1</pre></div>');const r=/\|(.+)\|[\r\n]\|[-:| ]+\|[\r\n]((?:\|.+\|[\r\n])+)/g;return e=e.replace(r,(c,a,n)=>{const V=a.split("|").map(i=>i.trim()).filter(i=>i),L=n.trim().split(`
`).map(i=>i.split("|").map(g=>g.trim()).filter(g=>g!==""));let u='<table class="message-table"><thead><tr>';return V.forEach(i=>{u+=`<th>${i}</th>`}),u+="</tr></thead><tbody>",L.forEach(i=>{u+="<tr>",i.forEach(g=>{u+=`<td>${g}</td>`}),u+="</tr>"}),u+="</tbody></table>",u}),e=e.replace(/\n/g,"<br>"),e},h=()=>{f.value&&(f.value.scrollTop=f.value.scrollHeight)};return H(()=>{E(),h()}),(t,e)=>{const r=M("el-button"),c=M("el-input");return p(),m("div",F,[l("div",K,[e[1]||(e[1]=l("h2",null,"PMO 数据库助手",-1)),e[2]||(e[2]=l("p",{class:"subtitle"},"使用自然语言查询项目管理数据",-1)),l("div",{class:S(["status-bar",{"status-ok":v.value==="ok","status-error":v.value==="error"}])},k(I.value),3)]),l("div",j,[l("div",{class:"messages",ref_key:"messagesContainer",ref:f},[(p(!0),m(P,null,R(o.value,(a,n)=>(p(),m("div",{key:n,class:S(["message",a.role==="user"?"user-message":"assistant-message"])},[a.role==="user"?(p(),m("div",z,k(a.content),1)):(p(),m("div",{key:1,class:"message-content",innerHTML:T(a.content)},null,8,G))],2))),128)),s.value?(p(),m("div",J,e[3]||(e[3]=[l("div",{class:"loading-indicator"},[l("span"),l("span"),l("span")],-1)]))):U("",!0)],512),l("div",Q,[w(c,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=a=>_.value=a),placeholder:"输入您的问题，例如：查询最近10个逾期项目",disabled:s.value,onKeyup:A(C,["enter"])},{append:x(()=>[w(r,{icon:s.value?"Loading":"Position",disabled:s.value,onClick:C},{default:x(()=>[q(k(s.value?"请稍候":"发送"),1)]),_:1},8,["icon","disabled"])]),_:1},8,["modelValue","disabled"])])])])}}},Z=N(W,[["__scopeId","data-v-b1b54d46"]]);export{Z as default};
