import{J as t}from"./index-76121fa4.js";function c(e={}){return t({url:"/project/list",method:"get",params:e})}function n(e){const o=e.project_code;return o?(console.log(`创建项目，项目编号: ${o}，数据:`,e),t({url:"/project/create",method:"post",data:{operation:"add",...e}})):Promise.reject(new Error("项目编号不能为空"))}function u(e,o){return t({url:`/project/${e}/update`,method:"post",data:o})}function p(){return t({url:"/project/options",method:"get"})}function s(e){return t({url:`/project/${e}`,method:"delete",data:{operation:"delete"}})}export{p as a,n as c,s as d,c as g,u};
