import{g as P,b as R,d as W}from"./team-e147e167.js";import{_ as q,c as D,r as G,p as H,o as K,d as c,L as Q,e as d,f as m,h as a,w as t,E as b,g as V,j as y,B as X,N as Z,t as k,m as ee}from"./index-76121fa4.js";const le={class:"users-list"},ae={class:"card-header"},se={class:"header-actions"},te={class:"filter-bar"},oe={key:0},ne=["onDblclick"],re={key:0},ie=["onDblclick"],de={key:0},ue=["onDblclick"],me={key:0},_e=["onDblclick"],ce={key:0},pe=["onDblclick"],fe={key:0},ve=["onDblclick"],be={key:0},ge=["onDblclick"],ye={key:0,class:"compact-buttons"},Ue={key:1},ke={__name:"Index",setup(De){const w=D(!1),p=D([]),f=D([]),x=D(0),u=G({role:"",status:"",searchText:""}),C=H(()=>p.value.filter(e=>{if(u.role&&e.role!=u.role||u.status&&e.is_disabled!==u.status)return!1;if(u.searchText){const s=u.searchText.toLowerCase();return e.UserID&&e.UserID.toLowerCase().includes(s)||e.name&&e.name.toLowerCase().includes(s)||e.username&&e.username.toLowerCase().includes(s)||e.department_name&&e.department_name.toLowerCase().includes(s)||e.company_name&&e.company_name.toLowerCase().includes(s)}return!0})),N=e=>({1:"管理员",2:"部门管理员",3:"普通用户",4:"只读用户",5:"外部用户"})[e]||"普通用户",I=async()=>{var e;w.value=!0;try{const s=await P(u.searchText);s.code===200&&((e=s.data)!=null&&e.users)?(p.value=s.data.users.map(i=>({...i,isEditing:!1,isNew:!1})),f.value=JSON.parse(JSON.stringify(p.value)),x.value=p.value.length):b({message:s.message||"获取人员列表失败",type:"warning"})}catch(s){b({message:s.message||"获取人员列表失败",type:"error"})}finally{w.value=!1}},E=()=>{I()},U=e=>{f.value.findIndex(i=>i.UserID===e.UserID)===-1&&f.value.push(JSON.parse(JSON.stringify(e))),e.isEditing=!0},z=e=>{if(e.isNew)p.value=p.value.filter(s=>s!==e);else{const s=f.value.find(i=>i.UserID===e.UserID);s&&Object.assign(e,JSON.parse(JSON.stringify(s))),e.isEditing=!1}},L=async e=>{var s,i,r;try{if(console.log("开始保存用户:",e),!e.name||!e.UserID){b({message:"姓名和工号不能为空",type:"error"});return}const o={UserID:e.UserID,name:e.name,username:e.username||"",company_name:e.company_name||"",department_name:e.department_name||"",LaborCost:e.LaborCost||0,role:e.role||3,is_disabled:e.is_disabled||"N"},_=await R(o,"admin");if(console.log("保存用户API响应:",_),_&&(_.code===200||_.statusCode===200)){b({message:e.isNew?"新增人员成功":"更新人员成功",type:"success"}),e.isEditing=!1,e.isNew=!1;const g=f.value.findIndex(h=>h.UserID===e.UserID);g!==-1?f.value[g]=JSON.parse(JSON.stringify(e)):f.value.push(JSON.parse(JSON.stringify(e))),e.isNew&&I()}else console.error("保存失败，响应不符合预期:",_),b({message:_&&_.message||"保存失败",type:"error"})}catch(o){console.error("保存用户错误:",o),console.error("错误详情:",((s=o.response)==null?void 0:s.data)||o.message),b({message:((r=(i=o.response)==null?void 0:i.data)==null?void 0:r.detail)||o.message||"保存失败",type:"error"})}},S=e=>{ee.confirm(`确定要删除用户 ${e.name}(${e.UserID}) 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{$(e)}).catch(()=>{})},$=async e=>{var s,i,r;try{console.log("开始删除用户:",e.UserID);const o=await W(e.UserID,"system");console.log("删除用户API响应:",o),o&&(o.code===200||o.statusCode===200)?(b({message:"删除人员成功",type:"success"}),p.value=p.value.filter(_=>_.UserID!==e.UserID),f.value=f.value.filter(_=>_.UserID!==e.UserID),x.value=p.value.length):(console.error("删除失败，响应不符合预期:",o),b({message:o&&o.message||"删除失败",type:"error"}))}catch(o){console.error("删除用户错误:",o),console.error("错误详情:",((s=o.response)==null?void 0:s.data)||o.message),b({message:((r=(i=o.response)==null?void 0:i.data)==null?void 0:r.detail)||o.message||"删除失败",type:"error"})}},T=()=>{const e={UserID:O(),name:"",username:"",company_name:"",department_name:"",LaborCost:0,role:3,is_disabled:"N",isEditing:!0,isNew:!0};p.value.unshift(e)},O=()=>"NEW_"+Date.now().toString(),J=()=>{},B=()=>{u.role="",u.status="",u.searchText=""};return K(()=>{I()}),(e,s)=>{const i=c("el-button"),r=c("el-option"),o=c("el-select"),_=c("el-form-item"),g=c("el-input"),h=c("el-form"),v=c("el-table-column"),Y=c("el-input-number"),F=c("el-tag"),M=c("el-table"),j=c("el-card"),A=Q("loading");return d(),m("div",le,[a(j,{class:"users-card"},{header:t(()=>[V("div",ae,[s[5]||(s[5]=V("span",null,"人员管理",-1)),V("div",se,[a(i,{type:"primary",size:"small",onClick:E},{default:t(()=>s[3]||(s[3]=[y("刷新")])),_:1,__:[3]}),a(i,{type:"success",size:"small",onClick:T},{default:t(()=>s[4]||(s[4]=[y("新增人员")])),_:1,__:[4]})])])]),default:t(()=>[V("div",te,[a(h,{inline:!0,model:u,size:"small"},{default:t(()=>[a(_,{label:"角色"},{default:t(()=>[a(o,{modelValue:u.role,"onUpdate:modelValue":s[0]||(s[0]=l=>u.role=l),clearable:"",placeholder:"选择角色"},{default:t(()=>[a(r,{label:"全部",value:""}),a(r,{label:"管理员",value:"1"}),a(r,{label:"部门管理员",value:"2"}),a(r,{label:"普通用户",value:"3"}),a(r,{label:"只读用户",value:"4"}),a(r,{label:"外部用户",value:"5"})]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"状态"},{default:t(()=>[a(o,{modelValue:u.status,"onUpdate:modelValue":s[1]||(s[1]=l=>u.status=l),clearable:"",placeholder:"选择状态"},{default:t(()=>[a(r,{label:"全部",value:""}),a(r,{label:"在职",value:"N"}),a(r,{label:"离职",value:"Y"})]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"搜索"},{default:t(()=>[a(g,{modelValue:u.searchText,"onUpdate:modelValue":s[2]||(s[2]=l=>u.searchText=l),placeholder:"姓名/工号/部门",clearable:""},null,8,["modelValue"])]),_:1}),a(_,null,{default:t(()=>[a(i,{type:"primary",onClick:J},{default:t(()=>s[6]||(s[6]=[y("查询")])),_:1,__:[6]}),a(i,{onClick:B},{default:t(()=>s[7]||(s[7]=[y("重置")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),X((d(),Z(M,{data:C.value,border:"",size:"small",style:{width:"100%"},"cell-style":{padding:"4px 0"},"header-cell-style":{padding:"6px 0",fontSize:"13px"}},{default:t(()=>[a(v,{prop:"UserID",label:"工号"}),a(v,{prop:"name",label:"姓名"},{default:t(l=>[l.row.isEditing?(d(),m("div",oe,[a(g,{modelValue:l.row.name,"onUpdate:modelValue":n=>l.row.name=n,size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(l.row.name),41,ne))]),_:1}),a(v,{prop:"username",label:"用户名"},{default:t(l=>[l.row.isEditing?(d(),m("div",re,[a(g,{modelValue:l.row.username,"onUpdate:modelValue":n=>l.row.username=n,size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(l.row.username),41,ie))]),_:1}),a(v,{prop:"company_name",label:"公司"},{default:t(l=>[l.row.isEditing?(d(),m("div",de,[a(g,{modelValue:l.row.company_name,"onUpdate:modelValue":n=>l.row.company_name=n,size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(l.row.company_name),41,ue))]),_:1}),a(v,{prop:"department_name",label:"部门"},{default:t(l=>[l.row.isEditing?(d(),m("div",me,[a(g,{modelValue:l.row.department_name,"onUpdate:modelValue":n=>l.row.department_name=n,size:"small"},null,8,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(l.row.department_name),41,_e))]),_:1}),a(v,{prop:"LaborCost",label:"人力成本"},{default:t(l=>[l.row.isEditing?(d(),m("div",ce,[a(Y,{modelValue:l.row.LaborCost,"onUpdate:modelValue":n=>l.row.LaborCost=n,size:"small",min:0,precision:2},null,8,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(l.row.LaborCost),41,pe))]),_:1}),a(v,{prop:"role",label:"角色"},{default:t(l=>[l.row.isEditing?(d(),m("div",fe,[a(o,{modelValue:l.row.role,"onUpdate:modelValue":n=>l.row.role=n,size:"small"},{default:t(()=>[a(r,{label:"管理员",value:1}),a(r,{label:"部门管理员",value:2}),a(r,{label:"普通用户",value:3}),a(r,{label:"只读用户",value:4}),a(r,{label:"外部用户",value:5})]),_:2},1032,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},k(N(l.row.role)),41,ve))]),_:1}),a(v,{prop:"is_disabled",label:"状态"},{default:t(l=>[l.row.isEditing?(d(),m("div",be,[a(o,{modelValue:l.row.is_disabled,"onUpdate:modelValue":n=>l.row.is_disabled=n,size:"small"},{default:t(()=>[a(r,{label:"在职",value:"N"}),a(r,{label:"离职",value:"Y"})]),_:2},1032,["modelValue","onUpdate:modelValue"])])):(d(),m("div",{key:1,onDblclick:n=>U(l.row)},[a(F,{size:"small",type:l.row.is_disabled==="Y"?"danger":"success"},{default:t(()=>[y(k(l.row.is_disabled==="Y"?"离职":"在职"),1)]),_:2},1032,["type"])],40,ge))]),_:1}),a(v,{label:"操作"},{default:t(l=>[l.row.isEditing?(d(),m("div",ye,[a(i,{size:"small",type:"primary",onClick:n=>L(l.row)},{default:t(()=>s[8]||(s[8]=[y("保存")])),_:2,__:[8]},1032,["onClick"]),a(i,{size:"small",onClick:n=>z(l.row)},{default:t(()=>s[9]||(s[9]=[y("取消")])),_:2,__:[9]},1032,["onClick"])])):(d(),m("div",Ue,[a(i,{size:"small",type:"danger",onClick:n=>S(l.row)},{default:t(()=>s[10]||(s[10]=[y(" 删除 ")])),_:2,__:[10]},1032,["onClick"])]))]),_:1})]),_:1},8,["data"])),[[A,w.value]])]),_:1})])}}},Ie=q(ke,[["__scopeId","data-v-be037083"]]);export{Ie as default};
