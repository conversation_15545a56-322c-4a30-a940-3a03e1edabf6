#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的服务器启动脚本
"""

import uvicorn
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动服务器"""
    print("🚀 启动PMO后端服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📍 API文档: http://localhost:8000/docs")
    print("📍 前端地址: http://localhost:3000")
    print("=" * 50)
    
    try:
        # 导入应用
        from main import app
        
        # 启动服务器
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000,
            reload=False,  # 禁用自动重载以提高稳定性
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
