#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量化知识库服务
基于Qdrant的企业级知识库解决方案
"""

import os
import json
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from sentence_transformers import SentenceTransformer
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False

from app.core.logger import get_logger
from app.services.knowledge_service import PMOKnowledgeService

logger = get_logger(__name__)

class VectorKnowledgeService:
    """向量化知识库服务"""
    
    def __init__(self):
        # 禁用向量知识库服务以提升启动速度
        self.base_knowledge_service = PMOKnowledgeService()
        self.collection_name = "pmo_knowledge"
        self.qdrant_client = None
        self.embedding_model = None
        logger.info("✅ 向量知识库服务已禁用（提升启动速度）")
        self.available = False
    
    def _ensure_collection(self):
        """确保集合存在 - 已禁用"""
        pass
    
    def _chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """将长文本分块"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 尝试在句号处分割
            if end < len(text):
                # 向后查找句号
                for i in range(end, min(end + 100, len(text))):
                    if text[i] in '。！？\n':
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def index_knowledge_base(self, force_rebuild: bool = False):
        """索引知识库内容 - 已禁用"""
        logger.info("向量知识库已禁用，跳过索引")
        return False
        
        try:
            logger.info("🔄 开始索引知识库内容...")
            
            # 获取完整知识库内容
            knowledge_content = self.base_knowledge_service.get_knowledge_base(force_refresh=force_rebuild)
            
            # 获取缓存数据用于构建结构化文档
            cache = self.base_knowledge_service.knowledge_cache
            
            documents = []
            
            # 1. 索引项目信息
            if 'red_black_board' in cache:
                red_black = cache['red_black_board']
                progress_list = red_black.get('progressList', [])
                
                for item in progress_list:
                    doc_text = f"""
项目名称: {item.get('project_name', '')}
项目编号: {item.get('project_code', '')}
投资主体: {item.get('investment_entity', '')}
项目状态: {item.get('status', '')}
投资金额: {item.get('annual_investment_plan', 0)}万元
实施阶段项目数: {item.get('implementation_count', 0)}
逾期项目数: {item.get('delayed_count', 0)}
当年人次: {item.get('current_year_person_times', 0)}
人工费: {item.get('labor_cost', 0)}万元
                    """.strip()
                    
                    documents.append({
                        'content': doc_text,
                        'type': 'project_summary',
                        'project_code': item.get('project_code', ''),
                        'investment_entity': item.get('investment_entity', ''),
                        'metadata': item
                    })
            
            # 2. 索引用户信息
            if 'user_data' in cache:
                user_data = cache['user_data']
                users = user_data.get('users', [])
                
                for user in users[:20]:  # 限制用户数量
                    doc_text = f"""
用户姓名: {user.get('name', '')}
用户名: {user.get('username', '')}
部门: {user.get('department_name', '')}
公司: {user.get('company_name', '')}
角色: {user.get('role', '')}
人工成本: {user.get('LaborCost', 0)}元/小时
                    """.strip()
                    
                    documents.append({
                        'content': doc_text,
                        'type': 'user_info',
                        'user_id': user.get('UserID', ''),
                        'department': user.get('department_name', ''),
                        'metadata': user
                    })
            
            # 3. 索引督办信息
            if 'supervision_data' in cache:
                supervision = cache['supervision_data']
                items = supervision.get('supervision_items', [])
                
                for item in items:
                    doc_text = f"""
督办事项: {item.get('item_name', '')}
督办维度: {item.get('dimension', '')}
督办状态: {item.get('status', '')}
创建时间: {item.get('created_at', '')}
描述: {item.get('description', '')}
                    """.strip()
                    
                    documents.append({
                        'content': doc_text,
                        'type': 'supervision',
                        'item_id': str(item.get('id', '')),
                        'dimension': item.get('dimension', ''),
                        'metadata': item
                    })
            
            # 4. 索引完整知识库（分块）
            knowledge_chunks = self._chunk_text(knowledge_content, chunk_size=800, overlap=100)
            for i, chunk in enumerate(knowledge_chunks):
                documents.append({
                    'content': chunk,
                    'type': 'knowledge_chunk',
                    'chunk_id': f"chunk_{i}",
                    'total_chunks': len(knowledge_chunks),
                    'metadata': {'chunk_index': i}
                })
            
            # 生成向量并存储
            if documents:
                self._store_documents(documents)
                logger.info(f"✅ 成功索引 {len(documents)} 个文档")
                return True
            else:
                logger.warning("⚠️ 没有文档需要索引")
                return False
                
        except Exception as e:
            logger.error(f"❌ 索引知识库失败: {str(e)}")
            return False
    
    def _store_documents(self, documents: List[Dict[str, Any]]):
        """存储文档到向量数据库"""
        try:
            # 清空现有数据
            self.qdrant_client.delete_collection(self.collection_name)
            self._ensure_collection()
            
            # 生成向量
            contents = [doc['content'] for doc in documents]
            vectors = self.embedding_model.encode(contents, show_progress_bar=True)
            
            # 构建点数据
            points = []
            for i, (doc, vector) in enumerate(zip(documents, vectors)):
                point = PointStruct(
                    id=i,
                    vector=vector.tolist(),
                    payload={
                        'content': doc['content'],
                        'type': doc['type'],
                        'metadata': doc.get('metadata', {}),
                        'indexed_at': datetime.now().isoformat(),
                        **{k: v for k, v in doc.items() if k not in ['content', 'metadata']}
                    }
                )
                points.append(point)
            
            # 批量插入
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                self.qdrant_client.upsert(
                    collection_name=self.collection_name,
                    points=batch
                )
            
            logger.info(f"✅ 成功存储 {len(points)} 个向量点")
            
        except Exception as e:
            logger.error(f"❌ 存储文档失败: {str(e)}")
            raise
    
    def semantic_search(self, query: str, limit: int = 10, score_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """语义搜索 - 已禁用"""
        logger.info("向量知识库已禁用，返回空结果")
        return []
        
        try:
            # 生成查询向量
            query_vector = self.embedding_model.encode([query])[0]
            
            # 执行搜索
            search_result = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_vector.tolist(),
                limit=limit,
                score_threshold=score_threshold
            )
            
            # 格式化结果
            results = []
            for hit in search_result:
                result = {
                    'content': hit.payload['content'],
                    'score': hit.score,
                    'type': hit.payload['type'],
                    'metadata': hit.payload.get('metadata', {}),
                    **{k: v for k, v in hit.payload.items() 
                       if k not in ['content', 'metadata', 'indexed_at']}
                }
                results.append(result)
            
            logger.info(f"🔍 语义搜索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ 语义搜索失败: {str(e)}")
            return self._fallback_search(query)
    
    def _fallback_search(self, query: str) -> List[Dict[str, Any]]:
        """备用搜索方法"""
        try:
            # 使用基础知识库的关键词搜索
            knowledge_content = self.base_knowledge_service.get_knowledge_base()
            
            # 简单的关键词匹配
            lines = knowledge_content.split('\n')
            matching_lines = []
            
            query_lower = query.lower()
            for line in lines:
                if query_lower in line.lower() and line.strip():
                    matching_lines.append({
                        'content': line.strip(),
                        'score': 0.8,  # 固定分数
                        'type': 'text_match',
                        'metadata': {}
                    })
                    
                    if len(matching_lines) >= 10:
                        break
            
            return matching_lines
            
        except Exception as e:
            logger.error(f"❌ 备用搜索失败: {str(e)}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """获取向量知识库状态"""
        status = {
            'available': self.available,
            'qdrant_installed': QDRANT_AVAILABLE
        }
        
        if self.available:
            try:
                collection_info = self.qdrant_client.get_collection(self.collection_name)
                status.update({
                    'collection_name': self.collection_name,
                    'vector_count': collection_info.vectors_count,
                    'indexed_at': datetime.now().isoformat()
                })
            except Exception as e:
                status['error'] = str(e)
        
        return status

# 全局向量知识库服务实例
vector_knowledge_service = VectorKnowledgeService()
