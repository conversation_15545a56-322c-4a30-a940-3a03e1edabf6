#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WrenAI集成服务 - PMO系统的智能数据分析
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import logging

from app.core.ai_client import ai_client
from app.core.database import get_db_connection

logger = logging.getLogger(__name__)

class WrenAIService:
    """WrenAI集成服务 - 自然语言查询PMO数据"""
    
    def __init__(self):
        self.database_schema = self._init_database_schema()
        self.quick_questions = self._init_quick_questions()
    
    def _init_database_schema(self) -> Dict[str, Any]:
        """初始化数据库结构信息"""
        return {
            "project_plan": {
                "comment": "项目计划表",
                "columns": [
                    {"name": "project_id", "type": "VARCHAR(50)", "comment": "项目ID"},
                    {"name": "category_level1", "type": "VARCHAR(100)", "comment": "一级分类"},
                    {"name": "category_level2", "type": "VARCHAR(100)", "comment": "二级分类"},
                    {"name": "project_name", "type": "VARCHAR(200)", "comment": "项目名称"},
                    {"name": "summary", "type": "TEXT", "comment": "项目摘要"},
                    {"name": "milestone_plan", "type": "TEXT", "comment": "里程碑计划"},
                    {"name": "priority", "type": "VARCHAR(50)", "comment": "优先级"},
                    {"name": "budget_2026", "type": "DECIMAL(15,2)", "comment": "2026年预算"},
                    {"name": "budget_2027", "type": "DECIMAL(15,2)", "comment": "2027年预算"},
                    {"name": "budget_2028", "type": "DECIMAL(15,2)", "comment": "2028年预算"},
                    {"name": "budget_2029", "type": "DECIMAL(15,2)", "comment": "2029年预算"},
                    {"name": "budget_2030", "type": "DECIMAL(15,2)", "comment": "2030年预算"},
                    {"name": "total_budget", "type": "DECIMAL(15,2)", "comment": "总预算"}
                ]
            },
            "project_account_book": {
                "comment": "项目账本表",
                "columns": [
                    {"name": "project_code", "type": "VARCHAR(50)", "comment": "项目编号"},
                    {"name": "total_investment", "type": "DECIMAL(15,2)", "comment": "总投资额"},
                    {"name": "completed_investment", "type": "DECIMAL(15,2)", "comment": "已完成投资"},
                    {"name": "investment_progress", "type": "DECIMAL(5,2)", "comment": "投资进度百分比"},
                    {"name": "budget_year", "type": "INT", "comment": "预算年度"},
                    {"name": "created_at", "type": "DATETIME", "comment": "创建时间"}
                ]
            },
            "project_hours": {
                "comment": "工时记录表",
                "columns": [
                    {"name": "project_code", "type": "VARCHAR(50)", "comment": "项目编号"},
                    {"name": "user_id", "type": "VARCHAR(50)", "comment": "用户ID"},
                    {"name": "work_date", "type": "DATE", "comment": "工作日期"},
                    {"name": "hours", "type": "DECIMAL(4,2)", "comment": "工时数"},
                    {"name": "work_content", "type": "TEXT", "comment": "工作内容"},
                    {"name": "created_at", "type": "DATETIME", "comment": "创建时间"}
                ]
            },
            "supervision_items": {
                "comment": "督办事项表",
                "columns": [
                    {"name": "item_id", "type": "INT", "comment": "事项ID"},
                    {"name": "title", "type": "VARCHAR(200)", "comment": "事项标题"},
                    {"name": "content", "type": "TEXT", "comment": "事项内容"},
                    {"name": "responsible_person", "type": "VARCHAR(100)", "comment": "责任人"},
                    {"name": "completion_deadline", "type": "DATE", "comment": "完成期限"},
                    {"name": "status", "type": "VARCHAR(20)", "comment": "状态"},
                    {"name": "progress_description", "type": "TEXT", "comment": "进展描述"},
                    {"name": "created_at", "type": "DATETIME", "comment": "创建时间"}
                ]
            }
        }
    
    def _init_quick_questions(self) -> List[str]:
        """初始化快捷问题"""
        return [
            "有哪些AI相关的项目？",
            "小贷有什么项目？",
            "项目计划中优先级最高的是哪些？",
            "2026年预算最多的项目是什么？",
            "有多少个重要不紧急的项目？",
            "AI²层分类下有哪些项目？",
            "模型中心有什么项目？",
            "创新中心的项目进展如何？",
            "总预算超过1000万的项目有哪些？",
            "项目的里程碑计划是什么？"
        ]
    
    async def ask_question(self, question: str, user_id: str = "system") -> Dict[str, Any]:
        """处理自然语言问题"""
        try:
            logger.info(f"🤖 用户 {user_id} 提问: {question}")
            
            # 1. 分析问题并生成SQL
            sql_analysis = await ai_client.analyze_sql_query(question, self.database_schema)
            
            # 2. 执行SQL查询
            query_result = await self._execute_sql_query(sql_analysis.get("sql", ""))
            
            # 3. 生成自然语言回答
            answer = await self._generate_answer(question, query_result, sql_analysis)
            
            # 4. 生成图表数据
            chart_data = await self._generate_chart_data(query_result, sql_analysis.get("chart_suggestion", {}))
            
            # 5. 生成洞察分析
            insights = await self._generate_insights(question, query_result)
            
            result = {
                "success": True,
                "question": question,
                "answer": answer,
                "sql": sql_analysis.get("sql", ""),
                "explanation": sql_analysis.get("explanation", ""),
                "data": query_result,
                "chart_data": chart_data,
                "insights": insights,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"✅ 问题处理完成，返回 {len(query_result)} 条数据")
            return result
            
        except Exception as e:
            logger.error(f"❌ 问题处理失败: {str(e)}")
            return {
                "success": False,
                "question": question,
                "answer": f"抱歉，处理您的问题时遇到了错误：{str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_sql_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询"""
        if not sql or sql.strip() == "":
            return []
        
        try:
            # 安全检查：只允许SELECT查询
            sql_upper = sql.upper().strip()
            if not sql_upper.startswith("SELECT"):
                logger.warning(f"⚠️ 非SELECT查询被拒绝: {sql[:100]}")
                return [{"error": "只允许查询操作"}]
            
            # 禁止危险操作
            dangerous_keywords = ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE"]
            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    logger.warning(f"⚠️ 包含危险关键词的查询被拒绝: {keyword}")
                    return [{"error": f"查询包含禁止的操作: {keyword}"}]
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute(sql)
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            
            # 转换为字典列表
            result = []
            for row in rows:
                # 检查row是否已经是字典格式（DictCursor的情况）
                if isinstance(row, dict):
                    # 处理字典格式的行数据
                    row_dict = {}
                    for key, value in row.items():
                        if isinstance(value, datetime):
                            row_dict[key] = value.isoformat()
                        elif value is None:
                            row_dict[key] = None
                        else:
                            row_dict[key] = value
                    result.append(row_dict)
                else:
                    # 处理元组格式的行数据
                    row_dict = {}
                    for i, value in enumerate(row):
                        if isinstance(value, datetime):
                            row_dict[columns[i]] = value.isoformat()
                        elif value is None:
                            row_dict[columns[i]] = None
                        else:
                            row_dict[columns[i]] = value
                    result.append(row_dict)
            
            cursor.close()
            conn.close()

            logger.info(f"📊 SQL查询成功，返回 {len(result)} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"❌ SQL查询失败: {str(e)}")
            return [{"error": f"查询执行失败: {str(e)}"}]
    
    async def _generate_answer(self, question: str, data: List[Dict[str, Any]], sql_analysis: Dict[str, Any]) -> str:
        """生成自然语言回答"""
        try:
            if not data or (len(data) == 1 and "error" in data[0]):
                return f"抱歉，没有找到相关数据来回答您的问题：{question}"
            
            # 构建数据摘要
            data_summary = self._summarize_data(data)
            
            prompt = f"""
            用户问题：{question}
            
            查询结果摘要：
            - 数据条数：{len(data)}
            - 主要字段：{list(data[0].keys()) if data else []}
            - 数据摘要：{data_summary}
            
            请用简洁、专业的中文回答用户的问题，重点突出关键数据和趋势。
            """
            
            answer = await ai_client.generate_response(prompt)
            return answer if answer else "数据查询成功，请查看详细结果。"
            
        except Exception as e:
            logger.error(f"❌ 生成回答失败: {str(e)}")
            return f"查询到 {len(data)} 条数据，请查看详细结果。"
    
    async def _generate_chart_data(self, data: List[Dict[str, Any]], chart_suggestion: Dict[str, Any]) -> Dict[str, Any]:
        """生成图表数据"""
        try:
            if not data or len(data) == 0:
                return {}
            
            chart_type = chart_suggestion.get("type", "table")
            
            if chart_type == "bar" and len(data) <= 20:
                # 生成柱状图数据
                return self._generate_bar_chart(data, chart_suggestion)
            elif chart_type == "line" and len(data) <= 50:
                # 生成折线图数据
                return self._generate_line_chart(data, chart_suggestion)
            elif chart_type == "pie" and len(data) <= 10:
                # 生成饼图数据
                return self._generate_pie_chart(data, chart_suggestion)
            else:
                # 默认表格显示
                return {
                    "type": "table",
                    "title": chart_suggestion.get("title", "查询结果"),
                    "data": data[:100]  # 限制显示前100条
                }
                
        except Exception as e:
            logger.error(f"❌ 生成图表数据失败: {str(e)}")
            return {
                "type": "table",
                "title": "查询结果",
                "data": data[:100]
            }
    
    def _generate_bar_chart(self, data: List[Dict[str, Any]], suggestion: Dict[str, Any]) -> Dict[str, Any]:
        """生成柱状图数据"""
        try:
            x_field = suggestion.get("x_axis", list(data[0].keys())[0])
            y_field = suggestion.get("y_axis", list(data[0].keys())[1] if len(data[0].keys()) > 1 else list(data[0].keys())[0])
            
            labels = [str(row.get(x_field, "")) for row in data]
            values = []
            
            for row in data:
                value = row.get(y_field, 0)
                if isinstance(value, (int, float)):
                    values.append(value)
                else:
                    values.append(0)
            
            return {
                "type": "bar",
                "title": suggestion.get("title", "柱状图"),
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "label": y_field,
                        "data": values,
                        "backgroundColor": "rgba(54, 162, 235, 0.6)"
                    }]
                }
            }
        except Exception as e:
            logger.error(f"生成柱状图失败: {str(e)}")
            return {"type": "table", "data": data}
    
    def _generate_line_chart(self, data: List[Dict[str, Any]], suggestion: Dict[str, Any]) -> Dict[str, Any]:
        """生成折线图数据"""
        # 类似柱状图的实现
        return self._generate_bar_chart(data, suggestion)
    
    def _generate_pie_chart(self, data: List[Dict[str, Any]], suggestion: Dict[str, Any]) -> Dict[str, Any]:
        """生成饼图数据"""
        try:
            label_field = suggestion.get("x_axis", list(data[0].keys())[0])
            value_field = suggestion.get("y_axis", list(data[0].keys())[1] if len(data[0].keys()) > 1 else list(data[0].keys())[0])
            
            labels = [str(row.get(label_field, "")) for row in data]
            values = []
            
            for row in data:
                value = row.get(value_field, 0)
                if isinstance(value, (int, float)):
                    values.append(value)
                else:
                    values.append(0)
            
            return {
                "type": "pie",
                "title": suggestion.get("title", "饼图"),
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "data": values,
                        "backgroundColor": [
                            "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", 
                            "#9966FF", "#FF9F40", "#FF6384", "#C9CBCF"
                        ]
                    }]
                }
            }
        except Exception as e:
            logger.error(f"生成饼图失败: {str(e)}")
            return {"type": "table", "data": data}
    
    async def _generate_insights(self, question: str, data: List[Dict[str, Any]]) -> List[str]:
        """生成数据洞察"""
        try:
            if not data or len(data) == 0:
                return ["暂无数据可供分析"]
            
            insights = []
            
            # 基础统计洞察
            insights.append(f"查询返回了 {len(data)} 条记录")
            
            # 数值字段分析
            numeric_fields = self._get_numeric_fields(data)
            for field in numeric_fields[:3]:  # 最多分析3个数值字段
                values = [row.get(field, 0) for row in data if isinstance(row.get(field), (int, float))]
                if values:
                    avg_val = sum(values) / len(values)
                    max_val = max(values)
                    min_val = min(values)
                    insights.append(f"{field}平均值: {avg_val:.2f}, 最大值: {max_val}, 最小值: {min_val}")
            
            return insights[:5]  # 最多返回5个洞察
            
        except Exception as e:
            logger.error(f"❌ 生成洞察失败: {str(e)}")
            return ["数据分析完成"]
    
    def _summarize_data(self, data: List[Dict[str, Any]]) -> str:
        """数据摘要"""
        if not data:
            return "无数据"
        
        summary = f"共{len(data)}条记录"
        
        # 分析数值字段
        numeric_fields = self._get_numeric_fields(data)
        if numeric_fields:
            summary += f"，包含数值字段: {', '.join(numeric_fields[:3])}"
        
        return summary
    
    def _get_numeric_fields(self, data: List[Dict[str, Any]]) -> List[str]:
        """获取数值字段"""
        if not data:
            return []
        
        numeric_fields = []
        for key, value in data[0].items():
            if isinstance(value, (int, float)) and key not in ['id', 'item_id']:
                numeric_fields.append(key)
        
        return numeric_fields
    
    def get_quick_questions(self) -> List[str]:
        """获取快捷问题"""
        return self.quick_questions
    
    async def get_database_summary(self) -> Dict[str, Any]:
        """获取数据库摘要信息"""
        try:
            summary = {
                "tables": len(self.database_schema),
                "table_info": {}
            }
            
            for table_name, table_info in self.database_schema.items():
                try:
                    # 获取表记录数
                    count_result = await self._execute_sql_query(f"SELECT COUNT(*) as count FROM {table_name}")
                    record_count = count_result[0].get("count", 0) if count_result else 0
                    
                    summary["table_info"][table_name] = {
                        "comment": table_info.get("comment", ""),
                        "columns": len(table_info.get("columns", [])),
                        "records": record_count
                    }
                except Exception as e:
                    logger.warning(f"获取表 {table_name} 信息失败: {str(e)}")
                    summary["table_info"][table_name] = {
                        "comment": table_info.get("comment", ""),
                        "columns": len(table_info.get("columns", [])),
                        "records": 0
                    }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 获取数据库摘要失败: {str(e)}")
            return {"error": str(e)}

# 全局WrenAI服务实例
wren_ai_service = WrenAIService()
