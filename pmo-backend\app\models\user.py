#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户模型 - 参考禅道zt_user表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import hashlib
import pymysql
from app.core.database import get_db_connection, close_db_connection

class User:
    """用户模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.account: str = ""
        self.password: str = ""
        self.role: str = ""
        self.realname: str = ""
        self.nickname: str = ""
        self.commiter: str = ""
        self.avatar: Optional[str] = None
        self.birthday: Optional[date] = None
        self.gender: str = "f"
        self.email: str = ""
        self.skype: str = ""
        self.qq: str = ""
        self.mobile: str = ""
        self.phone: str = ""
        self.address: Optional[str] = None
        self.zipcode: str = ""
        self.join_date: Optional[date] = None
        self.visits: int = 0
        self.ip: str = ""
        self.last_time: Optional[datetime] = None
        self.fails: int = 0
        self.locked: Optional[datetime] = None
        self.feedback: str = "1"
        self.mail: str = "0"
        self.clientStatus: str = "offline"
        self.clientLang: str = "zh-cn"
        self.score: int = 0
        self.scoreLevel: int = 0
        self.deleted: str = "0"
        self.created_at: Optional[datetime] = None
        self.updated_at: Optional[datetime] = None

    @staticmethod
    def hash_password(password: str) -> str:
        """密码MD5加密"""
        return hashlib.md5(password.encode('utf-8')).hexdigest()

    @classmethod
    def get_by_account(cls, account: str) -> Optional['User']:
        """根据账号获取用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_user 
                WHERE account = %s AND deleted = '0'
            """, (account,))
            
            row = cursor.fetchone()
            if row:
                user = cls()
                user._load_from_dict(row)
                return user
            return None
            
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_by_id(cls, user_id: int) -> Optional['User']:
        """根据ID获取用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_user 
                WHERE id = %s AND deleted = '0'
            """, (user_id,))
            
            row = cursor.fetchone()
            if row:
                user = cls()
                user._load_from_dict(row)
                return user
            return None
            
        except Exception as e:
            print(f"获取用户失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def authenticate(cls, account: str, password: str) -> Optional['User']:
        """用户认证"""
        user = cls.get_by_account(account)
        if user and user.password == cls.hash_password(password):
            # 更新登录信息
            user.update_login_info()
            return user
        return None

    @classmethod
    def get_all_users(cls, page: int = 1, page_size: int = 20, keyword: str = "") -> Dict[str, Any]:
        """获取用户列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["deleted = '0'"]
            params = []
            
            if keyword:
                where_conditions.append("(account LIKE %s OR realname LIKE %s OR email LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_user WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT id, account, realname, email, role, gender, mobile, 
                       join_date, visits, last_time, created_at
                FROM zt_user 
                WHERE {where_clause}
                ORDER BY id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            users = cursor.fetchall()
            
            return {
                "users": users,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return {"users": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查账号是否已存在
            cursor.execute("SELECT id FROM zt_user WHERE account = %s", (self.account,))
            if cursor.fetchone():
                raise ValueError(f"账号 {self.account} 已存在")
            
            # 插入用户
            cursor.execute("""
                INSERT INTO zt_user (
                    account, password, role, realname, nickname, email, 
                    gender, mobile, phone, join_date, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                )
            """, (
                self.account, self.password, self.role, self.realname, 
                self.nickname, self.email, self.gender, self.mobile, 
                self.phone, self.join_date
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建用户失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新用户信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_user SET 
                    realname = %s, nickname = %s, email = %s, gender = %s,
                    mobile = %s, phone = %s, role = %s, updated_at = NOW()
                WHERE id = %s
            """, (
                self.realname, self.nickname, self.email, self.gender,
                self.mobile, self.phone, self.role, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新用户失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update_login_info(self) -> bool:
        """更新登录信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_user SET 
                    visits = visits + 1, 
                    last_time = NOW(),
                    fails = 0
                WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新登录信息失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_user SET deleted = '1', updated_at = NOW()
                WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除用户失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            "id": self.id,
            "account": self.account,
            "role": self.role,
            "realname": self.realname,
            "nickname": self.nickname,
            "email": self.email,
            "gender": self.gender,
            "mobile": self.mobile,
            "phone": self.phone,
            "join_date": self.join_date.isoformat() if self.join_date else None,
            "visits": self.visits,
            "last_time": self.last_time.isoformat() if self.last_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
        
        if include_sensitive:
            data["password"] = self.password
            
        return data
