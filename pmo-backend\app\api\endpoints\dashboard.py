from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional
import json

from app.core.security import get_current_user
from app.core.logger import get_logger

# 导入云函数
from app.cloud_functions.getRedBlackBoard.index import main_handler as get_red_black_board_handler

logger = get_logger(__name__)
router = APIRouter()

@router.get("/redblack")
async def get_red_black_board(
    software_only: Optional[bool] = Query(False, description="是否只统计软件项目"),
    category: Optional[str] = Query(None, description="规划类别筛选"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取红黑榜
    """
    try:
        # 构造云函数需要的event对象
        query_params = {}
        if software_only:
            query_params["software_only"] = software_only
        if category:
            query_params["category"] = category

        event = {
            "queryString": query_params
        }
        
        # 调用云函数
        result = get_red_black_board_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"获取红黑榜失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取红黑榜失败")
    except Exception as e:
        logger.error(f"获取红黑榜异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 