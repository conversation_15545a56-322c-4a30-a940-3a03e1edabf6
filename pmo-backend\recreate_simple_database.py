#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建简化的督办管理数据库表结构
只使用3个表：supervision_items, companies, company_progress
"""

import pymysql
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=os.getenv('DB_HOST', 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com'),
        port=int(os.getenv('DB_PORT', 3306)),
        user=os.getenv('DB_USER', 'cyh'),
        password=os.getenv('DB_PASSWORD', 'Qq188788'),
        database=os.getenv('DB_NAME', 'kanban2'),
        charset='utf8mb4'
    )

def recreate_database():
    """重新创建数据库表结构"""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🚀 开始重新创建督办管理数据库表结构...")
        
        # 1. 删除所有相关的旧表
        print("🗑️  删除旧表...")
        old_tables = [
            'company_supervision_progress',
            'company_supervision_status', 
            'company_progress',
            'supervision_status_history',
            'progress_details',
            'supervision_items',
            'companies'
        ]
        
        for table in old_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"   ✅ 删除表 {table}")
            except Exception as e:
                print(f"   ⚠️  删除表 {table} 失败: {e}")
        
        # 2. 创建公司表
        print("🏢 创建公司表...")
        cursor.execute("""
            CREATE TABLE companies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_company_code (company_code),
                INDEX idx_display_order (display_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
        """)
        print("   ✅ companies 表创建成功")
        
        # 3. 创建督办事项表
        print("📋 创建督办事项表...")
        cursor.execute("""
            CREATE TABLE supervision_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sequence_number INT NOT NULL COMMENT '序号',
                work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
                completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                overall_progress VARCHAR(50) DEFAULT 'X 未启动' COMMENT '整体进度',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP NULL COMMENT '软删除时间',
                INDEX idx_sequence_number (sequence_number),
                INDEX idx_work_dimension (work_dimension),
                INDEX idx_deleted_at (deleted_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
        """)
        print("   ✅ supervision_items 表创建成功")
        
        # 4. 创建公司进度表
        print("📊 创建公司进度表...")
        cursor.execute("""
            CREATE TABLE company_progress (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                company_id INT NOT NULL COMMENT '公司ID',
                status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行',
                progress_description TEXT COMMENT '进展描述',
                existing_problems TEXT COMMENT '存在问题',
                next_plan TEXT COMMENT '下一步计划',
                completion_date DATE COMMENT '完成日期',
                updated_by VARCHAR(50) DEFAULT 'system' COMMENT '更新人',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY uk_item_company (supervision_item_id, company_id),
                FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
                FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                INDEX idx_supervision_item_id (supervision_item_id),
                INDEX idx_company_id (company_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司进度表'
        """)
        print("   ✅ company_progress 表创建成功")
        
        # 5. 插入公司数据
        print("🏢 插入公司数据...")
        companies_data = [
            ('caixian', '财险', 1),
            ('shouxian', '寿险', 2),
            ('jinzu', '金租', 3),
            ('ziguan', '资管', 4),
            ('guangzu', '广租', 5),
            ('tongsheng', '通盛', 6),
            ('danbao', '担保', 7),
            ('xiaodai', '小贷', 8),
            ('baoli', '保理', 9),
            ('budongchan', '不动产', 10),
            ('zhengxin', '征信', 11),
            ('jinfu', '金服', 12),
            ('benbu', '本部', 13)
        ]
        
        cursor.executemany("""
            INSERT INTO companies (company_code, company_name, display_order) 
            VALUES (%s, %s, %s)
        """, companies_data)
        print(f"   ✅ 插入 {len(companies_data)} 个公司数据")
        
        # 提交事务
        conn.commit()
        print("✅ 数据库表结构重建完成！")
        
        # 6. 验证表结构
        print("\n📋 验证表结构...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("当前数据库表:")
        for table in tables:
            print(f"   - {table[0]}")
        
        cursor.execute("SELECT COUNT(*) FROM companies")
        company_count = cursor.fetchone()[0]
        print(f"\n公司数据: {company_count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = recreate_database()
    if success:
        print("\n🎉 数据库重建成功！现在可以重新启动后端服务进行测试。")
    else:
        print("\n💥 数据库重建失败！请检查错误信息。")
