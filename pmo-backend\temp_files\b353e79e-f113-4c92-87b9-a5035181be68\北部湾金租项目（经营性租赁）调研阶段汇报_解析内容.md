=== 幻灯片 1 ===
北部湾金融租赁有限公司中小微经营性租赁业务系统（经营性租赁业务）
调研阶段汇报
[幻灯片1_图片内容]: # 文档内容

## 文本内容

宏景动力 www.hgplaw.com

## 表格内容

\begin{tabular}{|c|c|}
\hline
\multicolumn{2}{|c|}{\textbf{宏景动力}} \\ 
\hline
www.hongqiaw.com & \\
\hline
\end{tabular}
深圳宏景动力信息技术有限公司

2024.03.12

=== 幻灯片 2 ===
调研过程
项目目标
1
4
3
2
阶段工作总结
目录Contents
下一阶段工作

=== 幻灯片 3 ===
项目目标
基于当前已建成的融资租赁业务系统基础上，扩展建设可为所有部门使用的强大核心业务系统，该系统主要面向中小微客户，重点支持中小微的经营性租赁业务。
    系统现有的大单租赁业务系统良好集成，并且安全可靠，性能良好，且基于互联网使用，流程配置合理规范、功能直观易用、数据清晰可视，能大幅度提升各部门的工作效率，有效加强公司的业务风险管理水平，为公司新业务的快速发展提供支撑。
+
中小微+经营性租赁业务
=
融资租赁业务系统
助力公司新业务发展
[幻灯片3_图片内容]: # 文档内容

## 文本内容

The image contains a symbol that represents an award or a ribbon. There is no text content in the image.

## 表格内容

\begin{tabular}{|c|}
\hline
\rowcolor[rgb]{0.792, 0.929, 0.984} 
\multicolumn{1}{|c|}{\textbf{}} \\ 
\hline
\end{tabular}
基础
扩展
租赁
业务
系统

=== 幻灯片 4 ===
8
实施方法论
方法论包括启动、需求、方案、构建、测试、上线、验收纵向七大阶段，以及项目、业务、技术横向四项管理。

=== 幻灯片 5 ===
调研过程
项目目标
1
4
3
2
阶段工作总结
目录Contents
下一阶段工作

=== 幻灯片 6 ===
调研范围
租前：项目立项、采购订单
租中：租赁合同、交付确认、保证金及费用收款
租后：还款计划、租金收款、归还验收、租赁物管理、租后巡检、保证金及费用退款
监管报表：EAST报送（经营性租赁、共7张）
调研目的
在项目的实施初期阶段，实施顾问需要对北部湾金融租赁有限公司的整体相关业务进行调研，以了解所有租赁公司业务细节，并进行业务流程与系统的匹配。调研结束之后，经过需求分析，产品匹配和产品开发测试，形成蓝图设计方案。

=== 幻灯片 7 ===
调研计划
[幻灯片7_图片内容]: # 文档内容

## 文本内容

序号 调研时间 调研主题 主要调研内容 拟调研人 1 2024年3月4日 星期一上午 资料搜集及讲解 1.经营性租赁业务讲解 2.搜集公司组织、制度（经营性租赁相关）、经营性租赁工作审批 流程、经营性租赁审批表单等资料 3.业务现状沟通、业务过程遇见的问题以及系统诉求 4.现有IT系统架构、技术架构、网络架构等 综合管理部（科技条线） 2 星期一下午 经营性租赁业务流程 1.经营性租赁业务流程 2.经营性租赁的客户档案信息 4.经营性租赁各环节参与的部门 业务一部、业务二部、业 务三部 3 2024年3月5日 星期二上午 合同及租赁物管理 1.合同审批、签约流程 2.合同等文件模板 3.租后检查 4.租赁物交付、归还 5.租赁资产状态管理 6.合同及租赁物管理的相关审批流程 7.合同结清 合规与风险管理部 4 星期二下午 经营性租赁业务流程 1.经营性租赁业务流程（授信管理部参与的工作） 授信管理部 5 2024年3月6日 星期三 还款计划、租金等费 用收费、凭证 1.经营性租赁还款计划 2.租金、保证金、手续费等费用收付 3.凭证模板 4.核销规则 5.还款计划、租金等费用收支的审批流程 财务管理部 6 星期四上午 补充调研 调研记录整理，对疑难点确认、整理需求调研汇报 7 2024年3月7日 星期四下午 补充调研 调研记录整理，对疑难点确认、整理需求调研汇报 8

## 表格内容

<table>  <tr>    <td>序号</td>    <td>调研时间</td>    <td>调研主题</td>    <td>主要调研内容</td>    <td>拟调研人</td>  </tr>  <tr>    <td rowspan="2" colspan="1">1</td>    <td rowspan="4" colspan="1">2024年3月4日</td>    <td rowspan="2" colspan="1">星期一上午</td>    <td rowspan="2" colspan="1">资料搜集及讲解</td>    <td rowspan="2" colspan="1">1.经营性租赁业务讲解<br>2.搜集公司组织、制度（经营性租赁相关）、经营性租赁工作审批<br>流程、经营性租赁审批表单等资料<br>3.业务现状沟通、业务过程遇见的问题以及系统诉求<br>4.现有IT系统架构、技术架构、网络架构等</td>    <td rowspan="2" colspan="1">综合管理部（科技条线）</td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">2</td>    <td rowspan="2" colspan="1">星期一下午</td>    <td rowspan="2" colspan="1">经营性租赁业务流程</td>    <td rowspan="2" colspan="1">1.经营性租赁业务流程<br>2.经营性租赁的客户档案信息<br>4.经营性租赁各环节参与的部门</td>    <td rowspan="2" colspan="1">业务一部、业务二部、业<br>务三部</td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="4" colspan="1">3</td>    <td rowspan="6" colspan="1">2024年3月5日</td>    <td rowspan="4" colspan="1">星期二上午</td>    <td rowspan="4" colspan="1">合同及租赁物管理</td>    <td rowspan="4" colspan="1">1.合同审批、签约流程<br>2.合同等文件模板<br>3.租后检查<br>4.租赁物交付、归还<br>5.租赁资产状态管理<br>6.合同及租赁物管理的相关审批流程<br>7.合同结清</td>    <td rowspan="4" colspan="1">合规与风险管理部</td>  </tr>  <tr>  </tr>  <tr>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">4</td>    <td rowspan="2" colspan="1">星期二下午</td>    <td rowspan="2" colspan="1">经营性租赁业务流程</td>    <td rowspan="2" colspan="1">1.经营性租赁业务流程（授信管理部参与的工作）</td>    <td rowspan="2" colspan="1">授信管理部</td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">5</td>    <td rowspan="4" colspan="1">2024年3月6日</td>    <td rowspan="4" colspan="1">星期三</td>    <td rowspan="4" colspan="1">还款计划、租金等费<br>用收费、凭证</td>    <td rowspan="4" colspan="1">1.经营性租赁还款计划<br>2.租金、保证金、手续费等费用收付<br>3.凭证模板<br>4.核销规则<br>5.还款计划、租金等费用收支的审批流程</td>    <td rowspan="4" colspan="1">财务管理部</td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">6</td>    <td rowspan="2" colspan="1"></td>    <td rowspan="2" colspan="1"></td>    <td rowspan="2" colspan="1"></td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">7</td>    <td rowspan="4" colspan="1">2024年3月7日</td>    <td rowspan="2" colspan="1">星期四上午</td>    <td rowspan="2" colspan="1">补充调研</td>    <td rowspan="2" colspan="1">调研记录整理，对疑难点确认、整理需求调研汇报</td>    <td rowspan="2" colspan="1"></td>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">8</td>    <td rowspan="2" colspan="1">星期四下午</td>    <td rowspan="2" colspan="1">补充调研</td>    <td rowspan="2" colspan="1">调研记录整理，对疑难点确认、整理需求调研汇报</td>    <td rowspan="2" colspan="1"></td>  </tr>  <tr>  </tr>  <tr>  </tr></table>

=== 幻灯片 8 ===
调研参与情况
序号	调研工作	完成情况
1	访谈部门	综合管理部（IT条线）+业务三部+科融厂商
2	访谈次数	4次线上沟通
3	访谈时间	4个工作日

=== 幻灯片 9 ===
调研过程
项目历程
1
4
3
2
阶段工作总结
目录Contents
下一阶段工作

=== 幻灯片 10 ===
总体流程
[幻灯片10_图片内容]: # 文档内容

## 文本内容

总体流程-经营性租赁 经营性租赁主流程 客户财报 开始 客户管理 法人 采购订单 项目立项 租前阶段 租中阶段 租赁合同 保证金及费用收款 合同签约 交付确认 租赁物管理 还款计划 租金收款 归还验收 保证金及费用退款 合同关闭 结束 租后阶段

## 表格内容

\begin{tabular}{l|p{10cm}}
\toprule
\multicolumn{2}{l}{\textbf{总体流程-经营性租赁}} \\ 
\midrule
\multirow{7}{*}{\rotatebox[origin=c]{90}{租前阶段}} & \textcolor{blue}{\blacksquare} \\
\midrule
\multicolumn{2}{|c|}{\textcolor{blue}{\blacksquare} \textbf{经营性租赁主流程}} \\ 
\midrule
& \multirow{3}{*}{\textcolor{blue}{\blacksquare}} \\ 
& & \textcolor{blue}{\blacksquare} 开始 \\ 
& & \textcolor{blue}{\blacksquare} 客户管理 \\ 
& & \textcolor{blue}{\blacksquare} 法人 \\ 
& & \textcolor{blue}{\blacksquare} 项目立项 \\ 
& & \textcolor{blue}{\blacksquare} 采购订单 \\ 
& & \textcolor{blue}{\blacksquare} 客户财报 \\ 
\midrule
& \multirow{11}{*}{\textcolor{blue}{\blacksquare}} \\ 
& & \textcolor{blue}{\blacksquare} 租赁合同 \\ 
& & \textcolor{blue}{\blacksquare} 保证金及费用收款 \\ 
& & \textcolor{blue}{\blacksquare} 合同签约 \\ 
& & \textcolor{blue}{\blacksquare} 交付确认 \\ 
\midrule
& \multirow{15}{*}{\textcolor{blue}{\blacksquare}} \\ 
& & \textcolor{blue}{\blacksquare} 租赁物管理 \\ 
& & \textcolor{blue}{\blacksquare} 还款计划 \\ 
& & \textcolor{blue}{\blacksquare} 租金收款 \\ 
& & \textcolor{blue}{\blacksquare} 归还验收 \\ 
& & \textcolor{blue}{\blacksquare} 保证金及费用退款 \\ 
& & \textcolor{blue}{\blacksquare} 合同关闭 \\ 
& & \textcolor{blue}{\blacksquare} 结束 \\ 
\bottomrule
\multirow{2}{*}{租中阶段} & \textcolor{blue}{\blacksquare} \\ 
\midrule
& \multirow{8}{*}{\textcolor{blue}{\blacksquare}} \\ 
& & \textcolor{blue}{\blacksquare} 租赁物登记 \\ 
& & \textcolor{blue}{\blacksquare} 租后巡检 \\ 
& & \textcolor{blue}{\blacksquare} 租赁物管理 \\ 
& & \textcolor{blue}{\blacksquare} 租赁物登记 \\ 
& & \textcolor{blue}{\blacksquare} 租赁物登记 \\ 
& & \textcolor{blue}{\blacksquare} 租赁物登记 \\ 
\bottomrule
\multirow{2}{*}{租后阶段} & \textcolor{blue}{\blacksquare} \\ 
\midrule
& \multirow{7}{*}{\textcolor{blue}{\blacksquare}} \\ 
& & \textcolor{blue}{\blacksquare} 合同关闭 \\ 
& & \textcolor{blue}{\blacksquare} 结束 \\ 
\bottomrule
\end{tabular}

=== 幻灯片 11 ===
主要功能点
租前阶段
序号	功能点	需求分析方案	备注
1	客户管理	（1）客户登记：提供法人登记功能	
		（1）客户财报：提供客户财报录入功能	第二阶段实现OCR识别功能
2	项目立项	（1）录入经营性租赁项目立项业务单据，实现工作流程审批
（2）项目立项录入拟租赁的租赁物清单
（3）项目立项体现经营性租赁报价方案	
3	采购订单	（1）记录已完成采购的订单及各订单的租赁物清单
（2）采购订单的租赁物清单的租赁物信息同步至租赁物管理业务单据中	采购流程在OA系统进行，本系统仅记录采购的最终结果

=== 幻灯片 12 ===
主要功能点
租中阶段
序号	功能点	需求分析方案	备注
4	租赁合同	（1）租赁合同基本信息从项目立项获取，租赁合同的租赁物手工从租赁物管理中选择“待租”的租赁物
（2）租赁合同中体现第一回购方、第二回购方
（3）租赁合同附件包括主合同、买卖合同、回购协议
（4）租赁合同实现工作流程审批
（5）对正常结清的合同实现关闭，关闭时检查是否完成归还验收以及保证金费用是否退款
（6）合同体现违约处理方式：扣保证金或收取违约金
（7）实现租赁合同套打
（8）合同关闭时若租赁物处理方式为回购，输入实际的回购方，此时对应的租赁物在租赁物管理中的状态为“已回购”	
		（1）租赁合同变更	第二阶段实现

=== 幻灯片 13 ===
主要功能点
租中阶段
序号	功能点	需求分析方案	备注
5	保证金及费用收款	（1）收取租赁合同约定的保证金、手续费或其他费用收款金额	
6	合同签约	（1）实现租赁合同线下、线上签约流程审批	第二阶段实现线上电子合同签约
7	交付确认	（1）对租赁合同的租赁物进行交付确认
（2）交付确认的最终租赁物可在租赁合同的租赁物清单基础上做删减
（3）交付确认单实现套打功能
（4）交付确认单走工作流程审批	第二阶段实现交付确认单线上电子签约

=== 幻灯片 14 ===
主要功能点
租后阶段
序号	功能点	需求分析方案	备注
8	还款计划	（1）还款计划制作：租赁物清单以交付确认单的租赁物清单为准
（2）还款计划制作：还款总金额根据租赁物清单的所有租赁物资产现值汇总计算
（3）还款计划制作：审核后，还款计划的租赁物状态在租赁物管理中变更为“已租”	
		（1）还款计划变更：只针对展期做变更	第二阶段实现
9	提前还款	（1）提前还款：提前偿还部分租金
（2）提前结清：提前全部结清合同剩余租金	第二阶段实现
10	租金收款	（1）根据还款计划收取每期租金	

=== 幻灯片 15 ===
主要功能点
租后阶段
序号	功能点	需求分析方案	备注
11	租赁物管理	（1）接收采购订单同步的租赁物信息，按租赁物编号单独展示在列表中，显示租赁物采购的价格、租赁物使用年限
（2）用户人工维护租赁物资产原值、累计折旧值、资产残值、资产现值，其中资产现值=资产原值-资产折旧值
（3）显示租赁物状态：待租、已租、已回购
（4）显示租赁物地点：可编辑，如租赁物已租，显示交付地点，如租赁物归还验收，显示归还验收地点	累计折旧值人工线下从财务系统获取
12	租后巡检	（1）记录租后巡检过程	
13	租赁物登记	（1）记录租赁物登记结果（含中登网）	第二阶段实现

=== 幻灯片 16 ===
主要功能点
租后阶段
序号	功能点	需求分析方案	备注
14	归还验收	（1）分为正常结清验收和合同中止验收
（2）正常结清验收检查合同的租金是否结清	第二阶段实现合同中止验收
15	保证金及费用退款	（1）退还保证金、手续费或其他费用	
16	合同中止	（1）针对客户逾期违约的情况中止合同	第二阶段实现

=== 幻灯片 17 ===
主要功能点
监管报送（EAST）
序号	功能点	需求分析方案	备注
17	客户信息	（1）系统根据监管报送的报表提供对应的监管数据录入的页面
（2）监管数据录入页面的字段默认值从业务单据取值并计算，无法获取到值的字段让用户在监管数据录入页面中补充录入，录入之后保存到该单据对应的数据库表中
（3）提供各监管报送的数据库表的取数脚本给IT部门用户定期取数	
18	关联关系		
19	经营性租赁合同		优先实现
20	租赁物信息		优先实现
21	租赁方案调整		
22	担保合同		
23	抵质押物		

=== 幻灯片 18 ===
调研过程
项目历程
1
4
3
2
阶段工作总结
目录Contents
下一阶段工作

=== 幻灯片 19 ===
下一阶段工作计划
序号	工作事项	计划完成时间	负责人
1	系统业务蓝图方案设计（包括流程图、原型图、关键逻辑描述）	2024-3-14	廖根、陈聪
2	系统业务蓝图方案交流及确认	2024-3-15	
3	项目开发工作准备	2024-3-18	

=== 幻灯片 20 ===
蓝图案例效果
[幻灯片20_图片内容]: # 文档内容

## 文本内容

目录 目 录 章节 书签 查找和替换 智能识别目录

3.4.3.原型界面

3.5.租赁合同

3.5.1.业务场景

3.5.2.关键功能说明

3.5.3.原型界面

3.6.合同签约

3.6.1.业务场景

3.6.2.关键功能说明

3.6.3.原型界面

3.7.费用收款

3.7.1.业务场景

3.7.2.关键功能说明

3.7.3.原型界面

3.8.租赁交付确认

3.8.1.业务场景

3.8.2.关键功能说明

3.8.3.原型界面

3.9.还款计划

3.9.1.业务场景

经营性租赁需求规格说明

担保合同中，支持修改，支持一个担保合同多个担保人，点击存 后，生成担保合同的编号； 支持新增担保合同，可点击“新增担保”按钮快速新增担保合同， 填写担保信息后保存，相关担保信息会反写到合同担保信息分 录中； 6.支持快速删除担保合同，可点击“删除担保”按钮快速删除对应的担保 合同； 7.支持记录客户收款银行账户信息、约定违约处理方式； 8.支持上传合同模板后打印合同文档； 9.支持合同关闭、合同中止功能； 10.支持上传附件格式包括：word、excel、pdf 等格式。 3.6.合同签约

3.6.1.业务场景

合同文本（租赁主合同、担保合同、买卖合同、回购协议）审核通过之后， 法务部根据合同签署文件的签约方式（线上或线下签约），跟进合同签约的进度。 若签约方式为线上签约，可发起电子签章调用电子签章接口实现合同线上盖章。 3.6.2.关键功能说明

1.合同签约单来源包括：租赁主合同。 2.如签约方式为线上签约，则合同签约单审核通过后将单据内容传递给e 签宝， 并调用e 签宝不同的流程完成签约。 15

3.3.2.1 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.2 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.3 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.4 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.5 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.6 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.7 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.8 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.9 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.10 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.11 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.12 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.13 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司都完成电子签约时，更新合同签约单签署状态为签约完成。 ➔当合同签约单审核且e 签宝未完成签约时，可以通过工具栏的“撤销签约” 按钮进行撤销操作，成功后更新签署状态为撤销。 4.当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件 的电子版本，单据审核后签署状态为已签约。 5.合同签约需要走审批流程。 3.3.2.14 6 4 2 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40

经营性租赁需求规格说明

3.当线上签约时，签署状态根据流程进展显示不同的值： ➔生成合同签约单时签署状态默认为草稿。 ➔合同签约单审核后签署状态改为签署中。 ➔当e 签宝返回承租人签约完成后，更新合同签约单的签署状态为他方签约完 成。 ➔当承租人和公司

## 表格内容

<table>  <tr>    <td>目录</td>    <td>章节</td>    <td>书签</td>    <td>查找和替换</td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>  </tr>  <tr>    <td></td>    <td></td>    <td></td>    <td></td>    <td>智能识别目录</td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>    <td></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.4.3原型界面</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.5.租赁合同</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.5.1业务场景</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.5.2关键功能说明</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.5.3原型界面</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.6.合同签约</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.6.1业务场景</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.6.2关键功能说明</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.6.3原型界面</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.7.费用收款</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.7.1业务场景</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.7.2关键功能说明</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.7.3原型界面</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.8.租赁交付确认</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.8.1业务场景</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.8.2关键功能说明</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.8.3原型界面</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.9.还款计划</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>    <td rowspan="1" colspan="2">3.9.1业务场景</td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>    <td rowspan="1" colspan="2"></td>  </tr>  <tr>  </tr></table>

=== 幻灯片 21 ===
