#!/usr/bin/env python3
"""
测试新的多重备用.doc解析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def create_mock_doc_file():
    """创建模拟的.doc文件内容"""
    # OLE文件头
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    
    # 中文内容（UTF-16LE编码）
    chinese_content = "项目管理办公室\n工作计划\n需求分析\n系统设计\n测试方案".encode('utf-16le')
    
    # 英文内容
    english_content = b"Project Management Office\nWork Plan\nRequirement Analysis\nSystem Design\nTest Plan"
    
    # 组合成模拟的.doc文件
    mock_content = (
        ole_header + 
        b'\x00' * 200 +  # 填充
        chinese_content + 
        b'\x00' * 100 + 
        english_content + 
        b'\x00' * 500
    )
    
    return mock_content

def test_all_parsing_methods():
    """测试所有解析方法"""
    print("🔧 测试所有.doc解析方法...")
    
    parser = DocumentParser()
    mock_doc = create_mock_doc_file()
    
    print(f"模拟.doc文件大小: {len(mock_doc)} 字节")
    
    # 测试方法1: textract
    print("\n=== 方法1: textract ===")
    try:
        result = parser._parse_with_textract(mock_doc)
        print(f"textract结果: {result[:200]}...")
    except Exception as e:
        print(f"textract失败: {str(e)}")
    
    # 测试方法2: 改进的OLE解析
    print("\n=== 方法2: 改进的OLE解析 ===")
    try:
        result = parser._parse_ole_doc_improved(mock_doc)
        print(f"改进OLE结果: {result[:200]}...")
    except Exception as e:
        print(f"改进OLE失败: {str(e)}")
    
    # 测试方法3: 中文文本提取
    print("\n=== 方法3: 中文文本提取 ===")
    try:
        result = parser._extract_chinese_text_only(mock_doc)
        print(f"中文提取结果: {result[:200]}...")
    except Exception as e:
        print(f"中文提取失败: {str(e)}")
    
    # 测试完整流程
    print("\n=== 完整解析流程 ===")
    try:
        result = parser._parse_old_doc_file(mock_doc)
        print(f"完整解析结果:\n{result}")
    except Exception as e:
        print(f"完整解析失败: {str(e)}")

def test_individual_components():
    """测试各个组件"""
    print("\n🔧 测试各个组件...")
    
    parser = DocumentParser()
    mock_doc = create_mock_doc_file()
    
    # 测试Word文本表提取
    print("\n--- Word文本表提取 ---")
    try:
        result = parser._extract_word_text_table_improved(mock_doc)
        print(f"Word文本表结果: {result}")
    except Exception as e:
        print(f"Word文本表提取失败: {str(e)}")
    
    # 测试文本模式扫描
    print("\n--- 文本模式扫描 ---")
    try:
        result = parser._scan_for_text_patterns(mock_doc)
        print(f"模式扫描结果: {result}")
    except Exception as e:
        print(f"模式扫描失败: {str(e)}")
    
    # 测试oletools提取
    print("\n--- oletools提取 ---")
    try:
        result = parser._extract_with_oletools_improved(mock_doc)
        print(f"oletools结果: {result}")
    except Exception as e:
        print(f"oletools提取失败: {str(e)}")

def test_text_cleaning():
    """测试文本清理功能"""
    print("\n🔧 测试文本清理功能...")
    
    parser = DocumentParser()
    
    # 测试乱码文本清理
    messy_text = "蜾梾肛乌躴隯\n正常的中文内容\nNormal English text\n瘃倏錹绊元脍\n项目管理"
    
    print(f"原始文本: {messy_text}")
    
    # 测试OLE文本清理
    cleaned = parser._clean_ole_extracted_text(messy_text)
    print(f"OLE清理结果: {cleaned}")
    
    # 测试textract清理
    cleaned2 = parser._clean_textract_output(messy_text)
    print(f"textract清理结果: {cleaned2}")

if __name__ == "__main__":
    print("🔧 测试新的多重备用.doc解析器")
    print("=" * 60)
    
    try:
        # 测试所有解析方法
        test_all_parsing_methods()
        
        # 测试各个组件
        test_individual_components()
        
        # 测试文本清理
        test_text_cleaning()
        
        print("\n✅ 所有测试完成")
        print("💡 现在可以重新启动后端并测试实际的.doc文件")
        print("\n🚀 新解析器的优势:")
        print("1. 多重备用方案，确保总能提取到内容")
        print("2. 专门的中文文本处理")
        print("3. 改进的OLE文档解析")
        print("4. 智能的乱码过滤")
        print("5. 支持textract等专业工具")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
