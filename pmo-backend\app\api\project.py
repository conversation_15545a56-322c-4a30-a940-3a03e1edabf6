#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目管理API - 第四个功能模块：项目和任务管理
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import date
from app.models.user import User
from app.models.project import Project
from app.models.task import Task, Team
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/project", tags=["项目管理"])

# 请求模型
class ProjectCreateRequest(BaseModel):
    name: str
    code: str = ""
    type: str = "project"
    begin: Optional[date] = None
    end: Optional[date] = None
    desc: str = ""
    goal: str = ""
    PM: str = ""
    PO: str = ""
    QD: str = ""
    RD: str = ""
    pri: int = 1
    days: int = 0
    order: int = 0

class ProjectUpdateRequest(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    type: Optional[str] = None
    begin: Optional[date] = None
    end: Optional[date] = None
    desc: Optional[str] = None
    goal: Optional[str] = None
    PM: Optional[str] = None
    PO: Optional[str] = None
    QD: Optional[str] = None
    RD: Optional[str] = None
    status: Optional[str] = None
    pri: Optional[int] = None
    days: Optional[int] = None
    order: Optional[int] = None

class TaskCreateRequest(BaseModel):
    project: int
    name: str
    type: str = "devel"
    pri: int = 3
    estimate: float = 0
    left: float = 0
    deadline: Optional[date] = None
    assignedTo: str = ""
    desc: str = ""
    story: int = 0
    module: int = 0
    order: int = 0

class TaskUpdateRequest(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    pri: Optional[int] = None
    estimate: Optional[float] = None
    left: Optional[float] = None
    deadline: Optional[date] = None
    assignedTo: Optional[str] = None
    status: Optional[str] = None
    desc: Optional[str] = None
    order: Optional[int] = None

class TaskAssignRequest(BaseModel):
    assignedTo: str

class TaskFinishRequest(BaseModel):
    consumed: float = 0
    left: float = 0

class TaskCloseRequest(BaseModel):
    closedReason: str = ""

class TeamMemberRequest(BaseModel):
    account: str
    role: str = ""
    days: float = 22
    hours: float = 8

# 权限检查
def check_project_permission(current_user: User = Depends(get_current_user)):
    """检查项目权限"""
    if current_user.role not in ["admin", "pm"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员或项目经理权限"
        )
    return current_user

# 项目管理API
@router.get("/projects")
async def get_projects(
    page: int = 1,
    page_size: int = 20,
    keyword: str = "",
    status: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取项目列表"""
    try:
        result = Project.get_all_projects(page=page, page_size=page_size, keyword=keyword, status=status)
        return success_response(result, "获取项目列表成功")
    except Exception as e:
        return error_response(f"获取项目列表失败: {str(e)}")

@router.get("/projects/{project_id}")
async def get_project(project_id: int, current_user: User = Depends(get_current_user)):
    """获取项目详情"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)
        
        project_data = project.to_dict()
        
        # 获取项目统计信息
        tasks = project.get_tasks(page_size=1000)  # 获取所有任务用于统计
        project_data['task_count'] = tasks['total']
        
        # 按状态统计任务
        status_stats = {}
        for task in tasks['tasks']:
            task_status = task['status']
            status_stats[task_status] = status_stats.get(task_status, 0) + 1
        project_data['task_stats'] = status_stats
        
        # 获取项目团队、版本、需求
        project_data['team_members'] = project.get_team_members()
        project_data['builds'] = project.get_builds()
        project_data['stories'] = project.get_stories()
        
        return success_response(project_data, "获取项目详情成功")
    except Exception as e:
        return error_response(f"获取项目详情失败: {str(e)}")

@router.post("/projects")
async def create_project(
    request: ProjectCreateRequest,
    current_user: User = Depends(check_project_permission)
):
    """创建项目"""
    try:
        project = Project()
        project.name = request.name
        project.code = request.code
        project.type = request.type
        project.begin = request.begin
        project.end = request.end
        project.desc = request.desc
        project.goal = request.goal
        project.PM = request.PM
        project.PO = request.PO
        project.QD = request.QD
        project.RD = request.RD
        project.pri = request.pri
        project.days = request.days
        project.order = request.order
        project.openedBy = current_user.account
        project.status = "wait"
        
        if project.create():
            return success_response(project.to_dict(), "创建项目成功")
        else:
            return error_response("创建项目失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"创建项目失败: {str(e)}")

@router.put("/projects/{project_id}")
async def update_project(
    project_id: int,
    request: ProjectUpdateRequest,
    current_user: User = Depends(check_project_permission)
):
    """更新项目信息"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)
        
        # 更新项目信息
        if request.name is not None:
            project.name = request.name
        if request.code is not None:
            project.code = request.code
        if request.type is not None:
            project.type = request.type
        if request.begin is not None:
            project.begin = request.begin
        if request.end is not None:
            project.end = request.end
        if request.desc is not None:
            project.desc = request.desc
        if request.goal is not None:
            project.goal = request.goal
        if request.PM is not None:
            project.PM = request.PM
        if request.PO is not None:
            project.PO = request.PO
        if request.QD is not None:
            project.QD = request.QD
        if request.RD is not None:
            project.RD = request.RD
        if request.status is not None:
            project.status = request.status
        if request.pri is not None:
            project.pri = request.pri
        if request.days is not None:
            project.days = request.days
        if request.order is not None:
            project.order = request.order
        
        project.lastEditedBy = current_user.account
        
        if project.update():
            return success_response(project.to_dict(), "更新项目信息成功")
        else:
            return error_response("更新项目信息失败")
            
    except Exception as e:
        return error_response(f"更新项目信息失败: {str(e)}")

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: int,
    current_user: User = Depends(check_project_permission)
):
    """删除项目"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)
        
        if project.delete():
            return success_response(None, "删除项目成功")
        else:
            return error_response("删除项目失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"删除项目失败: {str(e)}")

# 任务管理API
@router.get("/tasks")
async def get_tasks(
    page: int = 1,
    page_size: int = 20,
    project_id: int = 0,
    status: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取任务列表"""
    try:
        result = Task.get_all_tasks(
            page=page, page_size=page_size, 
            project_id=project_id, status=status, keyword=keyword
        )
        return success_response(result, "获取任务列表成功")
    except Exception as e:
        return error_response(f"获取任务列表失败: {str(e)}")

@router.get("/tasks/{task_id}")
async def get_task(task_id: int, current_user: User = Depends(get_current_user)):
    """获取任务详情"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)
        
        return success_response(task.to_dict(), "获取任务详情成功")
    except Exception as e:
        return error_response(f"获取任务详情失败: {str(e)}")

@router.post("/tasks")
async def create_task(
    request: TaskCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """创建任务"""
    try:
        # 检查项目是否存在
        project = Project.get_by_id(request.project)
        if not project:
            return error_response("项目不存在", status_code=404)
        
        task = Task()
        task.project = request.project
        task.name = request.name
        task.type = request.type
        task.pri = request.pri
        task.estimate = request.estimate
        task.left = request.left if request.left > 0 else request.estimate
        task.deadline = request.deadline
        task.assignedTo = request.assignedTo
        task.desc = request.desc
        task.story = request.story
        task.module = request.module
        task.order = request.order
        task.openedBy = current_user.account
        task.status = "wait"
        
        if task.create():
            return success_response(task.to_dict(), "创建任务成功")
        else:
            return error_response("创建任务失败")
            
    except Exception as e:
        return error_response(f"创建任务失败: {str(e)}")

@router.put("/tasks/{task_id}")
async def update_task(
    task_id: int,
    request: TaskUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新任务信息"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)
        
        # 更新任务信息
        if request.name is not None:
            task.name = request.name
        if request.type is not None:
            task.type = request.type
        if request.pri is not None:
            task.pri = request.pri
        if request.estimate is not None:
            task.estimate = request.estimate
        if request.left is not None:
            task.left = request.left
        if request.deadline is not None:
            task.deadline = request.deadline
        if request.assignedTo is not None:
            task.assignedTo = request.assignedTo
        if request.status is not None:
            task.status = request.status
        if request.desc is not None:
            task.desc = request.desc
        if request.order is not None:
            task.order = request.order
        
        task.lastEditedBy = current_user.account
        
        if task.update():
            return success_response(task.to_dict(), "更新任务信息成功")
        else:
            return error_response("更新任务信息失败")
            
    except Exception as e:
        return error_response(f"更新任务信息失败: {str(e)}")

@router.delete("/tasks/{task_id}")
async def delete_task(
    task_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除任务"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)
        
        if task.delete():
            return success_response(None, "删除任务成功")
        else:
            return error_response("删除任务失败")
            
    except Exception as e:
        return error_response(f"删除任务失败: {str(e)}")

@router.post("/tasks/{task_id}/assign")
async def assign_task(
    task_id: int,
    request: TaskAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """指派任务"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)

        # 检查被指派用户是否存在
        assignee = User.get_by_account(request.assignedTo)
        if not assignee:
            return error_response("被指派用户不存在", status_code=404)

        if task.assign(request.assignedTo, current_user.account):
            return success_response(task.to_dict(), "指派任务成功")
        else:
            return error_response("指派任务失败")

    except Exception as e:
        return error_response(f"指派任务失败: {str(e)}")

@router.post("/tasks/{task_id}/start")
async def start_task(
    task_id: int,
    current_user: User = Depends(get_current_user)
):
    """开始任务"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)

        if task.start(current_user.account):
            return success_response(task.to_dict(), "开始任务成功")
        else:
            return error_response("开始任务失败")

    except Exception as e:
        return error_response(f"开始任务失败: {str(e)}")

@router.post("/tasks/{task_id}/finish")
async def finish_task(
    task_id: int,
    request: TaskFinishRequest,
    current_user: User = Depends(get_current_user)
):
    """完成任务"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)

        if task.finish(current_user.account, request.consumed, request.left):
            return success_response(task.to_dict(), "完成任务成功")
        else:
            return error_response("完成任务失败")

    except Exception as e:
        return error_response(f"完成任务失败: {str(e)}")

@router.post("/tasks/{task_id}/close")
async def close_task(
    task_id: int,
    request: TaskCloseRequest,
    current_user: User = Depends(get_current_user)
):
    """关闭任务"""
    try:
        task = Task.get_by_id(task_id)
        if not task:
            return error_response("任务不存在", status_code=404)

        if task.close(current_user.account, request.closedReason):
            return success_response(task.to_dict(), "关闭任务成功")
        else:
            return error_response("关闭任务失败")

    except Exception as e:
        return error_response(f"关闭任务失败: {str(e)}")

@router.get("/projects/{project_id}/tasks")
async def get_project_tasks(
    project_id: int,
    page: int = 1,
    page_size: int = 20,
    status: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取项目下的任务"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)

        result = project.get_tasks(page=page, page_size=page_size, status=status)
        return success_response(result, "获取项目任务成功")
    except Exception as e:
        return error_response(f"获取项目任务失败: {str(e)}")

# 团队管理API
@router.get("/projects/{project_id}/team")
async def get_project_team(
    project_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取项目团队成员"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)

        team_members = project.get_team_members()
        return success_response(team_members, "获取项目团队成功")
    except Exception as e:
        return error_response(f"获取项目团队失败: {str(e)}")

@router.post("/projects/{project_id}/team")
async def add_team_member(
    project_id: int,
    request: TeamMemberRequest,
    current_user: User = Depends(check_project_permission)
):
    """添加团队成员"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)

        # 检查用户是否存在
        user = User.get_by_account(request.account)
        if not user:
            return error_response("用户不存在", status_code=404)

        if Team.add_member(project_id, request.account, request.role, request.days, request.hours):
            return success_response(None, "添加团队成员成功")
        else:
            return error_response("添加团队成员失败")

    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"添加团队成员失败: {str(e)}")

@router.delete("/projects/{project_id}/team/{account}")
async def remove_team_member(
    project_id: int,
    account: str,
    current_user: User = Depends(check_project_permission)
):
    """移除团队成员"""
    try:
        project = Project.get_by_id(project_id)
        if not project:
            return error_response("项目不存在", status_code=404)

        if Team.remove_member(project_id, account):
            return success_response(None, "移除团队成员成功")
        else:
            return error_response("移除团队成员失败")

    except Exception as e:
        return error_response(f"移除团队成员失败: {str(e)}")
