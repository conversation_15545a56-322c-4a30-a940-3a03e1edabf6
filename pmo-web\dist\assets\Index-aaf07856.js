import{g as A}from"./project-0b1ee259.js";import{g as q}from"./options-85143662.js";import{_ as G,c as r,r as H,o as J,d as s,L as K,e as v,f as z,h as t,w as l,E as x,g as d,j as u,F as Q,D as R,N as h,B as W}from"./index-76121fa4.js";const X={class:"project-list"},Y={class:"card-header"},Z={class:"header-actions"},ee={class:"filter-bar"},te={class:"pagination"},le={class:"dialog-footer"},ae={__name:"Index",setup(oe){const g=r(!1),f=r([]),V=r([]),k=r(0),y=r(1),b=r(10),c=r(!1),w=r("add"),i=H({entity:"",category:""}),p=async()=>{var o;g.value=!0;try{const e={entity:i.entity,category:i.category,page:y.value,pageSize:b.value},a=await A(e);a&&a.code===200?(f.value=((o=a.data)==null?void 0:o.projects)||[],k.value=f.value.length):x({message:(a==null?void 0:a.message)||"获取项目列表失败",type:"warning"})}catch(e){x({message:e.message||"获取项目列表失败",type:"error"})}finally{g.value=!1}},E=async()=>{var o;try{const e=await q();e&&e.code===200?V.value=(((o=e.data)==null?void 0:o.entities)||[]).map(a=>({label:a,value:a})):console.warn("获取投资主体选项失败:",e==null?void 0:e.message)}catch(e){console.error("获取投资主体选项失败:",e)}},B=()=>{p()},I=()=>{w.value="add",c.value=!0},P=o=>{console.log("查看项目:",o)},D=o=>{console.log("编辑项目:",o),w.value="edit",c.value=!0},N=o=>{b.value=o,p()},S=o=>{y.value=o,p()};return J(()=>{E(),p()}),(o,e)=>{const a=s("el-button"),m=s("el-option"),j=s("el-select"),C=s("el-form-item"),$=s("el-form"),_=s("el-table-column"),F=s("el-table"),L=s("el-pagination"),U=s("el-card"),M=s("el-dialog"),O=K("loading");return v(),z("div",X,[t(U,{class:"project-card"},{header:l(()=>[d("div",Y,[e[7]||(e[7]=d("span",null,"项目列表",-1)),d("div",Z,[t(a,{type:"primary",size:"small",onClick:B},{default:l(()=>e[5]||(e[5]=[u("刷新")])),_:1,__:[5]}),t(a,{type:"success",size:"small",onClick:I},{default:l(()=>e[6]||(e[6]=[u("新增项目")])),_:1,__:[6]})])])]),default:l(()=>[d("div",ee,[t($,{inline:!0,model:i},{default:l(()=>[t(C,{label:"投资主体"},{default:l(()=>[t(j,{modelValue:i.entity,"onUpdate:modelValue":e[0]||(e[0]=n=>i.entity=n),clearable:"",placeholder:"选择投资主体"},{default:l(()=>[(v(!0),z(Q,null,R(V.value,n=>(v(),h(m,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"类别"},{default:l(()=>[t(j,{modelValue:i.category,"onUpdate:modelValue":e[1]||(e[1]=n=>i.category=n),clearable:"",placeholder:"选择类别"},{default:l(()=>[t(m,{label:"全部",value:""}),t(m,{label:"开工项目",value:"implementation"}),t(m,{label:"逾期项目",value:"delayed"})]),_:1},8,["modelValue"])]),_:1}),t(C,null,{default:l(()=>[t(a,{type:"primary",onClick:p},{default:l(()=>e[8]||(e[8]=[u("查询")])),_:1,__:[8]})]),_:1})]),_:1},8,["model"])]),W((v(),h(F,{data:f.value,border:"",style:{width:"100%"}},{default:l(()=>[t(_,{prop:"project_code",label:"项目编号",width:"120"}),t(_,{prop:"project_name",label:"项目名称"}),t(_,{prop:"investment_entity",label:"投资主体",width:"120"}),t(_,{prop:"current_progress",label:"当前进度",width:"120"}),t(_,{label:"操作",width:"200"},{default:l(n=>[t(a,{size:"small",onClick:T=>P(n.row)},{default:l(()=>e[9]||(e[9]=[u(" 查看 ")])),_:2,__:[9]},1032,["onClick"]),t(a,{size:"small",type:"primary",onClick:T=>D(n.row)},{default:l(()=>e[10]||(e[10]=[u(" 编辑 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[O,g.value]]),d("div",te,[t(L,{"current-page":y.value,"page-sizes":[10,20,50,100],"page-size":b.value,layout:"total, sizes, prev, pager, next, jumper",total:k.value,onSizeChange:N,onCurrentChange:S},null,8,["current-page","page-size","total"])])]),_:1}),t(M,{modelValue:c.value,"onUpdate:modelValue":e[4]||(e[4]=n=>c.value=n),title:w.value==="add"?"新增项目":"编辑项目",width:"800px"},{footer:l(()=>[d("span",le,[t(a,{onClick:e[2]||(e[2]=n=>c.value=!1)},{default:l(()=>e[11]||(e[11]=[u("取消")])),_:1,__:[11]}),t(a,{type:"primary",onClick:e[3]||(e[3]=n=>c.value=!1)},{default:l(()=>e[12]||(e[12]=[u("确认")])),_:1,__:[12]})])]),default:l(()=>[e[13]||(e[13]=d("p",null,"项目表单 (待实现)",-1))]),_:1,__:[13]},8,["modelValue","title"])])}}},ie=G(ae,[["__scopeId","data-v-0b13455c"]]);export{ie as default};
