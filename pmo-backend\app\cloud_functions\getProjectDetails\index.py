#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取项目详情云函数

功能：根据项目编码获取项目详细信息
接口：HTTP GET
参数：
  - project_code：项目编码（必填）

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "project": 项目详情对象
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def get_project_details(project_code):
    """
    获取项目详情
    :param project_code: 项目编码
    :return: 项目详情
    """
    if not project_code:
        raise ValueError("项目编码不能为空")
        
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 查询项目基本信息
            sql = """
            SELECT 
                project_code,
                project_name,
                investment_entity,
                project_name_1,
                excellence_level,
                project_planned_total_investment,
                annual_investment_plan,
                construction_content,
                project_category,
                investment_type,
                is_hardware,
                is_non_indigenous_innovation,
                is_project_established,
                project_establishment_year,
                line_work_special_project_work,
                responsible_department,
                project_overview,
                budget,
                business_research_time,
                solution_time,
                project_establishment_time,
                project_procurement_time,
                project_implementation_time,
                project_acceptance_time,
                progress_plan_locked,
                current_progress,
                next_steps,
                issues_to_be_coordinated_resolved,
                remarks,
                estimated_operating_revenue,
                CASE 
                    WHEN current_progress = '已完成' THEN 100
                    WHEN current_progress = '实施中' THEN 50
                    WHEN current_progress = '已立项' THEN 30
                    WHEN current_progress = '设计中' THEN 20
                    WHEN current_progress = '未实施' THEN 0
                    ELSE 0
                END as progress_percent,
                CASE 
                    WHEN DATE(NOW()) > DATE(project_establishment_time) AND current_progress = '未实施' 
                    THEN 1 ELSE 0 
                END as `delayed`,
                CASE 
                    WHEN DATE(NOW()) > DATE(project_establishment_time) AND current_progress = '未实施' 
                    THEN DATEDIFF(NOW(), project_establishment_time) ELSE 0 
                END as delay_days
            FROM Project_Account_Book
            WHERE project_code = %s
            """
            cursor.execute(sql, (project_code,))
            project = cursor.fetchone()
            
            if not project:
                return None
                
            # 查询工时记录
            hours_sql = """
            SELECT SUM(working_hours) as total_hours
            FROM project_hours
            WHERE project_code = %s
            """
            cursor.execute(hours_sql, (project_code,))
            hours_result = cursor.fetchone()
            
            if hours_result:
                project['total_hours'] = hours_result['total_hours'] or 0
            else:
                project['total_hours'] = 0
                
            # 处理日期格式
            date_fields = [
                'business_research_time', 'solution_time', 'project_establishment_time',
                'project_procurement_time', 'project_implementation_time', 'project_acceptance_time'
            ]
            
            for field in date_fields:
                if project[field]:
                    # 检查是否为日期对象
                    if hasattr(project[field], 'strftime'):
                        # 如果是日期对象，转换为字符串
                        project[field] = project[field].strftime('%Y-%m-%d')
                    else:
                        # 如果已经是字符串，保持原样
                        logger.info(f"字段 {field} 已经是字符串格式: {project[field]}")
            
            # 处理布尔值
            bool_fields = [
                'is_hardware', 'is_non_indigenous_innovation', 
                'is_project_established', 'progress_plan_locked', 'delayed'
            ]
            
            for field in bool_fields:
                project[field] = bool(project[field])
            
            return {'project': project}
    except Exception as e:
        logger.error(f"获取项目详情错误: {str(e)}")
        raise e
    finally:
        if conn:
            conn.close()

def parse_query_string(event):
    """解析请求参数"""
    query_parameters = {}
    
    if 'queryString' in event:
        for key, value in event['queryString'].items():
            query_parameters[key] = value
    elif 'queryStringParameters' in event and event['queryStringParameters']:
        query_parameters = event['queryStringParameters']
    
    return query_parameters

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析请求参数
        params = parse_query_string(event)
        project_code = params.get('project_code')
        
        if not project_code:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 400,
                    'message': '缺少必填参数 project_code',
                    'data': None
                }, ensure_ascii=False)
            }
        
        # 获取项目详情
        data = get_project_details(project_code)
        
        if not data:
            return {
                'statusCode': 404,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 404,
                    'message': f'未找到编码为 {project_code} 的项目',
                    'data': None
                }, ensure_ascii=False)
            }
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 200,
                'message': 'success',
                'data': data
            }, ensure_ascii=False)
        }
    except Exception as e:
        logger.error(f"处理请求错误: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'获取项目详情失败: {str(e)}',
                'data': None
            }, ensure_ascii=False)
        } 