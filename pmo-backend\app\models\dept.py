#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门模型 - 参考禅道zt_dept表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Dept:
    """部门模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.parent: int = 0
        self.path: str = ""
        self.grade: int = 0
        self.order_num: int = 0
        self.position: str = ""
        self.function: str = ""
        self.manager: str = ""
        self.deleted: str = "0"
        self.created_at: Optional[datetime] = None

    @classmethod
    def get_by_id(cls, dept_id: int) -> Optional['Dept']:
        """根据ID获取部门"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_dept 
                WHERE id = %s AND deleted = '0'
            """, (dept_id,))
            
            row = cursor.fetchone()
            if row:
                dept = cls()
                dept._load_from_dict(row)
                return dept
            return None
            
        except Exception as e:
            print(f"获取部门失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_depts(cls, parent_id: Optional[int] = None) -> List['Dept']:
        """获取部门列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if parent_id is not None:
                cursor.execute("""
                    SELECT * FROM zt_dept 
                    WHERE parent = %s AND deleted = '0'
                    ORDER BY order_num ASC, id ASC
                """, (parent_id,))
            else:
                cursor.execute("""
                    SELECT * FROM zt_dept 
                    WHERE deleted = '0'
                    ORDER BY order_num ASC, id ASC
                """)
            
            rows = cursor.fetchall()
            depts = []
            for row in rows:
                dept = cls()
                dept._load_from_dict(row)
                depts.append(dept)
            
            return depts
            
        except Exception as e:
            print(f"获取部门列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_dept_tree(cls) -> List[Dict[str, Any]]:
        """获取部门树形结构"""
        all_depts = cls.get_all_depts()
        dept_dict = {dept.id: dept.to_dict() for dept in all_depts}
        
        # 构建树形结构
        tree = []
        for dept in all_depts:
            dept_data = dept_dict[dept.id]
            dept_data['children'] = []
            
            if dept.parent == 0:
                tree.append(dept_data)
            else:
                if dept.parent in dept_dict:
                    dept_dict[dept.parent]['children'].append(dept_data)
        
        return tree

    def create(self) -> bool:
        """创建部门"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查部门名称是否已存在
            cursor.execute("""
                SELECT id FROM zt_dept 
                WHERE name = %s AND parent = %s AND deleted = '0'
            """, (self.name, self.parent))
            if cursor.fetchone():
                raise ValueError(f"部门 {self.name} 在同级目录下已存在")
            
            # 计算部门级别和路径
            if self.parent == 0:
                self.grade = 1
                self.path = ""
            else:
                parent_dept = self.get_by_id(self.parent)
                if not parent_dept:
                    raise ValueError("父部门不存在")
                self.grade = parent_dept.grade + 1
                self.path = parent_dept.path
            
            # 插入部门
            cursor.execute("""
                INSERT INTO zt_dept (
                    name, parent, path, grade, order_num, position, `function`, manager, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                )
            """, (
                self.name, self.parent, self.path, self.grade, 
                self.order_num, self.position, self.function, self.manager
            ))
            
            self.id = cursor.lastrowid
            
            # 更新路径
            if self.parent == 0:
                self.path = f",{self.id},"
            else:
                self.path = f"{self.path}{self.id},"
            
            cursor.execute("""
                UPDATE zt_dept SET path = %s WHERE id = %s
            """, (self.path, self.id))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建部门失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新部门信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_dept SET 
                    name = %s, position = %s, `function` = %s, 
                    manager = %s, order_num = %s
                WHERE id = %s
            """, (
                self.name, self.position, self.function, 
                self.manager, self.order_num, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新部门失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除部门"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查是否有子部门
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_dept 
                WHERE parent = %s AND deleted = '0'
            """, (self.id,))
            if cursor.fetchone()['count'] > 0:
                raise ValueError("该部门下还有子部门，无法删除")
            
            # 检查是否有用户
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_user 
                WHERE dept_id = %s AND deleted = '0'
            """, (self.id,))
            # 注意：这里假设用户表有dept_id字段，如果没有可以跳过这个检查
            
            cursor.execute("""
                UPDATE zt_dept SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除部门失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_children(self) -> List['Dept']:
        """获取子部门"""
        return self.get_all_depts(parent_id=self.id)

    def get_all_children(self) -> List['Dept']:
        """获取所有子孙部门"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_dept 
                WHERE path LIKE %s AND deleted = '0' AND id != %s
                ORDER BY grade ASC, order_num ASC
            """, (f"%,{self.id},%", self.id))
            
            rows = cursor.fetchall()
            depts = []
            for row in rows:
                dept = Dept()
                dept._load_from_dict(row)
                depts.append(dept)
            
            return depts
            
        except Exception as e:
            print(f"获取子孙部门失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "parent": self.parent,
            "path": self.path,
            "grade": self.grade,
            "order_num": self.order_num,
            "position": self.position,
            "function": self.function,
            "manager": self.manager,
            "deleted": self.deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
