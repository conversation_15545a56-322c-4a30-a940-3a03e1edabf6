#!/usr/bin/env python3
"""
测试textract库解析.doc文件
"""

import sys
import os
import tempfile

def test_textract_import():
    """测试textract导入"""
    try:
        import textract
        print("✅ textract导入成功")
        return True
    except ImportError as e:
        print(f"❌ textract导入失败: {str(e)}")
        return False

def test_textract_with_sample():
    """测试textract解析示例文件"""
    try:
        import textract
        
        # 创建一个简单的测试文本文件
        test_content = "这是一个测试文档\n包含中文内容\n用于验证textract功能"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_path = f.name
        
        try:
            # 测试textract解析
            extracted_text = textract.process(temp_path)
            print(f"✅ textract解析成功")
            print(f"原始内容: {test_content}")
            print(f"提取内容: {extracted_text.decode('utf-8')}")
            return True
        finally:
            os.unlink(temp_path)
            
    except Exception as e:
        print(f"❌ textract解析失败: {str(e)}")
        return False

def create_improved_doc_parser():
    """创建使用textract的改进解析器"""
    
    improved_code = '''
def _parse_old_doc_file_with_textract(self, file_content: bytes) -> str:
    """使用textract解析.doc文件 - 最稳定的方案"""
    logger.info("使用textract解析老版本DOC文件...")

    try:
        import textract
        import tempfile
        import os

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # 使用textract解析
            logger.info("开始使用textract解析.doc文件")
            extracted_text = textract.process(temp_file_path)
            
            # 解码文本
            if isinstance(extracted_text, bytes):
                # 尝试多种编码
                for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                    try:
                        text_content = extracted_text.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    text_content = extracted_text.decode('utf-8', errors='ignore')
            else:
                text_content = str(extracted_text)
            
            # 清理文本
            cleaned_text = self._clean_textract_output(text_content)
            
            if cleaned_text and len(cleaned_text.strip()) > 10:
                logger.info(f"textract解析成功，提取内容长度: {len(cleaned_text)}")
                return f"# Word文档内容\\n\\n{cleaned_text}"
            else:
                logger.warning("textract提取的内容为空或过短")
                return "textract解析成功，但未提取到有效内容"
                
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except ImportError:
        logger.warning("textract模块未安装，回退到其他方法")
        return self._parse_old_doc_file_fallback(file_content)
    except Exception as e:
        logger.error(f"textract解析失败: {str(e)}")
        return self._parse_old_doc_file_fallback(file_content)

def _clean_textract_output(self, text: str) -> str:
    """清理textract的输出"""
    try:
        import re
        
        if not text or not text.strip():
            return ""
        
        # 分行处理
        lines = text.split('\\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) < 2:
                continue
            
            # 移除明显的乱码行
            # 检查是否包含有意义的字符
            meaningful_chars = len(re.findall(r'[\\u4e00-\\u9fff\\w]', line))
            if meaningful_chars / len(line) > 0.3:  # 至少30%是有意义的字符
                cleaned_lines.append(line)
        
        if cleaned_lines:
            return '\\n\\n'.join(cleaned_lines)
        
        return ""
        
    except Exception as e:
        logger.error(f"textract输出清理失败: {str(e)}")
        return text

def _parse_old_doc_file_fallback(self, file_content: bytes) -> str:
    """textract失败时的备用方案"""
    # 这里可以调用之前的解析方法作为备用
    return self._extract_chinese_text_only(file_content) or "文档解析失败"
'''
    
    return improved_code

if __name__ == "__main__":
    print("🔧 测试textract库")
    print("=" * 50)
    
    # 测试导入
    if not test_textract_import():
        sys.exit(1)
    
    # 测试基本功能
    if not test_textract_with_sample():
        sys.exit(1)
    
    # 生成改进代码
    improved_code = create_improved_doc_parser()
    print("\n✅ textract测试完成")
    print("💡 可以将textract集成到DocumentParser中")
    
    print("\n🚀 textract的优势:")
    print("1. 专门支持.doc文件（使用antiword）")
    print("2. 跨平台兼容性好")
    print("3. 统一的API接口")
    print("4. 社区维护活跃")
    print("5. 支持多种文档格式")
