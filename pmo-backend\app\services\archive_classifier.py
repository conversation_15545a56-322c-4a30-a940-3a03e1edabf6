#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目档案AI分类服务
"""

import json
import re
import os
from typing import Dict, List, Optional, Tuple
from app.core.logger import get_logger
from app.services.ai_chat import AIChatService
from app.services.document_parser import DocumentParser

logger = get_logger(__name__)

class ProjectArchiveClassifier:
    """项目档案AI分类器"""
    
    def __init__(self, ai_service: AIChatService, document_parser: DocumentParser):
        self.ai_service = ai_service
        self.document_parser = document_parser
        
    async def classify_document(self, file_content: bytes, filename: str, content_type: str, checklist: List[Dict],
                              expected_project_name: str = "", expected_investment_entity: str = "") -> Dict:
        """
        使用AI对文档进行分类
        
        Args:
            file_content: 文件内容
            filename: 文件名
            content_type: 文件MIME类型
            checklist: 档案清单
            
        Returns:
            分类结果字典
        """
        try:
            # 1. 解析文档内容
            logger.info(f"开始解析文档: {filename}")
            parsed_content = self.document_parser.parse_document(file_content, filename, content_type)
            
            # 处理图片文件的异步解析
            if parsed_content.startswith("IMAGE_TO_PROCESS:"):
                image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                try:
                    parsed_content = await self.document_parser.parse_image_with_vision_model(image_base64)
                except Exception as e:
                    parsed_content = f"图片解析失败: {str(e)}"
                    logger.error(f"图片解析失败: {str(e)}")
            
            # 处理PPT中的图片
            elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                try:
                    parsed_content = await self.document_parser.process_ppt_images(parsed_content)
                except Exception as e:
                    parsed_content = f"PPT图片解析失败: {str(e)}"
                    logger.error(f"PPT图片解析失败: {str(e)}")
            
            logger.info(f"文档解析完成，内容长度: {len(parsed_content)}")
            
            # 2. 构建AI分类提示词
            prompt = self._build_classification_prompt(parsed_content, filename, checklist, expected_project_name, expected_investment_entity)
            
            # 3. 调用AI进行分类
            logger.info(f"开始AI分类: {filename}")
            classification_result = await self._call_ai_classification(prompt)
            
            # 4. 验证和处理分类结果
            validated_result = self._validate_classification_result(classification_result, checklist, expected_project_name, expected_investment_entity)
            
            logger.info(f"AI分类完成: {filename} -> {validated_result.get('output_name', 'Unknown')}")
            return validated_result
            
        except Exception as e:
            logger.error(f"文档分类失败 {filename}: {str(e)}")
            return {
                "main_process": "未分类",
                "sub_process": "未分类",
                "output_name": "未分类文档",
                "confidence": 0.0,
                "reason": f"分类失败: {str(e)}",
                "error": True
            }

    async def classify_parsed_content(self, parsed_content: str, filename: str, checklist: List[Dict],
                                    expected_project_name: str = "", expected_investment_entity: str = "") -> Dict:
        """
        使用已解析的内容进行AI分类（避免重复解析）

        Args:
            parsed_content: 已解析的文档内容
            filename: 文件名
            checklist: 档案清单

        Returns:
            分类结果字典
        """
        try:
            logger.info(f"开始AI分类（使用已解析内容）: {filename}")

            # 1. 构建AI分类提示词
            prompt = self._build_classification_prompt(parsed_content, filename, checklist, expected_project_name, expected_investment_entity)

            # 2. 调用AI进行分类
            classification_result = await self._call_ai_classification(prompt)

            # 3. 验证和处理分类结果
            validated_result = self._validate_classification_result(classification_result, checklist, expected_project_name, expected_investment_entity)

            logger.info(f"AI分类完成: {filename} -> {validated_result.get('output_name', 'Unknown')}")
            return validated_result

        except Exception as e:
            logger.error(f"文档分类失败 {filename}: {str(e)}")
            return {
                "main_process": "未分类",
                "sub_process": "未分类",
                "output_name": "未分类文档",
                "confidence": 0.0,
                "reason": f"分类失败: {str(e)}",
                "error": True
            }
    
    def _build_classification_prompt(self, content: str, filename: str, checklist: List[Dict],
                                   expected_project_name: str = "", expected_investment_entity: str = "") -> str:
        """构建AI分类提示词"""
        
        # 构建档案清单文本
        checklist_text = ""
        for i, item in enumerate(checklist, 1):
            checklist_text += f"{i}. {item['main_process']} -> {item['sub_process']} -> {item['output_name']}\n"
            checklist_text += f"   描述: {item['content_description']}\n"
            if item.get('keywords'):
                checklist_text += f"   关键词: {item['keywords']}\n"
            checklist_text += "\n"
        
        # 截取文档内容（避免过长）
        content_preview = content[:3000] if len(content) > 3000 else content
        
        prompt = f"""根据档案清单对文件进行分类，并识别文档中的项目信息。

档案清单：
{checklist_text}

文件：{filename}
内容：{content_preview}

要求：
1. 根据档案清单进行分类
2. 识别文档中的投资主体和项目名称
3. 只返回JSON格式结果，禁止输出任何思考过程、解释或其他文字

{{
    "main_process": "具体主流程名称或未分类",
    "sub_process": "具体子流程名称或未分类",
    "output_name": "具体输出物名称或未分类文档",
    "confidence": 0.85,
    "reason": "简短分类理由",
    "detected_investor": "从文档中识别的投资主体或空字符串",
    "detected_project": "从文档中识别的项目名称或空字符串"
}}"""

        return prompt
    
    async def _call_ai_classification(self, prompt: str) -> Dict:
        """调用AI进行分类"""
        try:
            logger.info("开始调用AI服务进行分类...")

            # 使用AI聊天服务进行分类
            import asyncio
            response = await asyncio.wait_for(
                self.ai_service.chat_completion(
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,  # 使用较低的温度确保结果稳定
                    max_tokens=1000  # 增加token数量确保能返回完整结果
                ),
                timeout=60.0  # 60秒超时
            )

            logger.info(f"AI分类原始响应: {response}")

            # 提取AI回复内容 - 兼容不同的响应格式
            if "content" in response:
                # 直接包含content字段的格式
                ai_response = response.get("content", "")
            elif "choices" in response:
                # OpenAI格式
                ai_response = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            else:
                ai_response = ""

            logger.info(f"AI分类回复内容: {ai_response}")

            # 尝试解析JSON
            return self._parse_ai_response(ai_response)

        except asyncio.TimeoutError:
            logger.error("AI分类调用超时（60秒）")
            raise Exception("AI分类调用超时")
        except Exception as e:
            logger.error(f"AI分类调用失败: {str(e)}")
            raise e
    
    def _parse_ai_response(self, ai_response: str) -> Dict:
        """解析AI回复的JSON结果"""
        try:
            logger.info(f"原始AI回复: {ai_response}")

            # 先移除<think>标签内容
            cleaned_response = re.sub(r'<think>.*?</think>\s*', '', ai_response, flags=re.DOTALL)
            cleaned_response = cleaned_response.strip()
            logger.info(f"清理后的回复: {cleaned_response}")

            # 简单直接的JSON提取 - 找到第一个{和最后一个}
            start_idx = cleaned_response.find('{')
            end_idx = cleaned_response.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = cleaned_response[start_idx:end_idx+1]
                logger.info(f"提取到JSON字符串: {json_str}")
                result = json.loads(json_str)
                return result
            else:
                # 如果清理后没找到，尝试从原始回复中提取
                start_idx = ai_response.find('{')
                end_idx = ai_response.rfind('}')

                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    json_str = ai_response[start_idx:end_idx+1]
                    logger.info(f"从原始回复提取JSON: {json_str}")
                    result = json.loads(json_str)
                    return result
                else:
                    raise ValueError("AI回复中未找到有效的JSON格式")

        except json.JSONDecodeError as e:
            logger.error(f"AI回复JSON解析失败: {str(e)}, 尝试解析的JSON: {json_str if 'json_str' in locals() else 'None'}")
            # 尝试简单的文本解析作为备选方案
            return self._fallback_text_parsing(ai_response)
        except Exception as e:
            logger.error(f"AI回复解析失败: {str(e)}")
            raise e
    
    def _fallback_text_parsing(self, ai_response: str) -> Dict:
        """备选的文本解析方案"""
        # 简单的文本解析逻辑
        result = {
            "main_process": "未分类",
            "sub_process": "未分类", 
            "output_name": "未分类文档",
            "confidence": 0.1,
            "reason": "AI回复格式异常，使用备选解析"
        }
        
        # 尝试从文本中提取关键信息
        if "项目启动" in ai_response:
            result["main_process"] = "项目启动"
        elif "项目规划" in ai_response:
            result["main_process"] = "项目规划"
        elif "项目执行" in ai_response:
            result["main_process"] = "项目执行"
        elif "项目监控" in ai_response:
            result["main_process"] = "项目监控"
        elif "项目收尾" in ai_response:
            result["main_process"] = "项目收尾"
            
        return result
    
    def _validate_classification_result(self, result: Dict, checklist: List[Dict],
                                      expected_project_name: str = "", expected_investment_entity: str = "") -> Dict:
        """验证分类结果的有效性"""
        try:
            # 检查必需字段
            required_fields = ["main_process", "sub_process", "output_name", "confidence", "reason"]
            for field in required_fields:
                if field not in result:
                    result[field] = "未知" if field != "confidence" else 0.0

            # 处理新增的项目信息字段
            detected_investor = result.get("detected_investor", "")
            detected_project = result.get("detected_project", "")

            # 添加调试日志
            logger.info(f"期望投资主体: '{expected_investment_entity}', 识别投资主体: '{detected_investor}'")
            logger.info(f"期望项目名称: '{expected_project_name}', 识别项目名称: '{detected_project}'")

            # 进行投资主体匹配
            investor_match = None
            if expected_investment_entity and detected_investor:
                # 模糊匹配：检查是否包含关键词
                investor_match = (expected_investment_entity.lower() in detected_investor.lower() or
                                detected_investor.lower() in expected_investment_entity.lower())
                logger.info(f"投资主体匹配结果: {investor_match}")

            # 进行项目名称匹配
            project_match = None
            if expected_project_name and detected_project:
                # 模糊匹配：检查是否包含关键词
                project_match = (expected_project_name.lower() in detected_project.lower() or
                               detected_project.lower() in expected_project_name.lower())
                logger.info(f"项目名称匹配结果: {project_match}")

            # 添加匹配结果到返回值
            result["detected_investor"] = detected_investor
            result["detected_project"] = detected_project
            result["investor_match"] = investor_match
            result["project_match"] = project_match
            
            # 验证置信度范围
            confidence = result.get("confidence", 0.0)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                result["confidence"] = 0.5
            
            # 验证分类是否在清单中存在
            is_valid = False
            for item in checklist:
                if (item["main_process"] == result["main_process"] and 
                    item["sub_process"] == result["sub_process"] and 
                    item["output_name"] == result["output_name"]):
                    is_valid = True
                    break
            
            if not is_valid:
                logger.warning(f"分类结果不在清单中: {result}")
                # 如果不在清单中，降低置信度
                result["confidence"] = min(result["confidence"], 0.3)
                result["reason"] += " (注意：此分类不在标准清单中)"
            
            return result
            
        except Exception as e:
            logger.error(f"分类结果验证失败: {str(e)}")
            return {
                "main_process": "未分类",
                "sub_process": "未分类",
                "output_name": "未分类文档",
                "confidence": 0.0,
                "reason": f"验证失败: {str(e)}",
                "error": True
            }
    
    def get_suggested_filename(self, original_filename: str, classification: Dict) -> str:
        """根据分类结果生成建议的文件名"""
        try:
            # 获取文件扩展名
            file_ext = os.path.splitext(original_filename)[1]
            
            # 根据输出物名称生成新文件名
            output_name = classification.get("output_name", "未分类文档")
            
            # 清理文件名中的特殊字符
            clean_name = re.sub(r'[<>:"/\\|?*]', '_', output_name)
            
            # 生成新文件名
            new_filename = f"{clean_name}{file_ext}"
            
            return new_filename
            
        except Exception as e:
            logger.error(f"生成文件名失败: {str(e)}")
            return original_filename
    
    def get_storage_path(self, project_code: str, classification: Dict) -> str:
        """根据分类结果生成存储路径"""
        try:
            main_process = classification.get("main_process", "未分类")
            sub_process = classification.get("sub_process", "未分类")
            
            # 清理路径中的特殊字符
            clean_main = re.sub(r'[<>:"/\\|?*]', '_', main_process)
            clean_sub = re.sub(r'[<>:"/\\|?*]', '_', sub_process)
            
            # 生成存储路径
            storage_path = os.path.join("project_archive_materials", project_code, clean_main, clean_sub)
            
            return storage_path
            
        except Exception as e:
            logger.error(f"生成存储路径失败: {str(e)}")
            return os.path.join("project_archive_materials", project_code, "未分类")
