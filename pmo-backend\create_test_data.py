#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据
"""

import pymysql
import os
from dotenv import load_dotenv
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data():
    """创建测试数据"""
    print("🔧 开始创建测试数据...")
    
    # 加载环境变量
    load_dotenv()
    
    config = {
        'host': os.getenv('DB_HOST'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME'),
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        
        # 1. 创建督办事项
        print("\n📝 创建督办事项...")
        
        supervision_items = [
            {
                'sequence_number': 1,
                'work_dimension': '科技条线',
                'work_theme': '系统升级改造工作',
                'supervision_source': '集团科技部',
                'work_content': '完成核心系统升级改造，提升系统性能和稳定性',
                'is_annual_assessment': '是',
                'completion_deadline': '12月末',
                'overall_progress': 'O'
            },
            {
                'sequence_number': 2,
                'work_dimension': '科技条线',
                'work_theme': '数据安全合规检查',
                'supervision_source': '集团科技部',
                'work_content': '完成数据安全合规检查，确保符合监管要求',
                'is_annual_assessment': '是',
                'completion_deadline': '11月末',
                'overall_progress': '√'
            },
            {
                'sequence_number': 3,
                'work_dimension': '科技条线',
                'work_theme': '新系统上线部署',
                'supervision_source': '集团科技部',
                'work_content': '完成新业务系统上线部署和测试验收',
                'is_annual_assessment': '否',
                'completion_deadline': '10月末',
                'overall_progress': '！'
            },
            {
                'sequence_number': 4,
                'work_dimension': '人力资源',
                'work_theme': '员工培训计划',
                'supervision_source': '集团人力资源部',
                'work_content': '完成年度员工技能培训计划执行',
                'is_annual_assessment': '否',
                'completion_deadline': '9月末',
                'overall_progress': 'X'
            },
            {
                'sequence_number': 5,
                'work_dimension': '风险管理',
                'work_theme': '风险评估报告',
                'supervision_source': '集团风险管理部',
                'work_content': '完成年度风险评估报告编制和提交',
                'is_annual_assessment': '是',
                'completion_deadline': '8月末',
                'overall_progress': '√'
            }
        ]

        # 插入督办事项
        for item in supervision_items:
            insert_item_sql = """
            INSERT INTO supervision_items (sequence_number, work_dimension, work_theme, supervision_source, work_content, is_annual_assessment, completion_deadline, overall_progress)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_item_sql, (
                item['sequence_number'],
                item['work_dimension'],
                item['work_theme'],
                item['supervision_source'],
                item['work_content'],
                item['is_annual_assessment'],
                item['completion_deadline'],
                item['overall_progress']
            ))
            print(f"  ✅ 创建督办事项: {item['work_theme']}")
        
        connection.commit()
        
        # 2. 获取所有督办事项和公司
        print("\n📊 获取督办事项和公司信息...")
        
        cursor.execute("SELECT id FROM supervision_items")
        item_ids = [row[0] for row in cursor.fetchall()]
        print(f"督办事项数量: {len(item_ids)}")
        
        cursor.execute("SELECT id, company_code FROM companies WHERE is_active = 1")
        companies = cursor.fetchall()
        print(f"活跃公司数量: {len(companies)}")
        
        # 3. 为每个督办事项创建公司进度记录
        print("\n🔄 创建公司进度记录...")
        
        # 定义不同的状态分布
        status_patterns = [
            ['√', 'O', '！', 'X', '—'],  # 事项1: 混合状态
            ['√', '√', 'O', 'O', 'X'],   # 事项2: 大部分完成
            ['！', '！', 'O', 'X', 'X'],  # 事项3: 大部分逾期
            ['X', 'X', 'X', 'X', 'X'],   # 事项4: 全部未开始
            ['√', '√', '√', '√', '—']    # 事项5: 大部分完成，寿险不需要
        ]
        
        for i, item_id in enumerate(item_ids):
            pattern = status_patterns[i % len(status_patterns)]
            
            for j, (company_id, company_code) in enumerate(companies):
                status = pattern[j % len(pattern)]
                
                # 特殊处理：寿险公司在某些事项中标记为"不需要执行"
                if company_code == 'shouxian' and i == 4:  # 事项5
                    status = '—'
                
                insert_progress_sql = """
                INSERT INTO company_progress (supervision_item_id, company_id, status, updated_by)
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(insert_progress_sql, (item_id, company_id, status, 'system'))
            
            print(f"  ✅ 为督办事项 {item_id} 创建了 {len(companies)} 条进度记录")
        
        connection.commit()
        
        # 4. 验证数据
        print("\n🔍 验证创建的数据...")
        
        cursor.execute("SELECT COUNT(*) FROM supervision_items")
        items_count = cursor.fetchone()[0]
        print(f"督办事项总数: {items_count}")
        
        cursor.execute("SELECT COUNT(*) FROM company_progress")
        progress_count = cursor.fetchone()[0]
        print(f"公司进度记录总数: {progress_count}")
        
        cursor.execute("SELECT COUNT(*) FROM companies WHERE is_active = 1")
        companies_count = cursor.fetchone()[0]
        print(f"活跃公司总数: {companies_count}")
        
        expected_progress = items_count * companies_count
        print(f"预期进度记录数: {expected_progress}")
        
        if progress_count == expected_progress:
            print("✅ 数据验证通过！")
        else:
            print(f"❌ 数据验证失败！实际: {progress_count}, 预期: {expected_progress}")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 测试数据创建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_test_data()
