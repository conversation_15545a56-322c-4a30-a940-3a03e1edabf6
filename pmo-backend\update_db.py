#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新数据库表结构
"""

from app.db.utils import execute_update, execute_query
import sys

def update_database():
    """更新数据库表结构"""
    try:
        print("开始更新数据库表结构...")
        
        # 检查deleted_at字段是否已存在
        try:
            result = execute_query("DESCRIBE supervision_items", ())
            columns = [row['Field'] for row in result]
            
            if 'deleted_at' not in columns:
                print("添加deleted_at字段...")
                execute_update("ALTER TABLE supervision_items ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER updated_at", ())
                print("✅ deleted_at字段添加成功")
            else:
                print("✅ deleted_at字段已存在")
                
        except Exception as e:
            print(f"❌ 添加deleted_at字段失败: {str(e)}")
            return False
        
        # 创建操作日志表
        try:
            create_log_table_sql = """
            CREATE TABLE IF NOT EXISTS supervision_operation_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                operation_type ENUM('IMPORT', 'EXPORT', 'CREATE', 'UPDATE', 'DELETE') NOT NULL COMMENT '操作类型',
                operation_details JSON COMMENT '操作详情',
                file_name VARCHAR(255) COMMENT '文件名（导入导出时）',
                affected_records INT DEFAULT 0 COMMENT '影响的记录数',
                success_count INT DEFAULT 0 COMMENT '成功处理数量',
                error_count INT DEFAULT 0 COMMENT '错误数量',
                error_details TEXT COMMENT '错误详情',
                operator VARCHAR(50) DEFAULT 'system' COMMENT '操作人',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                user_agent TEXT COMMENT '用户代理',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_operation_type (operation_type),
                INDEX idx_operator (operator),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办管理操作日志表'
            """
            
            execute_update(create_log_table_sql, ())
            print("✅ 操作日志表创建/更新成功")
            
        except Exception as e:
            print(f"❌ 创建操作日志表失败: {str(e)}")
            return False
        
        print("🎉 数据库表结构更新完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = update_database()
    if success:
        print("数据库更新成功！")
        sys.exit(0)
    else:
        print("数据库更新失败！")
        sys.exit(1)
