from fastapi import APIRouter, Query, Path, Depends, HTTPException
from typing import List, Optional, Dict, Any
from app.core.security import get_current_user
from app.db.connection import db_transaction
from app.core.websocket_manager import planning_ws_manager
import json
import asyncio

router = APIRouter()

@router.get("/categories")
async def get_planning_categories(current_user = Depends(get_current_user)):
    """获取规划分类列表"""
    categories = [
        {
            "id": "ai",
            "name": "AI²层",
            "description": "人工智能平方倍增效应",
            "color": "#4A90E2",
            "subcategories": ["模型中心", "创新中心"]
        },
        {
            "id": "management", 
            "name": "9大智慧管理体系",
            "description": "构筑集团的决策大脑",
            "color": "#7ED321",
            "subcategories": ["战略洞悉", "智慧协同", "智慧财务", "智慧风控", "纪检审计", "智能合规", "智能资产", "智慧人事", "智慧办公"]
        },
        {
            "id": "tech",
            "name": "3大技术底座", 
            "description": "夯实数字生命体的骨骼与血脉",
            "color": "#F5A623",
            "subcategories": ["云底座", "数据底座", "安全底座"]
        },
        {
            "id": "business",
            "name": "N个业态场景",
            "description": "赋能各业态场景创造价值", 
            "color": "#BD10E0",
            "subcategories": []  # 动态从数据库获取
        }
    ]
    
    # 动态获取N个业态场景的子分类
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                sql = """
                SELECT DISTINCT category_level2 
                FROM project_plan 
                WHERE category_level1 = 'N 个业务场景'
                ORDER BY category_level2
                """
                cursor.execute(sql)
                business_subcategories = [row["category_level2"] for row in cursor.fetchall()]
                categories[3]["subcategories"] = business_subcategories
    except Exception as e:
        print(f"获取业态场景子分类失败: {e}")
    
    return {"code": 200, "message": "success", "data": categories}

@router.get("/subcategories/{category_id}")
async def get_subcategories(
    category_id: str = Path(..., description="分类ID"),
    current_user = Depends(get_current_user)
):
    """获取指定分类的子分类列表"""
    category_mapping = {
        "ai": "AI²层",
        "management": "9 大智慧管理体系", 
        "tech": "3 大 AI 技术底座",
        "business": "N 个业务场景"
    }
    
    category_name = category_mapping.get(category_id)
    if not category_name:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                sql = """
                SELECT 
                    category_level2,
                    COUNT(*) as project_count,
                    SUM(CASE 
                        WHEN total_budget REGEXP '^[0-9]+(\.[0-9]+)?$' 
                        THEN CAST(total_budget AS DECIMAL(10,2))
                        ELSE 0 
                    END) as total_budget
                FROM project_plan 
                WHERE category_level1 = %s
                GROUP BY category_level2
                ORDER BY category_level2
                """
                cursor.execute(sql, (category_name,))
                subcategories = []
                
                for row in cursor.fetchall():
                    subcategories.append({
                        "name": row["category_level2"],
                        "project_count": row["project_count"],
                        "total_budget": float(row["total_budget"] or 0)
                    })
                
                return {"code": 200, "message": "success", "data": subcategories}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects")
async def get_planning_projects(
    category: Optional[str] = Query(None, description="一级分类"),
    subcategory: Optional[str] = Query(None, description="二级分类"),
    priority: Optional[str] = Query(None, description="优先级"),
    current_user = Depends(get_current_user)
):
    """获取规划项目列表"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                # 构建查询条件
                where_conditions = ["1=1"]
                params = []
                
                if category:
                    category_mapping = {
                        "ai": "AI²层",
                        "management": "9 大智慧管理体系",
                        "tech": "3 大 AI 技术底座", 
                        "business": "N 个业务场景"
                    }
                    category_name = category_mapping.get(category, category)
                    where_conditions.append("category_level1 = %s")
                    params.append(category_name)
                
                if subcategory:
                    where_conditions.append("category_level2 = %s")
                    params.append(subcategory)
                
                if priority:
                    where_conditions.append("priority = %s")
                    params.append(priority)
                
                # 查询项目列表
                sql = f"""
                SELECT * FROM project_plan
                WHERE {' AND '.join(where_conditions)}
                ORDER BY project_id
                """
                cursor.execute(sql, params)
                projects = []
                
                for row in cursor.fetchall():
                    projects.append({
                        "id": row["project_id"],
                        "name": row["project_name"],
                        "category": row["category_level1"],
                        "subcategory": row["category_level2"],
                        "summary": row["summary"],
                        "milestone_plan": row["milestone_plan"],
                        "priority": row["priority"],
                        "budgets": {
                            "2026": row["budget_2026"] or "0",
                            "2027": row["budget_2027"] or "0", 
                            "2028": row["budget_2028"] or "0",
                            "2029": row["budget_2029"] or "0",
                            "2030": row["budget_2030"] or "0"
                        },
                        "total_budget": row["total_budget"] or "0"
                    })
                
                return {
                    "code": 200,
                    "message": "success",
                    "data": {
                        "projects": projects,
                        "total": len(projects)
                    }
                }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}")
async def get_planning_project_detail(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """获取项目详情"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                sql = "SELECT * FROM project_plan WHERE project_id = %s"
                cursor.execute(sql, (project_id,))
                row = cursor.fetchone()
                
                if not row:
                    raise HTTPException(status_code=404, detail="项目不存在")
                
                project = {
                    "id": row["project_id"],
                    "name": row["project_name"],
                    "category": row["category_level1"],
                    "subcategory": row["category_level2"],
                    "summary": row["summary"],
                    "milestone_plan": row["milestone_plan"],
                    "priority": row["priority"],
                    "budgets": {
                        "2026": row["budget_2026"] or "0",
                        "2027": row["budget_2027"] or "0",
                        "2028": row["budget_2028"] or "0",
                        "2029": row["budget_2029"] or "0",
                        "2030": row["budget_2030"] or "0"
                    },
                    "total_budget": row["total_budget"] or "0"
                }
                
                return {"code": 200, "message": "success", "data": project}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics")
def get_planning_statistics(
    group_by: str = Query("category", description="分组方式: category, entity, priority, year"),
    current_user = Depends(get_current_user)
):
    """获取规划统计数据"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                if group_by == "category":
                    # 按分类统计
                    sql = """
                    SELECT 
                        category_level1 as name,
                        COUNT(*) as count,
                        SUM(
                            CASE 
                                WHEN total_budget REGEXP '^[0-9]+(\.[0-9]+)?$' THEN CAST(total_budget AS DECIMAL(10,2))
                                ELSE 0
                            END
                        ) as budget
                    FROM project_plan
                    GROUP BY category_level1
                    """
                elif group_by == "entity":
                    # 按实体统计
                    sql = """
                    SELECT 
                        category_level2 as name,
                        COUNT(*) as count,
                        SUM(
                            CASE 
                                WHEN total_budget REGEXP '^[0-9]+(\.[0-9]+)?$' THEN CAST(total_budget AS DECIMAL(10,2))
                                ELSE 0
                            END
                        ) as budget
                    FROM project_plan
                    GROUP BY category_level2
                    """
                elif group_by == "priority":
                    # 按优先级统计
                    sql = """
                    SELECT 
                        priority as name,
                        COUNT(*) as count,
                        SUM(
                            CASE 
                                WHEN total_budget REGEXP '^[0-9]+(\.[0-9]+)?$' THEN CAST(total_budget AS DECIMAL(10,2))
                                ELSE 0
                            END
                        ) as budget
                    FROM project_plan
                    GROUP BY priority
                    """
                elif group_by == "year":
                    # 按年份统计
                    statistics = []
                    for year in range(2026, 2031):
                        budget_col = f"budget_{year}"
                        sql = f"""
                        SELECT 
                            COUNT(*) as count,
                            SUM(
                                CASE 
                                    WHEN {budget_col} REGEXP '^[0-9]+(\.[0-9]+)?$' THEN CAST({budget_col} AS DECIMAL(10,2))
                                    ELSE 0
                                END
                            ) as budget
                        FROM project_plan
                        WHERE {budget_col} IS NOT NULL AND {budget_col} != ''
                        """
                        cursor.execute(sql)
                        result = cursor.fetchone()
                        statistics.append({
                            "name": str(year),
                            "count": result["count"],
                            "budget": float(result["budget"]) if result["budget"] else 0
                        })
                    return {"code": 200, "message": "success", "data": statistics}
                else:
                    raise HTTPException(status_code=400, detail="不支持的分组方式")
                
                # 执行查询
                cursor.execute(sql)
                results = cursor.fetchall()
                
                # 处理结果
                statistics = []
                for result in results:
                    statistics.append({
                        "name": result["name"],
                        "count": result["count"],
                        "budget": float(result["budget"]) if result["budget"] else 0
                    })
                
                return {"code": 200, "message": "success", "data": statistics}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 辅助函数：根据分类ID获取分类名称
def get_category_name(category_id: str) -> str:
    category_map = {
        "ai": "AI²层",
        "management": "9 大智慧管理体系",
        "tech": "3 大 AI 技术底座",
        "business": "N 个业务场景"
    }
    return category_map.get(category_id, category_id)

# 辅助函数：根据分类名称获取分类ID
def get_category_id(category_name: str) -> str:
    category_map = {
        "AI²层": "ai",
        "9 大智慧管理体系": "management",
        "3 大 AI 技术底座": "tech",
        "N 个业务场景": "business"
    }
    return category_map.get(category_name, "other") 

@router.post("/projects")
async def create_planning_project(
    project_data: dict,
    current_user = Depends(get_current_user)
):
    """创建规划项目"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                # 映射分类名称
                category_mapping = {
                    "ai": "AI²层",
                    "management": "9 大智慧管理体系",
                    "tech": "3 大 AI 技术底座",
                    "business": "N 个业务场景"
                }
                
                category_name = category_mapping.get(
                    project_data.get('category'), 
                    project_data.get('category')
                )
                
                sql = """
                INSERT INTO project_plan (
                    project_id, category_level1, category_level2, project_name,
                    summary, milestone_plan, priority, budget_2026, budget_2027,
                    budget_2028, budget_2029, budget_2030, total_budget
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                budgets = project_data.get('budgets', {})
                cursor.execute(sql, (
                    project_data['id'],
                    category_name,
                    project_data['subcategory'],
                    project_data['name'],
                    project_data.get('summary', ''),
                    project_data.get('milestone_plan', ''),
                    project_data.get('priority', ''),
                    budgets.get('2026', '0'),
                    budgets.get('2027', '0'),
                    budgets.get('2028', '0'),
                    budgets.get('2029', '0'),
                    budgets.get('2030', '0'),
                    project_data.get('total_budget', '0')
                ))
                
                conn.commit()

                # 发送WebSocket通知
                try:
                    await planning_ws_manager.notify_project_created({
                        "project_id": project_data['project_id'],
                        "project_name": project_data['project_name'],
                        "category_level1": project_data['category_level1'],
                        "category_level2": project_data['category_level2']
                    })
                except Exception as ws_error:
                    print(f"WebSocket通知失败: {ws_error}")

                return {"code": 200, "message": "创建成功", "data": None}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/projects/{project_id}")
async def update_planning_project(
    project_id: str,
    project_data: dict,
    current_user = Depends(get_current_user)
):
    """更新规划项目"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                sql = """
                UPDATE project_plan SET
                    project_name = %s, summary = %s, milestone_plan = %s,
                    priority = %s, budget_2026 = %s, budget_2027 = %s,
                    budget_2028 = %s, budget_2029 = %s, budget_2030 = %s,
                    total_budget = %s
                WHERE project_id = %s
                """
                
                budgets = project_data.get('budgets', {})
                cursor.execute(sql, (
                    project_data['name'],
                    project_data.get('summary', ''),
                    project_data.get('milestone_plan', ''),
                    project_data.get('priority', ''),
                    budgets.get('2026', '0'),
                    budgets.get('2027', '0'),
                    budgets.get('2028', '0'),
                    budgets.get('2029', '0'),
                    budgets.get('2030', '0'),
                    project_data.get('total_budget', '0'),
                    project_id
                ))
                
                if cursor.rowcount == 0:
                    raise HTTPException(status_code=404, detail="项目不存在")
                
                conn.commit()

                # 发送WebSocket通知
                try:
                    await planning_ws_manager.notify_project_updated({
                        "project_id": project_id,
                        "project_name": project_data['project_name']
                    })
                except Exception as ws_error:
                    print(f"WebSocket通知失败: {ws_error}")

                return {"code": 200, "message": "更新成功", "data": None}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/projects/{project_id}")
async def delete_planning_project(
    project_id: str,
    current_user = Depends(get_current_user)
):
    """删除规划项目"""
    try:
        with db_transaction() as conn:
            with conn.cursor() as cursor:
                # 先获取项目信息用于通知
                cursor.execute("SELECT project_name FROM project_plan WHERE project_id = %s", (project_id,))
                project_info = cursor.fetchone()

                if not project_info:
                    raise HTTPException(status_code=404, detail="项目不存在")

                project_name = project_info['project_name']

                # 删除项目
                sql = "DELETE FROM project_plan WHERE project_id = %s"
                cursor.execute(sql, (project_id,))

                conn.commit()

                # 发送WebSocket通知
                try:
                    await planning_ws_manager.notify_project_deleted({
                        "project_id": project_id,
                        "project_name": project_name
                    })
                except Exception as ws_error:
                    print(f"WebSocket通知失败: {ws_error}")

                return {"code": 200, "message": "删除成功", "data": None}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

