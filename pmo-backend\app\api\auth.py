#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证API - 第一个功能模块
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import jwt
import os
from app.models.user import User
from app.utils.response_utils import success_response, error_response

# 创建路由器
router = APIRouter(prefix="/auth", tags=["用户认证"])

# JWT配置
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "10080"))

# HTTP Bearer认证
security = HTTPBearer()

# 请求模型
class LoginRequest(BaseModel):
    account: str
    password: str

class RegisterRequest(BaseModel):
    account: str
    password: str
    realname: str
    email: EmailStr
    mobile: Optional[str] = ""
    role: str = "user"

class UserUpdateRequest(BaseModel):
    realname: Optional[str] = None
    nickname: Optional[str] = None
    email: Optional[EmailStr] = None
    mobile: Optional[str] = None
    phone: Optional[str] = None
    gender: Optional[str] = None

# JWT工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        account: str = payload.get("sub")
        if account is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return payload
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(token_data: Dict[str, Any] = Depends(verify_token)) -> User:
    """获取当前用户"""
    account = token_data.get("sub")
    user = User.get_by_account(account)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

# API端点
@router.post("/login")
async def login(request: LoginRequest):
    """用户登录"""
    try:
        # 验证用户
        user = User.authenticate(request.account, request.password)
        if not user:
            return error_response("账号或密码错误", status_code=401)
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.account, "role": user.role, "user_id": user.id},
            expires_delta=access_token_expires
        )
        
        return success_response({
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user.to_dict()
        }, "登录成功")
        
    except Exception as e:
        return error_response(f"登录失败: {str(e)}")

@router.post("/register")
async def register(request: RegisterRequest):
    """用户注册"""
    try:
        # 创建用户对象
        user = User()
        user.account = request.account
        user.password = User.hash_password(request.password)
        user.realname = request.realname
        user.email = request.email
        user.mobile = request.mobile or ""
        user.role = request.role
        user.join_date = datetime.now().date()
        
        # 保存用户
        if user.create():
            return success_response(user.to_dict(), "注册成功")
        else:
            return error_response("注册失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"注册失败: {str(e)}")

@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        return success_response(current_user.to_dict(), "获取用户信息成功")
    except Exception as e:
        return error_response(f"获取用户信息失败: {str(e)}")

@router.put("/me")
async def update_current_user(
    request: UserUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """更新当前用户信息"""
    try:
        # 更新用户信息
        if request.realname is not None:
            current_user.realname = request.realname
        if request.nickname is not None:
            current_user.nickname = request.nickname
        if request.email is not None:
            current_user.email = request.email
        if request.mobile is not None:
            current_user.mobile = request.mobile
        if request.phone is not None:
            current_user.phone = request.phone
        if request.gender is not None:
            current_user.gender = request.gender
        
        if current_user.update():
            return success_response(current_user.to_dict(), "更新用户信息成功")
        else:
            return error_response("更新用户信息失败")
            
    except Exception as e:
        return error_response(f"更新用户信息失败: {str(e)}")

@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    try:
        # 这里可以添加登出日志记录
        return success_response(None, "登出成功")
    except Exception as e:
        return error_response(f"登出失败: {str(e)}")

@router.get("/users")
async def get_users(
    page: int = 1,
    page_size: int = 20,
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取用户列表（需要管理员权限）"""
    try:
        # 检查权限
        if current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        result = User.get_all_users(page=page, page_size=page_size, keyword=keyword)
        return success_response(result, "获取用户列表成功")
        
    except Exception as e:
        return error_response(f"获取用户列表失败: {str(e)}")

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_user)
):
    """删除用户（需要管理员权限）"""
    try:
        # 检查权限
        if current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        # 不能删除自己
        if user_id == current_user.id:
            return error_response("不能删除自己", status_code=400)
        
        # 获取要删除的用户
        user = User.get_by_id(user_id)
        if not user:
            return error_response("用户不存在", status_code=404)
        
        if user.delete():
            return success_response(None, "删除用户成功")
        else:
            return error_response("删除用户失败")
            
    except Exception as e:
        return error_response(f"删除用户失败: {str(e)}")

@router.get("/check-token")
async def check_token(current_user: User = Depends(get_current_user)):
    """检查令牌有效性"""
    try:
        return success_response({
            "valid": True,
            "user": current_user.to_dict()
        }, "令牌有效")
    except Exception as e:
        return error_response(f"令牌验证失败: {str(e)}")
