#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
云函数入口文件，用于处理周报相关的请求
"""

import pymysql
import json
import traceback
from datetime import datetime, date

# 数据库连接配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    config = {
        'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
        'port': 3306,
        'user': 'cyh',
        'password': 'Qq188788',
        'database': 'kanban',
        'charset': 'utf8mb4'
    }
    return pymysql.connect(**config)

def date_handler(obj):
    """处理日期类型的JSON序列化"""
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    else:
        return obj

def format_response(success, message, data=None):
    """格式化响应"""
    try:
        # 如果data是pymysql的结果对象，转换为普通字典
        if data is not None:
            if isinstance(data, (list, tuple)):
                data = [dict(item) if hasattr(item, 'keys') else item for item in data]
            elif hasattr(data, 'keys'):
                data = dict(data)

        response = {
            "success": success,
            "message": message,
            "data": data
        }
        
        return {
            "isBase64Encoded": False,
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps(response, ensure_ascii=False, default=str)
        }
    except Exception as e:
        print(f"Response formatting error: {str(e)}")
        error_response = {
            "success": False,
            "message": f"响应格式化失败: {str(e)}",
            "data": None
        }
        return {
            "isBase64Encoded": False,
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps(error_response, ensure_ascii=False)
        }

def get_projects(params):
    """获取项目列表
    参数:
        user_id: 用户ID
        role: 用户角色
    """
    user_id = params.get("user_id")
    role = params.get("role")
    
    print(f"调用 get_projects - user_id: {user_id}, role: {role}")
    
    # 参数验证
    if not user_id:
        return format_response(False, "未提供user_id，请先登录")
    
    if role is None:
        return format_response(False, "未提供用户角色信息")
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查数据表状态
            print("检查数据库表状态...")
            check_weekly_sql = "SELECT COUNT(*) as count FROM weekly_reports"
            cursor.execute(check_weekly_sql)
            weekly_count = cursor.fetchone()[0]  # 使用索引访问元组
            print(f"weekly_reports 表中有 {weekly_count} 条记录")
            
            check_project_sql = "SELECT COUNT(*) as count FROM Project_Account_Book"
            cursor.execute(check_project_sql)
            project_count = cursor.fetchone()[0]  # 使用索引访问元组
            print(f"Project_Account_Book 表中有 {project_count} 条记录")
            
            # 检查Project_Account_Book表的结构
            desc_project_sql = "DESC Project_Account_Book"
            cursor.execute(desc_project_sql)
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]  # 使用索引访问元组
            print(f"Project_Account_Book 表的列名: {column_names}")
            
            # 如果是管理员(role=1)，获取所有项目
            if role == 1:
                print("以管理员身份查询所有项目...")
                sql = """
                SELECT 
                    p.project_code, 
                    p.project_name, 
                    p.investment_entity as department,
                    p.investment_entity,
                    p.excellence_level,
                    COUNT(w.id) AS report_count, 
                    MAX(w.updated_at) AS last_update_time
                FROM 
                    Project_Account_Book p
                LEFT JOIN 
                    weekly_reports w ON p.project_code = w.project_code
                GROUP BY 
                    p.project_code, p.project_name, p.investment_entity, p.excellence_level
                ORDER BY 
                    CASE p.excellence_level
                        WHEN '紧急且重要' THEN 1
                        WHEN '不紧急但重要' THEN 2
                        WHEN '紧急但不重要' THEN 3
                        WHEN '不紧急且不重要' THEN 4
                        ELSE 5
                    END,
                    p.investment_entity DESC, p.project_code
                """
                print(f"执行管理员SQL查询: {sql}")
                cursor.execute(sql)
            else:
                # 非管理员只能看到自己ITBP条线的项目
                print("以普通用户身份查询条线项目...")
                sql = """
                SELECT DISTINCT
                    p.project_code, 
                    p.project_name, 
                    p.investment_entity as department,
                    p.investment_entity,
                    p.excellence_level,
                    COUNT(w.id) AS report_count, 
                    MAX(w.updated_at) AS last_update_time
                FROM 
                    Project_Account_Book p
                LEFT JOIN 
                    weekly_reports w ON p.project_code = w.project_code
                INNER JOIN 
                    itbp i ON p.investment_entity = i.investment_entity
                WHERE 
                    i.UserID = %s
                GROUP BY 
                    p.project_code, p.project_name, p.investment_entity, p.excellence_level
                ORDER BY 
                    CASE p.excellence_level
                        WHEN '紧急且重要' THEN 1
                        WHEN '不紧急但重要' THEN 2
                        WHEN '紧急但不重要' THEN 3
                        WHEN '不紧急且不重要' THEN 4
                        ELSE 5
                    END,
                    p.investment_entity DESC, p.project_code
                """
                print(f"执行普通用户SQL查询: {sql}")
                cursor.execute(sql, (user_id,))
            
            results = cursor.fetchall()
            print(f"查询结果: 找到 {len(results)} 个项目")
            
            # 将元组转换为字典列表
            projects = []
            for row in results:
                project = {
                    'project_code': row[0],
                    'project_name': row[1],
                    'department': row[2],
                    'investment_entity': row[3],
                    'excellence_level': row[4],
                    'report_count': row[5],
                    'last_update_time': row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None
                }
                projects.append(project)
            
            # 打印前5个项目的信息用于调试
            if projects and len(projects) > 0:
                print("前5个项目详情:")
                for i, project in enumerate(projects[:5]):
                    print(f"项目 {i+1}: {project}")
            
            return format_response(True, "获取项目列表成功", projects)
    
    except Exception as e:
        print(f"获取项目列表异常: {str(e)}")
        print(traceback.format_exc())
        return format_response(False, f"获取项目列表失败: {str(e)}")
    
    finally:
        if conn:
            conn.close()

def get_weekly_reports(params):
    """获取周报列表"""
    conn = None
    cursor = None
    try:
        project_code = params.get('project_code')
        year = params.get('year')
        report_id = params.get('report_id')
    
        # 确保weekly_reports表存在
        check_weekly_reports_table()
    
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 构建基础查询
        base_query = "SELECT * FROM weekly_reports"

        # 根据参数构建WHERE子句
        where_clauses = []
        params_list = []

        if project_code:
            where_clauses.append("project_code = %s")
            params_list.append(project_code)

        if year:
            where_clauses.append("YEAR(created_at) = %s")
            params_list.append(year)
                
        if report_id:
            where_clauses.append("id = %s")
            params_list.append(report_id)

        # 组合WHERE子句
        if where_clauses:
            base_query += " WHERE " + " AND ".join(where_clauses)

        # 添加排序
        base_query += " ORDER BY created_at DESC"

        # 执行查询
        cursor.execute(base_query, params_list)
        results = cursor.fetchall()
                
        # 将结果转换为字典列表
        reports = []
        for row in results:
            # 处理日期和时间字段
            date_fields = ['project_start_date', 'contract_date', 'period_start_date', 'period_end_date']
            datetime_fields = ['created_at', 'updated_at']
            
            report = {}
            for key, value in row.items():
                if key in date_fields and value:
                    report[key] = value.strftime('%Y-%m-%d')
                elif key in datetime_fields and value:
                    report[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(value, (int, float, str, bool)) or value is None:
                    report[key] = value
                else:
                    report[key] = str(value)
            
            reports.append(report)
        
        return format_response(True, "获取周报列表成功", reports)
    
    except Exception as e:
        print(f"获取周报列表失败: {str(e)}")
        print(traceback.format_exc())
        return format_response(False, f"获取周报列表失败: {str(e)}")
    
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def update_itbp_report_count(project_code):
    """更新ITBP项目的周报数量
    
    Args:
        project_code: 项目代码
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取项目的周报数量
        sql = "SELECT COUNT(*) as count FROM weekly_reports WHERE project_code = %s"
        cursor.execute(sql, (project_code,))
        result = cursor.fetchone()
        report_count = result['count'] if result else 0
        
        # 更新最后更新时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return report_count, current_time
        
    except Exception as e:
        print(f"更新ITBP项目周报数量失败: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None, None

def add_weekly_report(params):
    """添加周报
    参数:
        report_data: 周报数据
    """
    try:
        report_data = params.get("report_data", {})
        
        # 设置创建和更新时间
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        report_data['created_at'] = now
        report_data['updated_at'] = now
        
        # 进度值已经是0-1之间的小数，不需要再除以100
        # 保留progress_fields定义，用于返回时的处理
        progress_fields = ['requirement_progress', 'development_progress', 'project_progress', 'cost_progress']
        
        print(f"添加周报数据: {report_data}")
        
        # 检查必填字段
        required_fields = ['project_code', 'project_name']
        for field in required_fields:
            if field not in report_data or not report_data[field]:
                return format_response(False, f"缺少必填字段: {field}")
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建SQL语句
            fields = list(report_data.keys())
            values = list(report_data.values())
            placeholders = ["%s"] * len(fields)
            
            sql = f"INSERT INTO weekly_reports ({','.join(fields)}) VALUES ({','.join(placeholders)})"
            
            # 执行插入
            cursor.execute(sql, values)
            new_id = cursor.lastrowid
            conn.commit()
            
            # 更新ITBP项目周报数量
            report_count, last_update_time = update_itbp_report_count(report_data['project_code'])
            
            return format_response(True, "添加周报成功", {
                "id": new_id,
                "report_count": report_count,
                "last_update_time": last_update_time
            })
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        
        finally:
            if conn:
                conn.close()
                
    except Exception as e:
        return format_response(False, f"添加周报失败: {str(e)}")

def update_weekly_report(params):
    """更新周报
    参数:
        report_data: 更新的周报数据
    """
    report_data = params.get("report_data", {})
    report_id = report_data.get("id")
    
    # 设置更新时间
    report_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 进度值已经是0-1之间的小数，不需要额外处理
    # 保留progress_fields定义，用于后续可能的处理
    progress_fields = ['requirement_progress', 'development_progress', 'project_progress', 'cost_progress']
    
    # 处理日期字段，将None或空字符串转换为NULL
    date_fields = ['period_start_date', 'period_end_date', 'project_start_date', 'contract_date']
    for field in date_fields:
        if field in report_data:
            if not report_data[field] or report_data[field] == "/" or report_data[field].strip() == "":
                report_data[field] = None
    
    print(f"更新周报 - report_id: {report_id}, 数据: {report_data}")
    
    if not report_id:
        return format_response(False, "缺少周报ID")
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 先检查周报是否存在
            check_sql = "SELECT COUNT(*) FROM weekly_reports WHERE id = %s"
            cursor.execute(check_sql, (report_id,))
            result = cursor.fetchone()
            if result[0] == 0:  # 使用索引访问元组
                return format_response(False, f"未找到ID为 {report_id} 的周报")
            
            # 构建UPDATE语句
            update_fields = []
            values = []
            for field, value in report_data.items():
                if field != 'id':  # 排除ID字段
                    update_fields.append(f"{field} = %s")
                    values.append(value)
            
            # 添加WHERE条件的ID值
            values.append(report_id)
            
            sql = f"UPDATE weekly_reports SET {', '.join(update_fields)} WHERE id = %s"
            
            print(f"执行SQL: {sql}")
            print(f"参数值: {values}")
            
            cursor.execute(sql, values)
            conn.commit()
            
            # 获取更新后的数据
            select_sql = """
            SELECT *
            FROM weekly_reports
            WHERE id = %s
            """
            cursor.execute(select_sql, (report_id,))
            updated_data = cursor.fetchone()
            
            # 转换日期格式
            if updated_data:
                date_fields = ['project_start_date', 'contract_date', 'period_start_date', 'period_end_date']
                datetime_fields = ['created_at', 'updated_at']
                
                for field in date_fields:
                    if field in updated_data and updated_data[field]:
                        updated_data[field] = updated_data[field].strftime('%Y-%m-%d')
                
                for field in datetime_fields:
                    if field in updated_data and updated_data[field]:
                        updated_data[field] = updated_data[field].strftime('%Y-%m-%d %H:%M:%S')
            
            return format_response(True, "更新周报成功", updated_data)
    
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"更新周报失败: {str(e)}")
        print(traceback.format_exc())
        return format_response(False, f"更新周报失败: {str(e)}")
    
    finally:
        if conn:
            conn.close()

def delete_weekly_report(params):
    """删除周报
    参数:
        report_id: 周报ID
    """
    try:
        report_id = params.get("report_id")
        project_code = params.get("project_code")  # 需要前端传入project_code
        
        if not report_id:
            return format_response(False, "缺少周报ID")
            
        if not project_code:
            return format_response(False, "缺少项目代码")
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 执行删除
            sql = "DELETE FROM weekly_reports WHERE id = %s"
            cursor.execute(sql, (report_id,))
            affected_rows = cursor.rowcount
            conn.commit()
            
            if affected_rows == 0:
                return format_response(False, f"未找到ID为{report_id}的周报")
            
            # 更新ITBP项目周报数量
            report_count, last_update_time = update_itbp_report_count(project_code)
            
            return format_response(True, "删除周报成功", {
                "report_count": report_count,
                "last_update_time": last_update_time
            })
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        
        finally:
            if conn:
                conn.close()
                
    except Exception as e:
        return format_response(False, f"删除周报失败: {str(e)}")

def get_report_stats(params):
    """获取项目周报统计信息
    参数:
        year: 年份(可选，默认当前年)
    """
    year = params.get("year", datetime.now().year)
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            sql = """
            SELECT project_code, project_name, department, 
                   COUNT(id) AS report_count, MAX(updated_at) AS last_update_time
            FROM weekly_reports
            WHERE YEAR(created_at) = %s
            GROUP BY project_code, project_name, department
            ORDER BY project_code
            """
            cursor.execute(sql, (year,))
            stats = cursor.fetchall()
            
            # 转换日期格式
            for item in stats:
                if item['last_update_time']:
                    item['last_update_time'] = item['last_update_time'].strftime('%Y-%m-%d %H:%M:%S')
            
            return format_response(True, "获取周报统计成功", stats)
    
    except Exception as e:
        return format_response(False, f"获取周报统计失败: {str(e)}")
    
    finally:
        if conn:
            conn.close()

def check_weekly_reports_table():
    """检查weekly_reports表是否存在，不存在则创建"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询是否存在weekly_reports表
        cursor.execute("SHOW TABLES LIKE 'weekly_reports'")
        if not cursor.fetchone():
            # 创建表
            create_table_sql = """
            CREATE TABLE weekly_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                project_code VARCHAR(50) NOT NULL COMMENT '项目代码',
                project_name VARCHAR(100) NOT NULL COMMENT '项目名称',
                department VARCHAR(50) COMMENT '负责部门',
                period_start_date DATE COMMENT '周期开始日期',
                period_end_date DATE COMMENT '周期结束日期',
                project_start_date DATE COMMENT '立项日期',
                project_amount DECIMAL(10,2) COMMENT '项目金额(万元)',
                construction_period INT COMMENT '建设周期(月)',
                contract_date DATE COMMENT '合同签订日期',
                total_cost DECIMAL(10,2) COMMENT '总成本(万元)',
                outsourcing_cost DECIMAL(10,2) COMMENT '外采成本(万元)',
                labor_cost DECIMAL(10,2) COMMENT '人力成本(万元)',
                requirement_progress DECIMAL(5,3) COMMENT '需求进度(0-1)',
                development_progress DECIMAL(5,3) COMMENT '开发进度(0-1)',
                project_progress DECIMAL(5,3) COMMENT '项目进度(0-1)',
                cost_progress DECIMAL(5,3) COMMENT '成本进度(0-1)',
                overall_situation TEXT COMMENT '一、项目整体情况',
                milestone_tasks TEXT COMMENT '二、项目里程碑/关键任务情况',
                quality_situation TEXT COMMENT '三、项目质量情况',
                resource_input TEXT COMMENT '四、资源投入情况',
                last_week_review TEXT COMMENT '五、上周工作回顾',
                this_week_completion TEXT COMMENT '六、本周完成内容',
                next_week_plan TEXT COMMENT '七、下周工作计划',
                communication TEXT COMMENT '八、沟通与协调情况',
                key_issues_risks TEXT COMMENT '九、关键问题/风险',
                conclusions TEXT COMMENT '十、结论与建议',
                created_by VARCHAR(50) COMMENT '创建人',
                created_at DATETIME NOT NULL COMMENT '创建时间',
                updated_at DATETIME NOT NULL COMMENT '更新时间',
                INDEX idx_project (project_code),
                INDEX idx_department (department),
                INDEX idx_period (period_start_date, period_end_date),
                INDEX idx_created_at (created_at),
                INDEX idx_updated_at (updated_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目周报表';
            """
            cursor.execute(create_table_sql)
            print("成功创建weekly_reports表")
        else:
            # 检查并更新表结构
            try:
                # 检查字段是否存在
                cursor.execute("DESCRIBE weekly_reports")
                existing_columns = [row[0] for row in cursor.fetchall()]
                
                # 需要添加的字段
                if 'period_start_date' not in existing_columns:
                    cursor.execute("ALTER TABLE weekly_reports ADD COLUMN period_start_date DATE COMMENT '周期开始日期' AFTER department")
                
                if 'period_end_date' not in existing_columns:
                    cursor.execute("ALTER TABLE weekly_reports ADD COLUMN period_end_date DATE COMMENT '周期结束日期' AFTER period_start_date")
                
                if 'created_by' not in existing_columns:
                    cursor.execute("ALTER TABLE weekly_reports ADD COLUMN created_by VARCHAR(50) COMMENT '创建人' AFTER conclusions")
                
                # 修改字段类型
                cursor.execute("""
                    ALTER TABLE weekly_reports
                    MODIFY requirement_progress DECIMAL(5,3) COMMENT '需求进度(0-1)',
                    MODIFY development_progress DECIMAL(5,3) COMMENT '开发进度(0-1)',
                    MODIFY project_progress DECIMAL(5,3) COMMENT '项目进度(0-1)',
                    MODIFY cost_progress DECIMAL(5,3) COMMENT '成本进度(0-1)'
                """)
                
                # 添加索引（如果不存在）
                cursor.execute("SHOW INDEX FROM weekly_reports")
                existing_indexes = [row[2] for row in cursor.fetchall()]  # 索引名在第三列
                
                if 'idx_period' not in existing_indexes:
                    cursor.execute("CREATE INDEX idx_period ON weekly_reports(period_start_date, period_end_date)")
                
                if 'idx_created_at' not in existing_indexes:
                    cursor.execute("CREATE INDEX idx_created_at ON weekly_reports(created_at)")
                
                if 'idx_updated_at' not in existing_indexes:
                    cursor.execute("CREATE INDEX idx_updated_at ON weekly_reports(updated_at)")
                
                conn.commit()
                print("成功更新weekly_reports表结构")
            except Exception as alter_error:
                print(f"更新表结构失败: {str(alter_error)}")
                raise
        
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"检查/创建weekly_reports表失败: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        raise

def main_handler(event, context):
    """云函数入口"""
    try:
        print(f"Received event: {json.dumps(event)}")
        if not event:
            return format_response(False, "请求参数不能为空")
            
        # 获取请求参数
        request_params = json.loads(event.get("body", "{}"))
        if not request_params:
            return format_response(False, "请求参数不能为空")
            
        action = request_params.get("action")
        params = request_params.get("params", {})
        
        print(f"处理请求: action={action}, params={params}")
        
        # 检查weekly_reports表
        check_weekly_reports_table()
        
        # 根据action调用对应的处理函数
        if action == "get_projects":
            return get_projects(params)
        elif action == "get_weekly_reports":
            return get_weekly_reports(params)
        elif action == "add_weekly_report":
            return add_weekly_report(params)
        elif action == "update_weekly_report":
            return update_weekly_report(params)
        elif action == "delete_weekly_report":
            return delete_weekly_report(params)
        elif action == "get_report_stats":
            return get_report_stats(params)
        else:
            return format_response(False, f"不支持的操作: {action}")
    
    except Exception as e:
        print(f"处理请求失败: {str(e)}")
        print(traceback.format_exc())
        return format_response(False, f"处理请求失败: {str(e)}") 