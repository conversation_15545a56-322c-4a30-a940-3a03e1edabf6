#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档模型 - 参考禅道zt_doc表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import pymysql
from app.core.database import get_db_connection, close_db_connection

class DocumentLib:
    """文档库模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.type: str = "product"
        self.product: int = 0
        self.project: int = 0
        self.execution: int = 0
        self.name: str = ""
        self.acl: str = "default"
        self.groups: Optional[str] = None
        self.users: Optional[str] = None
        self.main: str = "0"
        self.collector: Optional[str] = None
        self.order: int = 0
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, lib_id: int) -> Optional['DocumentLib']:
        """根据ID获取文档库"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_doclib WHERE id = %s AND deleted = '0'
            """, (lib_id,))
            
            row = cursor.fetchone()
            if row:
                lib = cls()
                lib._load_from_dict(row)
                return lib
            return None
            
        except Exception as e:
            print(f"获取文档库失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_libs(cls, lib_type: str = "", product_id: int = 0, project_id: int = 0) -> List[Dict[str, Any]]:
        """获取文档库列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["deleted = '0'"]
            params = []
            
            if lib_type:
                where_conditions.append("type = %s")
                params.append(lib_type)
            
            if product_id > 0:
                where_conditions.append("product = %s")
                params.append(product_id)
            
            if project_id > 0:
                where_conditions.append("project = %s")
                params.append(project_id)
            
            where_clause = " AND ".join(where_conditions)
            
            cursor.execute(f"""
                SELECT l.*, p.name as product_name, pr.name as project_name
                FROM zt_doclib l
                LEFT JOIN zt_product p ON l.product = p.id
                LEFT JOIN zt_project pr ON l.project = pr.id
                WHERE {where_clause}
                ORDER BY l.`order` ASC, l.id ASC
            """, params)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取文档库列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "vision": self.vision,
            "type": self.type,
            "product": self.product,
            "project": self.project,
            "execution": self.execution,
            "name": self.name,
            "acl": self.acl,
            "groups": self.groups,
            "users": self.users,
            "main": self.main,
            "collector": self.collector,
            "order": self.order,
            "deleted": self.deleted
        }


class Document:
    """文档模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.product: int = 0
        self.project: int = 0
        self.execution: int = 0
        self.lib: int = 0
        self.module: int = 0
        self.title: str = ""
        self.keywords: str = ""
        self.type: str = "text"
        self.content: Optional[str] = None
        self.contentType: str = "html"
        self.url: Optional[str] = None
        self.files: Optional[str] = None
        self.addedBy: str = ""
        self.addedDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.acl: str = "open"
        self.groups: Optional[str] = None
        self.users: Optional[str] = None
        self.version: int = 1
        self.status: str = "normal"
        self.views: int = 0
        self.collector: Optional[str] = None
        self.order: int = 0
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, doc_id: int) -> Optional['Document']:
        """根据ID获取文档"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT d.*, l.name as lib_name, p.name as product_name, pr.name as project_name
                FROM zt_doc d
                LEFT JOIN zt_doclib l ON d.lib = l.id
                LEFT JOIN zt_product p ON d.product = p.id
                LEFT JOIN zt_project pr ON d.project = pr.id
                WHERE d.id = %s AND d.deleted = '0'
            """, (doc_id,))
            
            row = cursor.fetchone()
            if row:
                doc = cls()
                doc._load_from_dict(row)
                return doc
            return None
            
        except Exception as e:
            print(f"获取文档失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_docs(cls, page: int = 1, page_size: int = 20, lib_id: int = 0, 
                    doc_type: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取文档列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["d.deleted = '0'"]
            params = []
            
            if lib_id > 0:
                where_conditions.append("d.lib = %s")
                params.append(lib_id)
            
            if doc_type:
                where_conditions.append("d.type = %s")
                params.append(doc_type)
            
            if keyword:
                where_conditions.append("(d.title LIKE %s OR d.keywords LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_doc d WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT d.id, d.title, d.type, d.status, d.addedBy, d.addedDate,
                       d.editedBy, d.editedDate, d.views, d.lib, l.name as lib_name,
                       d.product, p.name as product_name
                FROM zt_doc d
                LEFT JOIN zt_doclib l ON d.lib = l.id
                LEFT JOIN zt_product p ON d.product = p.id
                WHERE {where_clause}
                ORDER BY d.`order` ASC, d.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            docs = cursor.fetchall()
            
            return {
                "docs": docs,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取文档列表失败: {e}")
            return {"docs": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建文档"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入文档
            cursor.execute("""
                INSERT INTO zt_doc (
                    vision, product, project, execution, lib, module, title, keywords,
                    type, content, contentType, url, addedBy, addedDate, acl,
                    `groups`, `users`, version, status, `order`
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s, %s, %s
                )
            """, (
                self.vision, self.product, self.project, self.execution, self.lib,
                self.module, self.title, self.keywords, self.type, self.content,
                self.contentType, self.url, self.addedBy, self.acl, self.groups,
                self.users, self.version, self.status, self.order
            ))
            
            self.id = cursor.lastrowid
            
            # 插入文档内容版本
            cursor.execute("""
                INSERT INTO zt_doccontent (doc, title, content, type, version, addedBy, addedDate)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
            """, (self.id, self.title, self.content, self.type, self.version, self.addedBy))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建文档失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新文档信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_doc SET 
                    title = %s, keywords = %s, type = %s, content = %s,
                    contentType = %s, url = %s, editedBy = %s, editedDate = NOW(),
                    status = %s, `order` = %s
                WHERE id = %s
            """, (
                self.title, self.keywords, self.type, self.content,
                self.contentType, self.url, self.editedBy, self.status,
                self.order, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新文档失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除文档"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_doc SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除文档失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def increase_views(self) -> bool:
        """增加浏览次数"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_doc SET views = views + 1 WHERE id = %s
            """, (self.id,))
            
            self.views += 1
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"增加浏览次数失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "vision": self.vision,
            "product": self.product,
            "project": self.project,
            "execution": self.execution,
            "lib": self.lib,
            "module": self.module,
            "title": self.title,
            "keywords": self.keywords,
            "type": self.type,
            "content": self.content,
            "contentType": self.contentType,
            "url": self.url,
            "files": self.files,
            "addedBy": self.addedBy,
            "addedDate": self.addedDate.isoformat() if self.addedDate else None,
            "editedBy": self.editedBy,
            "editedDate": self.editedDate.isoformat() if self.editedDate else None,
            "acl": self.acl,
            "groups": self.groups,
            "users": self.users,
            "version": self.version,
            "status": self.status,
            "views": self.views,
            "collector": self.collector,
            "order": self.order,
            "deleted": self.deleted
        }
