#!/usr/bin/env python3
"""
文档解析缓存 - 避免重复解析
"""

import hashlib
import json
import time
import logging
import os
from typing import Optional, Dict, Any
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

class DocumentCache:
    """文档解析结果缓存"""
    
    def __init__(self, cache_dir: str = "cache", max_cache_size_mb: int = 500):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "document_cache.db"
        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        self._init_database()
    
    def _init_database(self):
        """初始化缓存数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS document_cache (
                        file_hash TEXT PRIMARY KEY,
                        filename TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        content_type TEXT,
                        parsed_content TEXT NOT NULL,
                        parse_time REAL NOT NULL,
                        access_time REAL NOT NULL,
                        created_time REAL NOT NULL
                    )
                """)
                
                # 创建索引
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_access_time 
                    ON document_cache(access_time)
                """)
                
                conn.commit()
                logger.info("文档缓存数据库初始化完成")
                
        except Exception as e:
            logger.error(f"缓存数据库初始化失败: {str(e)}")
    
    def _calculate_file_hash(self, file_content: bytes, filename: str) -> str:
        """计算文件哈希值"""
        hasher = hashlib.sha256()
        hasher.update(file_content)
        hasher.update(filename.encode('utf-8'))
        return hasher.hexdigest()
    
    def get_cached_result(self, file_content: bytes, filename: str) -> Optional[str]:
        """获取缓存的解析结果"""
        try:
            file_hash = self._calculate_file_hash(file_content, filename)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT parsed_content, parse_time 
                    FROM document_cache 
                    WHERE file_hash = ?
                """, (file_hash,))
                
                result = cursor.fetchone()
                
                if result:
                    parsed_content, parse_time = result
                    
                    # 更新访问时间
                    conn.execute("""
                        UPDATE document_cache 
                        SET access_time = ? 
                        WHERE file_hash = ?
                    """, (time.time(), file_hash))
                    
                    conn.commit()
                    
                    logger.info(f"缓存命中: {filename} (解析时间: {parse_time:.2f}s)")
                    return parsed_content
                
                return None
                
        except Exception as e:
            logger.error(f"获取缓存失败: {str(e)}")
            return None
    
    def cache_result(self, file_content: bytes, filename: str, content_type: str, 
                    parsed_content: str, parse_time: float):
        """缓存解析结果"""
        try:
            file_hash = self._calculate_file_hash(file_content, filename)
            current_time = time.time()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO document_cache 
                    (file_hash, filename, file_size, content_type, parsed_content, 
                     parse_time, access_time, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    file_hash,
                    filename,
                    len(file_content),
                    content_type,
                    parsed_content,
                    parse_time,
                    current_time,
                    current_time
                ))
                
                conn.commit()
                
                logger.info(f"缓存保存: {filename} (解析时间: {parse_time:.2f}s)")
                
                # 检查缓存大小并清理
                self._cleanup_cache_if_needed()
                
        except Exception as e:
            logger.error(f"缓存保存失败: {str(e)}")
    
    def _cleanup_cache_if_needed(self):
        """如果需要，清理缓存"""
        try:
            # 检查数据库大小
            db_size = os.path.getsize(self.db_path)
            
            if db_size > self.max_cache_size_bytes:
                logger.info(f"缓存大小超限 ({db_size / 1024 / 1024:.1f}MB)，开始清理...")
                
                with sqlite3.connect(self.db_path) as conn:
                    # 删除最旧的20%记录
                    cursor = conn.execute("SELECT COUNT(*) FROM document_cache")
                    total_count = cursor.fetchone()[0]
                    delete_count = max(1, total_count // 5)
                    
                    conn.execute("""
                        DELETE FROM document_cache 
                        WHERE file_hash IN (
                            SELECT file_hash FROM document_cache 
                            ORDER BY access_time ASC 
                            LIMIT ?
                        )
                    """, (delete_count,))
                    
                    conn.commit()
                    
                    logger.info(f"清理了 {delete_count} 个缓存记录")
                    
        except Exception as e:
            logger.error(f"缓存清理失败: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_files,
                        SUM(file_size) as total_size,
                        AVG(parse_time) as avg_parse_time,
                        MIN(created_time) as oldest_cache,
                        MAX(access_time) as latest_access
                    FROM document_cache
                """)
                
                result = cursor.fetchone()
                
                if result:
                    total_files, total_size, avg_parse_time, oldest_cache, latest_access = result
                    
                    db_size = os.path.getsize(self.db_path)
                    
                    return {
                        "total_cached_files": total_files or 0,
                        "total_content_size_mb": (total_size or 0) / 1024 / 1024,
                        "database_size_mb": db_size / 1024 / 1024,
                        "average_parse_time": avg_parse_time or 0,
                        "oldest_cache_age_hours": (
                            (time.time() - oldest_cache) / 3600 
                            if oldest_cache else 0
                        ),
                        "latest_access_age_hours": (
                            (time.time() - latest_access) / 3600 
                            if latest_access else 0
                        )
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {}
    
    def clear_cache(self):
        """清空所有缓存"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM document_cache")
                conn.commit()
                
            logger.info("缓存已清空")
            
        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")
    
    def remove_old_cache(self, max_age_days: int = 30):
        """删除过期缓存"""
        try:
            cutoff_time = time.time() - (max_age_days * 24 * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM document_cache 
                    WHERE access_time < ?
                """, (cutoff_time,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    logger.info(f"删除了 {deleted_count} 个过期缓存记录")
                
        except Exception as e:
            logger.error(f"删除过期缓存失败: {str(e)}")

# 全局缓存实例
document_cache = DocumentCache()

def get_document_cache() -> DocumentCache:
    """获取全局文档缓存实例"""
    return document_cache
