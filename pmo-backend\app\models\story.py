#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求模型 - 参考禅道zt_story表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Story:
    """需求模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.parent: int = 0
        self.product: int = 0
        self.branch: int = 0
        self.module: int = 0
        self.plan: Optional[str] = None
        self.source: str = ""
        self.sourceNote: str = ""
        self.fromBug: int = 0
        self.feedback: int = 0
        self.title: str = ""
        self.keywords: str = ""
        self.type: str = "story"
        self.category: str = "feature"
        self.pri: int = 3
        self.estimate: float = 0
        self.status: str = "draft"
        self.subStatus: str = ""
        self.color: str = ""
        self.stage: str = "wait"
        self.mailto: Optional[str] = None
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.assignedTo: str = ""
        self.assignedDate: Optional[datetime] = None
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.reviewedBy: str = ""
        self.reviewedDate: Optional[datetime] = None
        self.closedBy: str = ""
        self.closedDate: Optional[datetime] = None
        self.closedReason: str = ""
        self.toBug: int = 0
        self.childStories: Optional[str] = None
        self.linkStories: Optional[str] = None
        self.duplicateStory: int = 0
        self.version: int = 1
        self.order: int = 0
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, story_id: int) -> Optional['Story']:
        """根据ID获取需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT s.*, ss.spec, ss.verify, p.name as product_name
                FROM zt_story s
                LEFT JOIN zt_storyspec ss ON s.id = ss.story AND s.version = ss.version
                LEFT JOIN zt_product p ON s.product = p.id
                WHERE s.id = %s AND s.deleted = '0'
            """, (story_id,))
            
            row = cursor.fetchone()
            if row:
                story = cls()
                story._load_from_dict(row)
                return story
            return None
            
        except Exception as e:
            print(f"获取需求失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_stories(cls, page: int = 1, page_size: int = 20, product_id: int = 0, 
                       status: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取需求列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["s.deleted = '0'"]
            params = []
            
            if product_id > 0:
                where_conditions.append("s.product = %s")
                params.append(product_id)
            
            if status:
                where_conditions.append("s.status = %s")
                params.append(status)
            
            if keyword:
                where_conditions.append("(s.title LIKE %s OR s.keywords LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_story s WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT s.id, s.title, s.type, s.category, s.pri, s.status, s.stage,
                       s.openedBy, s.openedDate, s.assignedTo, s.estimate, s.product,
                       p.name as product_name
                FROM zt_story s
                LEFT JOIN zt_product p ON s.product = p.id
                WHERE {where_clause}
                ORDER BY s.`order` ASC, s.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            stories = cursor.fetchall()
            
            return {
                "stories": stories,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取需求列表失败: {e}")
            return {"stories": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self, spec: str = "", verify: str = "") -> bool:
        """创建需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入需求
            cursor.execute("""
                INSERT INTO zt_story (
                    vision, parent, product, branch, module, plan, source, sourceNote,
                    fromBug, feedback, title, keywords, type, category, pri, estimate,
                    status, subStatus, color, stage, mailto, openedBy, openedDate,
                    assignedTo, version, `order`
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s
                )
            """, (
                self.vision, self.parent, self.product, self.branch, self.module,
                self.plan, self.source, self.sourceNote, self.fromBug, self.feedback,
                self.title, self.keywords, self.type, self.category, self.pri,
                self.estimate, self.status, self.subStatus, self.color, self.stage,
                self.mailto, self.openedBy, self.assignedTo, self.version, self.order
            ))
            
            self.id = cursor.lastrowid
            
            # 插入需求规格
            if spec or verify:
                cursor.execute("""
                    INSERT INTO zt_storyspec (story, version, title, spec, verify) 
                    VALUES (%s, %s, %s, %s, %s)
                """, (self.id, self.version, self.title, spec, verify))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建需求失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self, spec: str = None, verify: str = None) -> bool:
        """更新需求信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 更新需求基本信息
            cursor.execute("""
                UPDATE zt_story SET 
                    title = %s, keywords = %s, type = %s, category = %s, pri = %s,
                    estimate = %s, status = %s, stage = %s, assignedTo = %s,
                    lastEditedBy = %s, lastEditedDate = NOW(), `order` = %s
                WHERE id = %s
            """, (
                self.title, self.keywords, self.type, self.category, self.pri,
                self.estimate, self.status, self.stage, self.assignedTo,
                self.lastEditedBy, self.order, self.id
            ))
            
            # 更新需求规格
            if spec is not None or verify is not None:
                # 检查是否存在当前版本的规格
                cursor.execute("""
                    SELECT 1 FROM zt_storyspec WHERE story = %s AND version = %s
                """, (self.id, self.version))
                
                if cursor.fetchone():
                    # 更新现有规格
                    cursor.execute("""
                        UPDATE zt_storyspec SET title = %s, spec = %s, verify = %s
                        WHERE story = %s AND version = %s
                    """, (self.title, spec, verify, self.id, self.version))
                else:
                    # 插入新规格
                    cursor.execute("""
                        INSERT INTO zt_storyspec (story, version, title, spec, verify) 
                        VALUES (%s, %s, %s, %s, %s)
                    """, (self.id, self.version, self.title, spec, verify))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新需求失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_story SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除需求失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def assign(self, assignedTo: str, assignedBy: str) -> bool:
        """指派需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_story SET 
                    assignedTo = %s, assignedDate = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (assignedTo, assignedBy, self.id))
            
            self.assignedTo = assignedTo
            self.assignedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"指派需求失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def close(self, closedBy: str, closedReason: str = "") -> bool:
        """关闭需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_story SET 
                    status = 'closed', closedBy = %s, closedDate = NOW(),
                    closedReason = %s, lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (closedBy, closedReason, closedBy, self.id))
            
            self.status = 'closed'
            self.closedBy = closedBy
            self.closedDate = datetime.now()
            self.closedReason = closedReason
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"关闭需求失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_spec(self, version: int = None) -> Dict[str, Any]:
        """获取需求规格"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if version is None:
                version = self.version
            
            cursor.execute("""
                SELECT * FROM zt_storyspec 
                WHERE story = %s AND version = %s
            """, (self.id, version))
            
            return cursor.fetchone() or {}
            
        except Exception as e:
            print(f"获取需求规格失败: {e}")
            return {}
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self, include_spec: bool = False) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            "id": self.id,
            "vision": self.vision,
            "parent": self.parent,
            "product": self.product,
            "branch": self.branch,
            "module": self.module,
            "plan": self.plan,
            "source": self.source,
            "sourceNote": self.sourceNote,
            "fromBug": self.fromBug,
            "feedback": self.feedback,
            "title": self.title,
            "keywords": self.keywords,
            "type": self.type,
            "category": self.category,
            "pri": self.pri,
            "estimate": self.estimate,
            "status": self.status,
            "subStatus": self.subStatus,
            "color": self.color,
            "stage": self.stage,
            "mailto": self.mailto,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "assignedTo": self.assignedTo,
            "assignedDate": self.assignedDate.isoformat() if self.assignedDate else None,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "reviewedBy": self.reviewedBy,
            "reviewedDate": self.reviewedDate.isoformat() if self.reviewedDate else None,
            "closedBy": self.closedBy,
            "closedDate": self.closedDate.isoformat() if self.closedDate else None,
            "closedReason": self.closedReason,
            "toBug": self.toBug,
            "childStories": self.childStories,
            "linkStories": self.linkStories,
            "duplicateStory": self.duplicateStory,
            "version": self.version,
            "order": self.order,
            "deleted": self.deleted
        }
        
        if include_spec:
            spec = self.get_spec()
            data.update({
                "spec": spec.get("spec", ""),
                "verify": spec.get("verify", "")
            })
        
        return data
