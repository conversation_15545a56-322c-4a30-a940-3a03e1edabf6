#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
督办管理Excel导入导出功能测试脚本
"""

import requests
import pandas as pd
import io
import os
import json
from datetime import datetime
import time

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1/new-supervision"

class SupervisionExcelTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message, details=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    def test_export_excel(self):
        """测试Excel导出功能"""
        try:
            response = self.session.get(f"{API_BASE}/export-excel")
            
            if response.status_code == 200:
                # 检查响应头
                content_type = response.headers.get('content-type', '')
                if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type:
                    # 保存文件用于后续测试
                    with open('test_export.xlsx', 'wb') as f:
                        f.write(response.content)
                    
                    # 验证Excel内容
                    df = pd.read_excel('test_export.xlsx')
                    required_columns = ['操作类型', 'ID', '序号', '工作维度', '工作主题', '督办来源', 
                                      '工作内容和完成标志', '是否年度绩效考核指标', '完成时限', '整体进度']
                    
                    missing_columns = [col for col in required_columns if col not in df.columns]
                    if missing_columns:
                        self.log_test("导出Excel格式验证", False, f"缺少必要列: {missing_columns}")
                        return False
                    
                    self.log_test("Excel导出功能", True, f"成功导出{len(df)}行数据", 
                                f"文件大小: {len(response.content)} bytes")
                    return True
                else:
                    self.log_test("Excel导出功能", False, f"响应类型错误: {content_type}")
                    return False
            else:
                self.log_test("Excel导出功能", False, f"HTTP状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Excel导出功能", False, f"异常: {str(e)}")
            return False
    
    def create_test_excel(self, filename, test_data):
        """创建测试用的Excel文件"""
        df = pd.DataFrame(test_data)
        df.to_excel(filename, index=False)
        return filename
    
    def test_import_add_operation(self):
        """测试新增操作"""
        test_data = [
            {
                '操作类型': 'ADD',
                'ID': '',
                '序号': 9999,
                '工作维度': '测试维度',
                '工作主题': '测试主题-新增',
                '督办来源': '测试来源',
                '工作内容和完成标志': '测试内容-新增操作',
                '是否年度绩效考核指标': '是',
                '完成时限': '2024-12-31',
                '整体进度': 'X 未启动'
            }
        ]
        
        filename = self.create_test_excel('test_add.xlsx', test_data)
        return self._test_import_file(filename, "新增操作测试", expected_created=1)
    
    def test_import_update_operation(self):
        """测试更新操作"""
        # 首先获取现有数据
        try:
            response = self.session.get(f"{API_BASE}/items")
            if response.status_code == 200:
                items = response.json()['data']
                if items:
                    first_item = items[0]
                    test_data = [
                        {
                            '操作类型': 'UPDATE',
                            'ID': first_item['id'],
                            '序号': first_item['sequence_number'],
                            '工作维度': first_item['work_dimension'],
                            '工作主题': '测试主题-已更新',
                            '督办来源': first_item['supervision_source'],
                            '工作内容和完成标志': '测试内容-更新操作',
                            '是否年度绩效考核指标': first_item['is_annual_assessment'],
                            '完成时限': first_item['completion_deadline'],
                            '整体进度': 'O进行中'
                        }
                    ]
                    
                    filename = self.create_test_excel('test_update.xlsx', test_data)
                    return self._test_import_file(filename, "更新操作测试", expected_updated=1)
                else:
                    self.log_test("更新操作测试", False, "没有可更新的数据")
                    return False
            else:
                self.log_test("更新操作测试", False, "获取现有数据失败")
                return False
        except Exception as e:
            self.log_test("更新操作测试", False, f"异常: {str(e)}")
            return False
    
    def test_import_delete_operation(self):
        """测试删除操作"""
        # 使用之前新增的测试数据进行删除
        test_data = [
            {
                '操作类型': 'DELETE',
                'ID': '',
                '序号': 9999,  # 删除之前新增的测试数据
                '工作维度': '',
                '工作主题': '',
                '督办来源': '',
                '工作内容和完成标志': '',
                '是否年度绩效考核指标': '',
                '完成时限': '',
                '整体进度': ''
            }
        ]
        
        filename = self.create_test_excel('test_delete.xlsx', test_data)
        return self._test_import_file(filename, "删除操作测试", expected_deleted=1)
    
    def _test_import_file(self, filename, test_name, expected_created=0, expected_updated=0, expected_deleted=0):
        """通用的导入文件测试方法"""
        try:
            with open(filename, 'rb') as f:
                files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                response = self.session.post(f"{API_BASE}/import-excel", files=files)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    created = data.get('created_count', 0)
                    updated = data.get('updated_count', 0)
                    deleted = data.get('deleted_count', 0)
                    errors = data.get('error_count', 0)
                    
                    success = (created == expected_created and 
                             updated == expected_updated and 
                             deleted == expected_deleted and 
                             errors == 0)
                    
                    details = f"新增:{created}, 更新:{updated}, 删除:{deleted}, 错误:{errors}"
                    self.log_test(test_name, success, result['message'], details)
                    return success
                else:
                    self.log_test(test_name, False, result.get('message', '导入失败'))
                    return False
            else:
                self.log_test(test_name, False, f"HTTP状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test(test_name, False, f"异常: {str(e)}")
            return False
        finally:
            # 清理测试文件
            if os.path.exists(filename):
                os.remove(filename)
    
    def test_import_invalid_format(self):
        """测试无效格式文件导入"""
        # 创建缺少必要列的Excel
        test_data = [
            {
                '序号': 1,
                '工作维度': '测试维度'
                # 缺少其他必要列
            }
        ]
        
        filename = self.create_test_excel('test_invalid.xlsx', test_data)
        
        try:
            with open(filename, 'rb') as f:
                files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                response = self.session.post(f"{API_BASE}/import-excel", files=files)
            
            # 应该返回400错误
            if response.status_code == 400:
                self.log_test("无效格式测试", True, "正确拒绝了无效格式文件")
                return True
            else:
                self.log_test("无效格式测试", False, f"未正确处理无效格式，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("无效格式测试", False, f"异常: {str(e)}")
            return False
        finally:
            if os.path.exists(filename):
                os.remove(filename)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始督办管理Excel导入导出功能测试")
        print("=" * 60)
        
        # 测试导出功能
        export_success = self.test_export_excel()
        
        # 测试导入功能
        if export_success:
            self.test_import_add_operation()
            time.sleep(1)  # 避免并发问题
            
            self.test_import_update_operation()
            time.sleep(1)
            
            self.test_import_delete_operation()
            time.sleep(1)
            
            self.test_import_invalid_format()
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 清理测试文件
        for filename in ['test_export.xlsx', 'test_add.xlsx', 'test_update.xlsx', 'test_delete.xlsx', 'test_invalid.xlsx']:
            if os.path.exists(filename):
                os.remove(filename)
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = SupervisionExcelTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        exit(0)
    else:
        print("\n💥 部分测试失败！")
        exit(1)
