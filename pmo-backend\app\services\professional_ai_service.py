#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业的WrenAI风格AI服务
基于语义层的Text-to-SQL和数据分析
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
from app.core.ai_client import ai_client
from app.core.database import get_db_connection

logger = logging.getLogger(__name__)

class ProfessionalAIService:
    """专业的WrenAI风格AI服务类 - 真正的GenBI实现"""
    
    def __init__(self):
        self.database_schema = self._init_database_schema()
        self.semantic_layer = self._init_semantic_layer()
        self.sql_templates = self._init_sql_templates()
        self.quick_questions = self._init_quick_questions()
    
    def _init_database_schema(self) -> Dict[str, Any]:
        """初始化完整的数据库语义模型"""
        return {
            "project_plan": {
                "comment": "项目计划表 - 存储项目规划和预算信息",
                "columns": [
                    {"name": "project_id", "type": "VARCHAR(50)", "comment": "项目ID", "key": "primary"},
                    {"name": "category_level1", "type": "VARCHAR(100)", "comment": "一级分类", "examples": ["AI智能化", "数字化转型", "基础设施"]},
                    {"name": "category_level2", "type": "VARCHAR(100)", "comment": "二级分类", "examples": ["机器学习", "数据平台", "云计算"]},
                    {"name": "project_name", "type": "VARCHAR(200)", "comment": "项目名称", "searchable": True},
                    {"name": "summary", "type": "TEXT", "comment": "项目摘要", "searchable": True},
                    {"name": "priority", "type": "VARCHAR(50)", "comment": "优先级", "values": ["高", "中", "低"]},
                    {"name": "budget_2026", "type": "DECIMAL(15,2)", "comment": "2026年预算(万元)", "aggregatable": True},
                    {"name": "budget_2027", "type": "DECIMAL(15,2)", "comment": "2027年预算(万元)", "aggregatable": True},
                    {"name": "budget_2028", "type": "DECIMAL(15,2)", "comment": "2028年预算(万元)", "aggregatable": True},
                    {"name": "budget_2029", "type": "DECIMAL(15,2)", "comment": "2029年预算(万元)", "aggregatable": True},
                    {"name": "budget_2030", "type": "DECIMAL(15,2)", "comment": "2030年预算(万元)", "aggregatable": True},
                    {"name": "total_budget", "type": "DECIMAL(15,2)", "comment": "总预算(万元)", "aggregatable": True}
                ],
                "business_metrics": {
                    "total_projects": "COUNT(*)",
                    "total_budget": "SUM(total_budget)",
                    "avg_budget": "AVG(total_budget)",
                    "high_priority_projects": "COUNT(*) WHERE priority = '高'"
                }
            },
            "project_account_book": {
                "comment": "项目账本表 - 存储项目投资和进度信息",
                "columns": [
                    {"name": "project_code", "type": "VARCHAR(50)", "comment": "项目编号", "key": "primary"},
                    {"name": "project_name", "type": "VARCHAR(200)", "comment": "项目名称", "searchable": True},
                    {"name": "investment_entity", "type": "VARCHAR(100)", "comment": "投资主体", "examples": ["小贷", "科技", "金融"]},
                    {"name": "project_category", "type": "VARCHAR(50)", "comment": "项目类别"},
                    {"name": "current_progress", "type": "VARCHAR(100)", "comment": "当前进度"},
                    {"name": "total_investment", "type": "DECIMAL(15,2)", "comment": "总投资额(万元)", "aggregatable": True},
                    {"name": "completed_investment", "type": "DECIMAL(15,2)", "comment": "已完成投资(万元)", "aggregatable": True},
                    {"name": "investment_progress", "type": "DECIMAL(5,2)", "comment": "投资进度(%)", "aggregatable": True}
                ],
                "business_metrics": {
                    "total_investment": "SUM(total_investment)",
                    "completed_investment": "SUM(completed_investment)",
                    "avg_progress": "AVG(investment_progress)",
                    "projects_by_entity": "COUNT(*) GROUP BY investment_entity"
                }
            }
        }
    
    def _init_semantic_layer(self) -> Dict[str, Any]:
        """初始化语义层 - 业务概念映射"""
        return {
            "entities": {
                "项目": ["project", "项目", "project_name", "project_code"],
                "预算": ["budget", "预算", "投资", "investment", "total_budget", "total_investment"],
                "进度": ["progress", "进度", "完成", "completion", "investment_progress"],
                "分类": ["category", "分类", "类别", "type", "category_level1", "category_level2"],
                "投资主体": ["entity", "主体", "投资主体", "investment_entity"],
                "AI": ["AI", "人工智能", "智能", "机器学习", "算法"],
                "小贷": ["小贷", "小额贷款", "贷款"],
                "数字化": ["数字化", "数字", "digital", "转型"]
            },
            "metrics": {
                "总数": ["COUNT", "数量", "个数", "总数"],
                "总和": ["SUM", "总和", "合计", "总计"],
                "平均": ["AVG", "平均", "均值"],
                "最大": ["MAX", "最大", "最高"],
                "最小": ["MIN", "最小", "最低"]
            },
            "filters": {
                "年份": ["2026", "2027", "2028", "2029", "2030"],
                "优先级": ["高", "中", "低"],
                "投资主体": ["小贷", "科技", "金融"]
            }
        }
    
    def _init_sql_templates(self) -> Dict[str, str]:
        """初始化SQL模板"""
        return {
            "project_count": "SELECT COUNT(*) as count FROM project_plan WHERE {conditions}",
            "budget_analysis": "SELECT SUM({budget_field}) as total_budget FROM project_plan WHERE {conditions}",
            "category_breakdown": "SELECT category_level1, COUNT(*) as count, SUM(total_budget) as budget FROM project_plan GROUP BY category_level1",
            "investment_entity_analysis": "SELECT investment_entity, COUNT(*) as projects, SUM(total_investment) as investment FROM project_account_book GROUP BY investment_entity",
            "project_search": "SELECT project_name, total_budget, category_level1 FROM project_plan WHERE project_name LIKE '%{keyword}%' OR summary LIKE '%{keyword}%'",
            "budget_ranking": "SELECT project_name, total_budget FROM project_plan ORDER BY total_budget DESC LIMIT {limit}",
            "progress_analysis": "SELECT project_name, investment_progress, total_investment FROM project_account_book WHERE investment_progress > {threshold}"
        }
    
    def _init_quick_questions(self) -> List[str]:
        """初始化快捷问题"""
        return [
            "有多少个项目？",
            "总预算是多少？",
            "有哪些AI相关的项目？",
            "小贷有什么项目？",
            "2026年预算最多的项目是什么？",
            "总预算超过1000万的项目有哪些？",
            "各投资主体的项目分布情况",
            "高优先级项目有哪些？",
            "数字化转型相关的项目",
            "投资进度最快的项目"
        ]
    
    async def process_question(self, question: str, user_id: str = "system") -> Dict[str, Any]:
        """处理自然语言问题 - 核心Text-to-SQL引擎"""
        try:
            logger.info(f"处理问题: {question}")
            
            # 1. 问题理解和意图识别
            intent = self._analyze_intent(question)
            logger.info(f"识别意图: {intent}")
            
            # 2. 实体识别和参数提取
            entities = self._extract_entities(question)
            logger.info(f"提取实体: {entities}")
            
            # 3. SQL生成
            sql_query = self._generate_sql(intent, entities, question)
            logger.info(f"生成SQL: {sql_query}")
            
            # 4. 执行查询
            data = await self._execute_query(sql_query)
            
            # 5. 生成AI回答
            answer = await self._generate_answer(question, sql_query, data)
            
            # 6. 生成图表建议
            chart_config = self._suggest_chart(intent, data)
            
            return {
                "success": True,
                "answer": answer,
                "sql": sql_query,
                "data": data,
                "chart_config": chart_config,
                "intent": intent,
                "entities": entities,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"处理问题失败: {str(e)}")
            return {
                "success": False,
                "answer": f"抱歉，处理您的问题时出现错误: {str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _analyze_intent(self, question: str) -> Dict[str, Any]:
        """分析问题意图"""
        intent = {
            "type": "unknown",
            "action": "query",
            "target": "project",
            "aggregation": None
        }
        
        # 数量查询
        if any(word in question for word in ["多少", "几个", "数量", "总数"]):
            intent["type"] = "count"
            intent["aggregation"] = "COUNT"
        
        # 金额查询
        elif any(word in question for word in ["预算", "投资", "金额", "总和", "合计"]):
            intent["type"] = "sum"
            intent["aggregation"] = "SUM"
            
        # 排名查询
        elif any(word in question for word in ["最多", "最大", "最高", "排名", "TOP"]):
            intent["type"] = "ranking"
            intent["aggregation"] = "MAX"
            
        # 搜索查询
        elif any(word in question for word in ["哪些", "什么", "相关", "包含"]):
            intent["type"] = "search"
            
        # 分析查询
        elif any(word in question for word in ["分布", "分析", "情况", "统计"]):
            intent["type"] = "analysis"
            intent["aggregation"] = "GROUP_BY"
            
        return intent
    
    def _extract_entities(self, question: str) -> Dict[str, Any]:
        """提取问题中的实体"""
        entities = {
            "keywords": [],
            "filters": {},
            "year": None,
            "threshold": None
        }
        
        # 提取关键词
        semantic_layer = self.semantic_layer
        for concept, keywords in semantic_layer["entities"].items():
            for keyword in keywords:
                if keyword in question:
                    entities["keywords"].append(concept)
                    break
        
        # 提取年份
        years = re.findall(r'20\d{2}', question)
        if years:
            entities["year"] = years[0]
            
        # 提取数值阈值
        numbers = re.findall(r'(\d+(?:\.\d+)?)\s*万', question)
        if numbers:
            entities["threshold"] = float(numbers[0])
            
        return entities
    
    def _generate_sql(self, intent: Dict[str, Any], entities: Dict[str, Any], question: str) -> str:
        """生成SQL查询"""
        try:
            # 根据意图选择基础模板
            if intent["type"] == "count":
                if "AI" in entities["keywords"]:
                    return "SELECT COUNT(*) as count FROM project_plan WHERE category_level1 LIKE '%AI%' OR category_level2 LIKE '%AI%' OR project_name LIKE '%AI%'"
                elif "小贷" in entities["keywords"]:
                    return "SELECT COUNT(*) as count FROM project_account_book WHERE investment_entity LIKE '%小贷%'"
                else:
                    return "SELECT COUNT(*) as count FROM project_plan"
                    
            elif intent["type"] == "sum":
                if entities["year"]:
                    return f"SELECT SUM(budget_{entities['year']}) as total_budget FROM project_plan WHERE budget_{entities['year']} > 0"
                else:
                    return "SELECT SUM(total_budget) as total_budget FROM project_plan"
                    
            elif intent["type"] == "search":
                if "AI" in entities["keywords"]:
                    return "SELECT project_name, total_budget, category_level1 FROM project_plan WHERE category_level1 LIKE '%AI%' OR category_level2 LIKE '%AI%' OR project_name LIKE '%AI%' OR summary LIKE '%AI%'"
                elif "小贷" in entities["keywords"]:
                    return "SELECT project_name, total_investment, project_category FROM project_account_book WHERE investment_entity LIKE '%小贷%'"
                elif "数字化" in entities["keywords"]:
                    return "SELECT project_name, total_budget, category_level1 FROM project_plan WHERE category_level1 LIKE '%数字%' OR project_name LIKE '%数字%'"
                elif entities["threshold"]:
                    return f"SELECT project_name, total_budget FROM project_plan WHERE total_budget > {entities['threshold']}"
                    
            elif intent["type"] == "ranking":
                if entities["year"]:
                    return f"SELECT project_name, budget_{entities['year']} as budget FROM project_plan WHERE budget_{entities['year']} > 0 ORDER BY budget_{entities['year']} DESC LIMIT 10"
                else:
                    return "SELECT project_name, total_budget FROM project_plan ORDER BY total_budget DESC LIMIT 10"
                    
            elif intent["type"] == "analysis":
                if "投资主体" in entities["keywords"]:
                    return "SELECT investment_entity, COUNT(*) as project_count, SUM(total_investment) as total_investment FROM project_account_book GROUP BY investment_entity"
                elif "分类" in entities["keywords"]:
                    return "SELECT category_level1, COUNT(*) as project_count, SUM(total_budget) as total_budget FROM project_plan GROUP BY category_level1"
                    
            # 默认查询
            return "SELECT project_name, total_budget, category_level1 FROM project_plan LIMIT 10"
            
        except Exception as e:
            logger.error(f"SQL生成失败: {str(e)}")
            return "SELECT COUNT(*) as count FROM project_plan"
    
    async def _execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute(sql)

            # 获取列名
            columns = [desc[0] for desc in cursor.description]

            # 获取数据并转换为字典列表
            rows = cursor.fetchall()
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))

            cursor.close()
            conn.close()
            return result
        except Exception as e:
            logger.error(f"查询执行失败: {str(e)}")
            return []
    
    async def _generate_answer(self, question: str, sql: str, data: List[Dict[str, Any]]) -> str:
        """生成AI回答"""
        try:
            if not data:
                return "抱歉，没有找到相关数据。"
                
            # 构建上下文
            context = f"""
            用户问题: {question}
            执行的SQL: {sql}
            查询结果: {json.dumps(data, ensure_ascii=False, indent=2)}
            
            请基于查询结果，用自然语言回答用户的问题。要求：
            1. 回答要准确、简洁
            2. 包含具体的数字和数据
            3. 语言要专业但易懂
            4. 如果有多条记录，要做适当的总结
            """
            
            response = await ai_client.generate_text(context)
            return response
            
        except Exception as e:
            logger.error(f"AI回答生成失败: {str(e)}")
            return f"查询成功，共找到 {len(data)} 条记录。"
    
    def _suggest_chart(self, intent: Dict[str, Any], data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """建议图表类型"""
        if not data:
            return None
            
        if intent["type"] == "analysis" and len(data) > 1:
            return {
                "type": "bar",
                "title": "数据分析图表",
                "data": data
            }
        elif intent["type"] == "ranking":
            return {
                "type": "bar",
                "title": "排名图表", 
                "data": data[:10]  # 只显示前10名
            }
        elif intent["type"] == "count" or intent["type"] == "sum":
            return {
                "type": "metric",
                "title": "统计结果",
                "data": data
            }
            
        return None
    
    def get_quick_questions(self) -> List[str]:
        """获取快捷问题"""
        return self.quick_questions
    
    def get_database_summary(self) -> Dict[str, Any]:
        """获取数据库概览"""
        return {
            "tables": list(self.database_schema.keys()),
            "schema": self.database_schema,
            "sample_questions": self.quick_questions[:5]
        }
