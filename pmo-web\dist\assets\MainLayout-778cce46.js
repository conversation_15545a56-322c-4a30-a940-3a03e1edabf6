import{_ as z}from"./logo-bcea583b.js";import{_ as R,u as W,b as j,c as f,o as q,d as _,e as w,f as h,g as r,t as T,k,h as e,w as t,j as L,l as A,F as N,m as K,E as S,n as G,a as J,p as X,q as Q,s as Z,v as ee,x as te,y as oe,z as se,A as F,B as ne,C as ae,D as le,G as re,i as ue,H as de,I}from"./index-76121fa4.js";const ie={class:"app-header"},ce={class:"header-right"},_e={class:"user-info"},me={class:"user-dropdown"},pe={class:"dialog-footer"},ve={__name:"Header",setup(B){const y=W(),u=j(),i=f(!1),d=f(!1),c=f(null),a=f({oldPassword:"",newPassword:"",confirmPassword:""}),b={oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(g,o,x)=>{o!==a.value.newPassword?x(new Error("两次输入的密码不一致")):x()},trigger:"blur"}]},M=g=>{g==="logout"?K.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{u.logout(),y.push("/login"),S({type:"success",message:"已成功退出登录"})}).catch(()=>{}):g==="changePassword"&&(i.value=!0)},D=async()=>{c.value&&await c.value.validate(async g=>{if(g){d.value=!0;try{const o=await G(a.value.oldPassword,a.value.newPassword);o.code===200?(S({type:"success",message:"密码修改成功"}),i.value=!1,a.value={oldPassword:"",newPassword:"",confirmPassword:""},u.logout(),y.push("/login")):S({type:"error",message:o.message||"密码修改失败"})}catch(o){S({type:"error",message:o.message||"密码修改时发生错误"})}finally{d.value=!1}}})};return q(async()=>{await u.fetchUserInfo()}),(g,o)=>{const x=_("el-icon"),V=_("el-dropdown-item"),H=_("el-dropdown-menu"),U=_("el-dropdown"),$=_("el-input"),s=_("el-form-item"),n=_("el-form"),m=_("el-button"),p=_("el-dialog");return w(),h(N,null,[r("div",ie,[o[7]||(o[7]=r("div",{class:"logo-container"},[r("img",{src:z,alt:"Logo",class:"logo"}),r("h1",{class:"title"},"条线项目管理系统")],-1)),r("div",ce,[r("span",_e,T(k(u).userName)+" | "+T(k(u).userRole),1),e(U,{onCommand:M},{dropdown:t(()=>[e(H,null,{default:t(()=>[e(V,{command:"changePassword"},{default:t(()=>o[5]||(o[5]=[L("修改密码")])),_:1,__:[5]}),e(V,{command:"logout"},{default:t(()=>o[6]||(o[6]=[L("退出登录")])),_:1,__:[6]})]),_:1})]),default:t(()=>[r("span",me,[e(x,null,{default:t(()=>[e(k(A))]),_:1})])]),_:1})])]),e(p,{modelValue:i.value,"onUpdate:modelValue":o[4]||(o[4]=l=>i.value=l),title:"修改密码",width:"400px","close-on-click-modal":!1},{footer:t(()=>[r("span",pe,[e(m,{onClick:o[3]||(o[3]=l=>i.value=!1)},{default:t(()=>o[8]||(o[8]=[L("取消")])),_:1,__:[8]}),e(m,{type:"primary",loading:d.value,onClick:D},{default:t(()=>o[9]||(o[9]=[L(" 确认 ")])),_:1,__:[9]},8,["loading"])])]),default:t(()=>[e(n,{ref_key:"passwordFormRef",ref:c,model:a.value,rules:b,"label-width":"100px"},{default:t(()=>[e(s,{label:"旧密码",prop:"oldPassword"},{default:t(()=>[e($,{modelValue:a.value.oldPassword,"onUpdate:modelValue":o[0]||(o[0]=l=>a.value.oldPassword=l),type:"password",placeholder:"请输入旧密码","show-password":""},null,8,["modelValue"])]),_:1}),e(s,{label:"新密码",prop:"newPassword"},{default:t(()=>[e($,{modelValue:a.value.newPassword,"onUpdate:modelValue":o[1]||(o[1]=l=>a.value.newPassword=l),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),e(s,{label:"确认新密码",prop:"confirmPassword"},{default:t(()=>[e($,{modelValue:a.value.confirmPassword,"onUpdate:modelValue":o[2]||(o[2]=l=>a.value.confirmPassword=l),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}},fe=R(ve,[["__scopeId","data-v-cabb4867"]]);const ge={class:"app-sidebar"},we={__name:"Sidebar",setup(B){const y=J(),u=X(()=>y.path);return(i,d)=>{const c=_("el-icon"),a=_("el-menu-item"),b=_("el-menu");return w(),h("div",ge,[e(b,{"default-active":u.value,class:"sidebar-menu","background-color":"#001529","text-color":"#fff","active-text-color":"#1890ff",router:!0},{default:t(()=>[e(a,{index:"/"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(Q))]),_:1}),d[0]||(d[0]=r("span",null,"红黑榜",-1))]),_:1,__:[0]}),e(a,{index:"/projects"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(Z))]),_:1}),d[1]||(d[1]=r("span",null,"项目管理",-1))]),_:1,__:[1]}),e(a,{index:"/personnel"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(ee))]),_:1}),d[2]||(d[2]=r("span",null,"人员管理",-1))]),_:1,__:[2]}),e(a,{index:"/timesheet"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(te))]),_:1}),d[3]||(d[3]=r("span",null,"工时录入",-1))]),_:1,__:[3]}),e(a,{index:"/weekly-report"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(oe))]),_:1}),d[4]||(d[4]=r("span",null,"周报录入",-1))]),_:1,__:[4]}),e(a,{index:"/ai-assistant"},{default:t(()=>[e(c,null,{default:t(()=>[e(k(se))]),_:1}),d[5]||(d[5]=r("span",null,"AI助手",-1))]),_:1,__:[5]})]),_:1},8,["default-active"])])}}},he=R(we,[["__scopeId","data-v-5c8c7ab5"]]);const ye={key:0,class:"el-icon-chat-dot-round"},be={key:1,class:"el-icon-close"},xe={class:"chat-window"},Pe={key:0,class:"message-content"},ke=["innerHTML"],$e={key:0,class:"message assistant-message"},Ce={class:"input-area"},Me={__name:"FloatingChat",setup(B){const y=f(""),u=f([{role:"assistant",content:"您好！我是PMO数据库助手，可以帮您查询项目数据、统计信息和进度报告。请问有什么可以帮您？"}]),i=f(!1),d=f(null),c=f(!1),a=f({x:30,y:30}),b=f(!1),M=f({x:0,y:0}),D=X(()=>({right:`${a.value.x}px`,bottom:`${a.value.y}px`})),g=s=>{s.preventDefault();const n=s.touches?s.touches[0].clientX:s.clientX,m=s.touches?s.touches[0].clientY:s.clientY,p=a.value.x,l=a.value.y;M.value={x:window.innerWidth-n-p,y:window.innerHeight-m-l},b.value=!0,document.addEventListener("mousemove",o),document.addEventListener("touchmove",o,{passive:!1}),document.addEventListener("mouseup",x),document.addEventListener("touchend",x)},o=s=>{if(!b.value)return;s.preventDefault();const n=s.touches?s.touches[0].clientX:s.clientX,m=s.touches?s.touches[0].clientY:s.clientY,p=window.innerWidth-n-M.value.x,l=window.innerHeight-m-M.value.y;a.value={x:Math.max(10,Math.min(window.innerWidth-60,p)),y:Math.max(10,Math.min(window.innerHeight-60,l))}},x=()=>{b.value=!1,document.removeEventListener("mousemove",o),document.removeEventListener("touchmove",o),document.removeEventListener("mouseup",x),document.removeEventListener("touchend",x)},V=()=>{c.value=!c.value,c.value&&I(()=>{$()})},H=async()=>{const s=y.value.trim();if(!(!s||i.value)){u.value.push({role:"user",content:s}),y.value="",await I(),$(),i.value=!0;try{u.value.push({role:"assistant",content:"正在查询数据库，复杂查询可能需要几分钟时间，请耐心等待..."}),await I(),$();const n=localStorage.getItem("token"),m=new EventSource(`/api/v1/db/chat/stream?query=${encodeURIComponent(s)}&token=${encodeURIComponent(n)}`);let p="";m.onmessage=l=>{try{const v=JSON.parse(l.data);v.content&&(p===""?(p=v.content,u.value[u.value.length-1].content=p):(p+=v.content,u.value[u.value.length-1].content=p),I(()=>{$()})),v.error&&(S.error(`错误: ${v.error}`),u.value[u.value.length-1].content=`抱歉，发生了错误: ${v.error}`,m.close(),i.value=!1)}catch(v){console.error("解析消息失败:",v)}},m.addEventListener("done",()=>{m.close(),i.value=!1}),m.onerror=l=>{console.error("事件流错误:",l),m.close(),i.value=!1,p===""&&(u.value[u.value.length-1].content="抱歉，处理您的请求时发生错误，请稍后再试。")},setTimeout(()=>{i.value&&p===""&&(m.close(),i.value=!1,u.value[u.value.length-1].content="抱歉，请求超时，请稍后再试。")},48e4)}catch(n){console.error("发送消息错误:",n),i.value=!1,u.value[u.value.length-1].content=`抱歉，发生了错误: ${n.message}`}}},U=s=>{if(!s)return"";let n=s.replace(/```sql([\s\S]*?)```/g,'<div class="code-block"><pre>$1</pre></div>');n=n.replace(/```([\s\S]*?)```/g,'<div class="code-block"><pre>$1</pre></div>');const m=/\|(.+)\|[\r\n]\|[-:| ]+\|[\r\n]((?:\|.+\|[\r\n])+)/g;return n=n.replace(m,(p,l,v)=>{const Y=l.split("|").map(P=>P.trim()).filter(P=>P),O=v.trim().split(`
`).map(P=>P.split("|").map(E=>E.trim()).filter(E=>E!==""));let C='<table class="message-table"><thead><tr>';return Y.forEach(P=>{C+=`<th>${P}</th>`}),C+="</tr></thead><tbody>",O.forEach(P=>{C+="<tr>",P.forEach(E=>{C+=`<td>${E}</td>`}),C+="</tr>"}),C+="</tbody></table>",C}),n=n.replace(/\n/g,"<br>"),n},$=()=>{d.value&&(d.value.scrollTop=d.value.scrollHeight)};return q(()=>{document.addEventListener("click",s=>{const n=document.querySelector(".floating-chat-container");n&&!n.contains(s.target)&&c.value&&(c.value=!1)})}),(s,n)=>{const m=_("el-button"),p=_("el-input");return w(),h("div",{class:"floating-chat-container",style:de(D.value)},[r("div",{class:F(["chat-button",{active:c.value}]),onClick:V,onMousedown:g,onTouchstart:g},[c.value?(w(),h("i",be)):(w(),h("i",ye))],34),ne(r("div",xe,[r("div",{class:"chat-header",onMousedown:g,onTouchstart:g},[n[1]||(n[1]=r("h3",null,"PMO 数据库助手",-1)),r("i",{class:"el-icon-close",onClick:V})],32),r("div",{class:"messages",ref_key:"messagesContainer",ref:d},[(w(!0),h(N,null,le(u.value,(l,v)=>(w(),h("div",{key:v,class:F(["message",l.role==="user"?"user-message":"assistant-message"])},[l.role==="user"?(w(),h("div",Pe,T(l.content),1)):(w(),h("div",{key:1,class:"message-content",innerHTML:U(l.content)},null,8,ke))],2))),128)),i.value?(w(),h("div",$e,n[2]||(n[2]=[r("div",{class:"loading-indicator"},[r("span"),r("span"),r("span")],-1)]))):re("",!0)],512),r("div",Ce,[e(p,{modelValue:y.value,"onUpdate:modelValue":n[0]||(n[0]=l=>y.value=l),placeholder:"输入您的问题...",disabled:i.value,onKeyup:ue(H,["enter"])},{append:t(()=>[e(m,{disabled:i.value,onClick:H},{default:t(()=>[L(T(i.value?"请稍候":"发送"),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","disabled"])])],512),[[ae,c.value]])],4)}}},Ve=R(Me,[["__scopeId","data-v-a3c6742f"]]);const Ee={class:"app-container"},Le={__name:"MainLayout",setup(B){return(y,u)=>{const i=_("el-aside"),d=_("el-header"),c=_("router-view"),a=_("el-main"),b=_("el-container");return w(),h("div",Ee,[e(b,{class:"layout-container"},{default:t(()=>[e(i,{width:"auto"},{default:t(()=>[e(he)]),_:1}),e(b,null,{default:t(()=>[e(d,null,{default:t(()=>[e(fe)]),_:1}),e(a,null,{default:t(()=>[e(c)]),_:1})]),_:1})]),_:1}),e(Ve)])}}},Ie=R(Le,[["__scopeId","data-v-c4481dd7"]]);export{Ie as default};
