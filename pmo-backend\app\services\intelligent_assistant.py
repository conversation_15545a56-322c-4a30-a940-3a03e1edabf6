#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能AI助手服务
基于动态知识库的智能问答系统
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from app.core.logger import get_logger
from app.services.knowledge_service import pmo_knowledge_service

# 尝试导入向量知识库
try:
    from app.services.vector_knowledge_service import vector_knowledge_service
    VECTOR_AVAILABLE = True
except ImportError:
    VECTOR_AVAILABLE = False

logger = get_logger(__name__)

class QueryClassifier:
    """查询分类器"""
    
    def __init__(self):
        # 查询类型模式
        self.query_patterns = {
            'project_search': [
                r'.*项目.*有哪些',
                r'.*项目.*列表',
                r'.*项目.*情况',
                r'查找.*项目',
                r'.*项目.*状态'
            ],
            'user_search': [
                r'.*用户.*信息',
                r'.*人员.*情况',
                r'.*员工.*列表',
                r'.*用户.*权限'
            ],
            'statistics': [
                r'.*统计.*',
                r'.*数量.*',
                r'.*总计.*',
                r'.*汇总.*',
                r'.*分析.*'
            ],
            'supervision': [
                r'.*督办.*',
                r'.*进展.*',
                r'.*进度.*',
                r'.*完成.*情况'
            ],
            'investment_entity': [
                r'.*(金租|商租|汽租|集团).*',
                r'.*投资主体.*',
                r'.*条线.*'
            ],
            'time_related': [
                r'.*今年.*',
                r'.*本月.*',
                r'.*最近.*',
                r'.*\d{4}年.*',
                r'.*时间.*'
            ]
        }
    
    def classify_query(self, query: str) -> List[str]:
        """分类查询"""
        query_lower = query.lower()
        matched_types = []
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    matched_types.append(query_type)
                    break
        
        return matched_types if matched_types else ['general']

class ContextExtractor:
    """上下文提取器"""
    
    def __init__(self):
        # 实体提取模式
        self.entity_patterns = {
            'investment_entities': [
                '金租', '商租', '汽租', '集团', '财险', '人寿', '养老', '健康',
                '集团战略部', '集团法规部', '集团风险部'
            ],
            'project_status': [
                '立项', '采购', '实施', '验收', '结项', '暂停', '取消'
            ],
            'time_expressions': [
                r'\d{4}年', r'\d{1,2}月', r'今年', r'去年', r'本月', r'上月',
                r'最近', r'当前', r'现在'
            ]
        }
    
    def extract_entities(self, query: str) -> Dict[str, List[str]]:
        """提取实体"""
        entities = {}
        
        # 提取投资主体
        investment_entities = []
        for entity in self.entity_patterns['investment_entities']:
            if entity in query:
                investment_entities.append(entity)
        if investment_entities:
            entities['investment_entities'] = investment_entities
        
        # 提取项目状态
        project_status = []
        for status in self.entity_patterns['project_status']:
            if status in query:
                project_status.append(status)
        if project_status:
            entities['project_status'] = project_status
        
        # 提取时间表达式
        time_expressions = []
        for pattern in self.entity_patterns['time_expressions']:
            matches = re.findall(pattern, query)
            time_expressions.extend(matches)
        if time_expressions:
            entities['time_expressions'] = time_expressions
        
        return entities

class IntelligentAssistant:
    """智能AI助手"""
    
    def __init__(self):
        self.classifier = QueryClassifier()
        self.extractor = ContextExtractor()
        self.conversation_history = []
        self.max_history = 10
    
    def process_query(self, query: str, user_id: str = None) -> Dict[str, Any]:
        """处理查询"""
        try:
            logger.info(f"处理用户查询: {query}")
            
            # 1. 分类查询
            query_types = self.classifier.classify_query(query)
            logger.debug(f"查询类型: {query_types}")
            
            # 2. 提取实体
            entities = self.extractor.extract_entities(query)
            logger.debug(f"提取实体: {entities}")
            
            # 3. 选择搜索策略
            search_results = self._search_knowledge(query, query_types, entities)
            
            # 4. 生成回答
            answer = self._generate_answer(query, search_results, query_types, entities)
            
            # 5. 记录对话历史
            self._add_to_history(query, answer, user_id)
            
            return {
                "query": query,
                "answer": answer,
                "query_types": query_types,
                "entities": entities,
                "search_results": search_results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"处理查询失败: {str(e)}")
            return {
                "query": query,
                "answer": "抱歉，处理您的查询时出现了问题，请稍后重试。",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _search_knowledge(self, query: str, query_types: List[str], entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """搜索知识库"""
        search_results = {
            "vector_results": [],
            "structured_data": {},
            "search_strategy": "hybrid"
        }
        
        try:
            # 1. 向量搜索（如果可用）
            if VECTOR_AVAILABLE and vector_knowledge_service.available:
                try:
                    vector_results = vector_knowledge_service.semantic_search(
                        query=query,
                        limit=10,
                        score_threshold=0.6
                    )
                    search_results["vector_results"] = vector_results
                    search_results["search_strategy"] = "vector_primary"
                    logger.debug(f"向量搜索返回 {len(vector_results)} 个结果")
                except Exception as e:
                    logger.warning(f"向量搜索失败: {str(e)}")
            
            # 2. 结构化数据搜索
            structured_data = self._search_structured_data(query_types, entities)
            search_results["structured_data"] = structured_data
            
            # 3. 如果向量搜索结果不足，使用传统搜索
            if len(search_results["vector_results"]) < 3:
                traditional_results = self._traditional_search(query)
                search_results["traditional_results"] = traditional_results
                search_results["search_strategy"] = "hybrid"
            
        except Exception as e:
            logger.error(f"搜索知识库失败: {str(e)}")
        
        return search_results
    
    def _search_structured_data(self, query_types: List[str], entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """搜索结构化数据"""
        structured_data = {}
        
        try:
            # 获取知识库缓存
            cache = pmo_knowledge_service.knowledge_cache
            
            # 根据查询类型获取相关数据
            if 'project_search' in query_types or 'investment_entity' in query_types:
                # 项目相关数据
                if 'red_black_board' in cache:
                    red_black = cache['red_black_board']
                    progress_list = red_black.get('progressList', [])
                    
                    # 根据投资主体过滤
                    if 'investment_entities' in entities:
                        filtered_projects = []
                        for entity in entities['investment_entities']:
                            filtered_projects.extend([
                                p for p in progress_list 
                                if entity in p.get('investment_entity', '')
                            ])
                        structured_data['filtered_projects'] = filtered_projects
                    else:
                        structured_data['all_projects'] = progress_list[:10]  # 限制数量
            
            if 'user_search' in query_types:
                # 用户相关数据
                if 'user_data' in cache:
                    user_data = cache['user_data']
                    users = user_data.get('users', [])
                    structured_data['users'] = users[:20]  # 限制数量
            
            if 'statistics' in query_types:
                # 统计数据
                if 'entity_stats' in cache:
                    structured_data['statistics'] = cache['entity_stats']
            
            if 'supervision' in query_types:
                # 督办数据
                if 'supervision_data' in cache:
                    supervision = cache['supervision_data']
                    structured_data['supervision'] = {
                        'items': supervision.get('supervision_items', []),
                        'progress': supervision.get('progress_details', [])
                    }
            
        except Exception as e:
            logger.error(f"搜索结构化数据失败: {str(e)}")
        
        return structured_data
    
    def _traditional_search(self, query: str) -> List[str]:
        """传统关键词搜索"""
        try:
            knowledge_content = pmo_knowledge_service.get_knowledge_base()
            
            # 简单的关键词匹配
            lines = knowledge_content.split('\n')
            matching_lines = []
            
            query_keywords = query.split()
            for line in lines:
                if any(keyword in line for keyword in query_keywords):
                    matching_lines.append(line.strip())
                    if len(matching_lines) >= 10:
                        break
            
            return matching_lines
            
        except Exception as e:
            logger.error(f"传统搜索失败: {str(e)}")
            return []
    
    def _generate_answer(self, query: str, search_results: Dict[str, Any], 
                        query_types: List[str], entities: Dict[str, List[str]]) -> str:
        """生成回答"""
        try:
            # 根据搜索结果生成回答
            answer_parts = []
            
            # 1. 处理向量搜索结果
            vector_results = search_results.get("vector_results", [])
            if vector_results:
                answer_parts.append("根据智能搜索，我找到了以下相关信息：\n")
                for i, result in enumerate(vector_results[:3], 1):
                    content = result['content']
                    score = result['score']
                    answer_parts.append(f"{i}. {content[:200]}... (相似度: {score:.2f})\n")
            
            # 2. 处理结构化数据
            structured_data = search_results.get("structured_data", {})
            
            if 'filtered_projects' in structured_data:
                projects = structured_data['filtered_projects']
                if projects:
                    answer_parts.append(f"\n找到 {len(projects)} 个相关项目：\n")
                    for project in projects[:5]:
                        name = project.get('project_name', '未知项目')
                        entity = project.get('investment_entity', '未知')
                        status = project.get('status', '未知状态')
                        answer_parts.append(f"• {name} ({entity}) - {status}\n")
            
            elif 'all_projects' in structured_data:
                projects = structured_data['all_projects']
                if projects:
                    answer_parts.append(f"\n当前共有 {len(projects)} 个项目，以下是部分列表：\n")
                    for project in projects[:5]:
                        name = project.get('project_name', '未知项目')
                        entity = project.get('investment_entity', '未知')
                        answer_parts.append(f"• {name} ({entity})\n")
            
            if 'statistics' in structured_data:
                stats = structured_data['statistics']
                if stats:
                    answer_parts.append(f"\n统计信息：\n")
                    for entity, data in list(stats.items())[:3]:
                        total = data.get('total_projects', 0)
                        answer_parts.append(f"• {entity}: {total} 个项目\n")
            
            if 'users' in structured_data:
                users = structured_data['users']
                if users:
                    answer_parts.append(f"\n找到 {len(users)} 个用户，以下是部分列表：\n")
                    for user in users[:5]:
                        name = user.get('name', '未知')
                        dept = user.get('department_name', '未知部门')
                        answer_parts.append(f"• {name} ({dept})\n")
            
            # 3. 处理传统搜索结果
            traditional_results = search_results.get("traditional_results", [])
            if traditional_results and not vector_results:
                answer_parts.append("\n相关信息：\n")
                for line in traditional_results[:3]:
                    if line.strip():
                        answer_parts.append(f"• {line}\n")
            
            # 4. 生成最终回答
            if answer_parts:
                final_answer = "".join(answer_parts)
            else:
                final_answer = "抱歉，我没有找到与您查询相关的信息。您可以尝试：\n" \
                             "1. 使用更具体的关键词\n" \
                             "2. 查询具体的项目名称或投资主体\n" \
                             "3. 询问统计信息或督办情况"
            
            # 5. 添加建议
            if 'investment_entity' in query_types:
                final_answer += "\n\n💡 提示：您可以询问特定投资主体的项目情况，如'金租的项目有哪些？'"
            
            return final_answer
            
        except Exception as e:
            logger.error(f"生成回答失败: {str(e)}")
            return "抱歉，生成回答时出现了问题。"
    
    def _add_to_history(self, query: str, answer: str, user_id: str = None):
        """添加到对话历史"""
        try:
            history_item = {
                "query": query,
                "answer": answer,
                "user_id": user_id,
                "timestamp": datetime.now().isoformat()
            }
            
            self.conversation_history.append(history_item)
            
            # 保持历史记录数量限制
            if len(self.conversation_history) > self.max_history:
                self.conversation_history = self.conversation_history[-self.max_history:]
                
        except Exception as e:
            logger.error(f"添加对话历史失败: {str(e)}")
    
    def get_conversation_history(self, user_id: str = None) -> List[Dict[str, Any]]:
        """获取对话历史"""
        if user_id:
            return [item for item in self.conversation_history if item.get('user_id') == user_id]
        return self.conversation_history
    
    def clear_history(self, user_id: str = None):
        """清除对话历史"""
        if user_id:
            self.conversation_history = [
                item for item in self.conversation_history 
                if item.get('user_id') != user_id
            ]
        else:
            self.conversation_history = []

# 全局智能助手实例
intelligent_assistant = IntelligentAssistant()
