#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import pymysql
import os
import time
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义需要排除的字段（这些字段在UI中显示但在数据库中不存在）
EXCLUDED_FIELDS = ['yearly_working_hours', 'working_hours', 'labor_cost']

def get_db_connection():
    """获取数据库连接"""
    try:
        # 直接使用硬编码的数据库连接信息，不再依赖环境变量
        logger.info("使用硬编码的数据库连接信息")
        
        conn = pymysql.connect(
            host='rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
            port=3306,
            user='cyh',
            password='Qq188788',
            database='kanban2',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return conn
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def log_changes(conn, project_code, user_id, name, action, old_value=None, new_value=None):
    """
    记录变更日志
    :param conn: 数据库连接
    :param project_code: 项目编码
    :param user_id: 用户ID
    :param name: 用户名
    :param action: 操作类型 (update/add/delete)
    :param old_value: 旧值
    :param new_value: 新值
    """
    try:
        with conn.cursor() as cursor:
            # 格式化日志内容
            if action == "update":
                # 更新操作，记录详细变更
                if old_value is None or new_value is None:
                    return
                    
                previous_value = f"项目:{project_code}, 原值:{json.dumps(old_value, ensure_ascii=False)}"
                new_value_str = json.dumps(new_value, ensure_ascii=False)
            elif action == "add":
                # 新增操作
                previous_value = f"新增项目:{project_code}"
                new_value_str = json.dumps(new_value, ensure_ascii=False) if new_value else ""
            elif action == "delete":
                # 删除操作
                previous_value = f"删除项目:{project_code}, 原数据:{json.dumps(old_value, ensure_ascii=False) if old_value else ''}"
                new_value_str = "已删除"
            else:
                return
                
            # 插入变更日志
            sql = """
            INSERT INTO change_log 
            (name, UserID, previous_value, new_value, change_time) 
            VALUES (%s, %s, %s, %s, NOW())
            """
            cursor.execute(sql, (name, user_id, previous_value, new_value_str))
            conn.commit()
            logger.info(f"已记录变更日志: {action} 项目 {project_code} 由 {name}({user_id}) 执行")
    except Exception as e:
        logger.error(f"记录变更日志错误: {str(e)}")
        # 记录日志错误不影响主要业务流程，不抛出异常

def add_project(data):
    """
    添加新项目
    :param data: 包含项目数据、用户ID和名称的字典
    """
    if not data:
        return {"code": 400, "message": "数据不能为空"}
    
    # 创建数据的副本，避免修改原始数据
    data = data.copy()
    
    # 移除操作类型字段，这个字段不应该插入到数据库
    data.pop("operation", None)
        
    # 提取用户数据
    user_id = data.pop("UserID", None)
    name = data.pop("name", None)
    
    # 确保存在project_code
    if "project_code" not in data:
        return {"code": 400, "message": "项目编码不能为空"}
    
    project_code = data.get("project_code")
    
    # 设置默认值
    # 处理is_hardware字段
    if "is_hardware" not in data or not data["is_hardware"]:
        data["is_hardware"] = "非硬件"
    elif data["is_hardware"] == "否":
        data["is_hardware"] = "非硬件"
    
    # 处理is_non_indigenous_innovation字段
    if "is_non_indigenous_innovation" not in data or not data["is_non_indigenous_innovation"]:
        data["is_non_indigenous_innovation"] = "非信创"
    elif data["is_non_indigenous_innovation"] == "否":
        data["is_non_indigenous_innovation"] = "非信创"
    
    # 过滤掉不存在于数据库表中的字段
    filtered_data = {k: v for k, v in data.items() if k not in EXCLUDED_FIELDS}
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目编码是否已存在
            check_sql = "SELECT COUNT(*) as count FROM Project_Account_Book WHERE project_code = %s"
            cursor.execute(check_sql, (project_code,))
            result = cursor.fetchone()
            if result and result["count"] > 0:
                return {"code": 400, "message": f"项目编码 {project_code} 已存在"}
                
            # 构建插入SQL
            fields = []
            placeholders = []
            values = []
            
            for key, value in filtered_data.items():
                fields.append(key)
                placeholders.append("%s")
                values.append(value)
                
            if not fields:
                return {"code": 400, "message": "没有提供有效的字段数据"}
                
            sql = f"""
            INSERT INTO Project_Account_Book
            ({", ".join(fields)})
            VALUES ({", ".join(placeholders)})
            """
            
            # 执行插入
            cursor.execute(sql, values)
            conn.commit()
            
            # 记录变更日志
            if user_id and name:
                log_changes(conn, project_code, user_id, name, "add", None, filtered_data)
                
            return {"code": 200, "message": "项目添加成功"}
                
    except Exception as e:
        logger.error(f"添加项目错误: {str(e)}")
        if conn:
            conn.rollback()
        return {"code": 500, "message": f"添加项目时出错: {str(e)}"}
    finally:
        if conn:
            conn.close()

def update_project(data):
    """
    更新项目
    :param data: 包含项目编码和需要更新的字段的字典
    """
    if not data:
        return {"code": 400, "message": "数据不能为空"}
    
    # 创建数据的副本，避免修改原始数据
    data = data.copy()
    
    # 移除操作类型字段，这个字段不应该更新到数据库
    data.pop("operation", None)
        
    if "project_code" not in data:
        return {"code": 400, "message": "项目编码不能为空"}
        
    project_code = data.pop("project_code")
    
    # 提取用户数据
    user_id = data.pop("UserID", None)
    name = data.pop("name", None)
    
    if not data:
        return {"code": 400, "message": "没有提供需要更新的字段"}
    
    # 处理默认值
    # 处理is_hardware字段
    if "is_hardware" in data:
        if not data["is_hardware"] or data["is_hardware"] == "否":
            data["is_hardware"] = "非硬件"
    
    # 处理is_non_indigenous_innovation字段
    if "is_non_indigenous_innovation" in data:
        if not data["is_non_indigenous_innovation"] or data["is_non_indigenous_innovation"] == "否":
            data["is_non_indigenous_innovation"] = "非信创"
    
    # 过滤掉不存在于数据库表中的字段
    filtered_data = {k: v for k, v in data.items() if k not in EXCLUDED_FIELDS}
        
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 先获取当前项目数据（用于记录变更日志）
            select_sql = "SELECT * FROM Project_Account_Book WHERE project_code = %s"
            cursor.execute(select_sql, (project_code,))
            old_data = cursor.fetchone()
            
            if not old_data:
                return {"code": 404, "message": f"未找到项目编码为 {project_code} 的记录"}
                
            # 构建更新SQL
            fields = []
            values = []
            
            for key, value in filtered_data.items():
                fields.append(f"{key} = %s")
                values.append(value)
                
            if not fields:
                return {"code": 400, "message": "没有提供有效的更新字段"}
                
            sql = f"""
            UPDATE Project_Account_Book
            SET {", ".join(fields)}
            WHERE project_code = %s
            """
            
            values.append(project_code)
            
            # 执行更新
            cursor.execute(sql, values)
            conn.commit()
            
            # 检查是否有记录被更新
            if cursor.rowcount > 0:
                # 记录变更日志
                if user_id and name:
                    # 提取需要记录的字段
                    old_values = {k: old_data[k] for k in filtered_data.keys() if k in old_data}
                    new_values = filtered_data
                    log_changes(conn, project_code, user_id, name, "update", old_values, new_values)
                    
                return {"code": 200, "message": "项目信息已更新"}
            else:
                return {"code": 404, "message": f"更新失败，未找到项目编码为 {project_code} 的记录"}
                
    except Exception as e:
        logger.error(f"更新项目错误: {str(e)}")
        if conn:
            conn.rollback()
        return {"code": 500, "message": f"更新项目时出错: {str(e)}"}
    finally:
        if conn:
            conn.close()

def delete_project(data):
    """
    删除项目
    :param data: 包含项目编码的字典
    """
    if not data:
        return {"code": 400, "message": "数据不能为空"}
    
    # 创建数据的副本，避免修改原始数据
    data = data.copy()
    
    # 移除操作类型字段
    data.pop("operation", None)
        
    if "project_code" not in data:
        return {"code": 400, "message": "项目编码不能为空"}
        
    project_code = data.get("project_code")
    
    # 提取用户数据
    user_id = data.get("UserID")
    name = data.get("name")
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 先获取当前项目数据（用于记录变更日志）
            select_sql = "SELECT * FROM Project_Account_Book WHERE project_code = %s"
            cursor.execute(select_sql, (project_code,))
            old_data = cursor.fetchone()
            
            if not old_data:
                return {"code": 404, "message": f"未找到项目编码为 {project_code} 的记录"}
                
            # 执行删除
            delete_sql = "DELETE FROM Project_Account_Book WHERE project_code = %s"
            cursor.execute(delete_sql, (project_code,))
            conn.commit()
            
            # 检查是否有记录被删除
            if cursor.rowcount > 0:
                # 记录变更日志
                if user_id and name:
                    log_changes(conn, project_code, user_id, name, "delete", old_data, None)
                    
                return {"code": 200, "message": "项目已删除"}
            else:
                return {"code": 404, "message": f"删除失败，未找到项目编码为 {project_code} 的记录"}
                
    except Exception as e:
        logger.error(f"删除项目错误: {str(e)}")
        if conn:
            conn.rollback()
        return {"code": 500, "message": f"删除项目时出错: {str(e)}"}
    finally:
        if conn:
            conn.close()

def batch_operation(data):
    """
    批量操作项目（添加、更新、删除）
    :param data: 包含操作类型和项目数据列表的字典
    """
    if not data or "operations" not in data:
        return {"code": 400, "message": "操作数据不能为空"}
        
    operations = data.get("operations", [])
    
    if not operations:
        return {"code": 400, "message": "没有提供操作数据"}
        
    results = {
        "success": 0,
        "failed": 0,
        "errors": []
    }
    
    for op in operations:
        op_type = op.get("type")
        op_data = op.get("data", {})
        
        # 添加用户信息到操作数据
        if "UserID" in data and "UserID" not in op_data:
            op_data["UserID"] = data.get("UserID")
        if "name" in data and "name" not in op_data:
            op_data["name"] = data.get("name")
            
        try:
            if op_type == "add":
                result = add_project(op_data)
            elif op_type == "update":
                result = update_project(op_data)
            elif op_type == "delete":
                result = delete_project(op_data)
            else:
                result = {"code": 400, "message": f"未知操作类型: {op_type}"}
                
            if result.get("code") == 200:
                results["success"] += 1
            else:
                results["failed"] += 1
                results["errors"].append({
                    "project_code": op_data.get("project_code", "未知"),
                    "type": op_type,
                    "message": result.get("message", "未知错误")
                })
        except Exception as e:
            results["failed"] += 1
            results["errors"].append({
                "project_code": op_data.get("project_code", "未知"),
                "type": op_type,
                "message": str(e)
            })
            
    return {
        "code": 200,
        "message": f"批量操作完成, 成功: {results['success']}, 失败: {results['failed']}",
        "data": results
    }

def main_handler(event, context):
    """
    主函数
    :param event: 事件数据
    :param context: 上下文
    :return: 处理结果
    """
    # 解析请求数据
    try:
        logger.info(f"收到请求: {json.dumps(event, ensure_ascii=False)}")
        
        # 判断是否是API网关触发
        is_api_gateway = isinstance(event, dict) and 'httpMethod' in event and 'headers' in event
        logger.info(f"是否API网关触发: {is_api_gateway}")
        
        # 处理API网关触发的请求
        if is_api_gateway and 'body' in event:
            try:
                # 如果body是字符串，尝试解析JSON
                if isinstance(event['body'], str):
                    logger.info(f"解析请求body(字符串): {event['body']}")
                    data = json.loads(event['body'])
                else:
                    logger.info(f"解析请求body(对象): {json.dumps(event['body'], ensure_ascii=False)}")
                    data = event['body']
            except json.JSONDecodeError as e:
                logger.error(f"解析请求body失败: {str(e)}")
                result = {"code": 400, "message": f"无效的JSON格式: {str(e)}"}
                # 如果是API网关请求，返回标准API响应
                if is_api_gateway:
                    return format_api_gateway_response(result)
                return result
        else:
            # 直接函数调用的情况
            data = event
            
        logger.info(f"解析后的数据: {json.dumps(data, ensure_ascii=False)}")
        operation = data.get("operation", "")
        logger.info(f"操作类型: {operation}")
        
        # 根据操作类型调用相应的函数
        if operation == "add":
            logger.info("执行添加操作")
            result = add_project(data)
        elif operation == "update":
            logger.info("执行更新操作")
            result = update_project(data)
        elif operation == "delete":
            logger.info("执行删除操作")
            result = delete_project(data)
        elif operation == "batch":
            logger.info("执行批量操作")
            result = batch_operation(data)
        else:
            logger.error(f"不支持的操作类型: {operation}")
            result = {"code": 400, "message": f"不支持的操作类型: {operation}"}
        
        # 记录操作结果
        logger.info(f"操作结果: {json.dumps(result, ensure_ascii=False)}")
        
        # 如果是API网关请求，返回标准API响应
        if is_api_gateway:
            api_response = format_api_gateway_response(result)
            logger.info(f"API网关响应: {json.dumps(api_response, ensure_ascii=False)}")
            return api_response
        
        return result
            
    except Exception as e:
        logger.error(f"处理请求出错: {str(e)}")
        result = {"code": 500, "message": f"处理请求出错: {str(e)}"}
        # 如果是API网关请求，返回标准API响应
        if is_api_gateway:
            return format_api_gateway_response(result)
        return result

def format_api_gateway_response(result):
    """
    格式化API网关响应
    :param result: 函数处理结果
    :return: API网关需要的响应格式
    """
    # 状态码映射，将自定义状态码映射到HTTP状态码
    status_code_map = {
        200: 200,  # 成功
        400: 400,  # 客户端错误
        404: 404,  # 不存在
        500: 500   # 服务器错误
    }
    
    # 获取HTTP状态码，默认为200
    http_status = status_code_map.get(result.get("code", 200), 200)
    
    # 构建API网关响应
    return {
        "isBase64Encoded": False,
        "statusCode": http_status,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type"
        },
        "body": json.dumps(result, ensure_ascii=False)
    }

# 本地测试代码
if __name__ == "__main__":
    # 测试数据
    test_data = {
        "operation": "add",
        "project_code": "TEST001",
        "project_name": "测试项目",
        "investment_entity": "测试实体",
        "UserID": "admin",
        "name": "管理员"
    }
    
    result = main_handler(test_data, None)
    print(json.dumps(result, ensure_ascii=False, indent=2)) 