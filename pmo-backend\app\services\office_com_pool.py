#!/usr/bin/env python3
"""
Office COM对象池 - 提升解析速度
"""

import threading
import time
import queue
import logging
from typing import Optional
import os

logger = logging.getLogger(__name__)

class OfficeComPool:
    """Office COM对象池，复用COM对象提升性能"""
    
    def __init__(self, pool_size: int = 3):
        self.pool_size = pool_size
        self.word_pool = queue.Queue(maxsize=pool_size)
        self.excel_pool = queue.Queue(maxsize=pool_size)
        self.ppt_pool = queue.Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self.initialized = False
        
    def initialize(self):
        """初始化COM对象池"""
        if self.initialized:
            return
            
        try:
            import win32com.client
            
            logger.info("开始初始化Office COM对象池...")
            
            # 初始化Word对象池
            for i in range(self.pool_size):
                try:
                    word = win32com.client.Dispatch("Word.Application")
                    word.Visible = False
                    word.DisplayAlerts = False
                    self.word_pool.put(word)
                    logger.info(f"Word COM对象 {i+1} 初始化成功")
                except Exception as e:
                    logger.warning(f"Word COM对象 {i+1} 初始化失败: {str(e)}")
            
            # 初始化Excel对象池
            for i in range(self.pool_size):
                try:
                    excel = win32com.client.Dispatch("Excel.Application")
                    excel.Visible = False
                    excel.DisplayAlerts = False
                    self.excel_pool.put(excel)
                    logger.info(f"Excel COM对象 {i+1} 初始化成功")
                except Exception as e:
                    logger.warning(f"Excel COM对象 {i+1} 初始化失败: {str(e)}")
            
            # 初始化PowerPoint对象池
            for i in range(self.pool_size):
                try:
                    ppt = win32com.client.Dispatch("PowerPoint.Application")
                    self.ppt_pool.put(ppt)
                    logger.info(f"PowerPoint COM对象 {i+1} 初始化成功")
                except Exception as e:
                    logger.warning(f"PowerPoint COM对象 {i+1} 初始化失败: {str(e)}")
            
            self.initialized = True
            logger.info("Office COM对象池初始化完成")
            
        except ImportError:
            logger.warning("win32com模块未安装，无法初始化COM对象池")
        except Exception as e:
            logger.error(f"COM对象池初始化失败: {str(e)}")
    
    def get_word_app(self, timeout: float = 5.0) -> Optional[object]:
        """获取Word应用程序对象"""
        try:
            return self.word_pool.get(timeout=timeout)
        except queue.Empty:
            logger.warning("Word COM对象池为空，创建新对象")
            try:
                import win32com.client
                word = win32com.client.Dispatch("Word.Application")
                word.Visible = False
                word.DisplayAlerts = False
                return word
            except Exception as e:
                logger.error(f"创建新Word COM对象失败: {str(e)}")
                return None
    
    def return_word_app(self, word_app: object):
        """归还Word应用程序对象"""
        try:
            # 清理可能的打开文档
            if word_app.Documents.Count > 0:
                for doc in word_app.Documents:
                    try:
                        doc.Close(SaveChanges=False)
                    except:
                        pass
            
            self.word_pool.put_nowait(word_app)
        except queue.Full:
            # 池已满，直接关闭对象
            try:
                word_app.Quit()
            except:
                pass
        except Exception as e:
            logger.warning(f"归还Word COM对象失败: {str(e)}")
    
    def get_excel_app(self, timeout: float = 5.0) -> Optional[object]:
        """获取Excel应用程序对象"""
        try:
            return self.excel_pool.get(timeout=timeout)
        except queue.Empty:
            logger.warning("Excel COM对象池为空，创建新对象")
            try:
                import win32com.client
                excel = win32com.client.Dispatch("Excel.Application")
                excel.Visible = False
                excel.DisplayAlerts = False
                return excel
            except Exception as e:
                logger.error(f"创建新Excel COM对象失败: {str(e)}")
                return None
    
    def return_excel_app(self, excel_app: object):
        """归还Excel应用程序对象"""
        try:
            # 清理可能的打开工作簿
            if excel_app.Workbooks.Count > 0:
                for wb in excel_app.Workbooks:
                    try:
                        wb.Close(SaveChanges=False)
                    except:
                        pass
            
            self.excel_pool.put_nowait(excel_app)
        except queue.Full:
            # 池已满，直接关闭对象
            try:
                excel_app.Quit()
            except:
                pass
        except Exception as e:
            logger.warning(f"归还Excel COM对象失败: {str(e)}")
    
    def get_ppt_app(self, timeout: float = 5.0) -> Optional[object]:
        """获取PowerPoint应用程序对象"""
        try:
            return self.ppt_pool.get(timeout=timeout)
        except queue.Empty:
            logger.warning("PowerPoint COM对象池为空，创建新对象")
            try:
                import win32com.client
                ppt = win32com.client.Dispatch("PowerPoint.Application")
                return ppt
            except Exception as e:
                logger.error(f"创建新PowerPoint COM对象失败: {str(e)}")
                return None
    
    def return_ppt_app(self, ppt_app: object):
        """归还PowerPoint应用程序对象"""
        try:
            # 清理可能的打开演示文稿
            if ppt_app.Presentations.Count > 0:
                for pres in ppt_app.Presentations:
                    try:
                        pres.Close()
                    except:
                        pass
            
            self.ppt_pool.put_nowait(ppt_app)
        except queue.Full:
            # 池已满，直接关闭对象
            try:
                ppt_app.Quit()
            except:
                pass
        except Exception as e:
            logger.warning(f"归还PowerPoint COM对象失败: {str(e)}")
    
    def cleanup(self):
        """清理COM对象池"""
        logger.info("开始清理Office COM对象池...")
        
        # 清理Word对象
        while not self.word_pool.empty():
            try:
                word = self.word_pool.get_nowait()
                word.Quit()
            except:
                pass
        
        # 清理Excel对象
        while not self.excel_pool.empty():
            try:
                excel = self.excel_pool.get_nowait()
                excel.Quit()
            except:
                pass
        
        # 清理PowerPoint对象
        while not self.ppt_pool.empty():
            try:
                ppt = self.ppt_pool.get_nowait()
                ppt.Quit()
            except:
                pass
        
        logger.info("Office COM对象池清理完成")

# 全局COM对象池实例
office_com_pool = OfficeComPool(pool_size=3)

def get_office_com_pool() -> OfficeComPool:
    """获取全局COM对象池实例"""
    if not office_com_pool.initialized:
        office_com_pool.initialize()
    return office_com_pool
