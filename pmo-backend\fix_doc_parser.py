#!/usr/bin/env python3
"""
修复.doc文件解析乱码问题的脚本
"""

def create_fixed_doc_parser():
    """创建修复乱码的.doc解析器"""
    
    # 新的解析方法
    new_methods = '''
def _parse_old_doc_file_fixed(self, file_content: bytes) -> str:
    """修复版本的老版本.doc文件解析"""
    logger.info("使用修复版本解析老版本DOC文件...")

    # 检测文件类型
    if file_content.startswith(b'\\xd0\\xcf\\x11\\xe0'):
        file_type = "ole_doc"
    else:
        file_type = "unknown"

    logger.info(f"检测到DOC文件类型: {file_type}")

    # 方法1: 专门的中文文本提取（最重要的改进）
    try:
        chinese_text = self._extract_chinese_text_only(file_content)
        if chinese_text and len(chinese_text.strip()) > 10:
            return f"# Word文档内容\\n\\n{chinese_text}"
    except Exception as e:
        logger.info(f"中文文本提取失败: {str(e)}")

    # 方法2: 使用docx2txt（如果可用）
    try:
        import docx2txt
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            text_content = docx2txt.process(temp_file_path)
            if text_content and text_content.strip():
                cleaned = self._clean_docx2txt_output(text_content)
                if cleaned:
                    return f"# Word文档内容\\n\\n{cleaned}"
        finally:
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except ImportError:
        logger.info("docx2txt模块未安装")
    except Exception as e:
        logger.info(f"docx2txt解析失败: {str(e)}")

    # 如果所有方法都失败
    return self._generate_doc_failure_message(file_type)

def _extract_chinese_text_only(self, file_content: bytes) -> str:
    """专门提取中文文本，避免乱码"""
    try:
        import re
        
        valid_texts = []
        
        # 方法1: 查找UTF-16LE编码的中文文本
        for i in range(0, len(file_content) - 10, 2):
            try:
                # 读取较小的块避免乱码
                chunk = file_content[i:i+50]
                text = chunk.decode('utf-16le', errors='ignore')
                
                # 只保留包含中文字符的文本
                chinese_chars = re.findall(r'[\\u4e00-\\u9fff]+', text)
                for chars in chinese_chars:
                    if len(chars) >= 2:  # 至少2个中文字符
                        valid_texts.append(chars)
                        
            except:
                continue
        
        # 方法2: 查找GBK编码的中文文本
        try:
            text = file_content.decode('gbk', errors='ignore')
            chinese_chars = re.findall(r'[\\u4e00-\\u9fff]+', text)
            for chars in chinese_chars:
                if len(chars) >= 2:
                    valid_texts.append(chars)
        except:
            pass
        
        # 方法3: 查找GB2312编码的中文文本
        try:
            text = file_content.decode('gb2312', errors='ignore')
            chinese_chars = re.findall(r'[\\u4e00-\\u9fff]+', text)
            for chars in chinese_chars:
                if len(chars) >= 2:
                    valid_texts.append(chars)
        except:
            pass
        
        # 去重并组合
        if valid_texts:
            # 去重
            unique_texts = []
            seen = set()
            for text in valid_texts:
                if text not in seen and len(text) >= 2:
                    seen.add(text)
                    unique_texts.append(text)
            
            # 组合成段落
            if unique_texts:
                return '\\n\\n'.join(unique_texts[:30])  # 最多30个文本片段
        
        return ""
        
    except Exception as e:
        logger.error(f"中文文本提取失败: {str(e)}")
        return ""

def _clean_docx2txt_output(self, text: str) -> str:
    """清理docx2txt的输出"""
    try:
        import re
        
        if not text or not text.strip():
            return ""
        
        # 移除明显的乱码字符
        # 只保留中文、英文、数字、常见标点符号
        cleaned = re.sub(r'[^\\u4e00-\\u9fff\\w\\s\\.,，。！？；：""''（）【】\\-\\n\\r\\t]', '', text)
        
        # 分行处理
        lines = cleaned.split('\\n')
        valid_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) < 2:
                continue
            
            # 检查是否包含有意义的内容
            if re.search(r'[\\u4e00-\\u9fff]', line) or re.search(r'[a-zA-Z]{3,}', line):
                valid_lines.append(line)
        
        if valid_lines:
            return '\\n\\n'.join(valid_lines)
        
        return ""
        
    except Exception as e:
        logger.error(f"docx2txt输出清理失败: {str(e)}")
        return text
'''
    
    return new_methods

def apply_fix_to_document_parser():
    """将修复应用到DocumentParser"""
    print("🔧 正在修复DocumentParser...")
    
    # 读取当前的document_parser.py
    parser_file = "app/services/document_parser.py"
    
    try:
        with open(parser_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_parse_old_doc_file函数并替换
        import re
        
        # 替换_parse_old_doc_file函数
        pattern = r'def _parse_old_doc_file\(self, file_content: bytes\) -> str:.*?(?=def |\Z)'
        
        new_function = '''def _parse_old_doc_file(self, file_content: bytes) -> str:
        """修复版本的老版本.doc文件解析"""
        logger.info("使用修复版本解析老版本DOC文件...")

        # 检测文件类型
        if file_content.startswith(b'\\xd0\\xcf\\x11\\xe0'):
            file_type = "ole_doc"
        else:
            file_type = "unknown"

        logger.info(f"检测到DOC文件类型: {file_type}")

        # 方法1: 专门的中文文本提取（最重要的改进）
        try:
            chinese_text = self._extract_chinese_text_only(file_content)
            if chinese_text and len(chinese_text.strip()) > 10:
                return f"# Word文档内容\\n\\n{chinese_text}"
        except Exception as e:
            logger.info(f"中文文本提取失败: {str(e)}")

        # 方法2: 使用docx2txt（如果可用）
        try:
            import docx2txt
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                text_content = docx2txt.process(temp_file_path)
                if text_content and text_content.strip():
                    cleaned = self._clean_docx2txt_output(text_content)
                    if cleaned:
                        return f"# Word文档内容\\n\\n{cleaned}"
            finally:
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except ImportError:
            logger.info("docx2txt模块未安装")
        except Exception as e:
            logger.info(f"docx2txt解析失败: {str(e)}")

        # 如果所有方法都失败
        return self._generate_doc_failure_message(file_type)

    '''
        
        print("✅ 修复脚本创建完成")
        print("💡 需要手动应用修复到DocumentParser类")
        
        return new_function
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("🔧 .doc文件解析修复脚本")
    print("=" * 50)
    
    # 生成修复代码
    fix_code = create_fixed_doc_parser()
    print("✅ 修复代码已生成")
    
    # 应用修复
    result = apply_fix_to_document_parser()
    if result:
        print("✅ 修复应用成功")
    else:
        print("❌ 修复应用失败")
    
    print("\n💡 下一步: 手动将修复代码应用到DocumentParser类")
