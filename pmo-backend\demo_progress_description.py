#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目进度情况字段功能演示
"""

import pymysql
import os
from dotenv import load_dotenv
from datetime import datetime

def demo_progress_description():
    """演示项目进度情况字段的使用"""
    print("🎯 项目进度情况字段功能演示")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    config = {
        'host': os.getenv('DB_HOST'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME'),
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        
        # 1. 获取督办事项和公司信息
        print("\n📋 获取督办事项和公司信息...")
        
        cursor.execute("""
            SELECT id, work_theme, completion_deadline 
            FROM supervision_items 
            ORDER BY id 
            LIMIT 3
        """)
        items = cursor.fetchall()
        
        cursor.execute("""
            SELECT id, company_name, company_code 
            FROM companies 
            WHERE is_active = 1 
            ORDER BY id 
            LIMIT 5
        """)
        companies = cursor.fetchall()
        
        print(f"督办事项数量: {len(items)}")
        print(f"公司数量: {len(companies)}")
        
        # 2. 为每个督办事项的每个公司添加详细的项目进度情况
        print("\n📝 添加项目进度情况示例数据...")
        
        # 定义不同类型的项目进度描述示例
        progress_examples = [
            {
                'status': '√',
                'description': '项目已全面完成！✅\n\n📊 完成情况：100%\n🎯 关键成果：\n• 系统升级改造全部完成\n• 性能提升30%\n• 用户满意度达95%\n• 通过所有验收测试\n\n🏆 项目亮点：\n• 提前2周完成交付\n• 零重大缺陷\n• 获得用户高度认可'
            },
            {
                'status': 'O',
                'description': '项目进展良好，正在稳步推进中 🚀\n\n📊 当前进度：75%\n✅ 已完成工作：\n• 需求分析和设计 ✓\n• 核心功能开发 ✓\n• 单元测试 ✓\n• 集成测试进行中...\n\n🔄 正在进行：\n• 系统集成测试\n• 用户验收测试准备\n• 部署环境搭建\n\n📅 预计完成时间：本月底'
            },
            {
                'status': '！',
                'description': '项目存在延期风险，需要重点关注 ⚠️\n\n📊 当前进度：60%\n🚨 主要问题：\n• 技术难点攻关耗时较长\n• 第三方接口对接延迟\n• 人员资源紧张\n\n🔧 应对措施：\n• 增加技术专家支持\n• 与第三方供应商加强沟通\n• 调整项目优先级\n• 制定风险应急预案\n\n📅 调整后预计完成：下月中旬'
            },
            {
                'status': 'X',
                'description': '项目尚未正式启动 📋\n\n📋 准备工作：\n• 项目立项申请已提交\n• 等待预算审批\n• 团队成员确认中\n• 需求调研计划制定\n\n📅 计划启动时间：下月初\n🎯 预期目标：\n• 完成详细需求分析\n• 制定技术方案\n• 确定项目里程碑\n• 建立项目团队'
            },
            {
                'status': '—',
                'description': '经评估，此项目不适用于本公司 📝\n\n🔍 评估结果：\n• 业务场景不匹配\n• 技术架构差异较大\n• 成本效益比不合理\n• 现有系统已满足需求\n\n✅ 替代方案：\n• 采用现有系统优化\n• 关注相关技术发展\n• 参与行业最佳实践分享'
            }
        ]
        
        # 为每个督办事项的每个公司添加进度描述
        for i, (item_id, work_theme, deadline) in enumerate(items):
            print(f"\n📋 处理督办事项: {work_theme}")
            
            for j, (company_id, company_name, company_code) in enumerate(companies):
                # 选择不同的进度示例
                example = progress_examples[(i + j) % len(progress_examples)]
                
                # 检查是否已存在记录
                cursor.execute("""
                    SELECT id FROM company_progress 
                    WHERE supervision_item_id = %s AND company_id = %s
                """, (item_id, company_id))
                
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    cursor.execute("""
                        UPDATE company_progress 
                        SET status = %s, progress_description = %s, updated_by = %s
                        WHERE supervision_item_id = %s AND company_id = %s
                    """, (example['status'], example['description'], 'demo_system', item_id, company_id))
                    action = "更新"
                else:
                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO company_progress 
                        (supervision_item_id, company_id, status, progress_description, updated_by)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (item_id, company_id, example['status'], example['description'], 'demo_system'))
                    action = "创建"
                
                print(f"  ✅ {action} {company_name} 的进度记录 (状态: {example['status']})")
        
        connection.commit()
        
        # 3. 验证数据并展示结果
        print("\n🔍 验证添加的项目进度情况...")
        
        cursor.execute("""
            SELECT 
                si.work_theme,
                c.company_name,
                cp.status,
                LEFT(cp.progress_description, 100) as progress_preview
            FROM company_progress cp
            JOIN supervision_items si ON cp.supervision_item_id = si.id
            JOIN companies c ON cp.company_id = c.id
            WHERE cp.progress_description IS NOT NULL 
            AND cp.progress_description != ''
            ORDER BY si.id, c.id
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 成功添加了 {len(results)} 条详细的项目进度记录")
        print("\n📋 示例数据预览:")
        print("-" * 80)
        
        for work_theme, company_name, status, progress_preview in results:
            print(f"督办事项: {work_theme}")
            print(f"公司: {company_name} | 状态: {status}")
            print(f"进度情况: {progress_preview}...")
            print("-" * 80)
        
        cursor.close()
        connection.close()
        
        print("\n🎉 项目进度情况字段演示完成！")
        print("\n📋 功能特点:")
        print("  ✅ 支持丰富的文本描述（最多500字符）")
        print("  ✅ 可以包含进度百分比、关键节点、问题描述等")
        print("  ✅ 支持表情符号和格式化文本")
        print("  ✅ 与状态字段配合使用，提供完整的项目信息")
        print("  ✅ 前端界面友好，带字数统计功能")
        
        print("\n🖥️ 使用方法:")
        print("  1. 在前端页面点击公司状态单元格")
        print("  2. 在弹出的编辑对话框中填写'项目进度情况'")
        print("  3. 可以详细描述项目进度、完成程度、关键节点等")
        print("  4. 点击保存按钮将数据存储到数据库")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    demo_progress_description()
