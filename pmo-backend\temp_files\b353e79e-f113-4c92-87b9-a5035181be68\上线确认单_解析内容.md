# 系统上线确认单  

<html>
<table>  <tr>    <td>项目</td>    <td>经营性租赁系统项目</td>    <td>合同编号</td>    <td>20000117-24-FW1508-0018<br>J_C_20240130_028</td>  </tr>  <tr>    <td>客户名称</td>    <td rowspan="1" colspan="3">广西数字金服科技有限公司</td>  </tr>  <tr>    <td>客户地址</td>    <td rowspan="1" colspan="3">广西省南宁市良庆区飞云路6号14层</td>  </tr>  <tr>    <td>客户联系人</td>    <td>廖芳</td>    <td>客户电话</td>    <td>13636481916</td>  </tr>  <tr>    <td>软件版本</td>    <td rowspan="1" colspan="3">金蝶云苍穹V6.0.1</td>  </tr>  <tr>    <td>上线范围</td>    <td rowspan="1" colspan="3">广西数字金服科技有限公司</td>  </tr>  <tr>    <td>上线主要内容</td>    <td rowspan="1" colspan="3">融资租赁管理、经营性租赁管理、接口管理等，具体上线功能详见：<br>附件一：经营性租赁清单<br>附件二：融资租赁清单</td>  </tr>  <tr>  </tr></table>
</html>  

经过双方共同努力，北部湾租赁业务系统以上功能模块已上线完成。经检查确认，上线运行结果 达到了预期效果，符合双方的实施标准和要求，具备按有关程序进行下一步实施工作的基础。同时， 乙方需根据实际业务开展后所遇影响业务正常办理的问题和建议（包含但不仅限于流程、表单、报价 测算等功能），不断优化完善系统功能，支持业务办理。  

![](images/8717454e48dd4a66ff8cc5500c6812e7125b211dcb06a360b55804d65273a25b.jpg)  

乙  方： 深圳宏景动力信息技术有限公司  

![](images/fd75426b10dba6cea98745bd0875d3e470079c5774aaf7fbde2d35588e1e6744.jpg)  

![](images/e05610b8e92cd2045a3194f7761c801d93fbbd50c465473ee70d1a1a381f9c86.jpg)  

附件1：经营性租赁清单  


<html>
<table>  <tr>    <td rowspan="1" colspan="5">金蝶云苍弯租赁业务管理系统功能清单(经营性租赁)</td>  </tr>  <tr>    <td>模块</td>    <td>功能类别</td>    <td>功能模块</td>    <td>功能描述</td>  </tr>  <tr>    <td rowspan="7" colspan="1">1、基础<br>资料</td>    <td rowspan="3" colspan="1">1.1客户资料</td>    <td>客户报表项目、借款<br>人性质、客户企业规<br>模、所属行业门类、<br>客户所有制性质区域<br>等</td>    <td>基础信息维护，使得系统更具有灵活性、扩展性。</td>  </tr>  <tr>    <td></td>    <td>同时系统分析数据可以依据不同纬度进行统计分析。</td>  </tr>  <tr>    <td></td>    <td>便于用户快速操作。</td>  </tr>  <tr>    <td>1.2租赁类型</td>    <td>租赁业务类型设置</td>    <td>系统支持面向融资租赁、厂商租赁、经营性租赁等业务类型，可<br>对业务类型进行参数配置，以适应不同的场景和流程。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">1.3系统资料</td>    <td>租赁形式、保险公司、<br>保险费率、央行存款<br>利率幅度维护、罚息<br>利率、费用类型、利<br>率类型、设备类型、<br>设备状态等</td>    <td>系统提供参数化设置，更灵活适用于不同业务场景。</td>  </tr>  <tr>    <td></td>    <td>如：设备保险自动计算；费用类型、罚息利率、罚息宽限天数个<br>性配置等。</td>  </tr>  <tr>    <td>1.4项目资料</td>    <td>律师事务所、信用等<br>级、担保保证类型、<br>评分项、保险公司等</td>    <td>系统提供项目相关资料。可支持手工新增、修改各资料内容，是<br>后续业务数据的基础信息。</td>  </tr>  <tr>    <td rowspan="7" colspan="1">2、客户<br>管理</td>    <td rowspan="2" colspan="1">2.1客户管理</td>    <td>法人客户</td>    <td rowspan="2" colspan="1">将客户分为法人客户登记、自然人登记、厂商登记、担保人，维<br>护相应客户的基本信息，并记录业务员日常客户拜访的信息。支<br>持对客户的名称及证照号码等进行修改；能够按提供的标准，对<br>企业的规划分类（大中小型）进行自动划分。</td>  </tr>  <tr>    <td>自然人客户</td>  </tr>  <tr>    <td rowspan="3" colspan="1">2.2客户报表<br>管理</td>    <td>报表项目</td>    <td>系统支持自定义报表项目，以及财务指标分析计算逻辑。</td>  </tr>  <tr>    <td>报表模板</td>    <td>企业在尽调风控环节需要搜集客户的财务报表及统计分析报表，<br>系统支持预设报表模板，便于灵活运用。</td>  </tr>  <tr>    <td>财务报表OCR识别</td>    <td>可通过调用接口OCR识别所搜索的PDF或JPG等格式的财务报<br>表，提升业务人员的报表录入效率。</td>  </tr>  <tr>    <td>2.3供应商</td>    <td>供应商管理</td>    <td>记录租赁业务中的供应商，如直租或厂商租赁业务中设备的供应<br>商。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">2.4黑名单</td>    <td rowspan="2" colspan="1">黑名单管理</td>    <td>对信誉不良的客户可直接黑名单处理，后续不再发生相关业务，<br>从而控制业务风险。</td>  </tr>  <tr>    <td>支持批量导入、支持从客户名单中转入黑名单、移除黑名单等功<br>能；控制强度支持多种：如预警、提醒、严格控制。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">3、租前<br>管理</td>    <td rowspan="2" colspan="1">3.1项目管理</td>    <td>项目过程管理</td>    <td>项目过程可以清晰了解每一笔业务的实际发生情况，实时了解项<br>目进展进度。<br>快速查找业务中每个环节发生的业务单据。<br>可通过颜色清晰标识项目异常情况。</td>  </tr>  <tr>    <td>项目报价</td>    <td>系统根据合同金额、期限及相应的还款方式及其它相应的影响还<br>款因素进行系统测算，生成客户还款表，系统支持多次报价。系</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td rowspan="5" colspan="1"></td>    <td></td>    <td>扰又符多件具/定灭/品配置。</td>  </tr>  <tr>    <td>项目立项</td>    <td>项目立项信息登记项目金额、租赁方案、计划期项等信息维护。</td>  </tr>  <tr>    <td>项目交接</td>    <td>项目执行过程中有相关责任人变调，则需要进行项目交接，系统<br>内可以通过项目交接单记录交接的项目及相关资料；支持一次批<br>量移交项目、客户及相关待办。</td>  </tr>  <tr>    <td>项目评审（项目申报）</td>    <td>项目完成业务报告及风控报告后，进行项目评审会议，审批中需<br>要记录商务条款、放款前落实条件、审批结论等内容。</td>  </tr>  <tr>    <td>项目变更</td>    <td>当项目审批通过后，如因特殊情况，需要变更项目重要信息，需<br>要发起项目变更审批。</td>  </tr>  <tr>    <td rowspan="4" colspan="1">3.2合同管理</td>    <td>经营性租赁合同</td>    <td>录入和维护经营性租赁合同，支持合同的审批和套打印，支持<br>合同的变更，可同步维护合同状态，或关闭合同。包含用印流程：<br>登记印章类型，盖章分类以及用印审批过程等信息。</td>  </tr>  <tr>    <td>合同综合查询</td>    <td>按合同纬度，进行查询合同相关的所有业务发生数据，如：合同<br>后期的放款计划、投放单、偿还计划、回笼、其他收退款等。</td>  </tr>  <tr>    <td>担保合同登记</td>    <td>租赁业务中，如需要提供抵押、质押、保证等多种担保类型，系<br>统支持多种合同登记。</td>  </tr>  <tr>    <td>抵质押物管理</td>    <td>抵质押物管理，系统支持单项或批量导入、登记、解押管理。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">3.3合同签约</td>    <td>合同签约</td>    <td>所有签约文件都在此单据记录签约流程，以及最终版签约文件，<br>支持线下签约和线上签约（使用电子签章功能）。</td>  </tr>  <tr>    <td>用印管理</td>    <td>可进行用印申请，登记印章类型，盖章分类以及用印审批过程等<br>信息。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">3.4合同变更</td>    <td>合同变更</td>    <td>合同生效后，需要进行合同修改，需要进行合同修改申请，申请<br>通过后，才允许进行合同内容变更。</td>  </tr>  <tr>    <td>合同变更记录查询</td>    <td>合同后期的变更历史记录查询。</td>  </tr>  <tr>    <td rowspan="10" colspan="1">4、租后<br>管理</td>    <td>资产采购</td>    <td>记录资产采购结果及资产的基本信息，以更新资产状态。</td>  </tr>  <tr>    <td>经营性租赁资产清单</td>    <td>可进行经营租赁资产的维护，如建筑机械、船舶、电站、物业等<br>租赁资产（包含在建项目资产、即在建资产），主要维护其基本<br>信息、登记信息、价值信息、关联合同及附件。</td>  </tr>  <tr>    <td>设备资产保险管理</td>    <td>自动对设备资产进行首保、续保计算，并根据实际情况生成凭证。</td>  </tr>  <tr>    <td>租赁资产交付确认</td>    <td>可录入租赁交付确认单，按合同要求将租赁物资产交付客户，记<br>录交付起租确认信息。</td>  </tr>  <tr>    <td>租赁资产归还验收</td>    <td>录入租赁资产归还及验收情况，可根据之前的经营性租赁资产交<br>付确认单生成，需要提供单据套打功能，以便可配置模板并打印<br>归还验收单。</td>  </tr>  <tr>    <td rowspan="5" colspan="1">4.2收退款</td>    <td>合同中涉及的保证金的收款管理。</td>  </tr>  <tr>    <td>系统支持保证金抵扣租金。</td>  </tr>  <tr>    <td>合同中涉及的保证金的退款管理。</td>  </tr>  <tr>    <td>保证金支持自动生成财务凭证。</td>  </tr>  <tr>    <td>其他收款</td>    <td>合同中涉及的咨询费、通道费、手续费等费用的收款管理。</td>  </tr>  <tr>    <td>其他退款</td>    <td>合同中涉及的咨询费、手续费等费用的退款管理。</td>  </tr>  <tr>    <td>4.4偿还计划</td>    <td>偿还计划（租金计划<br>表)</td>    <td>根据合同约定或合同后期修改约定的内容自动生成偿还计划，系<br>统支持分批投放分批起租；支持分批投放一次起租；支持租前结<br>息等多种起租逻辑。</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td></td>    <td></td>    <td>偿还计划复史</td>    <td>偿还计划可以通过支付进行支付。</td>  </tr>  <tr>    <td rowspan="3" colspan="1">4.5回笼与核<br>销</td>    <td>银行流水单</td>    <td>由银企直联、资金系统或手工导入成为银行流水单，作为重要<br>的原始收款记录，以供回笼核销。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">回笼单</td>    <td>根据偿还计划进行资金回笼，并自动计算出余额。</td>  </tr>  <tr>    <td>如未按偿还计划约定时间回笼，系统根据合同约定罚息利率，自<br>动计算出罚息；系统支持提前还款。</td>  </tr>  <tr>    <td rowspan="3" colspan="1">4.6逾期催收</td>    <td>逾期催收</td>    <td>对于存在逾期的租赁合同，记录相关的催收信息，并对催收过程<br>进行记录。</td>  </tr>  <tr>    <td>押金抵扣申请</td>    <td>对于部分逾期或有需要的客户，拟订风险金抵扣申请，以便使用<br>客户的风险金抵扣为应收租金。</td>  </tr>  <tr>    <td>租金收款通知书</td>    <td>系统可根据每期租金到期前N天，打印租金收款通知单给客户。</td>  </tr>  <tr>    <td rowspan="5" colspan="1">4.9制裁诉讼</td>    <td>起诉管理</td>    <td>含：诉讼申请、财产保全、诉状、起诉委托代理、一审法院受理、<br>二审法院受理。</td>  </tr>  <tr>    <td>应诉管理</td>    <td>含：应诉答辩、应诉委托代理。</td>  </tr>  <tr>    <td>诉讼费用</td>    <td>用于记录诉讼过程中产生的费用。</td>  </tr>  <tr>    <td>裁决管理</td>    <td>含：一审传票、二审传票、案件调解、案件撤诉、一审判决、二<br>审判决。</td>  </tr>  <tr>    <td>执行管理</td>    <td>含：结案管理、自动履行、申请执行、执行情况。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">4.10租后巡检</td>    <td>首次检查</td>    <td>针对投放后进行首次检查，核实投放资金情况及设备等情况。</td>  </tr>  <tr>    <td>租后巡检</td>    <td>常规检查，每季度或月进行租后巡检，针对租赁物、客户财务情<br>况等信息进行记录，并出具报告。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">4.11退租及结<br>清</td>    <td>结清管理</td>    <td>系统支持租赁正常结束、提前结束、异常结束等多种情况的方式，<br>进行结清结算。可以依据不同进行审批流程设置。</td>  </tr>  <tr>    <td>结清证明</td>    <td>为结清客户开具并打印结清证明。</td>  </tr>  <tr>    <td rowspan="3" colspan="1">5、档案<br>管理</td>    <td>业务清单配置</td>    <td rowspan="3" colspan="1">管理所有项目资料的移交、归档、借阅、归还、移出；以及项目<br>合同变更后的资料移交、归档、借阅、归还。</td>  </tr>  <tr>    <td>资料归档</td>  </tr>  <tr>    <td>资料借阅登记、归还、<br>移出</td>  </tr>  <tr>    <td rowspan="6" colspan="1">6、报表<br>管理</td>    <td>项目管理台账</td>    <td rowspan="6" colspan="1">系统内提供各类管理报表、业务分析报表、数据统计报表等。<br>个性开发10张</td>  </tr>  <tr>    <td>合同管理报表</td>  </tr>  <tr>    <td>收退款管理报表</td>  </tr>  <tr>    <td>偿还计划报表</td>  </tr>  <tr>    <td>逾期催收报表</td>  </tr>  <tr>    <td>其他重要业务报表<br>(15张)</td>  </tr>  <tr>    <td rowspan="3" colspan="1">6.2监管数据<br>报送和报表辅<br>助生成</td>    <td rowspan="3" colspan="1">系统模块的数据标准，严格按照金融基础数据、利率报备、银监<br>EAST报表等监管要求严格执行，可通过集中的报表工具，输出<br>需要报送监管的各类报表数据，在中台处理后，与其它数据整合，<br>共同辅助生成各类监管报表，以提升数据报送的效率。</td>  </tr>  <tr>  </tr>  <tr>  </tr>  <tr>    <td rowspan="2" colspan="1">9、系统<br>接口及<br>预警</td>    <td>企业信息大数据查询<br>抓取（企查查等）</td>    <td>从大数据接口读取客户动态信息。（不含对第三方的数据使用服<br>务费用）</td>  </tr>  <tr>    <td>手机短信</td>    <td>系统可发送短信提醒到员工或客户手机。（不含对第三方的数据<br>使用服务费用）</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td rowspan="1" colspan="4"></td>  </tr>  <tr>    <td rowspan="9" colspan="1">9.3系统集成</td>    <td></td>    <td>电子邮件</td>    <td>系统可发送邮件到指定人邮箱。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">与北金租现有业务中<br>台系统对接</td>    <td>与北金租现有业务中台系统对接，统一用户组织及权限管理，实<br>现单点登录，重要的客户、供应商、黑名单等基础数据保持同步。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">与钉钉对接，实现移<br>动审批</td>    <td>与北金租当前使用的钉钉进行移动端集成，在前端也实现单点登<br>录、统一所有租赁业务的待办和已办任务，实现统一的业务审批<br>处理。系统在前端即能够区分不同租赁审批任务对应的实际中后<br>台，然后提交相应的中后台进行处理。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">与资金系统对接</td>    <td>通过接口向资金系统提供应收款代扣数据，并查询获取实际代扣<br>结果。或者获取收款账户的银行账户流水，即实际收款数据。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">反洗钱对接</td>    <td>实现与北金租当前的反洗钱系统对接，以便能在租赁业务审批全<br>流程满足公司反洗钱管理要求，如：项目准入控制、业务审批参<br>考以及风险预警。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">中登网</td>    <td>实现系统导出中登网信息登记采集表（按业务系统模板导出数据，<br>手工上报)</td>  </tr>  <tr>    <td rowspan="1" colspan="2">财务报表OCR识别</td>    <td>自动对接OCR识别接口。（不含对第三方的接口使用服务费用）</td>  </tr>  <tr>    <td rowspan="1" colspan="2">赛弥斯风险系统对接</td>    <td>对接赛弥斯风险系统接口开发（传EXCEL版本的三大财报，返回<br>PDF)</td>  </tr>  <tr>    <td rowspan="1" colspan="2">与财务系统对接</td>    <td>与金蝶EAS系统对接，以实现生成凭证、传递凭证、查询凭证等<br>业务财务一体化功能。</td>  </tr>  <tr>    <td rowspan="1" colspan="1">9.4辅助服务</td>    <td rowspan="1" colspan="2">合同文档WPS在线<br>预览、编辑</td>    <td>支持合同文件等在线预览和编辑。</td>  </tr>  <tr>    <td rowspan="3" colspan="1">10、自<br>动任务<br>和预警</td>    <td rowspan="3" colspan="1">10.1系统自动<br>任务和预警</td>    <td rowspan="1" colspan="2">自动任务配置</td>    <td>根据用户的要求，预先设置好需要执行的自动任务，并指定其执<br>行计划，以便让系统自动按计划执行。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">待办任务提醒</td>    <td>对于每个用户的待办任务，系统根据预设的紧急及重要程度进行<br>自动提醒。</td>  </tr>  <tr>    <td rowspan="1" colspan="2">其它自动任务</td>    <td>可根据需要，配置提供如：档案归档、合同签署、起租提醒、还<br>款提醒、逾期提醒、催收提醒等不超10项的自动预警任务。</td>  </tr>  <tr>  </tr></table>
</html>  

# 附件二：融资租赁清单  

金蝶云苍穹租赁业务管理系统功能清单（融资租赁）  


<html>
<table>  <tr>    <td>模块</td>    <td>功能类别</td>    <td>功能模块</td>    <td>功能描述</td>  </tr>  <tr>    <td rowspan="7" colspan="1">1、基础<br>资料</td>    <td rowspan="3" colspan="1">1.1客户资料</td>    <td>客户报表项目、借款<br>人性质、客户企业规<br>模、所属行业门类、<br>客户所有制性质区域<br>等</td>    <td>基础信息维护，使得系统更具有灵活性、扩展性。</td>  </tr>  <tr>    <td></td>    <td>系统提供项目相关资料。可支持手工新增、修改各资料内容，是<br>后续业务数据的基础信息。</td>  </tr>  <tr>    <td></td>    <td>便于用户快速操作。</td>  </tr>  <tr>    <td>1.2租赁类型</td>    <td>租赁业务类型设置</td>    <td>系统支持面向各类客户业务的直租、回租等业务类型，可对业务<br>类型进行参数配置，以适应不同的场景和流程。</td>  </tr>  <tr>    <td rowspan="3" colspan="1">1.3系统资料</td>    <td>利率参数、央行贷款<br>利率幅度维护、租赁<br>形式、保险公司、保<br>险费率、央行存款利<br>率幅度维护、罚息利<br>率、费用类型、利率<br>类型、设备类型、设<br>备状态等</td>    <td>系统提供参数化设置，更灵活适用于不同业务场景。</td>  </tr>  <tr>    <td></td>    <td>如：设置利率参数、央行贷款利率幅度维护等数据，系统可以自<br>动进行单笔、批量调息处理；设备保险自动计算；费用类型、罚<br>息利率、罚息宽限天数个性配置等</td>  </tr>  <tr>    <td></td>    <td></td>  </tr>  <tr>    <td></td>    <td>1.4项目资料</td>    <td>律师事务所、信用等<br>级、担保保证类型、<br>评分项、保险公司等</td>    <td>系统提供项目相关资料。可支持手工新增、修改各资料内容，是<br>后续业务数据的基础信息。</td>  </tr>  <tr>    <td rowspan="9" colspan="1">2、客户<br>管理</td>    <td rowspan="2" colspan="1">2.1客户管理</td>    <td>法人客户</td>    <td rowspan="2" colspan="1">将客户分为法人客户登记、自然人登记、厂商登记、担保人，维<br>护相应客户的基本信息，并记录业务员日常客户拜访的信息；支<br>持对客户的名称及证照号码等进行修改；能够按提供的标准，对<br>企业的规划分类（大中小型）进行自动划分。</td>  </tr>  <tr>    <td>自然人客户</td>  </tr>  <tr>    <td rowspan="3" colspan="1">2.2客户财务<br>报表</td>    <td>报表项目</td>    <td>系统支持自定义报表项目，以及财务指标分析计算逻辑。</td>  </tr>  <tr>    <td>报表模板</td>    <td>企业在尽调风控环节需要搜集客户的财务报表及统计分析报表，<br>系统支持预设报表模板，便于灵活运用。</td>  </tr>  <tr>    <td>财务报表OCR识别</td>    <td>可调用接口，OCR识别所搜索的PDF或JPG等格式的财务报表，<br>提升业务人员的报表录入效率。</td>  </tr>  <tr>    <td>2.3供应商</td>    <td>供应商管理</td>    <td>记录租赁业务中的供应商，如直租业务中设备的供应商。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">2.4商机管理</td>    <td>商机登记</td>    <td>记录客户名称、联系人等信息</td>  </tr>  <tr>    <td>商机跟进(客户拜访)</td>    <td>记录商机跟进的情况。支持移动端录入和填写拜访记录，支持资<br>料、拍照及视频等文件上传。能够准确的按项目按业务人员统计<br>拜访次数、拜访问隔等，能按制度自动计算业务工作得分。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">2.5黑名单</td>    <td rowspan="2" colspan="1">黑名单管理</td>    <td>对信誉不良的客户可直接黑名单处理，后续不再发生相关业务，<br>从而控制业务风险。</td>  </tr>  <tr>    <td>支持批量导入、支持从客户名单中转入黑名单、移除黑名单等功能；控制强度支持多种：如预警、提醒、严格控制。</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td rowspan="10" colspan="1">3、租前<br>管理</td>    <td>项目过程管理</td>    <td>项目进展进度。<br>快速查找业务中每个环节发生的业务单据。<br>可通过颜色清晰标识项目异常情况。</td>  </tr>  <tr>    <td>项目登记</td>    <td>客户经理根据前期与客户的接触，初步录入客户融资需求及融资<br>项目基本情况，及收集相关前期资料，判断项目是否有必要进一<br>步推进。</td>  </tr>  <tr>    <td>项目报价</td>    <td>系统根据融资金额、融资期限、利率及相应的还款方式及其它相<br>应的影响还款因素进行系统测算，生成客户还款表，及项目IRR，<br>系统支持多次报价。系统支持多种算法灵活配置，能够计算和实<br>时显示项目的XIRR和净现值。</td>  </tr>  <tr>    <td>项目立项</td>    <td>项目立项信息登记项目融资金额、租赁方案、计划期项等信息维<br>护</td>  </tr>  <tr>    <td>项目交接</td>    <td>项目执行过程中有相关责任人变调，则需要进行项目交接，系统<br>内可以通过项目交接单记录交接的项目及相关资料；支持一次批<br>量移交项目、客户及相关待办。</td>  </tr>  <tr>    <td>项目变更</td>    <td>当项目审批通过后，如因特殊情况，需要变更项目重要信息，需<br>要发起项目变更审批。</td>  </tr>  <tr>    <td>尽职调查（客户及担<br>保人）</td>    <td>项目立项完成后对客户及担保人经营及财务情况进行项目可行性<br>调查分析。<br>已经审批通过的尽职调查需要调整，需通过变更申请进行调整。<br>尽职调查变更审批通过后，变更内容才生效。</td>  </tr>  <tr>    <td>风险报告（客户及担<br>保人）</td>    <td>依据业务调查报告编写风险报告，体现风险依据、风险评估等内<br>容</td>  </tr>  <tr>    <td>合规意见（客户及担<br>保人）</td>    <td>依据业务调查报告，法务岗编写合规依据，出具相关报告内容。</td>  </tr>  <tr>    <td>项目审批</td>    <td>项目完成业务报告及风控报告后，进行项目评审会议，审批中需<br>要记录商务条款、放款前落实条件、审批结论等内容</td>  </tr>  <tr>    <td rowspan="4" colspan="1">3.3合同管理</td>    <td>融资租赁合同</td>    <td>支持各类合同（如租赁合同、买卖合同、回租回购协议等）按模<br>板进行生成和打印，或套打输出为Word、PDF，进行相应的预<br>览、编辑；支持电子签章；支持各种复杂流程审批配置。包含用<br>印流程：登记印章类型，盖章分类以及用印审批过程等信息。</td>  </tr>  <tr>    <td>合同综合查询</td>    <td>按合同纬度，进行查询合同相关的所有业务发生数据，如：合同<br>后期的放款计划、投放单、偿还计划、回笼、其他收退款等。</td>  </tr>  <tr>    <td>担保合同登记</td>    <td>融资租赁业务中，需要提供抵押、质押、保证等多种担保类型，<br>系统支持多种合同登记</td>  </tr>  <tr>    <td>抵质押物管理</td>    <td>抵质押物管理；系统支持批量导入、登记、解押管理</td>  </tr>  <tr>    <td rowspan="2" colspan="1">3.4合同签约</td>    <td>合同签约</td>    <td>所有签约文件都在此单据记录签约流程，以及最终版签约文件，<br>支持线下签约和线上签约（使用电子签章功能）。</td>  </tr>  <tr>    <td>用印管理</td>    <td>可进行用印申请，登记印章类型，盖章分类以及用印审批过程等<br>信息。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">3.5合同变更</td>    <td>合同后期变更</td>    <td>合同后期的变更记录查询</td>  </tr>  <tr>    <td></td>    <td>合同生效后，需要进行合同修改，需要进行合同修改申请，申请</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td></td>    <td rowspan="1" colspan="1"></td>    <td></td>    <td>通过后，才允许进行台间内容变更。</td>  </tr>  <tr>    <td></td>    <td rowspan="1" colspan="1"></td>    <td>合同变更记录查询</td>    <td>查询合同的变更记录。</td>  </tr>  <tr>    <td rowspan="10" colspan="1">4.1租赁物管<br>理</td>    <td rowspan="1" colspan="1"></td>    <td>租赁物登记</td>    <td>租赁合同涉及的租赁物登记信息，可反写合同</td>  </tr>  <tr>    <td rowspan="1" colspan="1"></td>    <td>设备保险</td>    <td>自动对设备进行首保、续保计算，并根据实际情况生成凭证。</td>  </tr>  <tr>    <td rowspan="2" colspan="1"></td>    <td>产权转出，合同结束前设备产权转移的登记及产权转移单的打印</td>  </tr>  <tr>    <td>回租业务中，由承租方向出租方销售租赁后，需要将设备产权转<br>移到租赁公司</td>  </tr>  <tr>    <td rowspan="4" colspan="1"></td>    <td>合同中涉及的保证金的收款管理</td>  </tr>  <tr>    <td>系统支持保证金内扣逻辑；支持保证金抵扣租金。</td>  </tr>  <tr>    <td>合同中涉及的保证金的退款管理；</td>  </tr>  <tr>    <td>保证金支持自动生成财务凭证。</td>  </tr>  <tr>    <td rowspan="2" colspan="1"></td>    <td>其他收款</td>    <td>合同中涉及的咨询费、通道费、手续费等费用的收款管理</td>  </tr>  <tr>    <td rowspan="1" colspan="1"></td>    <td>其他退款</td>    <td>合同中涉及的咨询费、手续费等费用的退款管理</td>  </tr>  <tr>    <td rowspan="10" colspan="1">4.2收退款</td>    <td rowspan="1" colspan="1"></td>    <td>投放申请</td>    <td>由业务员根据合同约定编制投放计划，支持多次投放计划的编制</td>  </tr>  <tr>    <td rowspan="1" colspan="1"></td>    <td>资金投放</td>    <td>支持投放前提条件自动生成，及系统控制</td>  </tr>  <tr>    <td rowspan="5" colspan="1"></td>    <td>起租管理</td>    <td>审批通过的投放申请，为财务部可依据计划做资金投放；系统能<br>自动根据费用是否为内扣而生成不同凭证</td>  </tr>  <tr>    <td>投放结息</td>    <td>确认投放的起租日期，系统支持分批投放分批起租；支持分批投<br>放一次起租；支持租前结息等多种起租逻辑。可按照预设的起租<br>通知书模板生成相应的起租通知书。</td>  </tr>  <tr>    <td>偿还计划制作</td>    <td>根据合同约定或合同后期修改约定的内容自动生成偿还计划，系<br>统支持分批投放分批起租；支持分批投放一次起租；支持租前结<br>息等多种起租逻辑。</td>  </tr>  <tr>    <td>偿还计划变更</td>    <td>偿还计划可以通过变更单进行变更处理。</td>  </tr>  <tr>    <td>偿还计划调息</td>    <td>依据央行利率等信息的维护，系统可以自动计算偿还计划调息浮<br>动；支持单笔、批量两种方式</td>  </tr>  <tr>    <td rowspan="3" colspan="1"></td>    <td>银行流水单</td>    <td>由银企直联、资金系统或手工导入成为银行流水单，作为重要的<br>原始收款记录，以供回笼核销。</td>  </tr>  <tr>    <td rowspan="2" colspan="1"></td>    <td>回笼单</td>    <td>根据偿还计划进行资金回笼，并自动计算出余额。</td>  </tr>  <tr>    <td>或有收入回笼</td>    <td>如未按偿还计划约定时间回笼，系统根据合同约定罚息利率，自<br>动计算出罚息；系统支持提前还款；</td>  </tr>  <tr>    <td rowspan="4" colspan="1"></td>    <td rowspan="1" colspan="1"></td>    <td>逾期催收</td>    <td>对于存在逾期的租赁合同，记录相关的催收信息，并对催收过程<br>进行记录；</td>  </tr>  <tr>    <td rowspan="3" colspan="1"></td>    <td>逾期停息</td>    <td>针对已逾期的期项，系统可以设置停息日期，停息日期后系统将<br>不再计算罚息；</td>  </tr>  <tr>    <td>到期租金通知单</td>    <td>系统可根据每期租金到期前N天，打印租金通知单给客户；</td>  </tr>  <tr>    <td>坏账处理</td>    <td>当逾期超过设定天数时，财务人员需要进行坏账处理。</td>  </tr>  <tr>    <td rowspan="2" colspan="1"></td>    <td rowspan="2" colspan="1"></td>    <td>融资租赁收入计提</td>    <td>支持按照实际利率法，或者直线法来计提租息收入，支持初始化，<br>支持每月批量计提，支持租金表变更后，计提轧差处理。</td>  </tr>  <tr>    <td>坏账比,例配置</td>    <td>配置五级分类结果对应的坏账准备计提比例，支持按照合同配置</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td rowspan="3" colspan="1"></td>    <td rowspan="3" colspan="1"></td>    <td></td>    <td>坏账计提比例，支持调整坏账准备计提比例。</td>  </tr>  <tr>    <td>坏账准备计提</td>    <td>支持按照根据合同五级分类结果，查询坏账计提比例后，计提坏<br>账准备，支持初始化，支持每月批量计提，支持租金回笼后坏账<br>准备冲销。</td>  </tr>  <tr>    <td>坏账损失确认</td>    <td>当项目认定为损失后，需要进行坏账损失确认，用此单记录坏账<br>损失确认金额。</td>  </tr>  <tr>    <td rowspan="5" colspan="1">4.9 制裁诉讼</td>    <td>起诉管理</td>    <td>含：诉讼申请、财产保全、诉状、起诉委托代理、一审法院受理、<br>二审法院受理</td>  </tr>  <tr>    <td>应诉管理</td>    <td>含：应诉答辩、应诉委托代理</td>  </tr>  <tr>    <td>诉讼费用</td>    <td>用于记录诉讼过程中产生的费用</td>  </tr>  <tr>    <td>裁决管理</td>    <td>含：一审传票、二审传票、案件调解、案件撤诉、一审判决、二<br>审判决</td>  </tr>  <tr>    <td>执行管理</td>    <td>含：结案管理、自动履行、申请执行、执行情况</td>  </tr>  <tr>    <td rowspan="2" colspan="1">4.10租后巡检</td>    <td>首次检查</td>    <td>针对投放后进行首次检查，核实投放资金情况及设备等情况</td>  </tr>  <tr>    <td>租后巡检</td>    <td>常规检查，每季度或月进行租后巡检，针对租赁物、客户财务情<br>况等信息进行记录，并出具报告。</td>  </tr>  <tr>    <td rowspan="7" colspan="1">4.11风险管理</td>    <td>五级分类</td>    <td>系统可以对所有合同按五种分类归档，并记录归类原因。系统可<br>以实现审批流及多级、批量审批功能。</td>  </tr>  <tr>    <td>风险专项报告</td>    <td>当客户逾期或经营情况不良时，按照公司制度填写风险专项报告，<br>分析风险原因及应对措施</td>  </tr>  <tr>    <td>风险预警</td>    <td>记录客户发生的风险预警，如股东变更，破产，舆情等预警信息。<br>此单可以对接接口，接收大数据风险预警。</td>  </tr>  <tr>    <td>风险处置</td>    <td>针对风险预警进行风险处置，记录处置结果和处置措施。</td>  </tr>  <tr>    <td>合同冻结/解冻</td>    <td>针对客户发生高级别的风险预警，需要进行合同冻结，终止在执<br>行的项目，禁止进行资金投放。待风险解除后，可以通过解冻功<br>能，恢复放款。</td>  </tr>  <tr>    <td>资产处置</td>    <td>针对项目发生风险的情况，可以对租赁物或抵质押物及进行资产<br>处置，此单记录资产处置的结果和最终回收价值。</td>  </tr>  <tr>    <td>产权转移</td>    <td>回租业务中，由承租方向出租方销售租赁后，需要将设备产权转<br>移到租赁公司。或在正常直租的合同结束前，对设备产权转移的<br>登记及产权转移单的打印。</td>  </tr>  <tr>    <td rowspan="2" colspan="1">4.12合同结清</td>    <td>合同结清</td>    <td>系统支持合同正常结束、提前结束、异常结束等多种情况的方式。<br>可以依据不同进行审批流程设置。</td>  </tr>  <tr>    <td rowspan="3" colspan="1"></td>    <td rowspan="3" colspan="1"></td>    <td rowspan="3" colspan="1"></td>  </tr>  <tr>    <td rowspan="3" colspan="1">5、档案<br>管理</td>    <td>业务清单配置</td>  </tr>  <tr>    <td>资料归档</td>  </tr>  <tr>    <td rowspan="3" colspan="1">档案管理</td>    <td>资料档案登记、查询、<br>借阅、归还</td>  </tr>  <tr>    <td rowspan="5" colspan="1">6.、报表<br>管理</td>    <td>项目管理台账</td>    <td rowspan="5" colspan="1">系统内提供各类管理报表、业务分析报表、数据统计报表等。<br>个性开发10张报表</td>  </tr>  <tr>    <td>合同管理报表</td>  </tr>  <tr>    <td>投放管理报表</td>  </tr>  <tr>    <td>收退款管理报表</td>  </tr>  <tr>    <td>偿还计划报表</td>  </tr>  <tr>  </tr></table>
</html>  

<html>
<table>  <tr>    <td rowspan="1" colspan="1"></td>    <td rowspan="2" colspan="1"></td>    <td>逾期催收报表</td>    <td rowspan="2" colspan="1"></td>  </tr>  <tr>    <td>其他重要业务报表<br>(15张)</td>  </tr>  <tr>    <td rowspan="1" colspan="1"></td>    <td rowspan="1" colspan="1">6.2监管数据<br>报送和报表辅<br>助生成</td>    <td>辅助生成需要报送的<br>监管报表</td>    <td>系统模块的数据标准，严格按照金融基础数据、利率报备、银监<br>EAST报表等监管要求严格执行，可通过集中的报表工具，输出<br>需要报送监管的各类报表数据，在中台处理后，与其它数据整合，<br>共同辅助生成各类监管报表，以提升数据报送的效率。</td>  </tr>  <tr>    <td rowspan="1" colspan="1">7、系统<br>接口集<br>成</td>    <td rowspan="1" colspan="1">系统接口</td>    <td rowspan="1" colspan="1">系统相关功能接口</td>    <td>（与经营性租赁功能清单接口相同）</td>  </tr>  <tr>  </tr></table>
</html>  