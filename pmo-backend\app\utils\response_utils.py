from typing import Any, Dict, Optional

def standard_response(
    data: Any = None,
    code: int = 200,
    message: str = "success",
    meta: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    标准响应格式
    
    Args:
        data: 响应数据
        code: 状态码
        message: 响应消息
        meta: 元数据
        
    Returns:
        标准格式的响应字典
    """
    response = {
        "code": code,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
        
    if meta is not None:
        response["meta"] = meta
        
    return response



def success_response(
    data: Any = None,
    message: str = "操作成功",
    status_code: int = 200
) -> Dict[str, Any]:
    """
    成功响应格式

    Args:
        data: 响应数据
        message: 成功消息
        status_code: HTTP状态码

    Returns:
        标准格式的成功响应字典
    """
    return {
        "success": True,
        "message": message,
        "data": data,
        "status_code": status_code
    }

def error_response(
    message: str = "操作失败",
    status_code: int = 500,
    detail: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    错误响应格式（重新定义以匹配新格式）

    Args:
        message: 错误消息
        status_code: HTTP状态码
        detail: 错误详情

    Returns:
        标准格式的错误响应字典
    """
    response = {
        "success": False,
        "message": message,
        "data": None,
        "status_code": status_code
    }

    if detail is not None:
        response["detail"] = detail

    return response

def paginated_response(
    data: Any,
    total: int,
    page: int,
    page_size: int,
    code: int = 200,
    message: str = "success"
) -> Dict[str, Any]:
    """
    分页响应格式
    
    Args:
        data: 分页数据
        total: 总记录数
        page: 当前页码
        page_size: 每页大小
        code: 状态码
        message: 响应消息
        
    Returns:
        标准格式的分页响应字典
    """
    return standard_response(
        data=data,
        code=code,
        message=message,
        meta={
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size
            }
        }
    ) 