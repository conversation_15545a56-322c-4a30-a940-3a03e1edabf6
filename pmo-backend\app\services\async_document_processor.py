#!/usr/bin/env python3
"""
异步文档处理器 - 提升用户体验
"""

import asyncio
import uuid
import time
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ProcessStatus(Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class FileProcessTask:
    """文件处理任务"""
    task_id: str
    filename: str
    file_size: int
    status: ProcessStatus
    progress: float  # 0-100
    result: Optional[str] = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

class AsyncDocumentProcessor:
    """异步文档处理器"""
    
    def __init__(self):
        self.tasks: Dict[str, FileProcessTask] = {}
        self.processing_queue = asyncio.Queue()
        self.max_concurrent = 3  # 最大并发处理数
        self.workers_started = False
    
    async def start_workers(self):
        """启动工作线程"""
        if self.workers_started:
            return
        
        logger.info(f"启动{self.max_concurrent}个文档处理工作线程")
        
        for i in range(self.max_concurrent):
            asyncio.create_task(self._worker(f"worker-{i+1}"))
        
        self.workers_started = True
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"文档处理工作线程 {worker_name} 启动")
        
        while True:
            try:
                # 从队列获取任务
                task_id = await self.processing_queue.get()
                
                if task_id not in self.tasks:
                    continue
                
                task = self.tasks[task_id]
                logger.info(f"{worker_name} 开始处理文件: {task.filename}")
                
                # 更新任务状态
                task.status = ProcessStatus.PROCESSING
                task.start_time = time.time()
                task.progress = 10
                
                # 模拟处理过程（实际处理逻辑）
                await self._process_file_async(task)
                
                # 标记任务完成
                self.processing_queue.task_done()
                
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 处理异常: {str(e)}")
                if task_id in self.tasks:
                    self.tasks[task_id].status = ProcessStatus.FAILED
                    self.tasks[task_id].error = str(e)
    
    async def _process_file_async(self, task: FileProcessTask):
        """异步处理单个文件"""
        try:
            from .document_parser import DocumentParser
            
            # 创建文档解析器
            parser = DocumentParser()
            
            # 模拟进度更新
            task.progress = 20
            await asyncio.sleep(0.1)  # 让出控制权
            
            # 这里需要实际的文件内容和解析逻辑
            # 由于是异步处理，需要从存储中读取文件内容
            
            task.progress = 50
            await asyncio.sleep(0.1)
            
            # 实际解析逻辑会在这里
            # result = await parser.parse_document_async(file_content, task.filename)
            
            task.progress = 80
            await asyncio.sleep(0.1)
            
            # 模拟解析完成
            task.result = f"文件 {task.filename} 解析完成"
            task.progress = 100
            task.status = ProcessStatus.COMPLETED
            task.end_time = time.time()
            
            logger.info(f"文件 {task.filename} 处理完成")
            
        except Exception as e:
            task.status = ProcessStatus.FAILED
            task.error = str(e)
            task.end_time = time.time()
            logger.error(f"文件 {task.filename} 处理失败: {str(e)}")
    
    async def submit_file(self, filename: str, file_size: int) -> str:
        """提交文件处理任务"""
        task_id = str(uuid.uuid4())
        
        task = FileProcessTask(
            task_id=task_id,
            filename=filename,
            file_size=file_size,
            status=ProcessStatus.PENDING,
            progress=0
        )
        
        self.tasks[task_id] = task
        
        # 启动工作线程（如果还没启动）
        await self.start_workers()
        
        # 添加到处理队列
        await self.processing_queue.put(task_id)
        
        logger.info(f"文件 {filename} 已提交处理，任务ID: {task_id}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task_id,
            "filename": task.filename,
            "status": task.status.value,
            "progress": task.progress,
            "result": task.result,
            "error": task.error,
            "processing_time": (
                (task.end_time or time.time()) - task.start_time
                if task.start_time else None
            )
        }
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有任务状态"""
        return [self.get_task_status(task_id) for task_id in self.tasks.keys()]
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        tasks_to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [ProcessStatus.COMPLETED, ProcessStatus.FAILED] and
                task.end_time and 
                current_time - task.end_time > max_age_seconds):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 个过期任务")

# 全局异步处理器实例
async_processor = AsyncDocumentProcessor()

def get_async_processor() -> AsyncDocumentProcessor:
    """获取全局异步处理器实例"""
    return async_processor
