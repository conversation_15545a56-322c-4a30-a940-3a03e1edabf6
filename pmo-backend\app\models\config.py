#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置模型 - 参考禅道zt_config表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import pymysql
import json
from app.core.database import get_db_connection, close_db_connection

class Config:
    """系统配置模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.owner: str = "system"
        self.module: str = ""
        self.section: str = ""
        self.key: str = ""
        self.value: Optional[str] = None

    @classmethod
    def get_config(cls, owner: str = "system", module: str = "", section: str = "", key: str = "") -> Optional[str]:
        """获取配置值"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT `value` FROM zt_config 
                WHERE owner = %s AND module = %s AND section = %s AND `key` = %s
            """, (owner, module, section, key))
            
            row = cursor.fetchone()
            return row['value'] if row else None
            
        except Exception as e:
            print(f"获取配置失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def set_config(cls, owner: str = "system", module: str = "", section: str = "", key: str = "", value: str = "") -> bool:
        """设置配置值"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_config (owner, module, section, `key`, `value`) 
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)
            """, (owner, module, section, key, value))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"设置配置失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_configs_by_module(cls, owner: str = "system", module: str = "") -> List[Dict[str, Any]]:
        """获取模块配置"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_config 
                WHERE owner = %s AND module = %s
                ORDER BY section, `key`
            """, (owner, module))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取模块配置失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_configs(cls, owner: str = "system") -> List[Dict[str, Any]]:
        """获取所有配置"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_config 
                WHERE owner = %s
                ORDER BY module, section, `key`
            """, (owner,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取所有配置失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def delete_config(cls, owner: str = "system", module: str = "", section: str = "", key: str = "") -> bool:
        """删除配置"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM zt_config 
                WHERE owner = %s AND module = %s AND section = %s AND `key` = %s
            """, (owner, module, section, key))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"删除配置失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)


class Lang:
    """数据字典模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.lang: str = "zh-cn"
        self.module: str = ""
        self.section: str = ""
        self.key: str = ""
        self.value: Optional[str] = None
        self.system: str = "1"
        self.vision: str = "rnd"

    @classmethod
    def get_lang_data(cls, lang: str = "zh-cn", module: str = "", section: str = "") -> Dict[str, str]:
        """获取语言数据"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            where_conditions = ["lang = %s"]
            params = [lang]
            
            if module:
                where_conditions.append("module = %s")
                params.append(module)
            
            if section:
                where_conditions.append("section = %s")
                params.append(section)
            
            where_clause = " AND ".join(where_conditions)
            
            cursor.execute(f"""
                SELECT `key`, `value` FROM zt_lang 
                WHERE {where_clause}
                ORDER BY `key`
            """, params)
            
            rows = cursor.fetchall()
            return {row['key']: row['value'] for row in rows}
            
        except Exception as e:
            print(f"获取语言数据失败: {e}")
            return {}
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_lang_data(cls, lang: str = "zh-cn") -> List[Dict[str, Any]]:
        """获取所有语言数据"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_lang 
                WHERE lang = %s
                ORDER BY module, section, `key`
            """, (lang,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取所有语言数据失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def set_lang_data(cls, lang: str = "zh-cn", module: str = "", section: str = "", key: str = "", value: str = "", system: str = "0") -> bool:
        """设置语言数据"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_lang (lang, module, section, `key`, `value`, `system`) 
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)
            """, (lang, module, section, key, value, system))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"设置语言数据失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)


class SystemLog:
    """系统日志模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.objectType: str = ""
        self.objectID: int = 0
        self.action: str = ""
        self.actor: str = ""
        self.date: Optional[datetime] = None
        self.comment: Optional[str] = None
        self.extra: Optional[str] = None
        self.read: str = "0"
        self.vision: str = "rnd"
        self.deleted: str = "0"

    @classmethod
    def add_log(cls, objectType: str, objectID: int, action: str, actor: str, comment: str = "", extra: str = "") -> bool:
        """添加系统日志"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_log (objectType, objectID, action, actor, comment, extra) 
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (objectType, objectID, action, actor, comment, extra))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"添加系统日志失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_logs(cls, objectType: str = "", actor: str = "", limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取系统日志"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            where_conditions = ["deleted = '0'"]
            params = []
            
            if objectType:
                where_conditions.append("objectType = %s")
                params.append(objectType)
            
            if actor:
                where_conditions.append("actor = %s")
                params.append(actor)
            
            where_clause = " AND ".join(where_conditions)
            params.extend([limit, offset])
            
            cursor.execute(f"""
                SELECT * FROM zt_log 
                WHERE {where_clause}
                ORDER BY date DESC
                LIMIT %s OFFSET %s
            """, params)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取系统日志失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)


class SystemCache:
    """系统缓存模型类"""
    
    @classmethod
    def get_cache(cls, key: str) -> Optional[str]:
        """获取缓存"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT `value` FROM zt_cache 
                WHERE `key` = %s AND (expiredTime IS NULL OR expiredTime > NOW())
            """, (key,))
            
            row = cursor.fetchone()
            return row['value'] if row else None
            
        except Exception as e:
            print(f"获取缓存失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def set_cache(cls, key: str, value: str, expired_time: Optional[datetime] = None) -> bool:
        """设置缓存"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_cache (`key`, `value`, expiredTime) 
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), expiredTime = VALUES(expiredTime), updatedTime = NOW()
            """, (key, value, expired_time))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"设置缓存失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def delete_cache(cls, key: str) -> bool:
        """删除缓存"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM zt_cache WHERE `key` = %s", (key,))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"删除缓存失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def clear_expired_cache(cls) -> bool:
        """清理过期缓存"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM zt_cache WHERE expiredTime IS NOT NULL AND expiredTime <= NOW()")
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"清理过期缓存失败: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                close_db_connection(conn)
