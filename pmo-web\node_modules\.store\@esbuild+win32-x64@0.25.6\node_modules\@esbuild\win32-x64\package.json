{"name": "@esbuild/win32-x64", "version": "0.25.6", "description": "The Windows 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["win32"], "cpu": ["x64"], "__npminstall_done": true, "_from": "@esbuild/win32-x64@0.25.6", "_resolved": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.25.6.tgz"}