import os
from typing import List
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 简化的配置类
class Settings:
    """应用配置"""
    # 应用设置
    PROJECT_NAME: str = "PMO系统"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False

    # 数据库设置
    DB_TYPE: str = os.getenv("DB_TYPE", "mysql")
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: int = int(os.getenv("DB_PORT", "3306"))
    DB_USER: str = os.getenv("DB_USER", "root")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_NAME: str = os.getenv("DB_NAME", "pmo")
    SQLITE_FILE: str = os.getenv("SQLITE_FILE", "pmo.db")

    # 安全设置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "")
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "10080"))

    # CORS设置
    CORS_ORIGINS: List[str] = ["*"]

    # 日志设置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 测试用户
    ADMIN_USERNAME: str = os.getenv("ADMIN_USERNAME", "admin")
    ADMIN_PASSWORD: str = os.getenv("ADMIN_PASSWORD", "admin123")

    # 其他设置
    USE_MOCK_DATA: bool = os.getenv("USE_MOCK_DATA", "false").lower() == "true"

settings = Settings()