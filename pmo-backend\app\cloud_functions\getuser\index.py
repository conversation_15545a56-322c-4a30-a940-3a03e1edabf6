                                                                #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
getuser 云函数
用于用户登录验证
"""

import json
import os
import pymysql
import hashlib
import re
from datetime import datetime

# 配置 PyMySQL 以禁用 cryptography
pymysql.install_as_MySQLdb()
# 禁用 ssl 验证，避免需要 cryptography
pymysql._auth.sha256_password = None 
pymysql._auth.caching_sha2_password = None
print("已禁用 PyMySQL 中的 cryptography 依赖")

def get_db_config():
    """获取数据库配置"""
    return {
        'host': os.environ['DB_HOST'],
        'port': int(os.environ['DB_PORT']),
        'user': os.environ['DB_USER'],
        'password': os.environ['DB_PASSWORD'],
        'database': os.environ['DB_NAME'],
        'charset': 'utf8mb4'
    }

def encrypt_password(password):
    """使用SHA-256加密密码"""
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def is_hash(password):
    """判断是否为SHA-256哈希值（64位十六进制字符）"""
    return len(password) == 64 and bool(re.match(r'^[0-9a-f]+$', password, re.I))

def get_user_service_lines(connection, user_id):
    """
    获取用户服务的条线列表
    :param connection: 数据库连接
    :param user_id: 用户ID
    :return: 条线列表
    """
    if not user_id:
        return []
        
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        # 查询用户在itbp表中的记录
        sql = """
        SELECT DISTINCT
            investment_entity
        FROM itbp
        WHERE UserID = %s
            AND investment_entity IS NOT NULL
            AND investment_entity <> ''
        ORDER BY investment_entity
        """
        cursor.execute(sql, (user_id,))
        rows = cursor.fetchall()
        
        # 提取投资主体列表
        entities = [row['investment_entity'] for row in rows]
        print(f"用户 {user_id} 共有 {len(entities)} 个服务条线")
        
        return entities
    except Exception as e:
        print(f"获取用户服务条线错误: {str(e)}")
        return []
    finally:
        cursor.close()

def get_user_itbp_info(connection, user_id):
    """
    获取用户在ITBP表中的详细信息
    :param connection: 数据库连接
    :param user_id: 用户ID
    :return: ITBP信息列表
    """
    if not user_id:
        return []
        
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        # 查询用户在itbp表中的详细信息
        sql = """
        SELECT 
            id,
            investment_entity,
            NAME,
            UserID,
            staff_category,
            start_time,
            end_time
        FROM itbp
        WHERE UserID = %s
        ORDER BY investment_entity
        """
        cursor.execute(sql, (user_id,))
        itbp_info = cursor.fetchall()
        
        # 处理日期格式
        for item in itbp_info:
            if item['start_time']:
                item['start_time'] = item['start_time'].strftime('%Y-%m-%d')
            if item['end_time']:
                item['end_time'] = item['end_time'].strftime('%Y-%m-%d')
        
        print(f"用户 {user_id} 共有 {len(itbp_info)} 条ITBP信息")
        
        return itbp_info
    except Exception as e:
        print(f"获取用户ITBP信息错误: {str(e)}")
        return []
    finally:
        cursor.close()

def main_handler(event, context):
    """云函数入口点"""
    print("getuser函数被调用")
    print(f"接收到的事件: {event}")
    
    # 解析请求参数，支持多种请求格式
    try:
        # 尝试从body中解析JSON
        if isinstance(event, dict) and 'body' in event:
            if isinstance(event['body'], str):
                try:
                    body = json.loads(event['body'])
                except:
                    print(f"无法解析body字符串: {event['body']}")
                    body = {}
            else:
                body = event['body'] or {}
        # 直接将event作为请求体
        else:
            body = event
        
        # 直接检查查询字符串中是否有action参数
        if isinstance(event, dict) and 'queryString' in event and event['queryString']:
            query_string = event['queryString']
            print(f"查询参数: {query_string}")
            # 明确检查是否存在修改密码相关参数
            if 'action' in query_string:
                body['action'] = query_string['action']
                print(f"从queryString获取到action: {body['action']}")
            if 'old_password' in query_string:
                body['old_password'] = query_string['old_password']
                print(f"从queryString获取到old_password参数")
            if 'new_password' in query_string:
                body['new_password'] = query_string['new_password']
                print(f"从queryString获取到new_password参数")
            if 'userid' in query_string or 'UserID' in query_string:
                userid_key = 'userid' if 'userid' in query_string else 'UserID'
                body['UserID'] = query_string[userid_key]
                print(f"从queryString获取到UserID: {body['UserID']}")
            # 处理用户名和密码
            if 'username' in query_string and not body.get('username'):
                body['username'] = query_string['username']
            if 'password' in query_string and not body.get('password'):
                body['password'] = query_string['password']
        
        # 尝试从queryStringParameters中获取参数
        if isinstance(event, dict) and 'queryStringParameters' in event:
            query_params = event['queryStringParameters'] or {}
            # 处理action参数
            if 'action' in query_params and 'action' not in body:
                body['action'] = query_params.get('action')
            if not body.get('username'):
                body['username'] = query_params.get('username')
            if not body.get('password'):
                body['password'] = query_params.get('password')
            # 处理密码修改相关参数
            if 'old_password' in query_params and 'old_password' not in body:
                body['old_password'] = query_params.get('old_password')
            if 'new_password' in query_params and 'new_password' not in body:
                body['new_password'] = query_params.get('new_password')
            # 处理大小写不敏感的UserID
            if 'UserID' not in body:
                # 尝试用不同的大小写格式查找
                for key in ['UserID', 'userid', 'userId', 'USERID']:
                    if key in query_params:
                        body['UserID'] = query_params.get(key)
                        break
                
        # 尝试从pathParameters中获取参数
        if isinstance(event, dict) and 'pathParameters' in event:
            path_params = event['pathParameters'] or {}
            if not body.get('username'):
                body['username'] = path_params.get('username')
            if not body.get('password'):
                body['password'] = path_params.get('password')
            # 处理大小写不敏感的UserID
            if 'UserID' not in body:
                # 尝试用不同的大小写格式查找
                for key in ['UserID', 'userid', 'userId', 'USERID']:
                    if key in path_params:
                        body['UserID'] = path_params.get(key)
                        break
        
        # 尝试从event直接查找UserID参数
        if 'UserID' not in body and isinstance(event, dict):
            # 尝试用不同的大小写格式直接从event查找
            for key in ['UserID', 'userid', 'userId', 'USERID']:
                if key in event:
                    body['UserID'] = event.get(key)
                    break
            
        print(f"解析后的参数: {body}")
    except Exception as e:
        print(f"解析参数错误: {str(e)}")
        body = {}
    
    # 处理修改密码的请求
    if body.get('action') == 'change_password':
        user_id = body.get('UserID')
        old_password = body.get('old_password')
        new_password = body.get('new_password')
        
        # 参数验证
        if not user_id or not old_password or not new_password:
            return {
                'code': 400,
                'message': '缺少必要参数'
            }
            
        try:
            # 获取数据库配置
            db_config = get_db_config()
            
            # 创建数据库连接
            connection = pymysql.connect(**db_config)
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 查询用户信息
            query = """
                SELECT UserID, username, password_hash
                FROM users
                WHERE UserID = %s AND is_disabled = 'N'
            """
            cursor.execute(query, (user_id,))
            user = cursor.fetchone()
            
            if not user:
                print(f"用户ID {user_id} 不存在或已被禁用")
                cursor.close()
                connection.close()
                return {
                    'code': 404,
                    'message': '用户不存在或已被禁用'
                }
            
            # 获取存储的密码
            stored_password = user['password_hash']
            password_match = False
            
            # 验证原密码
            # 判断存储的密码是否是哈希值
            if is_hash(stored_password):
                # 如果是哈希值，计算输入密码的哈希并比较
                old_password_hash = encrypt_password(old_password)
                password_match = (old_password_hash == stored_password)
                print(f"哈希密码比较: 输入={old_password_hash}, 存储={stored_password}, 匹配={password_match}")
            else:
                # 如果不是哈希值，直接比较明文密码
                password_match = (old_password == stored_password)
                print(f"明文密码比较: 匹配={password_match}")
            
            if not password_match:
                cursor.close()
                connection.close()
                return {
                    'code': 401,
                    'message': '原密码不正确'
                }
            
            # 修改密码
            new_password_hash = encrypt_password(new_password)
            
            # 更新密码
            cursor.execute(
                'UPDATE users SET password_hash = %s WHERE UserID = %s',
                (new_password_hash, user_id)
            )
            connection.commit()
            
            # 记录密码变更日志
            try:
                # 确保change_log表存在
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS change_log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100),
                        UserID VARCHAR(50) NOT NULL,
                        previous_value VARCHAR(100),
                        new_value VARCHAR(100),
                        change_time DATETIME NOT NULL
                    )
                """)
                
                # 记录变更时间（东八区时间）
                now = datetime.utcnow()
                china_time = now.replace(hour=(now.hour + 8) % 24)
                change_time = china_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 插入变更记录 (不存储实际密码，只标记为已修改)
                cursor.execute(
                    'INSERT INTO change_log (UserID, name, previous_value, new_value, change_time) VALUES (%s, %s, %s, %s, %s)',
                    (user_id, "密码", "已加密", "已修改", change_time)
                )
                connection.commit()
                
                print(f"已记录用户 {user_id} 的密码变更日志")
            except Exception as log_error:
                # 记录日志信息失败，但不影响密码修改
                print(f"记录密码变更日志失败: {str(log_error)}")
            
            cursor.close()
            connection.close()
            
            print("密码修改成功")
            return {
                'code': 200,
                'message': '密码修改成功'
            }
        except Exception as e:
            print(f"修改密码失败: {str(e)}")
            return {
                'code': 500,
                'message': '修改密码失败',
                'error': str(e)
            }
    
    # 处理根据UserID直接查询用户信息的情况
    if 'UserID' in body and not body.get('username') and not body.get('password'):
        try:
            # 获取数据库配置
            db_config = get_db_config()
            
            # 创建数据库连接
            connection = pymysql.connect(**db_config)
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 查询用户信息
            query = """
                SELECT UserID, username, name, role, department_name, company_name
                FROM users
                WHERE UserID = %s AND is_disabled = 'N'
            """
            cursor.execute(query, (body['UserID'],))
            user = cursor.fetchone()
            
            if not user:
                print(f"用户ID {body['UserID']} 不存在或已被禁用")
                cursor.close()
                connection.close()
                return {
                    'code': 404,
                    'message': '用户不存在或已被禁用'
                }
            
            # 获取用户服务条线
            service_lines = get_user_service_lines(connection, user['UserID'])
            user['service_lines'] = service_lines
            
            # 查询用户ITBP信息
            itbp_info = get_user_itbp_info(connection, user['UserID'])
            user['itbp_info'] = itbp_info
            
            # 从ITBP信息中提取条线信息
            if itbp_info and not service_lines:
                # 提取所有不重复的条线名称
                extracted_lines = []
                for item in itbp_info:
                    if isinstance(item, dict) and 'investment_entity' in item:
                        entity = item.get('investment_entity')
                        if entity and entity not in extracted_lines:
                            extracted_lines.append(entity)
                
                if extracted_lines:
                    print(f"从ITBP信息中提取到 {len(extracted_lines)} 个条线: {extracted_lines}")
                    user['service_lines'] = extracted_lines
            
            # 关闭数据库连接
            cursor.close()
            connection.close()
            
            return {
                'code': 200,
                'data': user
            }
        except Exception as e:
            print(f"查询用户信息失败: {str(e)}")
            return {
                'code': 500,
                'message': '数据库查询失败',
                'error': str(e)
            }
    
    username = body.get('username')
    password = body.get('password')
    
    # 参数验证
    if not username or not password:
        return {
            'code': 400,
            'message': '缺少用户名或密码'
        }
    
    try:
        # 获取数据库配置
        db_config = get_db_config()
        
        # 创建数据库连接
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 先查询用户信息
        query = """
            SELECT UserID, username, name, role, department_name, company_name, password_hash
            FROM users
            WHERE username = %s AND is_disabled = 'N'
        """
        cursor.execute(query, (username,))
        user = cursor.fetchone()
        
        if not user:
            print(f"用户 {username} 不存在或已被禁用")
            cursor.close()
            connection.close()
            return {
                'code': 404,
                'message': '用户名或密码错误，或账户已被禁用'
            }
        
        # 获取存储的密码
        stored_password = user['password_hash']
        password_match = False
        
        # 判断存储的密码是否是哈希值
        if is_hash(stored_password):
            # 如果是哈希值，计算输入密码的哈希并比较
            password_hash = encrypt_password(password)
            password_match = (password_hash == stored_password)
            print(f"哈希密码比较: 输入={password_hash}, 存储={stored_password}, 匹配={password_match}")
        else:
            # 如果不是哈希值，直接比较明文密码
            password_match = (password == stored_password)
            print(f"明文密码比较: 匹配={password_match}")
            
            # 如果密码匹配，立即将密码更新为哈希值存储
            if password_match:
                new_password_hash = encrypt_password(password)
                try:
                    cursor.execute(
                        'UPDATE users SET password_hash = %s WHERE UserID = %s',
                        (new_password_hash, user['UserID'])
                    )
                    connection.commit()
                    print(f"用户 {user['UserID']} 的密码已更新为哈希存储")
                except Exception as update_error:
                    print(f"更新密码为哈希值失败: {str(update_error)}")
                    # 继续处理，不影响登录
        
        if not password_match:
            cursor.close()
            connection.close()
            return {
                'code': 404,
                'message': '用户名或密码错误，或账户已被禁用'
            }
        
        # 移除password_hash字段，避免返回给前端
        del user['password_hash']
        
        # 获取用户服务条线
        service_lines = get_user_service_lines(connection, user['UserID'])
        user['service_lines'] = service_lines
        
        # 查询用户ITBP信息
        itbp_info = get_user_itbp_info(connection, user['UserID'])
        user['itbp_info'] = itbp_info
        
        # 从ITBP信息中提取条线信息
        if itbp_info and not service_lines:
            # 提取所有不重复的条线名称
            extracted_lines = []
            for item in itbp_info:
                if isinstance(item, dict) and 'investment_entity' in item:
                    entity = item.get('investment_entity')
                    if entity and entity not in extracted_lines:
                        extracted_lines.append(entity)
            
            if extracted_lines:
                print(f"从ITBP信息中提取到 {len(extracted_lines)} 个条线: {extracted_lines}")
                user['service_lines'] = extracted_lines
        
        # 记录用户登录信息
        try:
            # 确保user_logins表存在
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_logins (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    UserID VARCHAR(50) NOT NULL,
                    name VARCHAR(100),
                    login_time DATETIME NOT NULL
                )
            """)
            
            # 记录登录时间（东八区时间）
            now = datetime.utcnow()
            china_time = now.replace(hour=(now.hour + 8) % 24)
            login_time = china_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 插入登录记录
            cursor.execute(
                'INSERT INTO user_logins (UserID, name, login_time) VALUES (%s, %s, %s)',
                (user['UserID'], user['name'], login_time)
            )
            connection.commit()
            
            print(f"已记录用户 {user['UserID']} ({user['name']}) 的登录信息")
        except Exception as log_error:
            # 记录登录信息失败，但不影响用户登录
            print(f"记录登录信息失败: {str(log_error)}")
        
        # 关闭数据库连接
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'data': user
        }
    except Exception as e:
        print(f"getuser查询失败: {str(e)}")
        return {
            'code': 500,
            'message': '数据库查询失败',
            'error': str(e)
        } 