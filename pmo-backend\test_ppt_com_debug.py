#!/usr/bin/env python3
"""
专门测试PowerPoint COM解析的调试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser
import tempfile

def test_powerpoint_com_availability():
    """测试PowerPoint COM可用性"""
    print("🔧 测试PowerPoint COM可用性...")
    
    try:
        import win32com.client
        print("✅ win32com模块已安装")
        
        # 尝试创建PowerPoint应用程序
        try:
            ppt = win32com.client.Dispatch("PowerPoint.Application")
            ppt.Visible = 0
            print("✅ PowerPoint应用程序可用")
            ppt.Quit()
            return True
        except Exception as e:
            print(f"❌ PowerPoint应用程序不可用: {str(e)}")
            return False
            
    except ImportError:
        print("❌ win32com模块未安装")
        return False

def test_ppt_file_detection():
    """测试PPT文件格式检测"""
    print("\n🔧 测试PPT文件格式检测...")
    
    # 模拟.ppt文件头（OLE格式）
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    ppt_content = ole_header + b'\x00' * 1000
    
    print(f"文件开头: {ppt_content[:16].hex()}")
    
    if ppt_content.startswith(b'\xd0\xcf\x11\xe0'):
        print("✅ 检测为OLE格式(.ppt文件)")
        return True
    else:
        print("❌ 文件格式检测失败")
        return False

def test_ppt_com_parsing_direct():
    """直接测试PowerPoint COM解析"""
    print("\n🔧 直接测试PowerPoint COM解析...")
    
    parser = DocumentParser()
    
    # 创建一个简单的测试PPT文件
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    test_content = ole_header + b'\x00' * 2000 + "测试PowerPoint内容".encode('utf-16le') + b'\x00' * 1000
    
    try:
        result = parser._parse_ppt_with_com(test_content)
        if result:
            print(f"✅ PowerPoint COM解析成功")
            print(f"解析结果: {result[:200]}...")
            return True
        else:
            print("❌ PowerPoint COM解析返回空内容")
            return False
    except Exception as e:
        print(f"❌ PowerPoint COM解析异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ppt_parsing_full_flow():
    """测试完整的PPT解析流程"""
    print("\n🔧 测试完整的PPT解析流程...")
    
    parser = DocumentParser()
    
    # 创建测试PPT文件
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    test_content = ole_header + b'\x00' * 2000 + "测试PowerPoint内容".encode('utf-16le') + b'\x00' * 1000
    
    try:
        result = parser.parse_ppt_file(test_content)
        print(f"完整解析结果:")
        print(result)
        
        if "PowerPoint文档内容" in result:
            print("✅ 完整解析流程成功")
            return True
        else:
            print("❌ 完整解析流程失败")
            return False
    except Exception as e:
        print(f"❌ 完整解析流程异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_real_ppt_file():
    """测试真实的PPT文件"""
    print("\n🔧 测试真实的PPT文件...")
    
    # 这里可以放一个真实的PPT文件路径进行测试
    test_file_path = "test.ppt"  # 如果有真实文件可以测试
    
    if os.path.exists(test_file_path):
        print(f"找到测试文件: {test_file_path}")
        
        with open(test_file_path, 'rb') as f:
            file_content = f.read()
        
        parser = DocumentParser()
        
        try:
            result = parser.parse_ppt_file(file_content)
            print(f"真实文件解析结果:")
            print(result[:500] + "..." if len(result) > 500 else result)
            
            if len(result) > 50 and "乱码" not in result:
                print("✅ 真实文件解析成功")
                return True
            else:
                print("❌ 真实文件解析失败或包含乱码")
                return False
        except Exception as e:
            print(f"❌ 真实文件解析异常: {str(e)}")
            return False
    else:
        print("⚠️  没有找到测试文件，跳过真实文件测试")
        return True

def test_powerpoint_com_step_by_step():
    """逐步测试PowerPoint COM解析"""
    print("\n🔧 逐步测试PowerPoint COM解析...")
    
    try:
        import win32com.client
        import tempfile
        
        # 创建测试文件
        ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
        test_content = ole_header + b'\x00' * 5000
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ppt') as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name
        
        print(f"创建临时文件: {temp_file_path}")
        
        try:
            # 步骤1: 创建PowerPoint应用程序
            print("步骤1: 创建PowerPoint应用程序...")
            ppt = win32com.client.Dispatch("PowerPoint.Application")
            ppt.Visible = 0
            print("✅ PowerPoint应用程序创建成功")
            
            # 步骤2: 尝试打开文件
            print("步骤2: 尝试打开PPT文件...")
            try:
                presentation = ppt.Presentations.Open(temp_file_path, ReadOnly=True, WithWindow=False)
                print("✅ PPT文件打开成功")
                
                # 步骤3: 获取幻灯片数量
                print(f"步骤3: 幻灯片数量: {presentation.Slides.Count}")
                
                # 步骤4: 尝试读取内容
                if presentation.Slides.Count > 0:
                    slide = presentation.Slides(1)
                    print(f"步骤4: 第一张幻灯片形状数量: {slide.Shapes.Count}")
                
                presentation.Close()
                print("✅ 逐步测试成功")
                
            except Exception as e:
                print(f"❌ 打开PPT文件失败: {str(e)}")
            
            ppt.Quit()
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ 逐步测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 PowerPoint COM解析调试测试")
    print("=" * 60)
    
    try:
        # 测试PowerPoint COM可用性
        com_available = test_powerpoint_com_availability()
        
        # 测试文件格式检测
        format_ok = test_ppt_file_detection()
        
        if com_available:
            # 逐步测试COM解析
            test_powerpoint_com_step_by_step()
            
            # 直接测试COM解析
            test_ppt_com_parsing_direct()
            
            # 测试完整流程
            test_ppt_parsing_full_flow()
            
            # 测试真实文件
            test_real_ppt_file()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
