#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取红黑榜数据云函数

功能：获取条线红黑榜数据，包括按进度推进的条线和逾期条线
接口：HTTP GET
权限：无需用户登录

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "progressList": [进度条线列表],
    "delayedList": [逾期条线列表],
    "redEntities": [红色标识的实体],
    "blackEntities": [黑色标识的实体],
    "redListCriteria": "无逾期任务，按开工金额从高到低排序",
    "blackListCriteria": "存在逾期任务，按逾期金额从小到大排序"
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime, date

# 添加自定义JSON编码器处理date类型
class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def is_project_delayed(project, current_date):
    """
    判断任务是否逾期
    :param project: 项目数据
    :param current_date: 当前日期
    :return: 是否逾期
    """
    # 如果任务没有设置当前进度，无法判断是否逾期
    if not project.get('current_progress'):
        return False

    # 定义任务阶段及其先后顺序（数据库中实际的阶段名称）
    stage_order = [
        '未启动', 
        '业务调研', 
        '解决方案', 
        '项目立项', 
        '任务采购',
        '项目实施', 
        '项目验收', 
        '项目结项',
        '已完成'
    ]
    
    # 获取当前阶段在顺序中的索引
    current_stage_index = stage_order.index(project['current_progress']) if project['current_progress'] in stage_order else -1
    
    # 如果当前阶段不在定义的顺序中，无法判断是否逾期
    if current_stage_index == -1:
        return False
    
    # 阶段时间字段映射（需要与数据库中的字段对应）
    stage_time_fields = {
        '未启动': 'business_research_time',  # 未启动阶段，检查业务调研时间
        '业务调研': 'business_research_time',
        '解决方案': 'solution_time',
        '项目立项': 'project_establishment_time',
        '任务采购': 'project_procurement_time',
        '项目实施': 'project_implementation_time',
        '项目验收': 'project_acceptance_time',
        '项目结项': None,  # 结项阶段不再有后续时间点
        '已完成': None   # 已完成阶段不再有后续时间点
    }
    
    # 获取当前阶段对应的计划完成时间字段
    time_field = stage_time_fields.get(project['current_progress'])
    
    # 如果是最终阶段或无对应时间字段，则不算逾期
    if not time_field:
        return False
    
    # 获取计划完成时间
    planned_date_str = project.get(time_field)
    if not planned_date_str:
        return False
        
    try:
        # 如果已经是日期对象，直接使用
        if hasattr(planned_date_str, 'year'):
            planned_date = planned_date_str
        else:
            # 处理包含时间部分的日期字符串
            if isinstance(planned_date_str, str) and ' 00:00:00' in planned_date_str:
                planned_date_str = planned_date_str.split(' ')[0]  # 只保留日期部分
            planned_date = datetime.strptime(planned_date_str, '%Y-%m-%d').date()
        # 如果当前日期超过了计划完成时间，则任务逾期
        return current_date.date() > planned_date
    except (ValueError, TypeError):
        return False

def enhance_project_with_delay_info(project, current_date):
    """
    添加任务逾期信息处理函数
    :param project: 项目数据
    :param current_date: 当前日期
    :return: 添加逾期信息后的项目数据
    """
    # 如果任务没有设置当前进度，无法判断是否逾期
    if not project.get('current_progress'):
        project['delayed'] = False
        project['stage_time'] = None
        project['delay_days'] = 0
        project['delay_reason'] = '未设置当前阶段'
        return project

    # 定义任务阶段及其先后顺序（数据库中实际的阶段名称）
    stage_order = [
        '未启动', 
        '业务调研', 
        '解决方案', 
        '项目立项', 
        '任务采购',
        '项目实施', 
        '项目验收', 
        '项目结项',
        '已完成'
    ]
    
    # 获取当前阶段在顺序中的索引
    current_stage_index = stage_order.index(project['current_progress']) if project['current_progress'] in stage_order else -1
    
    # 如果当前阶段不在定义的顺序中，无法判断是否滞后
    if current_stage_index == -1:
        project['delayed'] = False
        project['stage_time'] = None
        project['delay_days'] = 0
        project['delay_reason'] = '无法确定当前阶段'
        return project
    
    # 阶段时间字段映射（需要与数据库中的字段对应）
    stage_time_fields = {
        '未启动': 'business_research_time',  # 未启动阶段，检查业务调研时间
        '业务调研': 'business_research_time',
        '解决方案': 'solution_time',
        '项目立项': 'project_establishment_time',
        '任务采购': 'project_procurement_time',
        '项目实施': 'project_implementation_time',
        '项目验收': 'project_acceptance_time',
        '项目结项': None,  # 结项阶段不再有后续时间点
        '已完成': None   # 已完成阶段不再有后续时间点
    }
    
    # 获取当前阶段对应的计划完成时间字段
    time_field = stage_time_fields.get(project['current_progress'])
    
    # 如果是最终阶段或无对应时间字段，则不算逾期
    if not time_field:
        project['delayed'] = False
        project['stage_time'] = None
        project['delay_days'] = 0
        project['delay_reason'] = '任务已进入最终阶段'
        return project
    
    # 获取计划完成时间
    stage_time_str = project.get(time_field)
    
    # 如果没有计划时间，则不算逾期
    if not stage_time_str:
        project['delayed'] = False
        project['stage_time'] = None
        project['delay_days'] = 0
        project['delay_reason'] = '无计划完成时间'
        return project
    
    # 转换为日期对象并验证
    try:
        # 如果已经是日期对象，直接使用
        if hasattr(stage_time_str, 'year'):
            stage_date = stage_time_str
            # 将日期对象转换为字符串用于显示
            stage_time_str = stage_date.strftime('%Y-%m-%d')
        else:
            # 处理包含时间部分的日期字符串
            if isinstance(stage_time_str, str) and ' 00:00:00' in stage_time_str:
                stage_time_str = stage_time_str.split(' ')[0]  # 只保留日期部分
            stage_date = datetime.strptime(stage_time_str, '%Y-%m-%d').date()
            
        # 判断是否逾期
        is_delayed = current_date.date() > stage_date
        
        delay_days = 0
        delay_reason = '任务进度正常'
        
        if is_delayed:
            delta = current_date.date() - stage_date
            delay_days = delta.days
            
            stage_names = {
                'business_research_time': '业务调研',
                'solution_time': '解决方案',
                'project_establishment_time': '项目立项',
                'project_procurement_time': '任务采购',
                'project_implementation_time': '项目实施',
                'project_acceptance_time': '项目验收'
            }
            
            next_stage_name = stage_names.get(time_field, '')
            delay_reason = f'任务当前处于"{project["current_progress"]}"阶段，应在 {stage_time_str} 前完成{next_stage_name}，但目前仍未完成，已逾期 {delay_days} 天'
        
        project['delayed'] = is_delayed
        project['stage_time'] = stage_time_str
        project['delay_days'] = delay_days
        project['delay_reason'] = delay_reason
        
        return project
    except (ValueError, TypeError) as e:
        logger.error(f"处理日期时出错: {str(e)}, 值: {stage_time_str}, 类型: {type(stage_time_str)}")
        project['delayed'] = False
        project['stage_time'] = str(stage_time_str) if stage_time_str else None
        project['delay_days'] = 0
        project['delay_reason'] = '计划时间格式无效'
        return project

def get_itbp_team_members(connection):
    """
    获取itbp团队成员信息
    :param connection: 数据库连接
    :return: 按投资主体分组的团队成员字典
    """
    try:
        # 获取当前日期
        today = datetime.now()
        today_str = today.strftime('%Y-%m-%d')
        
        # 计算一年前的日期
        one_year_ago = datetime(today.year - 1, today.month, today.day)
        one_year_ago_str = one_year_ago.strftime('%Y-%m-%d')
        
        logger.info(f"查询ITBP团队成员，今天: {today_str}, 一年前: {one_year_ago_str}")
        
        with connection.cursor() as cursor:
            # 查询itbp表中的所有成员
            # 选择所有当前有效的成员（end_time为NULL或当前日期未到end_time）
            # 或者已到期但未超过一年的成员
            cursor.execute("""
                SELECT NAME, UserID, investment_entity, staff_category, start_time, end_time 
                FROM itbp 
                WHERE end_time IS NULL 
                   OR end_time >= %s 
                   OR (end_time < %s AND end_time >= %s)
                ORDER BY investment_entity, staff_category
            """, (today_str, today_str, one_year_ago_str))
            
            members = cursor.fetchall()
        
        # 为每个成员标记状态
        for member in members:
            if not member.get('end_time'):
                member['status'] = 'active'  # 在职
            else:
                # 确保end_time是字符串
                end_time = member['end_time']
                if hasattr(end_time, 'strftime'):  # 如果是日期对象
                    end_time_str = end_time.strftime('%Y-%m-%d')
                    member['end_time'] = end_time_str  # 转换为字符串
                    is_expired = end_time <= today.date()
                else:
                    try:
                        # 处理包含时间部分的日期字符串
                        if isinstance(end_time, str) and ' 00:00:00' in end_time:
                            end_time = end_time.split(' ')[0]  # 只保留日期部分
                        end_date = datetime.strptime(end_time, '%Y-%m-%d').date()
                        is_expired = end_date <= today.date()
                    except (ValueError, TypeError):
                        logger.warning(f"无效的结束日期格式: {end_time}, 类型: {type(end_time)}")
                        is_expired = False
                
                member['status'] = 'expired' if is_expired else 'active'
        
        logger.info(f"获取到 {len(members)} 个ITBP团队成员")
        
        # 按条线分组
        teams_by_entity = {}
        for member in members:
            entity = member.get('investment_entity', '')
            if entity not in teams_by_entity:
                teams_by_entity[entity] = []
            
            # 为负责人(staff_category=1)添加PM标识
            if member.get('staff_category') == 1:
                member['role'] = 'PM'
            
            teams_by_entity[entity].append(member)
        
        logger.info(f"共有 {len(teams_by_entity)} 个投资主体有团队成员数据")
        return teams_by_entity
    except Exception as e:
        logger.error(f"获取ITBP团队成员失败: {str(e)}")
        return {}  # 返回空对象

def get_labor_costs_by_entity():
    """
    获取按投资主体分组的当年人次合计和人工费数据
    :return: 投资主体名称为键，包含人次和人工费的字典为值
    """
    conn = None
    try:
        conn = get_db_connection()
        
        # 获取当前年份
        current_year = datetime.now().year
        year_str = str(current_year)
        
        with conn.cursor() as cursor:
            # 查询工时数据 - 不再引用不存在的investment_entity字段
            cursor.execute("""
                SELECT ph.project_code, ph.UserID, ph.working_hours, ph.month,
                       u.LaborCost
                FROM project_hours ph
                LEFT JOIN users u ON ph.UserID = u.UserID
                WHERE SUBSTRING(ph.month, 1, 4) = %s
            """, (year_str,))
            
            hours_data = cursor.fetchall()
            
            logger.info(f"获取到 {len(hours_data)} 条工时记录，年份：{year_str}")
            
            # 查询项目信息，获取投资主体
            cursor.execute("""
                SELECT project_code, investment_entity
                FROM Project_Account_Book
            """)
            projects_data = {p['project_code']: p['investment_entity'] for p in cursor.fetchall()}
            
            logger.info(f"获取到 {len(projects_data)} 条项目记录用于关联投资主体信息")
            
            # 按投资主体分组计算
            entity_labor_costs = {}
            
            for record in hours_data:
                # 从项目表中获取投资主体信息
                project_code = record.get('project_code')
                if not project_code or project_code not in projects_data:
                    logger.warning(f"工时记录未找到对应项目或项目编号为空，跳过: {record}")
                    continue
                
                # 通过项目编号获取投资主体
                entity = projects_data[project_code]
                if not entity:
                    logger.warning(f"项目 {project_code} 没有设置投资主体，跳过")
                    continue
                
                # 确保LaborCost是数值
                labor_cost_value = 0
                try:
                    if record.get('LaborCost') is not None:
                        labor_cost_value = float(record['LaborCost'])
                except (ValueError, TypeError) as e:
                    logger.warning(f"LaborCost解析错误: {e}, 原始值: {record.get('LaborCost')}")
                
                # 确保working_hours是数值
                working_hours_value = 0
                try:
                    if record.get('working_hours') is not None:
                        working_hours_value = float(record['working_hours'])
                except (ValueError, TypeError) as e:
                    logger.warning(f"working_hours解析错误: {e}, 原始值: {record.get('working_hours')}")
                
                # 计算该条记录的人工费: (年人工费 / 12) * 工时
                monthly_labor_cost = labor_cost_value / 12 if labor_cost_value > 0 else 0
                record_labor_cost = monthly_labor_cost * working_hours_value
                
                # 添加到对应投资主体的统计中
                if entity not in entity_labor_costs:
                    entity_labor_costs[entity] = {
                        'working_hours': 0,
                        'labor_cost': 0
                    }
                
                entity_labor_costs[entity]['working_hours'] += working_hours_value
                entity_labor_costs[entity]['labor_cost'] += record_labor_cost
            
            # 四舍五入人工费到整数
            for entity in entity_labor_costs:
                entity_labor_costs[entity]['working_hours'] = round(entity_labor_costs[entity]['working_hours'], 2)
                entity_labor_costs[entity]['labor_cost'] = round(entity_labor_costs[entity]['labor_cost'])
            
            logger.info(f"计算完成，共有 {len(entity_labor_costs)} 个投资主体的人工费数据")
            return entity_labor_costs
            
    except Exception as e:
        logger.error(f"获取人工费数据出错: {str(e)}")
        return {}
    finally:
        if conn:
            conn.close()

def calculate_red_black_board(projects, itbp_team_members):
    """
    计算红黑榜数据
    :param projects: 项目列表
    :param itbp_team_members: 团队成员字典
    :return: 红黑榜数据
    """
    # 按投资主体分组
    entities_map = {}
    
    for project in projects:
        entity = project.get('investment_entity', '未填列')
        if entity not in entities_map:
            entities_map[entity] = {
                'entity': entity,
                'projects': [],
                'delayedProjects': [],
                'implementationProjects': [],  # 开工金额项目
            }
        
        entities_map[entity]['projects'].append(project)
        
        if project.get('delayed'):
            entities_map[entity]['delayedProjects'].append(project)
        
        # 指定的五类项目来计算开工金额
        # '任务采购', '项目实施', '项目验收', '项目结项', '已完成'
        implementation_stages = ['任务采购', '项目实施', '项目验收', '项目结项', '已完成']
        
        # 检查是否为实施阶段
        if project.get('current_progress') in implementation_stages:
            # 加入实施阶段项目列表
            entities_map[entity]['implementationProjects'].append(project)
            
            # 输出调试信息
            status = "逾期" if project.get('delayed') else "未逾期"
            # 优先使用转换后的annual_investment_value字段
            try:
                amount = float(project.get('annual_investment_value', 0) or 0)
            except (ValueError, TypeError):
                amount = float(project.get('annual_investment_plan', 0) or 0)
            
            logger.info(f"实施阶段项目: {project.get('project_name')}, 编号: {project.get('project_code')}, "
                       f"阶段: {project.get('current_progress')}, 状态: {status}, 投资金额: {amount}")
    
    # 获取工时和人工费数据
    labor_costs_by_entity = get_labor_costs_by_entity()
    logger.info(f"获取到 {len(labor_costs_by_entity)} 个投资主体的人工费数据")
    
    # 计算每个投资主体的统计数据
    entities = []
    for item in entities_map.values():
        logger.info(f"{item['entity']} 实施阶段项目数: {len(item['implementationProjects'])}")
        
        # 计算总预算
        try:
            total_budget = sum(float(p.get('annual_investment_value', 0) or 0) if p.get('annual_investment_value') is not None 
                             else float(p.get('annual_investment_plan', 0) or 0) for p in item['projects'])
            total_budget = round(total_budget)
        except (ValueError, TypeError) as e:
            logger.error(f"计算总预算时出错: {str(e)}")
            total_budget = 0
            
        total_count = len(item['projects'])
        
        # 计算逾期预算
        try:
            delayed_budget = sum(float(p.get('annual_investment_value', 0) or 0) if p.get('annual_investment_value') is not None 
                               else float(p.get('annual_investment_plan', 0) or 0) for p in item['delayedProjects'])
            delayed_budget = round(delayed_budget)
        except (ValueError, TypeError) as e:
            logger.error(f"计算逾期预算时出错: {str(e)}")
            delayed_budget = 0
            
        delayed_count = len(item['delayedProjects'])
        
        # 计算实施预算
        implementation_budget = 0
        for p in item['implementationProjects']:
            try:
                # 优先使用转换后的annual_investment_value字段
                if p.get('annual_investment_value') is not None:
                    implementation_budget += float(p['annual_investment_value'] or 0)
                else:
                    value = float(p.get('annual_investment_plan', 0) or 0)
                    implementation_budget += value
            except (ValueError, TypeError) as e:
                logger.error(f"{item['entity']} 项目 {p.get('project_name')} 金额解析错误: {p.get('annual_investment_plan')}, 错误: {str(e)}")
        
        implementation_budget = round(implementation_budget)
        
        # 如果有实施阶段项目但总金额为0，使用总投资金额的比例作为替代
        if len(item['implementationProjects']) > 0 and implementation_budget == 0:
            # 使用总投资金额的一部分作为开工金额估算
            ratio = len(item['implementationProjects']) / (total_count or 1)  # 防止除以零
            implementation_budget = round(total_budget * ratio)
            logger.info(f"{item['entity']}: 实施项目无投资金额，使用估算: {implementation_budget}万元 "
                       f"(总投资{total_budget}万元的{(ratio*100):.1f}%)")
        
        implementation_count = len(item['implementationProjects'])
        
        # 获取当年人次合计和人工费
        entity_name = item['entity']
        working_hours = 0
        labor_cost = 0
        
        if entity_name in labor_costs_by_entity:
            working_hours = labor_costs_by_entity[entity_name]['working_hours']
            labor_cost = labor_costs_by_entity[entity_name]['labor_cost']
        
        logger.info(f"投资主体: {item['entity']}")
        logger.info(f"  总项目数: {total_count}, 总预算: {total_budget}")
        logger.info(f"  逾期项目数: {delayed_count}, 逾期预算: {delayed_budget}")
        logger.info(f"  实施项目数: {implementation_count}, 实施预算: {implementation_budget}")
        logger.info(f"  当年人次合计: {working_hours}, 人工费: {labor_cost}万元")
        
        # 计算开工率 - 使用实施金额除以总投资额
        completion_rate = 0
        if total_budget > 0:
            completion_rate = round((implementation_budget / total_budget) * 100, 1)
        
        # 为投资主体添加ITBP团队成员信息
        team_members = itbp_team_members.get(item['entity'], [])
        
        # 格式化团队成员信息，标记PM和普通成员
        for member in team_members:
            if member.get('staff_category') == 1:
                member['role'] = 'PM'
        
        entities.append({
            'entity': item['entity'],
            'totalBudget': total_budget,
            'totalCount': total_count,
            'delayedBudget': delayed_budget,
            'delayedCount': delayed_count,
            'implementationBudget': implementation_budget,
            'implementationCount': implementation_count,
            'completionRate': completion_rate,  # 添加开工率
            'workingHours': working_hours,  # 添加当年人次合计
            'laborCost': labor_cost,       # 添加人工费
            'projectIds': [p.get('project_code') for p in item['projects']],
            'delayedProjectIds': [p.get('project_code') for p in item['delayedProjects']],
            'implementationProjectIds': [p.get('project_code') for p in item['implementationProjects']],
            'teamMembers': team_members
        })
    
    # 过滤掉没有项目的投资主体
    entities = [entity for entity in entities if entity['totalCount'] > 0]
    
    # 分为两块
    # 1. 没有出现逾期的投资主体（按进度推进）：按照项目开工金额从大到小排序
    no_delay_list = sorted(
        [e for e in entities if e['delayedCount'] == 0],
        key=lambda x: x['implementationBudget'],
        reverse=True
    )
    
    logger.info(f"没有逾期的投资主体共有 {len(no_delay_list)} 个")
    
    # 按进度推进榜红色标识：前三名没有逾期且开工金额从大到小排序的投资主体
    red_list = no_delay_list[:min(3, len(no_delay_list))]
    red_entities = [e['entity'] for e in red_list]
    
    if no_delay_list:
        logger.info(f"按进度推进榜红色标识: {', '.join(red_entities)}")
    
    # 2. 出现逾期的投资主体（逾期推进）：按照逾期金额从大到小排序
    delayed_list = sorted(
        [e for e in entities if e['delayedCount'] > 0],
        key=lambda x: x['delayedBudget'],
        reverse=False  # 从小到大排序，逾期金额越大排位越后
    )
    
    logger.info(f"有逾期的投资主体共有 {len(delayed_list)} 个")
    
    # 逾期推进榜黑色标识：最后三名出现逾期且逾期金额从小到大排序的投资主体
    black_list = delayed_list[max(0, len(delayed_list)-3):]
    black_entities = [e['entity'] for e in black_list]
    
    if delayed_list:
        logger.info(f"逾期推进榜黑色标识: {', '.join(black_entities)}")
    
    # 计算总体统计数据
    total_budget = sum(entity['totalBudget'] for entity in entities)
    total_count = sum(entity['totalCount'] for entity in entities)
    implementation_budget = sum(entity['implementationBudget'] for entity in entities)
    implementation_count = sum(entity['implementationCount'] for entity in entities)
    delayed_budget = sum(entity['delayedBudget'] for entity in entities)
    delayed_count = sum(entity['delayedCount'] for entity in entities)
    
    # 计算总体开工率
    overall_completion_rate = 0
    if total_budget > 0:
        overall_completion_rate = round((implementation_budget / total_budget) * 100, 1)
    
    # 设置进度榜的标准描述
    red_list_criteria = "无逾期任务，按开工金额从高到低排序"
    black_list_criteria = "存在逾期任务，按逾期金额从小到大排序"
    
    # 计算总体人工费数据并添加到stats中
    total_working_hours = sum(entity.get('workingHours', 0) for entity in no_delay_list + delayed_list)
    total_labor_cost = sum(entity.get('laborCost', 0) for entity in no_delay_list + delayed_list)
    
    # 返回按进度推进和逾期推进两个列表，以及总体统计数据
    return {
        'progressList': no_delay_list,  # 按进度推进列表（无逾期）
        'delayedList': delayed_list,    # 逾期推进列表（有逾期）
        'redEntities': red_entities,    # 红色标识的实体
        'blackEntities': black_entities,  # 黑色标识的实体
        'redListCriteria': red_list_criteria,  # 描述
        'blackListCriteria': black_list_criteria,  # 描述
        'stats': {  # 添加统计数据，方便前端直接使用
            'totalBudget': total_budget,
            'totalCount': total_count,
            'implementationBudget': implementation_budget,
            'implementationCount': implementation_count,
            'delayedBudget': delayed_budget,
            'delayedCount': delayed_count,
            'completionRate': overall_completion_rate,
            'delayedEntitiesCount': len(delayed_list),
            'totalWorkingHours': total_working_hours,
            'totalLaborCost': total_labor_cost
        }
    }

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    current_date = datetime.now()
    
    try:
        # 检查筛选参数
        software_only = False
        category_filter = None
        if isinstance(event, dict) and 'queryString' in event and event['queryString']:
            query_params = event['queryString']
            if isinstance(query_params, dict):
                if 'software_only' in query_params:
                    software_only = True
                    logger.info("启用软件筛选模式，只返回非硬件项目")
                if 'category' in query_params:
                    category_filter = query_params['category']
                    logger.info(f"启用规划类别筛选: {category_filter}")
        
        # 创建数据库连接
        conn = get_db_connection()
        
        try:
            with conn.cursor() as cursor:
                # 构建查询条件
                where_conditions = []
                query_params = []

                if category_filter:
                    where_conditions.append("category_level2 = %s")
                    query_params.append(category_filter)

                # 构建完整的SQL查询
                base_query = """
                    SELECT *, CAST(annual_investment_plan AS DECIMAL(10,2)) AS annual_investment_value
                    FROM Project_Account_Book
                """

                if where_conditions:
                    base_query += " WHERE " + " AND ".join(where_conditions)

                logger.info(f"执行查询: {base_query}")
                logger.info(f"查询参数: {query_params}")

                cursor.execute(base_query, query_params)
                projects = cursor.fetchall()
                logger.info(f"查询到的项目数量: {len(projects)}")

            # 如果启用了软件筛选，过滤出非硬件项目
            if software_only:
                projects = [p for p in projects if p.get('is_hardware') == '非硬件']
                logger.info(f"筛选后的非硬件项目数量: {len(projects)}")
            
            # 获取ITBP团队成员信息
            itbp_team_members = get_itbp_team_members(conn)
            
            # 为所有任务添加逾期信息
            projects_with_delay_info = [enhance_project_with_delay_info(p, current_date) for p in projects]
            
            # 计算进度榜数据
            board_data = calculate_red_black_board(projects_with_delay_info, itbp_team_members)
            
            # 计算总体人工费数据并添加到stats中
            total_working_hours = sum(entity.get('workingHours', 0) for entity in board_data['progressList'] + board_data['delayedList'])
            total_labor_cost = sum(entity.get('laborCost', 0) for entity in board_data['progressList'] + board_data['delayedList'])
            
            # 添加到统计数据中
            board_data['stats']['totalWorkingHours'] = total_working_hours
            board_data['stats']['totalLaborCost'] = total_labor_cost
            
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 200,
                    'message': 'success',
                    'data': board_data
                }, ensure_ascii=False, cls=DateEncoder)
            }
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"进度榜数据查询失败: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'数据库查询失败: {str(e)}',
                'error': str(e)
            }, ensure_ascii=False, cls=DateEncoder)
        } 