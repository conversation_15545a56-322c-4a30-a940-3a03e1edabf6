from fastapi import APIRouter, Depends, HTTPException, Request, Query, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
import json
import time
import traceback
import logging
import os
from datetime import datetime, date
import decimal
import asyncio
import httpx
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import re # Added for regex processing
import base64
import io
from pathlib import Path

from app.core.security import get_current_user
from app.core.logger import get_logger
from app.core.config import settings
from app.services.document_parser import DocumentParser
from app.services.ai_chat import AIChatService

logger = get_logger(__name__)
router = APIRouter()

# 创建一个不需要认证的路由器用于文件上传
file_router = APIRouter()

# 数据库配置
DB_HOST = settings.DB_HOST
DB_PORT = settings.DB_PORT
DB_USER = settings.DB_USER
DB_PASSWORD = settings.DB_PASSWORD
DB_NAME = settings.DB_NAME

# API配置
API_URL = settings.LLM_API_URL
API_KEY = settings.LLM_API_KEY
API_MODEL = settings.LLM_MODEL

# 备用API地址
BACKUP_API_URLS = [
    settings.LLM_API_URL,  # 主API地址
    settings.LLM_API_URL_BACKUP if hasattr(settings, 'LLM_API_URL_BACKUP') else None,  # 备用API地址
]
# 过滤掉None值
BACKUP_API_URLS = [url for url in BACKUP_API_URLS if url]

# 当前使用的API地址索引
current_api_index = 0

# 获取当前API地址
def get_current_api_url():
    global current_api_index
    if not BACKUP_API_URLS:
        return API_URL  # 如果没有配置备用地址，使用默认地址
    return BACKUP_API_URLS[current_api_index % len(BACKUP_API_URLS)]

# 切换到下一个API地址
def switch_to_next_api():
    global current_api_index
    if len(BACKUP_API_URLS) > 1:
        current_api_index = (current_api_index + 1) % len(BACKUP_API_URLS)
        new_api_url = get_current_api_url()
        logger.warning(f"切换到备用API地址: {new_api_url}")
        return new_api_url
    return get_current_api_url()

# 自定义JSON编码器，处理特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif hasattr(obj, 'hex'):  # 处理二进制数据
            return obj.hex()
        return super(CustomJSONEncoder, self).default(obj)

# 自定义中文SQL提示模板
CHINESE_SQL_PROMPT = """你是一个SQL专家，需要根据用户的问题生成MySQL查询语句。

数据库信息:
{db_info}

系统中的投资主体/业务类型包括：
- 汽租
- 商租
- 金租
- 征信
- 资管
- 集团战略
- 集团财务
- 小贷
- 集团
- 不动产
- 担保
- 集团保全
- 集团风控
- 集团办公室
- 集团人力
- 集团协同
- 金服
- 外部主体
- 集团法规
- 集团审计
- 保理
- 财险

用户问题: {query}

查询处理指南:
1. 优先识别用户提到的投资主体（如上述列表中的项目），这是查询的核心关键信息
2. 如果用户提到了具体投资主体，请务必在查询中加入相关条件
3. 如果用户没有明确提及投资主体，但查询内容与特定投资主体相关，应该返回所有投资主体的相关数据
4. 其次识别项目名称、项目状态、时间范围等其他条件
5. 使用适当的JOIN连接相关表，确保数据完整性

SQL生成规则:
1. 只返回SQL查询语句，不要包含任何解释、思考过程或其他文本
2. 确保SQL语法正确，符合MySQL语法
3. 只使用表中存在的列
4. 使用适当的WHERE条件、JOIN和聚合函数
5. 限制结果数量，避免返回过多数据
6. 如果用户问题无法通过SQL回答(比如问候、闲聊等)，只返回NULL，不要返回任何其他内容
7. 不要使用<think>标签或其他XML/HTML标签

SQL查询(只返回SQL语句，不要有任何其他文本):"""

# 获取数据库连接
def get_db_connection():
    """创建数据库连接"""
    try:
        connection_string = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        engine = create_engine(connection_string)
        logger.info(f"成功连接到数据库: {DB_NAME}")
        return engine
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")

# 获取数据库表结构信息
def get_db_info():
    """获取数据库表结构信息"""
    try:
        engine = get_db_connection()
        with engine.connect() as connection:
            # 获取所有表
            result = connection.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = :db_name
            """), {"db_name": DB_NAME})
            tables = [row[0] for row in result]
            
            # 固定的投资主体列表
            fixed_investment_entities = [
                "汽租", "商租", "金租", "征信", "资管", "集团战略", "集团财务", 
                "小贷", "集团", "不动产", "担保", "集团保全", "集团风控", 
                "集团办公室", "集团人力", "集团协同", "金服", "外部主体", 
                "集团法规", "集团审计", "保理", "财险"
            ]
            
            # 获取投资主体信息
            investment_entities = []
            try:
                # 尝试获取投资主体表或相关字段
                for table in tables:
                    columns_result = connection.execute(text("""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_schema = :db_name AND table_name = :table_name
                    """), {"db_name": DB_NAME, "table_name": table})
                    
                    columns = [row[0] for row in columns_result]
                    
                    # 检查是否有投资主体相关的列
                    investment_columns = [col for col in columns if 'invest' in col.lower() or 'company' in col.lower() or 'client' in col.lower() or 'customer' in col.lower() or 'business_type' in col.lower() or 'business_unit' in col.lower()]
                    
                    if investment_columns:
                        for col in investment_columns:
                            try:
                                # 尝试获取投资主体的唯一值，添加条件过滤掉空值和非字符串值
                                entities_result = connection.execute(text(f"""
                                    SELECT DISTINCT {col} FROM {table} 
                                    WHERE {col} IS NOT NULL AND {col} != '' 
                                    AND {col} REGEXP '^[^0-9]+$'
                                    LIMIT 30
                                """))
                                entities = []
                                for row in entities_result:
                                    # 增加类型检查，确保只处理字符串类型的投资主体
                                    if row[0] and isinstance(row[0], str):
                                        entities.append(row[0])
                                    elif row[0]:
                                        # 记录非字符串类型的值
                                        logger.warning(f"投资主体值类型不是字符串: {type(row[0])}, 值: {row[0]}")
                                if entities:
                                    investment_entities.extend(entities)
                            except Exception as e:
                                logger.warning(f"获取表 {table} 列 {col} 的投资主体信息失败: {str(e)}")
                                logger.debug(traceback.format_exc())
            except Exception as e:
                logger.warning(f"获取投资主体信息失败: {str(e)}")
                logger.debug(traceback.format_exc())
            
            # 合并固定列表和数据库中发现的投资主体
            all_entities = set(fixed_investment_entities)
            
            # 确保所有添加的投资主体都是字符串类型
            for entity in investment_entities:
                if isinstance(entity, str):
                    all_entities.add(entity)
                else:
                    logger.warning(f"跳过非字符串类型的投资主体: {type(entity)}, 值: {entity}")
            
            investment_entities = sorted(list(all_entities))
            
            # 获取每个表的结构
            db_info = []
            
            # 添加投资主体信息到数据库信息开头
            db_info.append("投资主体/业务类型列表:")
            for entity in investment_entities:  # 显示所有投资主体
                db_info.append(f"  - {entity}")
            db_info.append("")
            
            # 添加表结构信息
            for table in tables:
                # 获取表注释
                result = connection.execute(text("""
                    SELECT table_comment
                    FROM information_schema.tables
                    WHERE table_schema = :db_name AND table_name = :table_name
                """), {"db_name": DB_NAME, "table_name": table})
                
                table_comment = next(iter(result))[0] if result.rowcount > 0 else "No comment"
                
                # 获取列信息
                result = connection.execute(text("""
                    SELECT column_name, data_type, column_comment, 
                           is_nullable, column_key
                    FROM information_schema.columns
                    WHERE table_schema = :db_name AND table_name = :table_name
                """), {"db_name": DB_NAME, "table_name": table})
                
                columns = []
                for row in result:
                    col_name = row[0]
                    data_type = row[1]
                    comment = row[2] if row[2] else "No comment"
                    is_nullable = row[3]
                    key_type = row[4]
                    
                    key_info = ""
                    if key_type == "PRI":
                        key_info = " (主键)"
                    elif key_type == "UNI":
                        key_info = " (唯一键)"
                    elif key_type == "MUL":
                        key_info = " (索引)"
                        
                    nullable_info = " [可空]" if is_nullable == "YES" else ""
                    
                    columns.append(f"{col_name} ({data_type}){key_info}{nullable_info}: {comment}")
                
                # 尝试获取表的行数估计
                try:
                    count_result = connection.execute(text(f"SELECT COUNT(*) FROM {table} LIMIT 1"))
                    row_count = next(iter(count_result))[0]
                    row_info = f" (约 {row_count} 行)"
                except:
                    row_info = ""
                
                db_info.append(f"表名: {table}{row_info} - {table_comment}")
                db_info.append("列:")
                for column in columns:
                    db_info.append(f"  {column}")
                
                # 尝试获取表之间的关系
                try:
                    fk_result = connection.execute(text("""
                        SELECT column_name, referenced_table_name, referenced_column_name
                        FROM information_schema.key_column_usage
                        WHERE table_schema = :db_name 
                          AND table_name = :table_name
                          AND referenced_table_name IS NOT NULL
                    """), {"db_name": DB_NAME, "table_name": table})
                    
                    foreign_keys = []
                    for row in fk_result:
                        foreign_keys.append(f"{row[0]} -> {row[1]}.{row[2]}")
                    
                    if foreign_keys:
                        db_info.append("外键关系:")
                        for fk in foreign_keys:
                            db_info.append(f"  {fk}")
                except:
                    pass
                
                db_info.append("")
            
            return "\n".join(db_info)
    except Exception as e:
        logger.error(f"获取数据库信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据库信息失败: {str(e)}")

# 执行SQL查询
def execute_sql(sql):
    """执行SQL查询并返回结果"""
    try:
        if not sql or sql.strip().lower() == "null":
            return "无法生成有效的SQL查询"
        
        # 清理SQL中可能的思考过程或注释
        sql = sql.strip()
        if "<think>" in sql.lower():
            try:
                sql = sql.split("</think>")[1].strip()
            except IndexError:
                sql = sql.lower().replace("<think>", "").replace("</think>", "").strip()
        
        # 安全检查
        if any(keyword in sql.lower() for keyword in ["drop", "delete", "truncate", "update", "insert", "alter", "create"]):
            return "出于安全原因，不允许执行修改数据库的操作"
        
        # 执行查询
        engine = get_db_connection()
        with engine.connect() as connection:
            result = connection.execute(text(sql))
            columns = result.keys()
            rows = [dict(zip(columns, row)) for row in result]
            
            # 限制返回的行数
            if len(rows) > 50:
                rows = rows[:50]
                rows.append({"注意": f"结果已截断，共有{len(rows)}行"})
                
            return rows
    except SQLAlchemyError as e:
        error_msg = f"SQL执行错误: {str(e)}"
        logger.error(f"{error_msg}, SQL: {sql}")
        return error_msg
    except Exception as e:
        error_msg = f"执行查询时发生错误: {str(e)}"
        logger.error(f"{error_msg}, SQL: {sql}")
        return error_msg

# 生成SQL查询
async def generate_sql(prompt, temperature=0.7, max_tokens=1000):
    """调用API生成SQL查询"""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 获取当前API地址
            api_url = get_current_api_url()
            logger.info(f"开始生成SQL查询，尝试次数: {retry_count + 1}/{max_retries}，使用API: {api_url}")
            
            request_body = {
                "model": API_MODEL,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            logger.info(f"API请求参数: 模型={API_MODEL}, 温度={temperature}, 最大tokens={max_tokens}")
            
            async with httpx.AsyncClient(timeout=480.0) as client:  # 调整超时时间到480秒(8分钟)
                logger.info(f"发送API请求到: {api_url}")
                start_time = time.time()
                response = await client.post(
                    api_url,
                    json=request_body,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {API_KEY}"
                    }
                )
                end_time = time.time()
                logger.info(f"API响应时间: {end_time - start_time:.2f}秒, 状态码: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"API错误: {response.status_code} - {response.text}")
                    
                    # 如果是服务器错误(5xx)，尝试切换API
                    if response.status_code >= 500 and len(BACKUP_API_URLS) > 1:
                        switch_to_next_api()
                        
                    if retry_count < max_retries - 1:
                        retry_count += 1
                        logger.info(f"准备重试，等待2秒...")
                        await asyncio.sleep(2)
                        continue
                    return None
                    
                response_data = response.json()
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    sql = response_data["choices"][0]["message"]["content"].strip()
                    logger.info(f"API返回原始内容: {sql[:100]}...")
                    
                    # 移除可能的代码块标记
                    if "```sql" in sql:
                        sql = sql.split("```sql")[1].split("```")[0].strip()
                    elif "```" in sql:
                        sql = sql.split("```")[1].split("```")[0].strip()
                    
                    # 移除<think>标签及其内容
                    if "<think>" in sql.lower():
                        logger.info("检测到<think>标签，正在处理...")
                        # 尝试提取<think>标签之后的内容
                        try:
                            sql = sql.split("</think>")[1].strip()
                        except IndexError:
                            # 如果分割失败，则移除整个<think>部分
                            sql = sql.lower().replace("<think>", "").replace("</think>", "").strip()
                    
                    # 处理NULL返回
                    if sql.strip().upper() == "NULL":
                        logger.info("模型返回NULL，表示无法生成有效的SQL查询")
                        return None
                        
                    # 确保SQL不为空
                    if not sql.strip():
                        logger.info("生成的SQL为空")
                        return None
                    
                    # 处理SQL中的投资主体条件，确保正确处理空格问题
                    sql = process_investment_entity_in_sql(sql)
                        
                    logger.info(f"成功生成SQL: {sql}")
                    return sql
                else:
                    logger.error(f"API返回格式错误: {response_data}")
                    if retry_count < max_retries - 1:
                        retry_count += 1
                        logger.info(f"准备重试，等待2秒...")
                        await asyncio.sleep(2)
                        continue
                    return None
        except httpx.ReadTimeout as e:
            logger.error(f"API请求读取超时: {str(e)}")
            # 超时时切换API
            if len(BACKUP_API_URLS) > 1:
                switch_to_next_api()
            if retry_count < max_retries - 1:
                retry_count += 1
                logger.info(f"发生超时，准备重试，等待2秒...")
                await asyncio.sleep(2)
                continue
            return None
        except httpx.ConnectTimeout as e:
            logger.error(f"API请求连接超时: {str(e)}")
            # 超时时切换API
            if len(BACKUP_API_URLS) > 1:
                switch_to_next_api()
            if retry_count < max_retries - 1:
                retry_count += 1
                logger.info(f"发生连接超时，准备重试，等待2秒...")
                await asyncio.sleep(2)
                continue
            return None
        except Exception as e:
            logger.error(f"生成SQL错误: {str(e)}\n{traceback.format_exc()}")
            if retry_count < max_retries - 1:
                retry_count += 1
                logger.info(f"发生异常，准备重试，等待2秒...")
                await asyncio.sleep(2)
                continue
            return None
    
    logger.error(f"生成SQL失败，已达到最大重试次数: {max_retries}")
    return None

# 处理SQL中的投资主体条件
def process_investment_entity_in_sql(sql):
    """处理SQL中的投资主体条件，确保正确处理空格问题"""
    try:
        # 检查是否包含投资主体条件
        investment_entity_patterns = [
            r"investment_entity\s*=\s*['\"]([^'\"]+)['\"]",
            r"investment_entity\s+LIKE\s+['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in investment_entity_patterns:
            matches = re.findall(pattern, sql, re.IGNORECASE)
            if matches:
                for entity in matches:
                    # 检查是否有前导或尾随空格
                    entity_stripped = entity.strip()
                    if entity != entity_stripped:
                        # 替换带空格的实体名称
                        logger.info(f"修正投资主体名称中的空格: '{entity}' -> '{entity_stripped}'")
                        sql = sql.replace(f"'{entity}'", f"'{entity_stripped}'")
                        sql = sql.replace(f"\"{entity}\"", f"\"{entity_stripped}\"")
                    
                    # 特殊处理"小贷"投资主体
                    if entity_stripped == "小贷":
                        # 使用LIKE进行模糊匹配
                        logger.info(f"特殊处理'小贷'投资主体，使用LIKE匹配")
                        sql = sql.replace(
                            f"investment_entity = '{entity_stripped}'", 
                            f"investment_entity LIKE '%小贷%'"
                        )
                        sql = sql.replace(
                            f"investment_entity = \"{entity_stripped}\"", 
                            f"investment_entity LIKE \"%小贷%\""
                        )
        
        return sql
    except Exception as e:
        logger.warning(f"处理SQL中的投资主体条件时出错: {str(e)}")
        return sql  # 如果出错，返回原始SQL

# 生成回答
async def stream_answer(prompt, temperature=0.7, max_tokens=4000):
    """流式生成回答"""
    max_retries = 2
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 获取当前API地址
            api_url = get_current_api_url()
            logger.info(f"开始流式生成回答，尝试次数: {retry_count + 1}/{max_retries}，使用API: {api_url}")
            
            request_body = {
                "model": API_MODEL,
                "messages": [{"role": "user", "content": prompt}],
                "stream": True,
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            logger.info(f"流式API请求参数: 模型={API_MODEL}, 温度={temperature}, 最大tokens={max_tokens}")
            
            async with httpx.AsyncClient(timeout=480.0) as client:  # 调整超时时间到480秒(8分钟)
                logger.info(f"发送流式API请求到: {api_url}")
                start_time = time.time()
                
                try:
                    async with client.stream(
                        "POST",
                        api_url,
                        json=request_body,
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {API_KEY}"
                        }
                    ) as response:
                        logger.info(f"收到流式响应，状态码: {response.status_code}")
                        
                        if response.status_code != 200:
                            error_msg = await response.aread()
                            logger.error(f"流式API错误: {response.status_code} - {error_msg}")
                            
                            # 如果是服务器错误(5xx)，尝试切换API
                            if response.status_code >= 500 and len(BACKUP_API_URLS) > 1:
                                switch_to_next_api()
                                
                            if retry_count < max_retries - 1:
                                retry_count += 1
                                logger.info(f"准备重试流式请求，等待2秒...")
                                await asyncio.sleep(2)
                                continue
                            yield f"data: {json.dumps({'error': f'API错误: {error_msg}'})}\n\n"
                            yield f"data: [DONE]\n\n"
                            return
                        
                        # 流式处理响应
                        buffer = ""
                        chunk_count = 0
                        last_log_time = time.time()
                        
                        async for chunk in response.aiter_bytes():
                            try:
                                current_time = time.time()
                                chunk_count += 1
                                
                                # 每10个块或每5秒记录一次日志
                                if chunk_count % 10 == 0 or (current_time - last_log_time) > 5:
                                    logger.info(f"已处理 {chunk_count} 个数据块，流式响应持续时间: {current_time - start_time:.2f}秒")
                                    last_log_time = current_time
                                
                                chunk_text = chunk.decode("utf-8")
                                buffer += chunk_text
                                
                                # 处理可能的多行数据
                                while "\n\n" in buffer:
                                    line, buffer = buffer.split("\n\n", 1)
                                    if line.startswith("data: "):
                                        data = line[6:]  # 去掉 "data: " 前缀
                                        if data == "[DONE]":
                                            logger.info(f"流式响应完成，总共处理 {chunk_count} 个数据块，总时间: {time.time() - start_time:.2f}秒")
                                            yield f"data: [DONE]\n\n"
                                            return
                                            
                                        try:
                                            json_data = json.loads(data)
                                            if "choices" in json_data and len(json_data["choices"]) > 0:
                                                delta = json_data["choices"][0].get("delta", {})
                                                if "content" in delta and delta["content"]:
                                                    content = delta["content"]
                                                    # 发送小块数据
                                                    yield f"data: {json.dumps({'content': content})}\n\n"
                                        except json.JSONDecodeError as e:
                                            logger.warning(f"JSON解析错误: {e}, 数据: {data[:100]}...")
                                            continue
                            except Exception as e:
                                logger.error(f"处理流数据块时出错: {str(e)}")
                                continue
                        
                        logger.info(f"流式响应处理完毕，但未收到[DONE]标记，总共处理 {chunk_count} 个数据块")
                        yield f"data: [DONE]\n\n"
                        return
                        
                except httpx.ReadTimeout as e:
                    logger.error(f"流式API请求读取超时: {str(e)}")
                    # 超时时切换API
                    if len(BACKUP_API_URLS) > 1:
                        switch_to_next_api()
                    if retry_count < max_retries - 1:
                        retry_count += 1
                        logger.info(f"准备重试流式请求，等待2秒...")
                        await asyncio.sleep(2)
                        continue
                    yield f"data: {json.dumps({'error': '请求超时，请稍后再试'})}\n\n"
                    yield f"data: [DONE]\n\n"
                    return
                    
                except httpx.ConnectTimeout as e:
                    logger.error(f"流式API请求连接超时: {str(e)}")
                    # 超时时切换API
                    if len(BACKUP_API_URLS) > 1:
                        switch_to_next_api()
                    if retry_count < max_retries - 1:
                        retry_count += 1
                        logger.info(f"准备重试流式请求，等待2秒...")
                        await asyncio.sleep(2)
                        continue
                    yield f"data: {json.dumps({'error': '连接超时，请稍后再试'})}\n\n"
                    yield f"data: [DONE]\n\n"
                    return
                    
        except Exception as e:
            error_msg = f"流式回答生成错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            if retry_count < max_retries - 1:
                retry_count += 1
                logger.info(f"发生异常，准备重试流式请求，等待2秒...")
                await asyncio.sleep(2)
                continue
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
            yield f"data: [DONE]\n\n"
            return
    
    logger.error(f"流式生成回答失败，已达到最大重试次数: {max_retries}")
    yield f"data: {json.dumps({'error': '请求失败，已达到最大重试次数'})}\n\n"
    yield f"data: [DONE]\n\n"

# 流式聊天API端点
@router.get("/chat/stream")
async def db_chat_stream(
    query: str = Query(..., description="用户查询"),
    token: str = Query(None, description="认证令牌"),
    temperature: float = Query(0.7, description="温度参数"),
    max_tokens: int = Query(4000, description="最大生成token数"),
    current_user: Dict = None
):
    """
    流式数据库知识问答API
    接收用户问题并以流式方式返回基于数据库知识的AI回复
    """
    try:
        # 如果是欢迎消息，返回固定内容
        if query.lower() in ["你好", "hello", "hi", "嗨", "welcome", "欢迎", "开始", "start"]:
            welcome_message = """欢迎使用PMO数据库助手！我可以帮您查询项目管理系统中的数据。
            
您可以这样提问：
1. 查询特定投资主体的项目：
   - "金租有哪些项目？"
   - "小贷有什么项目？"
   - "汽租的项目情况如何？"

2. 查询项目状态：
   - "商租有多少逾期项目？"
   - "金租项目的总投资是多少？"
   - "保理业务的项目进度如何？"

3. 查询项目明细：
   - "集团战略的项目负责人是谁？"
   - "金租的年度投资计划是多少？"
   - "小贷的项目类别有哪些？"

请尽量在问题中明确提及投资主体（如金租、商租、汽租、小贷等），这样我能更准确地回答您的问题。"""
            
            return StreamingResponse(
                welcome_stream(welcome_message),
                media_type="text/event-stream"
            )
        
        # 如果提供了token参数，则使用token进行认证
        if token and not current_user:
            from app.core.security import decode_access_token
            try:
                payload = decode_access_token(token)
                if payload:
                    # 简单验证，实际应用中可能需要更复杂的逻辑
                    current_user = {"id": payload.get("sub"), "username": payload.get("username")}
            except Exception as e:
                logger.error(f"Token验证失败: {str(e)}")
                
        # 如果没有认证信息，返回一个简单的未认证响应
        if not current_user and not token:
            return StreamingResponse(
                error_stream("未认证，请先登录"),
                media_type="text/event-stream"
            )
            
        logger.info(f"收到数据库聊天请求: {query[:50]}...")
        
        # 返回流式响应
        return StreamingResponse(
            db_stream_chat(query, temperature, max_tokens),
            media_type="text/event-stream"
        )
            
    except Exception as e:
        error_msg = f"Stream chat error: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return StreamingResponse(
            error_stream(str(e)),
            media_type="text/event-stream"
        )

async def welcome_stream(welcome_message):
    """生成欢迎消息流"""
    yield f"data: {json.dumps({'content': welcome_message})}\n\n"
    yield f"data: [DONE]\n\n"

async def db_stream_chat(query, temperature=0.7, max_tokens=4000):
    """
    处理数据库流式聊天
    """
    start_time = time.time()
    logger.info(f"开始处理数据库流式聊天，查询: {query[:50]}...")
    
    try:
        # 获取数据库表结构信息
        try:
            db_info = get_db_info()
            logger.info(f"获取到数据库表结构信息: {len(db_info)} 字符")
        except Exception as e:
            error_msg = f"获取数据库表结构失败: {str(e)}"
            logger.error(error_msg)
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
            yield f"data: [DONE]\n\n"
            return
        
        # 生成SQL查询
        sql_prompt = CHINESE_SQL_PROMPT.format(db_info=db_info, query=query)
        logger.info(f"生成SQL提示: {sql_prompt[:100]}...")
        
        # 调用API生成SQL
        try:
            logger.info("开始调用API生成SQL...")
            sql = await generate_sql(sql_prompt, temperature, max_tokens)
            logger.info(f"SQL生成结果: {'成功' if sql else '失败'}")
        except Exception as e:
            error_msg = f"生成SQL时发生异常: {str(e)}"
            logger.error(error_msg)
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
            yield f"data: [DONE]\n\n"
            return
        
        # 如果SQL为None或NULL，直接调用大模型回答
        if not sql:
            # 如果无法生成SQL，直接调用大模型回答
            logger.info("无法生成有效的SQL查询，直接调用大模型回答")
            yield f"data: {json.dumps({'content': '正在为您查询相关信息...'})}\n\n"
            general_prompt = f"""
            你是一个数据库助手，用户问了一个问题，但我们无法将其转换为SQL查询。
            
            用户问题: {query}
            
            请礼貌地回答用户，如果这是关于数据库的问题，告诉用户需要更具体的信息；
            如果是一般性问题，直接回答；如果是问候，友好地回应。
            """
            
            logger.info("开始调用流式回答...")
            try:
                async for content in stream_answer(general_prompt, temperature, max_tokens):
                    yield content
            except Exception as e:
                error_msg = f"流式回答时发生异常: {str(e)}"
                logger.error(error_msg)
                yield f"data: {json.dumps({'error': error_msg})}\n\n"
                yield f"data: [DONE]\n\n"
                
            return
            
        logger.info(f"生成的SQL: {sql}")
        yield f"data: {json.dumps({'content': '正在查询数据库...'})}\n\n"
        
        # 执行SQL查询
        try:
            logger.info("开始执行SQL查询...")
            results = execute_sql(sql)
            logger.info(f"SQL执行结果类型: {type(results)}, 是否为字符串: {isinstance(results, str)}")
            
            # 如果结果是字符串且包含"错误"，说明执行出错
            if isinstance(results, str) and "错误" in results:
                # 尝试重新生成更简单的SQL
                logger.info(f"SQL执行出错: {results}，尝试生成更简单的查询")
                yield f"data: {json.dumps({'content': '查询出错，尝试使用更简单的方式查询...'})}\n\n"
                
                # 构造更简单的提示
                simple_prompt = f"""
                用户问题: {query}
                
                之前的SQL查询失败了，请生成一个更简单的SQL查询，只查询最基本的信息。
                确保SQL语法正确，并且只使用表中确定存在的列。
                """
                
                # 重新生成SQL
                logger.info("尝试生成简化SQL...")
                simple_sql = await generate_sql(simple_prompt, temperature, max_tokens)
                if not simple_sql:
                    # 如果仍然无法生成有效SQL，直接回答
                    logger.info("无法生成简化SQL，直接回答用户")
                    general_prompt = f"""
                    你是一个数据库助手，用户问了问题："{query}"，但我们无法生成有效的SQL查询。
                    
                    请礼貌地回答用户，告诉他我们无法查询到相关数据，并尝试提供一些可能的原因。
                    """
                    
                    async for content in stream_answer(general_prompt, temperature, max_tokens):
                        yield content
                    
                    return
                
                # 执行简化后的SQL
                logger.info(f"尝试执行简化SQL: {simple_sql}")
                results = execute_sql(simple_sql)
                if isinstance(results, str) and "错误" in results:
                    # 如果简化SQL仍然失败，直接回答
                    error_msg = f"简化SQL执行出错: {results}"
                    logger.error(error_msg)
                    yield f"data: {json.dumps({'content': error_msg})}\n\n"
                    yield f"data: [DONE]\n\n"
                    return
            
            # 如果结果为空
            if not results or len(results) == 0:
                logger.info("SQL查询结果为空")
                yield f"data: {json.dumps({'content': '没有找到相关数据。'})}\n\n"
                yield f"data: [DONE]\n\n"
                return
                
            logger.info(f"SQL查询结果: {len(results)} 行")
            
            # 构造回答提示
            answer_prompt = f"""基于以下信息回答用户问题。
            
            用户问题: {query}
            SQL查询: {sql}
            SQL结果: {json.dumps(results, ensure_ascii=False, cls=CustomJSONEncoder)}
            
            如果SQL结果为空或无法回答用户问题，请礼貌地告知无法找到相关信息。
            如果SQL结果有数据，请基于这些数据详细回答用户问题，并以表格形式展示关键数据。
            """
            
            # 调用API生成回答
            logger.info("开始生成最终回答...")
            async for content in stream_answer(answer_prompt, temperature, max_tokens):
                yield content
                
        except Exception as e:
            error_msg = f"SQL执行或回答生成错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            yield f"data: {json.dumps({'content': f'处理查询时出错: {str(e)}'})}\n\n"
            yield f"data: [DONE]\n\n"
    
    except Exception as e:
        error_msg = f"数据库聊天错误: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        yield f"data: {json.dumps({'error': str(e)})}\n\n"
        yield f"data: [DONE]\n\n"
    
    finally:
        end_time = time.time()
        logger.info(f"数据库流式聊天处理完成，总耗时: {end_time - start_time:.2f}秒")

async def error_stream(error_message):
    """
    生成错误流
    """
    yield f"data: {json.dumps({'error': error_message}, cls=CustomJSONEncoder)}\n\n"
    yield f"data: [DONE]\n\n"

# 非流式聊天API端点
@router.post("/chat/completion")
async def db_chat_completion(
    query: str = Query(..., description="用户查询"),
    token: str = Query(None, description="认证令牌"),
    temperature: float = Query(0.7, description="温度参数"),
    max_tokens: int = Query(4000, description="最大生成token数"),
    current_user: Dict = None
):
    """
    非流式数据库知识问答API
    接收用户问题并返回基于数据库知识的AI回复
    """
    try:
        # 如果提供了token参数，则使用token进行认证
        if token and not current_user:
            from app.core.security import decode_access_token
            try:
                payload = decode_access_token(token)
                if payload:
                    # 简单验证，实际应用中可能需要更复杂的逻辑
                    current_user = {"id": payload.get("sub"), "username": payload.get("username")}
            except Exception as e:
                logger.error(f"Token验证失败: {str(e)}")
                
        # 如果没有认证信息，返回错误
        if not current_user and not token:
            raise HTTPException(status_code=401, detail="未认证，请先登录")
            
        logger.info(f"收到非流式数据库聊天请求: {query[:50]}...")
        
        # 获取数据库表结构信息
        db_info = get_db_info()
        
        # 生成SQL查询
        sql_prompt = CHINESE_SQL_PROMPT.format(db_info=db_info, query=query)
        sql = await generate_sql(sql_prompt, temperature, 1000)
        
        if not sql:
            return {
                "id": f"dbchat-{int(time.time())}",
                "created": int(time.time()),
                "content": "无法为您的问题生成有效的SQL查询，请尝试更明确的问题。",
                "sql": None,
                "results": None
            }
        
        # 执行SQL查询
        results = execute_sql(sql)
        if isinstance(results, str) and "错误" in results:
            return {
                "id": f"dbchat-{int(time.time())}",
                "created": int(time.time()),
                "content": f"SQL执行出错: {results}",
                "sql": sql,
                "results": None
            }
        
        # 构造回答提示
        answer_prompt = f"""基于以下信息回答用户问题。
        
        用户问题: {query}
        SQL查询: {sql}
        SQL结果: {json.dumps(results, ensure_ascii=False, cls=CustomJSONEncoder)}
        
        如果SQL结果为空或无法回答用户问题，请礼貌地告知无法找到相关信息。
        如果SQL结果有数据，请基于这些数据详细回答用户问题，并以表格形式展示关键数据。
        """
        
        # 调用API生成回答
        request_body = {
            "model": API_MODEL,
            "messages": [{"role": "user", "content": answer_prompt}],
            "stream": False,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        max_retries = 2
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 获取当前API地址
                api_url = get_current_api_url()
                logger.info(f"开始非流式生成回答，尝试次数: {retry_count + 1}/{max_retries}，使用API: {api_url}")
                
                async with httpx.AsyncClient(timeout=480.0) as client:  # 调整超时时间到480秒(8分钟)
                    logger.info(f"发送非流式API请求到: {api_url}")
                    start_time = time.time()
                    response = await client.post(
                        api_url,
                        json=request_body,
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {API_KEY}"
                        }
                    )
                    end_time = time.time()
                    logger.info(f"非流式API响应时间: {end_time - start_time:.2f}秒, 状态码: {response.status_code}")
                    
                    if response.status_code != 200:
                        logger.error(f"非流式API错误: {response.status_code} - {response.text}")
                        
                        # 如果是服务器错误(5xx)，尝试切换API
                        if response.status_code >= 500 and len(BACKUP_API_URLS) > 1:
                            switch_to_next_api()
                            
                        if retry_count < max_retries - 1:
                            retry_count += 1
                            logger.info(f"准备重试非流式请求，等待2秒...")
                            await asyncio.sleep(2)
                            continue
                        raise HTTPException(status_code=500, detail=f"API错误: {response.text}")
                        
                    response_data = response.json()
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        content = response_data["choices"][0]["message"]["content"]
                        # 使用自定义编码器处理返回结果
                        return json.loads(json.dumps({
                            "id": f"dbchat-{int(time.time())}",
                            "created": int(time.time()),
                            "content": content,
                            "sql": sql,
                            "results": results
                        }, cls=CustomJSONEncoder))
                    else:
                        logger.error(f"API返回格式错误: {response_data}")
                        if retry_count < max_retries - 1:
                            retry_count += 1
                            logger.info(f"准备重试非流式请求，等待2秒...")
                            await asyncio.sleep(2)
                            continue
                        raise HTTPException(status_code=500, detail="API返回格式错误")
                        
            except httpx.ReadTimeout as e:
                logger.error(f"非流式API请求读取超时: {str(e)}")
                # 超时时切换API
                if len(BACKUP_API_URLS) > 1:
                    switch_to_next_api()
                if retry_count < max_retries - 1:
                    retry_count += 1
                    logger.info(f"发生超时，准备重试非流式请求，等待2秒...")
                    await asyncio.sleep(2)
                    continue
                raise HTTPException(status_code=500, detail=f"请求超时: {str(e)}")
                
            except httpx.ConnectTimeout as e:
                logger.error(f"非流式API请求连接超时: {str(e)}")
                # 超时时切换API
                if len(BACKUP_API_URLS) > 1:
                    switch_to_next_api()
                if retry_count < max_retries - 1:
                    retry_count += 1
                    logger.info(f"发生连接超时，准备重试非流式请求，等待2秒...")
                    await asyncio.sleep(2)
                    continue
                raise HTTPException(status_code=500, detail=f"连接超时: {str(e)}")
                
            except Exception as e:
                error_msg = f"非流式回答生成错误: {str(e)}\n{traceback.format_exc()}"
                logger.error(error_msg)
                if retry_count < max_retries - 1:
                    retry_count += 1
                    logger.info(f"发生异常，准备重试非流式请求，等待2秒...")
                    await asyncio.sleep(2)
                    continue
                raise HTTPException(status_code=500, detail=str(e))
        
        logger.error(f"非流式生成回答失败，已达到最大重试次数: {max_retries}")
        raise HTTPException(status_code=500, detail="请求失败，已达到最大重试次数")
    
    except Exception as e:
        error_msg = f"数据库聊天错误: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=str(e))

# 状态检查API端点
@router.get("/status")
async def check_status(
    token: str = Query(None, description="认证令牌"),
    current_user: Dict = None
):
    """
    检查API状态
    """
    try:
        # 如果提供了token参数，则使用token进行认证
        if token and not current_user:
            from app.core.security import decode_access_token
            try:
                payload = decode_access_token(token)
                if payload:
                    # 简单验证，实际应用中可能需要更复杂的逻辑
                    current_user = {"id": payload.get("sub"), "username": payload.get("username")}
            except Exception as e:
                logger.error(f"Token验证失败: {str(e)}")
                
        # 如果没有认证信息，返回错误
        if not current_user and not token:
            raise HTTPException(status_code=401, detail="未认证，请先登录")
            
        # 测试数据库连接
        engine = get_db_connection()
        with engine.connect() as connection:
            # 获取所有表
            result = connection.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = :db_name
            """), {"db_name": DB_NAME})
            tables = [row[0] for row in result]
        
        return {
            "status": "ok",
            "timestamp": time.time(),
            "version": "1.0.0",
            "database": DB_NAME,
            "tables_count": len(tables),
            "tables": tables[:10]  # 只返回前10个表名
        }
    except Exception as e:
        logger.error(f"状态检查失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        }

# ==================== 初始化服务实例 ====================

# 初始化文档解析器
document_parser = DocumentParser(
    vision_api_url=get_current_api_url(),
    vision_api_key=API_KEY,
    vision_model=API_MODEL
)

# 初始化AI聊天服务
ai_chat_service = AIChatService(
    api_url=API_URL,
    api_key=API_KEY,
    model=API_MODEL,
    backup_urls=BACKUP_API_URLS
)

# ==================== 文件上传API ====================
    """轻量级Excel解析 - 只用openpyxl + xlrd，总共15MB"""
    try:
        from io import BytesIO

        # 检查文件是否为空
        if not file_content or len(file_content) == 0:
            return "文件内容为空"

        content_lines = []

        # 方法1: 检测文件格式并选择合适的解析器
        # xlsx文件通常以PK开头（ZIP格式）
        if file_content.startswith(b'PK') or file_content.startswith(b'\x50\x4b'):
            # 使用openpyxl解析.xlsx文件
            try:
                import openpyxl
                wb = openpyxl.load_workbook(BytesIO(file_content), data_only=True)
                ws = wb.active

                for row in ws.iter_rows(values_only=True):
                    if any(cell for cell in row):  # 跳过空行
                        # 处理制表符分隔的数据，适合你的项目规划格式
                        row_values = []
                        for cell in row:
                            if cell is None:
                                row_values.append('')
                            elif isinstance(cell, (int, float)):
                                # 处理数字，避免科学计数法
                                if isinstance(cell, float) and cell.is_integer():
                                    row_values.append(str(int(cell)))
                                else:
                                    row_values.append(str(cell))
                            else:
                                row_values.append(str(cell))

                        row_text = '\t'.join(row_values)
                        content_lines.append(row_text)

                if content_lines:
                    return '\n'.join(content_lines)

            except Exception as e:
                logger.warning(f"openpyxl解析失败: {str(e)}")

        # 方法2: 尝试xlrd解析.xls文件
        try:
            import xlrd
            workbook = xlrd.open_workbook(file_contents=file_content)
            sheet = workbook.sheet_by_index(0)

            for row_idx in range(sheet.nrows):
                row_values = []
                has_content = False

                for col_idx in range(sheet.ncols):
                    cell_value = sheet.cell_value(row_idx, col_idx)

                    if cell_value:
                        has_content = True
                        # 处理不同类型的单元格值
                        if isinstance(cell_value, float) and cell_value.is_integer():
                            row_values.append(str(int(cell_value)))
                        else:
                            row_values.append(str(cell_value))
                    else:
                        row_values.append('')

                if has_content:  # 只添加有内容的行
                    row_text = '\t'.join(row_values)
                    content_lines.append(row_text)

            if content_lines:
                return '\n'.join(content_lines)

        except Exception as e:
            logger.warning(f"xlrd解析失败: {str(e)}")

        # 方法3: 如果都失败，尝试作为CSV处理
        try:
            # 尝试UTF-8编码
            text_content = file_content.decode('utf-8')
            lines = text_content.strip().split('\n')

            for line in lines:
                if line.strip():
                    # 将CSV格式转换为制表符分隔
                    if ',' in line:
                        # CSV格式，转换为制表符
                        row_text = line.replace(',', '\t')
                    else:
                        # 已经是制表符或其他分隔符
                        row_text = line
                    content_lines.append(row_text)

            if content_lines:
                return '\n'.join(content_lines)

        except Exception as e:
            logger.warning(f"CSV解析失败: {str(e)}")

        # 如果所有方法都失败
        return f"Excel解析失败: 文件格式不支持或文件损坏。支持的格式: .xlsx, .xls"

    except ImportError as e:
        logger.error(f"Excel解析库未安装: {str(e)}")
        return f"Excel解析失败: 缺少必要库，请安装: pip install openpyxl xlrd"
    except Exception as e:
        logger.error(f"Excel解析失败: {str(e)}")
        return f"Excel解析失败: {str(e)}"

def parse_word_file(file_content: bytes) -> str:
    """解析Word文件内容"""
    try:
        from docx import Document
        from io import BytesIO

        doc = Document(BytesIO(file_content))
        content_lines = []

        # 提取段落文本
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content_lines.append(paragraph.text.strip())

        # 提取表格数据
        for table in doc.tables:
            for row in table.rows:
                row_text = '\t'.join(cell.text.strip() for cell in row.cells)
                if row_text.strip():
                    content_lines.append(row_text)

        return '\n'.join(content_lines)
    except ImportError as e:
        logger.error(f"python-docx模块未安装: {str(e)}")
        return f"Word解析失败: 缺少python-docx模块，请安装: pip install python-docx"
    except Exception as e:
        logger.error(f"Word解析失败: {str(e)}")
        return f"Word解析失败: {str(e)}"

def parse_pdf_file(file_content: bytes) -> str:
    """解析PDF文件内容"""
    try:
        import pdfplumber
        from io import BytesIO

        content_lines = []

        with pdfplumber.open(BytesIO(file_content)) as pdf:
            for page in pdf.pages:
                # 提取文本
                text = page.extract_text()
                if text:
                    content_lines.append(text)

                # 提取表格
                tables = page.extract_tables()
                for table in tables:
                    for row in table:
                        if row and any(cell for cell in row):
                            row_text = '\t'.join(str(cell) if cell else '' for cell in row)
                            content_lines.append(row_text)

        return '\n'.join(content_lines)
    except ImportError as e:
        logger.error(f"pdfplumber模块未安装: {str(e)}")
        return f"PDF解析失败: 缺少pdfplumber模块，请安装: pip install pdfplumber"
    except Exception as e:
        logger.error(f"PDF解析失败: {str(e)}")
        return f"PDF解析失败: {str(e)}"

def parse_text_file(file_content: bytes, filename: str) -> str:
    """解析文本文件内容"""
    try:
        # 检查文件是否为空
        if not file_content or len(file_content) == 0:
            return "文件内容为空"

        import chardet

        # 检测编码
        detected = chardet.detect(file_content)
        encoding = detected.get('encoding')

        # 如果检测不到编码，使用默认编码
        if not encoding:
            encoding = 'utf-8'

        # 解码文本
        text = file_content.decode(encoding)
        return text
    except ImportError as e:
        logger.error(f"chardet模块未安装: {str(e)}")
        # 如果没有chardet，尝试常见编码
        try:
            return file_content.decode('utf-8')
        except:
            try:
                return file_content.decode('gbk')
            except:
                return f"文本解析失败: 编码检测失败，请安装chardet模块"
    except Exception as e:
        logger.error(f"文本解析失败: {str(e)}")
        return f"文本解析失败: {str(e)}"

def parse_ppt_file(file_content: bytes) -> str:
    """解析PowerPoint文件内容 - python-pptx + 视觉大模型组合"""
    try:
        from pptx import Presentation
        from io import BytesIO
        import base64

        # 检查文件是否为空
        if not file_content or len(file_content) == 0:
            return "文件内容为空"

        prs = Presentation(BytesIO(file_content))
        content_lines = []
        image_tasks = []  # 存储需要视觉识别的图片

        for slide_num, slide in enumerate(prs.slides, 1):
            # 添加幻灯片标题
            content_lines.append(f"=== 幻灯片 {slide_num} ===")

            for shape in slide.shapes:
                # 1. 处理文本内容
                if hasattr(shape, "text") and shape.text.strip():
                    text = shape.text.strip()
                    content_lines.append(text)

                # 2. 处理表格
                if shape.has_table:
                    table = shape.table
                    for row in table.rows:
                        row_values = []
                        for cell in row.cells:
                            cell_text = cell.text.strip() if cell.text else ''
                            row_values.append(cell_text)

                        if any(val for val in row_values):  # 跳过空行
                            row_text = '\t'.join(row_values)
                            content_lines.append(row_text)

                # 3. 处理图片（关键改进：用视觉大模型识别）
                if hasattr(shape, 'image') and shape.image:
                    try:
                        # 提取图片数据
                        image_stream = shape.image.blob
                        image_base64 = base64.b64encode(image_stream).decode('utf-8')

                        # 构造图片数据URL
                        image_data_url = f"data:image/png;base64,{image_base64}"

                        # 标记需要视觉识别的图片
                        image_tasks.append({
                            'slide_num': slide_num,
                            'image_data': image_data_url,
                            'placeholder': f"[幻灯片{slide_num}_图片内容_待识别]"
                        })

                        # 先添加占位符
                        content_lines.append(f"[幻灯片{slide_num}_图片内容_待识别]")

                    except Exception as img_error:
                        logger.warning(f"图片提取失败: {str(img_error)}")
                        content_lines.append(f"[幻灯片{slide_num}_图片内容: 提取失败]")

            content_lines.append("")  # 幻灯片之间添加空行

        # 处理文本内容
        text_content = '\n'.join(content_lines)

        # 如果有图片需要识别，返回特殊标记
        if image_tasks:
            # 将图片任务信息编码到结果中
            import json
            image_info = json.dumps(image_tasks)
            return f"PPT_WITH_IMAGES:{image_info}|||{text_content}"

        return text_content

    except ImportError as e:
        logger.error(f"python-pptx模块未安装: {str(e)}")
        return f"PPT解析失败: 缺少python-pptx模块，请安装: pip install python-pptx"
    except Exception as e:
        logger.error(f"PPT解析失败: {str(e)}")
        return f"PPT解析失败: {str(e)}"

async def parse_image_with_vision_model(image_base64: str, prompt: str = None) -> str:
    """使用本地视觉大模型解析图片内容"""
    try:
        # 参考ocr_2的实现方式
        if not prompt:
            prompt = """请仔细分析这张图片，提取其中的所有文字内容。
            如果是表格数据，请按照制表符分隔的格式输出，保持原有的行列结构。
            如果是项目规划类的表格，请特别注意项目编号、管理体系、业务领域、具体内容等字段。
            请确保输出格式清晰，便于后续处理。"""

        # 调用内部视觉模型API（参考ocr_2的实现）
        api_url = get_current_api_url()

        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_base64
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]

        request_body = {
            "model": API_MODEL,
            "messages": messages,
            "temperature": 0.1,  # 低温度确保准确性
            "max_tokens": 4000
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                api_url,
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {API_KEY}"
                }
            )

            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                return content
            else:
                logger.error(f"视觉模型API错误: {response.status_code} - {response.text}")
                return f"图片识别失败: API错误 {response.status_code}"

    except Exception as e:
        logger.error(f"图片解析失败: {str(e)}")
        return f"图片解析失败: {str(e)}"

def parse_document(file_content: bytes, filename: str, content_type: str) -> str:
    """统一文档解析接口"""
    file_ext = Path(filename).suffix.lower()

    try:
        # 检查文件是否为空
        if not file_content or len(file_content) == 0:
            return f"文件内容为空: {filename}"

        if file_ext in ['.xlsx', '.xls']:
            return parse_excel_file(file_content)
        elif file_ext in ['.docx', '.doc']:
            return parse_word_file(file_content)
        elif file_ext in ['.pptx', '.ppt']:
            ppt_result = parse_ppt_file(file_content)
            # 检查是否包含需要视觉识别的图片
            if ppt_result.startswith("PPT_WITH_IMAGES:"):
                return ppt_result  # 返回特殊标记，在上层处理图片识别
            return ppt_result
        elif file_ext == '.pdf':
            return parse_pdf_file(file_content)
        elif file_ext in ['.txt', '.csv']:
            # 对于CSV文件，特殊处理转换为制表符格式
            if file_ext == '.csv':
                try:
                    text_content = file_content.decode('utf-8')
                    lines = text_content.strip().split('\n')
                    content_lines = []

                    for line in lines:
                        if line.strip():
                            # 将CSV格式转换为制表符分隔
                            row_text = line.replace(',', '\t')
                            content_lines.append(row_text)

                    return '\n'.join(content_lines)
                except:
                    pass

            return parse_text_file(file_content, filename)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片需要转换为base64后调用视觉模型
            image_base64 = f"data:{content_type};base64,{base64.b64encode(file_content).decode('utf-8')}"
            # 这里返回一个标记，实际处理在异步函数中
            return f"IMAGE_TO_PROCESS:{image_base64}"
        else:
            return f"不支持的文件格式: {file_ext}"
    except Exception as e:
        logger.error(f"文档解析失败: {str(e)}")
        return f"文档解析失败: {str(e)} (文件: {filename})"

async def generate_file_answer(query: str) -> str:
    """
    为文件内容生成AI回答
    """
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            api_url = get_current_api_url()

            messages = [
                {
                    "role": "user",
                    "content": query
                }
            ]

            request_body = {
                "model": API_MODEL,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 4000
            }

            # 增加超时时间并添加重试机制
            timeout = httpx.Timeout(connect=30.0, read=300.0, write=30.0, pool=30.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                logger.info(f"发送AI请求到: {api_url} (尝试 {retry_count + 1}/{max_retries})")

                response = await client.post(
                    api_url,
                    json=request_body,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {API_KEY}"
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    if content:
                        return content
                    else:
                        logger.warning("AI返回空内容，尝试重试")
                        retry_count += 1
                        continue
                else:
                    logger.error(f"AI API错误: {response.status_code} - {response.text}")
                    if response.status_code >= 500 and retry_count < max_retries - 1:
                        # 服务器错误，尝试切换API
                        switch_to_next_api()
                        retry_count += 1
                        await asyncio.sleep(2)
                        continue
                    return f"AI回答生成失败: API错误 {response.status_code}"

        except (httpx.TimeoutException, httpx.ConnectError, httpx.RemoteProtocolError) as e:
            logger.error(f"网络连接错误 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
            if retry_count < max_retries - 1:
                # 网络错误，尝试切换API
                switch_to_next_api()
                retry_count += 1
                await asyncio.sleep(2)
                continue
            return f"AI回答生成失败: 网络连接超时，请稍后重试"
        except Exception as e:
            logger.error(f"生成AI回答失败: {str(e)}")
            if retry_count < max_retries - 1:
                retry_count += 1
                await asyncio.sleep(1)
                continue
            return f"AI回答生成失败: {str(e)}"

    return "AI回答生成失败: 已达到最大重试次数"

# ==================== 文件上传API ====================

@file_router.post("/upload-files")
async def upload_files(
    files: List[UploadFile] = File(...)
):
    """
    上传多个文件并解析内容
    支持Excel、Word、PDF、图片等格式
    """
    try:
        parsed_files = []

        for file in files:
            # 读取文件内容
            file_content = await file.read()

            # 验证文件大小（限制为10MB）
            if len(file_content) > 10 * 1024 * 1024:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件大小超过10MB限制"
                })
                continue

            # 验证文件内容不为空
            if not file_content or len(file_content) == 0:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件内容为空"
                })
                continue

            # 解析文档内容
            parsed_content = parse_document(file_content, file.filename, file.content_type)

            # 如果是图片，需要异步处理
            if parsed_content.startswith("IMAGE_TO_PROCESS:"):
                image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                try:
                    parsed_content = await parse_image_with_vision_model(image_base64)
                except Exception as e:
                    parsed_content = f"图片解析失败: {str(e)}"

            # 如果是包含图片的PPT，需要异步处理图片识别
            elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                try:
                    import json
                    # 分离图片信息和文本内容
                    parts = parsed_content.replace("PPT_WITH_IMAGES:", "").split("|||", 1)
                    image_tasks = json.loads(parts[0])
                    text_content = parts[1] if len(parts) > 1 else ""

                    # 处理每个图片
                    for task in image_tasks:
                        try:
                            # 用视觉大模型识别图片
                            image_result = await parse_image_with_vision_model(
                                task['image_data'],
                                "请提取这张PPT图片中的所有文字内容，如果是表格请按制表符分隔格式输出，如果是项目规划相关内容请特别注意项目编号、管理体系、业务领域等字段。"
                            )

                            # 替换占位符
                            text_content = text_content.replace(
                                task['placeholder'],
                                f"[幻灯片{task['slide_num']}_图片内容]: {image_result}"
                            )

                        except Exception as img_error:
                            logger.error(f"PPT图片识别失败: {str(img_error)}")
                            text_content = text_content.replace(
                                task['placeholder'],
                                f"[幻灯片{task['slide_num']}_图片内容]: 识别失败 - {str(img_error)}"
                            )

                    parsed_content = text_content

                except Exception as e:
                    logger.error(f"PPT图片处理失败: {str(e)}")
                    parsed_content = f"PPT解析失败: 图片处理错误 - {str(e)}"

            parsed_files.append({
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(file_content),
                "content": parsed_content[:5000],  # 限制返回内容长度
                "full_content": parsed_content  # 完整内容
            })

        return {
            "code": 200,
            "message": "文件解析成功",
            "data": {
                "files_count": len(parsed_files),
                "files": parsed_files
            }
        }

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

@file_router.post("/chat-with-files")
async def chat_with_files(
    query: str = Form(...),
    files: List[UploadFile] = File(...)
):
    """
    带文件的聊天API
    用户可以上传文件并提问
    """
    try:
        # 解析所有文件
        file_contents = []

        for file in files:
            file_content = await file.read()

            # 验证文件大小
            if len(file_content) > 10 * 1024 * 1024:
                continue

            # 解析文档内容
            parsed_content = parse_document(file_content, file.filename, file.content_type)

            # 如果是图片，需要异步处理
            if parsed_content.startswith("IMAGE_TO_PROCESS:"):
                image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                try:
                    parsed_content = await parse_image_with_vision_model(image_base64)
                except Exception as e:
                    parsed_content = f"图片解析失败: {str(e)}"

            # 如果是包含图片的PPT，需要异步处理图片识别
            elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                try:
                    import json
                    # 分离图片信息和文本内容
                    parts = parsed_content.replace("PPT_WITH_IMAGES:", "").split("|||", 1)
                    image_tasks = json.loads(parts[0])
                    text_content = parts[1] if len(parts) > 1 else ""

                    # 处理每个图片
                    for task in image_tasks:
                        try:
                            # 用视觉大模型识别图片
                            image_result = await parse_image_with_vision_model(
                                task['image_data'],
                                "请提取这张PPT图片中的所有文字内容，如果是表格请按制表符分隔格式输出，如果是项目规划相关内容请特别注意项目编号、管理体系、业务领域等字段。"
                            )

                            # 替换占位符
                            text_content = text_content.replace(
                                task['placeholder'],
                                f"[幻灯片{task['slide_num']}_图片内容]: {image_result}"
                            )

                        except Exception as img_error:
                            logger.error(f"PPT图片识别失败: {str(img_error)}")
                            text_content = text_content.replace(
                                task['placeholder'],
                                f"[幻灯片{task['slide_num']}_图片内容]: 识别失败 - {str(img_error)}"
                            )

                    parsed_content = text_content

                except Exception as e:
                    logger.error(f"PPT图片处理失败: {str(e)}")
                    parsed_content = f"PPT解析失败: 图片处理错误 - {str(e)}"

            file_contents.append({
                "filename": file.filename,
                "content": parsed_content
            })

        # 构建包含文件内容的提示，并控制长度
        files_context_parts = []
        total_length = 0
        max_context_length = 30000  # 限制上下文长度，为AI回答预留空间

        for fc in file_contents:
            file_part = f"文件名: {fc['filename']}\n内容:\n{fc['content']}"

            # 检查是否会超过长度限制
            if total_length + len(file_part) > max_context_length:
                # 截断内容
                remaining_length = max_context_length - total_length - 100  # 预留一些空间
                if remaining_length > 500:  # 如果剩余空间足够，添加截断的内容
                    truncated_content = fc['content'][:remaining_length]
                    file_part = f"文件名: {fc['filename']}\n内容:\n{truncated_content}...[内容已截断]"
                    files_context_parts.append(file_part)
                break

            files_context_parts.append(file_part)
            total_length += len(file_part)

        files_context = "\n\n".join(files_context_parts)

        enhanced_query = f"""用户问题: {query}

相关文件内容:
{files_context}

请基于上述文件内容回答用户的问题。如果文件中包含表格数据（如项目规划表），请特别注意数据的结构和关联关系。如果内容被截断，请基于可见部分进行分析。"""

        # 调用AI模型
        response_text = await generate_file_answer(enhanced_query)

        return {
            "code": 200,
            "message": "成功",
            "data": {
                "answer": response_text,
                "files_processed": len(file_contents),
                "query": query
            }
        }

    except Exception as e:
        logger.error(f"带文件聊天处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")