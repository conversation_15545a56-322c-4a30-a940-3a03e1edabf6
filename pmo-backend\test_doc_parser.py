#!/usr/bin/env python3
"""
专门测试.doc文件解析的脚本
"""

import sys
import os
import tempfile
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_doc_parsing_methods():
    """测试不同的.doc解析方法"""
    print("🔧 开始测试.doc文件解析方法...")
    
    # 创建一个简单的测试.doc文件内容（模拟OLE格式）
    test_content = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1' + b'测试文档内容' * 100 + b'\x00' * 1000
    
    parser = DocumentParser()
    
    print("\n=== 方法1: docx2txt测试 ===")
    try:
        import docx2txt
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
            temp_file.write(test_content)
            temp_file_path = temp_file.name
        
        try:
            text_content = docx2txt.process(temp_file_path)
            print(f"docx2txt结果: {text_content[:200]}...")
        finally:
            os.unlink(temp_file_path)
    except Exception as e:
        print(f"docx2txt失败: {str(e)}")
    
    print("\n=== 方法2: oletools测试 ===")
    try:
        from oletools import olefile
        if olefile.isOleFile(test_content):
            print("✅ 检测到OLE文件")
            ole = olefile.OleFileIO(test_content)
            print(f"OLE流列表: {ole.listdir()}")
            ole.close()
        else:
            print("❌ 不是有效的OLE文件")
    except Exception as e:
        print(f"oletools失败: {str(e)}")
    
    print("\n=== 方法3: 编码检测测试 ===")
    encodings = ['utf-16le', 'utf-16be', 'gbk', 'gb2312', 'gb18030', 'utf-8']
    for encoding in encodings:
        try:
            decoded = test_content.decode(encoding, errors='ignore')
            chinese_chars = len([c for c in decoded if '\u4e00' <= c <= '\u9fff'])
            print(f"{encoding}: 中文字符数={chinese_chars}, 前100字符: {decoded[:100]}")
        except Exception as e:
            print(f"{encoding}: 解码失败 - {str(e)}")

def create_improved_doc_parser():
    """创建改进的.doc解析器"""
    print("\n🚀 创建改进的.doc解析器...")
    
    improved_code = '''
def parse_doc_file_improved(self, file_content: bytes) -> str:
    """改进的.doc文件解析 - 专门处理乱码问题"""
    try:
        import re
        import struct
        
        logger.info("开始改进的.doc文件解析...")
        
        # 检查文件头
        if not file_content.startswith(b'\\xd0\\xcf\\x11\\xe0'):
            return "不是有效的OLE格式.doc文件"
        
        # 方法1: 专门的中文文本提取
        chinese_text = self._extract_chinese_from_ole(file_content)
        if chinese_text and len(chinese_text.strip()) > 20:
            return f"# Word文档内容（中文提取）\\n\\n{chinese_text}"
        
        # 方法2: 结构化OLE解析
        structured_text = self._structured_ole_parsing(file_content)
        if structured_text and len(structured_text.strip()) > 20:
            return f"# Word文档内容（结构化解析）\\n\\n{structured_text}"
        
        # 方法3: 字节模式匹配
        pattern_text = self._pattern_based_extraction(file_content)
        if pattern_text and len(pattern_text.strip()) > 20:
            return f"# Word文档内容（模式匹配）\\n\\n{pattern_text}"
        
        return "无法从.doc文件中提取有效文本内容"
        
    except Exception as e:
        logger.error(f"改进的.doc解析失败: {str(e)}")
        return f"解析失败: {str(e)}"

def _extract_chinese_from_ole(self, file_content: bytes) -> str:
    """专门提取中文文本"""
    try:
        import re
        
        # 查找UTF-16LE编码的中文文本
        chinese_texts = []
        
        # 扫描整个文件，查找中文字符模式
        for i in range(0, len(file_content) - 6, 2):
            try:
                # 尝试UTF-16LE解码
                chunk = file_content[i:i+100]
                text = chunk.decode('utf-16le', errors='ignore')
                
                # 查找连续的中文字符
                chinese_matches = re.findall(r'[\\u4e00-\\u9fff]+', text)
                for match in chinese_matches:
                    if len(match) >= 2:  # 至少2个中文字符
                        chinese_texts.append(match)
                        
            except:
                continue
        
        # 去重并组合
        if chinese_texts:
            unique_texts = list(dict.fromkeys(chinese_texts))
            return '\\n'.join(unique_texts[:50])  # 最多50个片段
        
        return ""
        
    except Exception as e:
        logger.error(f"中文提取失败: {str(e)}")
        return ""

def _structured_ole_parsing(self, file_content: bytes) -> str:
    """结构化OLE解析"""
    try:
        # 解析OLE文件结构
        # OLE文件头: 前512字节
        header = file_content[:512]
        
        # 查找文档流的位置
        # Word文档通常在特定偏移位置存储文本
        text_parts = []
        
        # 常见的Word文本存储位置
        offsets = [0x200, 0x400, 0x800, 0x1000, 0x2000]
        
        for offset in offsets:
            if offset < len(file_content):
                chunk = file_content[offset:offset+2000]
                
                # 尝试多种编码
                for encoding in ['utf-16le', 'gbk', 'gb2312']:
                    try:
                        text = chunk.decode(encoding, errors='ignore')
                        # 查找有意义的文本
                        import re
                        meaningful = re.findall(r'[\\u4e00-\\u9fff\\w\\s]{5,}', text)
                        if meaningful:
                            text_parts.extend(meaningful[:10])
                    except:
                        continue
        
        if text_parts:
            return '\\n\\n'.join(text_parts[:20])
        
        return ""
        
    except Exception as e:
        logger.error(f"结构化解析失败: {str(e)}")
        return ""

def _pattern_based_extraction(self, file_content: bytes) -> str:
    """基于模式的文本提取"""
    try:
        import re
        
        # 查找文本模式
        patterns = [
            # 中文字符模式（UTF-16LE）
            rb'[\\xe4-\\xe9][\\x80-\\xbf][\\x80-\\xbf]',
            # 中文字符模式（GBK）
            rb'[\\xa1-\\xfe][\\xa1-\\xfe]',
        ]
        
        extracted_texts = []
        
        for pattern in patterns:
            matches = re.findall(pattern, file_content)
            for match in matches[:100]:  # 限制匹配数量
                try:
                    # 尝试解码
                    for encoding in ['utf-8', 'gbk', 'gb2312']:
                        try:
                            text = match.decode(encoding)
                            if len(text.strip()) > 0:
                                extracted_texts.append(text.strip())
                                break
                        except:
                            continue
                except:
                    continue
        
        if extracted_texts:
            unique_texts = list(dict.fromkeys(extracted_texts))
            return '\\n'.join(unique_texts[:30])
        
        return ""
        
    except Exception as e:
        logger.error(f"模式提取失败: {str(e)}")
        return ""
'''
    
    print("改进的解析器代码已生成")
    return improved_code

if __name__ == "__main__":
    print("🔧 .doc文件解析测试脚本")
    print("=" * 50)
    
    # 测试现有方法
    test_doc_parsing_methods()
    
    # 生成改进代码
    improved_code = create_improved_doc_parser()
    
    print("\n✅ 测试完成")
    print("💡 建议: 基于测试结果改进DocumentParser类")
