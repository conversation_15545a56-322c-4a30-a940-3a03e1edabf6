#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目模型 - 参考禅道zt_project表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Project:
    """项目模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.program: int = 0
        self.model: str = "scrum"
        self.type: str = "project"
        self.budget: str = "0"
        self.budgetUnit: str = "CNY"
        self.attribute: str = ""
        self.percent: float = 0
        self.milestone: str = "0"
        self.output: Optional[str] = None
        self.auth: str = "extend"
        self.parent: int = 0
        self.path: str = ""
        self.grade: int = 1
        self.name: str = ""
        self.code: str = ""
        self.hasProduct: str = "1"
        self.begin: Optional[date] = None
        self.end: Optional[date] = None
        self.realBegan: Optional[date] = None
        self.realEnd: Optional[date] = None
        self.status: str = "wait"
        self.subStatus: str = ""
        self.pri: int = 1
        self.desc: Optional[str] = None
        self.goal: Optional[str] = None
        self.team: Optional[str] = None
        self.acl: str = "open"
        self.whitelist: Optional[str] = None
        self.order: int = 0
        self.vision: str = "rnd"
        self.displayCards: Optional[str] = None
        self.fluidBoard: str = "0"
        self.deleted: str = "0"
        self.PM: str = ""
        self.PO: str = ""
        self.QD: str = ""
        self.RD: str = ""
        self.feedback: str = ""
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.openedVersion: str = ""
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.closedBy: str = ""
        self.closedDate: Optional[datetime] = None
        self.canceledBy: str = ""
        self.canceledDate: Optional[datetime] = None
        self.suspendedDate: Optional[date] = None
        self.days: int = 0

    @classmethod
    def get_by_id(cls, project_id: int) -> Optional['Project']:
        """根据ID获取项目"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_project 
                WHERE id = %s AND deleted = '0'
            """, (project_id,))
            
            row = cursor.fetchone()
            if row:
                project = cls()
                project._load_from_dict(row)
                return project
            return None
            
        except Exception as e:
            print(f"获取项目失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_projects(cls, page: int = 1, page_size: int = 20, keyword: str = "", status: str = "") -> Dict[str, Any]:
        """获取项目列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["deleted = '0'"]
            params = []
            
            if keyword:
                where_conditions.append("(name LIKE %s OR code LIKE %s OR `desc` LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword, like_keyword])
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_project WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT id, name, code, type, status, begin, end, PM, PO, 
                       openedBy, openedDate, `desc`, pri
                FROM zt_project 
                WHERE {where_clause}
                ORDER BY `order` ASC, id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            projects = cursor.fetchall()
            
            return {
                "projects": projects,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取项目列表失败: {e}")
            return {"projects": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建项目"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查项目名称是否已存在
            cursor.execute("SELECT id FROM zt_project WHERE name = %s AND deleted = '0'", (self.name,))
            if cursor.fetchone():
                raise ValueError(f"项目 {self.name} 已存在")
            
            # 检查项目代号是否已存在
            if self.code:
                cursor.execute("SELECT id FROM zt_project WHERE code = %s AND deleted = '0'", (self.code,))
                if cursor.fetchone():
                    raise ValueError(f"项目代号 {self.code} 已存在")
            
            # 插入项目
            cursor.execute("""
                INSERT INTO zt_project (
                    program, model, type, budget, budgetUnit, name, code, hasProduct,
                    begin, end, status, pri, `desc`, goal, PM, PO, QD, RD,
                    acl, `order`, openedBy, openedDate, days
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s
                )
            """, (
                self.program, self.model, self.type, self.budget, self.budgetUnit,
                self.name, self.code, self.hasProduct, self.begin, self.end,
                self.status, self.pri, self.desc, self.goal, self.PM, self.PO,
                self.QD, self.RD, self.acl, self.order, self.openedBy, self.days
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建项目失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新项目信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_project SET 
                    name = %s, code = %s, type = %s, status = %s, begin = %s, end = %s,
                    `desc` = %s, goal = %s, PM = %s, PO = %s, QD = %s, RD = %s,
                    pri = %s, `order` = %s, lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (
                self.name, self.code, self.type, self.status, self.begin, self.end,
                self.desc, self.goal, self.PM, self.PO, self.QD, self.RD,
                self.pri, self.order, self.lastEditedBy, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新项目失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除项目"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查是否有关联的任务
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_task 
                WHERE project = %s AND deleted = '0'
            """, (self.id,))
            if cursor.fetchone()['count'] > 0:
                raise ValueError("该项目下还有任务，无法删除")
            
            cursor.execute("""
                UPDATE zt_project SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除项目失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_tasks(self, page: int = 1, page_size: int = 20, status: str = "") -> Dict[str, Any]:
        """获取项目下的任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["project = %s", "deleted = '0'"]
            params = [self.id]
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_task WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT id, name, type, pri, status, assignedTo, estimate, 
                       consumed, `left`, openedBy, openedDate, deadline
                FROM zt_task 
                WHERE {where_clause}
                ORDER BY `order` ASC, id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            tasks = cursor.fetchall()
            
            return {
                "tasks": tasks,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取项目任务失败: {e}")
            return {"tasks": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def get_team_members(self) -> List[Dict[str, Any]]:
        """获取项目团队成员"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, u.realname, u.email 
                FROM zt_team t
                LEFT JOIN zt_user u ON t.account = u.account
                WHERE t.root = %s AND t.type = 'project'
                ORDER BY t.`order` ASC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取项目团队失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def get_builds(self) -> List[Dict[str, Any]]:
        """获取项目版本"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_build 
                WHERE project = %s AND deleted = '0'
                ORDER BY date DESC, id DESC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取项目版本失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def get_stories(self) -> List[Dict[str, Any]]:
        """获取项目关联的需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT s.id, s.title, s.type, s.category, s.pri, s.status, s.stage,
                       s.openedBy, s.assignedTo, s.estimate, p.name as product_name
                FROM zt_projectstory ps
                LEFT JOIN zt_story s ON ps.story = s.id
                LEFT JOIN zt_product p ON ps.product = p.id
                WHERE ps.project = %s AND s.deleted = '0'
                ORDER BY ps.`order` ASC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取项目需求失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "program": self.program,
            "model": self.model,
            "type": self.type,
            "budget": self.budget,
            "budgetUnit": self.budgetUnit,
            "name": self.name,
            "code": self.code,
            "hasProduct": self.hasProduct,
            "begin": self.begin.isoformat() if self.begin else None,
            "end": self.end.isoformat() if self.end else None,
            "realBegan": self.realBegan.isoformat() if self.realBegan else None,
            "realEnd": self.realEnd.isoformat() if self.realEnd else None,
            "status": self.status,
            "subStatus": self.subStatus,
            "pri": self.pri,
            "desc": self.desc,
            "goal": self.goal,
            "PM": self.PM,
            "PO": self.PO,
            "QD": self.QD,
            "RD": self.RD,
            "acl": self.acl,
            "order": self.order,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "closedBy": self.closedBy,
            "closedDate": self.closedDate.isoformat() if self.closedDate else None,
            "days": self.days,
            "deleted": self.deleted
        }
