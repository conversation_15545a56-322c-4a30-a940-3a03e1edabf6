#!/usr/bin/env python3
"""
测试项目管理页面关闭按钮修复
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_close_button():
    """测试关闭按钮功能"""
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 10)
        
        print("🚀 开始测试项目管理页面关闭按钮...")
        
        # 1. 访问首页
        print("📍 访问首页...")
        driver.get("http://localhost:3000")
        time.sleep(2)
        
        # 2. 点击项目管理菜单
        print("📍 点击项目管理菜单...")
        project_menu = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '项目管理')]"))
        )
        project_menu.click()
        time.sleep(3)
        
        # 3. 检查页面头部是否存在
        print("📍 检查页面头部...")
        try:
            page_header = wait.until(
                EC.presence_of_element_located((By.CLASS_NAME, "page-header"))
            )
            print("✅ 页面头部存在")
        except:
            print("❌ 页面头部不存在")
            return False
        
        # 4. 检查页面标题
        print("📍 检查页面标题...")
        try:
            page_title = driver.find_element(By.CLASS_NAME, "page-title")
            if "项目管理" in page_title.text:
                print("✅ 页面标题正确")
            else:
                print(f"❌ 页面标题错误: {page_title.text}")
        except:
            print("❌ 页面标题不存在")
        
        # 5. 检查关闭按钮是否存在
        print("📍 检查关闭按钮...")
        try:
            close_button = driver.find_element(By.XPATH, "//button[contains(., '关闭')]")
            print("✅ 关闭按钮存在")
            
            # 6. 测试关闭按钮功能
            print("📍 测试关闭按钮功能...")
            close_button.click()
            time.sleep(2)
            
            # 检查是否返回首页
            current_url = driver.current_url
            if current_url.endswith('/') or 'projects' not in current_url:
                print("✅ 关闭按钮功能正常，已返回首页")
                return True
            else:
                print(f"❌ 关闭按钮功能异常，当前URL: {current_url}")
                return False
                
        except Exception as e:
            print(f"❌ 关闭按钮不存在或无法点击: {e}")
            
            # 截图保存
            driver.save_screenshot("close_button_error.png")
            print("📸 已保存错误截图: close_button_error.png")
            
            # 打印页面源码的关键部分
            print("📄 页面源码片段:")
            page_source = driver.page_source
            if "关闭" in page_source:
                print("✅ 页面源码中包含'关闭'文字")
            else:
                print("❌ 页面源码中不包含'关闭'文字")
            
            if "page-header" in page_source:
                print("✅ 页面源码中包含'page-header'类")
            else:
                print("❌ 页面源码中不包含'page-header'类")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        if driver:
            driver.save_screenshot("test_error.png")
            print("📸 已保存错误截图: test_error.png")
        return False
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("🧪 项目管理页面关闭按钮测试")
    print("=" * 50)
    
    success = test_close_button()
    
    print("=" * 50)
    if success:
        print("🎉 测试通过！关闭按钮功能正常")
    else:
        print("❌ 测试失败！关闭按钮存在问题")
