#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PMO项目管理系统后端主程序
"""

import os
import uvicorn
import asyncio
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from app.api.api import api_router
from app.core.websocket_manager import websocket_manager
from app.core.logger import get_logger
from dotenv import load_dotenv

logger = get_logger(__name__)

# 加载环境变量
load_dotenv()

app = FastAPI(title="PMO项目管理系统", description="PMO项目管理系统API接口", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加全局异常处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误"""
    logger.error(f"请求验证错误: {exc.errors()}")
    logger.error(f"请求URL: {request.url}")
    logger.error(f"请求方法: {request.method}")
    try:
        body = await request.body()
        logger.error(f"请求体: {body.decode('utf-8')}")
    except:
        logger.error("无法读取请求体")

    return JSONResponse(
        status_code=422,
        content={
            "detail": exc.errors(),
            "message": "请求数据验证失败",
            "errors": [{"field": ".".join(str(loc) for loc in error["loc"]), "message": error["msg"]} for error in exc.errors()]
        }
    )

@app.exception_handler(ValidationError)
async def pydantic_validation_exception_handler(request: Request, exc: ValidationError):
    """处理Pydantic验证错误"""
    logger.error(f"Pydantic验证错误: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": exc.errors(),
            "message": "数据模型验证失败"
        }
    )

# 添加API路由
app.include_router(api_router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件"""
    try:
        print("🚀 PMO系统启动中...")

        # 快速启动模式：跳过所有预热步骤
        print("⚡ 快速启动模式：跳过预热步骤")

        print("✅ PMO系统启动完成")

    except Exception as e:
        print(f"❌ 启动过程出错: {e}")

if __name__ == "__main__":
    # 设置较长的超时时间，支持MonkeyOCR的长时间处理
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        timeout_keep_alive=600,  # 保持连接超时：10分钟
        timeout_graceful_shutdown=30,  # 优雅关闭超时：30秒
        limit_max_requests=1000,  # 最大请求数
        limit_concurrency=100  # 最大并发数
    )