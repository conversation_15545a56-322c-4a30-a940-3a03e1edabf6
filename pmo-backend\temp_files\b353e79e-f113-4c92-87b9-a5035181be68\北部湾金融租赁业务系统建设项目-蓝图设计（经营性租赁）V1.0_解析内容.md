# Word文档内容


【签字确认页】

甲方：

北部湾金融租赁有限公司（盖章）

项目经理签字：

签字日期：

乙方：

深圳宏景动力信息技术有限公司（盖章）

项目经理签字：

签字日期：

## 前言

### 目的

本文针对北部湾金融租赁有限公司租赁业务管理系统（经营性租赁业务），在前期对业务需求及现有流程进行充分调研分析后，通过对系统功能进行流程匹配和优化做出的总体设计，为项目边界、需求范围、关键功能及实现方案进行描述。并对具体的实现方案进行详细描述，以此作为系统详细设计、开发和实施上线、项目验收的依据。

在交付的过程中出现新的需求或重要需求变更，应按需求变更流程提出书面申请，经双方对变更申请签字确认后方可生效，需求变更可能会产生新的费用。

### 读者

本文档可作为最终用户、系统设计、开发人员，测试人员，系统维护人员的参考资料。

### 项目背景

北部湾金融租赁有限公司（以下简称北部湾金租）成立于2012年9月，系经原中国银监会批准、由广西金融投资集团联合广西柳工集团发起设立的全国性非银行业金融机构，总部设在广西南宁市，注册资本10亿元。自2020年起，公司持续保持AA＋主体信用评级。

北部湾金租立足广西、面向全国，聚焦服务工业振兴，积极对接高端金属新材料、电子信息、高端装备制造、新能源、生物医药等重点产业集群，持续加大对实体企业的支持力度，并通过“桂惠贷”政策进一步降低金融服务成本，助推重点产业集群加快创新升级步伐，公司发展质量和服务地方经济社会能力稳步提升。截至目前，公司已累计为实体经济投放资金超过130亿，荣获行业“最佳区域经济助力企业”奖、“纳税信用3连A企业”等荣誉称号。

“十四五”期间，公司将重点围绕广西重点优势产业圈与广西投资集团实体产业圈、广西区直企业产业圈、广西优质地级市国企圈、全国先进产业圈为业务发展方向，全面提升金融服务能力，努力建设特色鲜明、管理规范、专业高效、业绩优良的金融租赁公司。

随着公司的业务发展，为扩展经营性租赁方向，需要在当前已有中台业务系统的基础上，进行业务管理功能扩展，以适应公司新的业务发展需要。

### 参考资料

《北部湾租赁业务系统功能清单（经营性租赁）(SOW)》

《北部湾金租融资租赁经营性租赁业务系统建设项目需求调研清单》

## 系统功能框架

### 公司组织架构

### 项目目标

基于当前已建成的融资租赁业务系统基础上，扩展建设可为所有部门使用的强大核心业务系统，该系统主要支持经营性租赁业务。系统能与现有的大单租赁业务系统良好集成，并且安全可靠，性能良好，且基于互联网使用，流程配置合理规范、功能直观易用、数据清晰可视，能大幅度提升各部门的工作效率，有效加强公司的业务风险管理水平，为公司新业务的快速发展提供支撑。

### 系统整体业务流程（经营性租赁）

### 整体蓝图架构

系统整体蓝图设计是基于金蝶云苍穹平台的标准经营性租赁产品，结合北部湾金租的业务特点和需求，出具的蓝图，包括经营性租赁业务，移动端使用钉钉进行移动审批。中间会涉及对接外部系统企查查、反洗钱、资金系统、北金租业务中台、财报OCR。

## 功能需求描述（经营性租赁）

### 法人登记

#### 业务场景

业务经理根据公司相关规定，并线下进行初步分析判断项目可行性，如项目可行，则业务经理对客户的基本信息、股权结构、管理团队等相关信息进行系统录入。

#### 关键功能说明

支持业务经理通过第三方数据平台下载客户资料，包括基本信息、客户类别、股权结构、管理团队等相关信息；

支持唯一性控制：以证件类型+证件号码作为客户唯一性校验；

支持记录客户财务信息：总资产、净资产、去年营业收入等财务信息；

支持记录客户关联方信息，可调整关联方是否有效；

支持以客户为维度综合查看该客户的项目信息、合同及租赁物信息、还款计划及回笼等情况；

支持上传附件格式包括：word、excel、pdf等格式。

#### 原型界面

### 黑名单

#### 业务场景

法人客户与集团黑名单接口对接，若集团黑名单接口返回法人客户是黑名单，则将法人客户移入黑名单中，若集团黑名单接口返回法人客户不是黑名单，则将法人客户从黑名单中移除。

#### 关键功能说明

1.支持对法人及自然人客户通过集团黑名单接口移入黑名单；

2.支持通过设置黑名单参数来控制黑名单客户能否做项目：

若参数选择“预警提示”，黑名单客户做业务时提示，可继续操作；即做项目立项时可以选择黑名单客户，但是需要提醒；

若参数选择“禁止交易”，则禁止黑名单客户进行相关业务操作；即做项目立项时系统控制不能选黑名单客户；

3.黑名单不支持反审核，只允许通过集团黑名单接口将其从黑名单中移除。

#### 原型界面

### 客户拜访

#### 业务场景

用来记录在项目过程中，如立项、申报及租后巡检各个阶段，员工进行客户拜访的情况，包括拜访的客户信息、项目进展阶段、拜访记录说明等信息。

#### 功能需求说明

记录客户拜访信息，包括客户名称、拜访日期、同行拜访人、项目进展阶段、拜访记录说明、拜访次数、拜访间隔等；

支持选择项目进展阶段：商机跟进、立项、申报、租后巡检；

同行拜访人员支持多选；

#### 系统实现

### 客户交接

#### 业务场景

项目正常进行过程中，当项目所属人发生变动时，需要对该项目或者该客户进行移交操作。

#### 关键功能说明

根据查询条件点击查询后查询项目立项中承租方=客户名称的项目立项单据，带出相关信息；

支持客户及相关项目进行交接，客户及项目关联单据同步移交；

支持客户和相关项目分别移交，即客户不移交，项目移交；

支持上传附件格式包括：word、excel、pdf等格式。

#### 原型界面

### 客户财务报表

#### 业务场景

市场经理需要收集客户财务报表，将客户财务报表录入到系统中，做客户财务分析。

#### 关键功能说明

报表项目：支持自定义报表项目及计算方式；

财务报表模板：支持自定义报表（资产负债表、利润表、现金流量表、财务分析表）模板；

支持录入客户的财务报表数据，财务指标根据计算公式可自动计算；

支持下载报表模板，可在EXCEL中录入数据导入到系统中；

支持上传附件格式包括：word、excel、pdf等格式。

#### 原型界面

### 项目立项

#### 业务场景

业务经理经初步调查认为企业符合立项条件的，按要求收集相关资料，并与客户沟通确定经营性租赁方案及采购清单后进行立项审批，并发送消息通知各领导。

#### 关键功能说明

支持选择客户后自动带出客户相关信息；

支持项目立项单记录客户租赁形式、是否关联交易、报价信息、客户基本信息、采购清单、用途说明等重要信息；

支持根据风险等级对项目进行提醒；

支持在项目立项时，记录报价信息，快速查看报价内容；

支持上传附件格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→协办人员→部门负责人→会签→业务分管领导

#### 套打说明

支持打印《项目立项审批表》

打印表单添加水印，水印格式为登录用户名+系统当前日期。

#### 原型界面

### 项目申报

#### 业务场景

项目立项后，业务经理发起项目申报，填写“业务申报说明”后发起项目申报审批流程，经业务部门负责人及其分管领导审批后，由授信管理部审查人填写审查结论意见，评审会秘书发起评审会，评审会委员进行投票表决，出具项目评审委员会意见。评审会结束后，由评审会秘书录入项目评审委委员会意见以及评审会议的最终确定的租赁方案、租赁计算规则、租赁设备清单，经评审会主任确认、有权审批人复核无误后审批通过。

#### 关键功能说明

项目申报单通过项目立项单生成，自动携带出项目要素、租赁信息、费用清单、租赁计算规则、担保方式、租赁设备清单；

业务经理填报业务申报说明，业务申报说明包括反洗钱等级（低等级、中等级、高等级）、反洗钱情况说明、担保情况说明、担保措施说明、限制条件说明、管理条件说明、租赁物分类（监）、租赁物类型（新国标）；

设备清单的租赁物分类（监）在业务经理提交时必填，且在提交时，判断租赁物分类（监）如果选择“其它租赁物”，则弹窗提示“设备种类为“其它租赁物”时须审慎确认”，继续选择“是”则可提交，选“否”则不提交；

租赁物分类（监）、租赁物类型（新国标）与监管口径一致，填报时只允许选择最末级节点；

审查人在审批流程中填写审查结论意见（审查人与评审会秘书为同一人），审查结论意见包括材料完整性审查、合法合规性审查、交易结构审查、担保措施审查、项目综合审查、限制条件审查、管理要求条件审查、综合审查意见；

审查人可修改租赁物分类（监）、租赁物类型（新国标）字段，修改后再提交至流程中时，判断租赁物分类（监）如果选择“其它租赁物”，则弹窗提示“设备种类为“其它租赁物”时须审慎确认”，继续选择“是”则可提交，选“否”则不提交；

评审会秘书发起评审会议，填写评审会议名称、评审投票人员、会议主持人；

评审投票人员在审批流程中对项目申报单据进行投票表决；

评审会结束后，评审会秘书填写项目评审委员会意见，项目评审委员会意见包括材料完整性审查、审查结论、表决情况、前提条件、管理要求、项目过会时间；同时评审会秘书可查看评票情况；评审会秘书根据评审会结论修改项目的租赁信息、租赁计算规则、设备清单，租赁合同以项目申报此处确认的租赁信息、计算规则设置为准；

支持根据评审结论-再议，由业务经理决定在三个月内发起再议，再议时可进行二次发起评审流程；

支持做项目申报变更，需记录变更原因；

评审通过后，系统发送消息提醒各部门；

支持上传附件格式包括：word、excel、pdf等格式。

#### 审批流程

1.业务经理-部门负责人-业务分管领导-收件人-审查人（认领模式，填写审查结论意见，审查人与评审会秘书为同一人）-评审部负责人-合规分管领导-评审会秘书（发起评审会，填写评审会议名称、评审投票人员，评审会议投票人员在流程中进行线上投票）-评审会（填写评审会议结果）-评审会主任-有权审批人

#### 套打说明

套打说明：支持打印《项目申报审批表》（投票情况打印输出至表决情况一栏中）、《项目评审结论呈报表》；

打印表单添加水印，水印格式为登录用户名+系统当前日期。

#### 原型界面

### 采购订单

#### 业务场景

项目申报审批过后，业务部门人员根据立项审批结果记录租赁设备采购的实际情况。

#### 关键功能说明

通过项目申报单生成，自动携带出项目申报的相关信息：项目名称、承租方、项目申报单编号、租赁设备清单等；

记录采购时租赁设备当前具体情况：包括设备名称、规格型号、单位、数量、单价，剩余使用年限、评估价值、认定价值、资产原值、资产残值等；

采购订单的设备清单只允许在项目申报单的设备清单基础上做删减；

采购订单的租赁设备清单审核通过后同步至租赁设备档案的业务单据中；

支持上传附件格式包括：word、excel、pdf等格式。

#### 审批流程

#### 套打说明

#### 原型界面

### 租赁合同

#### 业务场景

项目申报审批通过、租赁设备采购完成后由市场经理在系统制作租赁合同及合同阶段的租金表，提交到法务核实合同文本（包括租赁主合同、担保合同、回购协议、买卖合同），并上传到系统签约文件中，并进行合同签约。

#### 关键功能说明

通过选单项目申报单生成经营性租赁合同，支持同一项目申报生成多笔合同；

记录合同当前状态：合同签订、租赁交付、合同回款、租赁验收等；

记录合同关键信息：项目金额、租赁方案、担保合同、租赁物清单、展期次数、重组次数、费用信息、回购方等；

费用明细分录：记录费用的相关情况，包括风险金、手续费等；

支持记录客户收款银行账户信息、约定违约处理方式；

支持上传合同模板后打印合同文档；

租赁合同提交时校验项目立项关联的租赁合同项目金额之和不得大于立项金额；

设备清单通过项目申报单携带，选择后带出相应字段到设备清单中；设备清单只在项目申报单的基础上做删减；

支持上传附件格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→业务部门负责人→业务分管领导→法律审查岗→合规与风险管理部门负责人→总法律顾问→合规分管领导→有权审批人

#### 套打说明

《租赁合同》、《合同审批表》

#### 原型界面

### 租赁合同变更

#### 业务场景

当租赁合同出现租赁物变更、替换担保、追加担保、租赁方案调整等相关情况时，需要通过合同变更单对合同登记的信息进行更新，以保持与业务情况的一致性。

#### 关键功能说明

合同变更单据选单租赁合同生成，自动携带原合同相关信息；

合同变更方式分两种：签约前变更、签约后变更：

若是签约前变更，系统可将原合同的信息及签约文件携带过来，若发生追加担保，新增担保也需要生成新的签约文件；

若是签约后变更，则不用携带原合同的签约文件，再由业务经理将补充协议上传到签约文件中；

系统原则上一个合同号对应一版有效的合同；

系统支持将变更过的内容标识出来，并可展示变更前后的数据

#### 原型界面

#### 审批流程

业务经理提交→业务部门负责人审批→业务分管领导审批→资产管理岗/审查人→评审部门负责人→风险分管领导→首席风险官→评审会秘书→评审号→评审会主持人→有权审批人

#### 打印输出

《合同变更审批表》

### 担保合同

#### 业务场景

项目正常进行过程中，根据经营性租赁合同规定出具相关的担保合同对项目进行担保，业务人员需要在系统中维护担保合同的详细信息。

#### 关键功能说明

通过选单租赁合同生成担保合同，支持同一经营性租赁合同生成多笔合同；

支持担保方式：抵押、质押、保证等担保方式；、

支持担保合同中支持维护担保方相关信息，并根据担保方相关信息生成对应附件；

支持记录抵质押物的基本信息在抵质押物清单中；

担保合同审核后会将担保合同信息显示在经营性租赁合同中；

支持上传附件格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→业务部门负责人→业务分管领导→法律审查岗→合规与风险管理部门负责人→总法律顾问→合规分管领导→有权审批人

#### 套打说明

#### 原型界面

### 抵质押物登记

#### 业务场景

合同签订后，若涉及抵质押物做担保时，合规与风险管理部人员可使用抵质押登记单记录抵质押物的情况。

#### 关键功能说明

1.抵押物登记单通过选单担保合同生成，自动携带出相关信息，包括担保合同信息、抵质押物清单等；

2.支持补充抵质押物登记，可通过分录新增按钮补充抵质押物，审核后将补充后抵质押物反写到对应担保合同抵质押物清单中。

3.支持做多次登记，多次登记数量合计不能超过总的押物数量；

4.单据审核后，录入的“本次登记数量”会反写担保合同中的“已登记数量”。

#### 审批流程

#### 套打说明

#### 原型界面

### 抵质押物解押

#### 业务场景

根据项目实际情况，客户申请部分抵质押物解押，或当项目结清时，由业务部门经理发起解押申请，经相关领导审批后，再由合规与风险管理部经理办理抵质押物解押。

#### 关键功能说明

选择担保合同，带出该项目担保合同、抵质押物清单、抵质押物详情等信息；

记录实际解押的数量，当抵质押物解押单审核后，反写到担保合同、抵押物登记单的“已解押数量”。

#### 审批流程

#### 套打说明

#### 原型界面

### 合同签约

#### 业务场景

合同文本审核通过之后，合规与风险管理根据合同签署文件的签约方式（线下签约），跟进合同签约的进度。

#### 关键功能说明

合同签约单来源包括：租赁主合同、担保合同。

当签约方式为线下签约时，签署状态默认是草稿，并上传双方已签署合同文件的电子版本，单据审核后签署状态为已签约。

#### 审批流程

#### 套打说明

#### 原型界面

### 用印申请

#### 业务场景

合同文件签约后，由业务经理对合同签约文件提交用印申请流程，跟进合同签约文件的盖章进度。

#### 关键功能说明

用印申请单通过选单合同签约单生成；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

1.业务经理-部门负责人-分管领导-有权审批人

#### 套打说明

2.打印《北部湾金融租赁有限公司用印申请表》

#### 原型界面

### 费用收款

#### 业务场景

合同中涉及到放款之前收取的款项，包括风险金、手续费、服务费等，当收到对应的费用后，由出纳将收款信息通过收款单录入到系统中，财务负责人审批。

#### 关键功能说明

费用类型包括：风险金、手续费、服务费、违约金等其他费用；

选择合同后，根据实际收款信息填写实际收款日期、收款金额、结算方式、收款银行账户等关键信息；

收款单审核后，可将实收金额反写到合同中的费用已收金额；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→业务部门负责人

#### 套打说明

无

#### 原型界面

### 租赁交付确认

#### 业务场景

租赁物合同审批通过后，出租方向承租方交付租赁物，并记录交付地点、交付时间、交付人等重要信息。

#### 关键功能说明

支持根据已审核通过的经营性租赁合同下推生成，从租赁合同携带合同名称、承租方、租赁物清单；

支持一个合同生成一笔交付确认单，一笔交付确认单生成一笔还款计划；

支持对租赁物的交付进行确认，记录交付地点、交付时间、出租方经办人、承租方验收人、验收人签字时间等重要信息；

支持在租赁合同携带的租赁设备基础上进行删减，还款计划的租赁设备清单以租赁交付确认的租赁设备清单为准；

交付确认提交时，需检查交付确认单关联的租赁合同，若有关联的抵质押合同是否有审核通过的抵质押物登记才允许提交，否则给与提示：“请进行抵质押物登记”；

交付确认提交时，校验收款记录中的收款金额是否=费用明细中的本次应收金额，若不相等则不允许提交；

交付确认单审核通过后，该交付确认单的设备清单的租赁设备档案的状态为“已租”；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

1.业务经理-业务部门负责人-出纳-放款审核岗（附件必须上传已打印的《交付确认单》）-合规与风险管理部负责人-总法律顾问-分管合规领导

#### 套打说明

打印《交付确认申请单》、《交付确认单》

添加水印，水印为登录用户名+系统当前日期

#### 原型界面

### 还款计划

#### 业务场景

经营性租赁合同中的租赁物交付确认后，业务经理根据合同约定的实际还款周期及还款金额在系统中制作还款计划，并且维护具体的还款方式，同时该还款计划作为后续还款及租后业务的依据。

#### 关键功能说明

支持通过选单由经营性租赁交付确认单生成对应的还款计划携带合同中约定的租赁方案。

支持二次修改租赁方案，灵活应对各种实际还款场景。

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理-业务部门负责人-分管业务领导-会计核算岗

#### 套打说明

1.支持打印《实际租金计划表》、《租金收取通知书》

#### 原型界面

### 还款计划变更

#### 业务场景

项目正常进行过程中客户提出对还款计划表进行更改，需要在系统中发起申请。

#### 关键功能说明

选单还款计划携带原还款计划信息，录入还款计划变更原因对还款计划进行变更；

支持手动修改还款计划表，

还款计划变更单审核后自动将修改的信息更新到客户还款计划单中，并保存变更前版本到后台，并将单据编号改为原还款计划编号[1]，点击查看变更历史弹出原还款计划列表;

选单时校验若该还款计划存在暂存、提交的租金回款单则不允许选单；

支持附件上传格式包括：word、excel、pdf等格式.

#### 审批流程

业务经理提交→业务部门负责人审批→业务分管领导审批→资产管理岗/审查人→评审部门负责人→风险分管领导→首席风险官→评审会秘书→评审号→评审会主持人→有权审批人

#### 套打说明

《还款计划变更表》

#### 原型界面

### 租金收款

#### 业务场景

起租后，当收到客户租金，财务经理在回笼单中录入客户实际还款情况，记录还款金额、还款日期，是否风险金抵扣。

#### 关键功能说明

选择偿还计划后，选择还款状态为部分回笼、未回笼或罚息余额不为0的期项进行还款；

还款后，偿还计划对应期项的状态变为：已回笼、部分回笼，租金余额根据实际还款情况减少。

风险金抵扣时：

是否风险金抵扣--选择“是”，该“风险金抵扣金额”字段可录入，否则灰色显示；

自动携带剩余可抵扣金额；

校验录入的金额不能大于剩余金额。

根据实际还款情况可配置多条工作流程；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

#### 套打说明

#### 原型界面

### 租赁设备档案

#### 业务场景

租赁物采购订单审批通过后，将采购订单的租赁设备清单入库，对租赁物进行统一管理，记录租赁的出租状态、租赁物存放地、评估价值、认定价值、累计折旧值、资产残值等信息。

#### 关键功能说明

接收采购订单同步的租赁设备清单；

支持手工维护租赁物资产原值、累计折旧，并且计算资产净值；

租赁合同审核通过后，记录租赁设备关联的租赁合同及其实际承租方；

交付确认审核通过后，更新租赁物的租赁状态为“在租”；

交付确认及归还验收后更新租赁设备管理中的设备所在位置；

合同正常结束后，根据合同的租赁物处理方式，更新租赁设备的租赁状态（如选回购，则状态为“已回购”；如选“收回”，则状态为“待租”）。

#### 审批流程

#### 套打说明

#### 原型界面

### 保险管理

#### 业务场景

当客户购买保险后，若购买的保险期限覆盖了租赁期限，业务经理需将保单信息录入到系统中，并将保单通过附件上传，合规与风险管理部落实保单情况。

#### 关键功能说明

支持维护租赁设备的投保信息及抵质押物的投保信息；

租赁物保险单由租赁合同选单生成，抵质押物保险单由担保合同选单生成；

支持多个合同或多个担保合同关联生成保险单，保险明细分录生成多行数据，记录投保信息；

支持携带出合同金额、开始日期、结束日期、签署后的租赁设备清单文件、租赁设备清单数据。

支持将保单扫描件通过附件上传，格式包括：word、excel、pdf等格式。

#### 审批流程

#### 套打说明

#### 原型界面

### 租后巡检

#### 业务场景

合同起租后，出租方按照约定对承租方的租赁物使用情况进行查验，并记录租赁物当前使用情况，并对承租方进行评定，维护承租方五级分类等级。

#### 关键功能说明

选择承租方，带出合同信息，资管经理录入承租方的租后实际巡检情况，租后巡检调查结果；

巡检计划生成规则：

交付确认单审核通过后自动生成租后巡检单，计划巡检日期=交付确认日期+90天；

租后巡检审核后，下次计划巡检日期=上次计划巡检日期+90天。

系统提前30天将巡检计划发送消息提醒合规与风险管理部法律审查岗（租后），风险管理部法律审查岗（租后）巡检完成后需要发送消息给业务经理；

系统支持巡检过程中收集的客户资料上传附件到系统中；

支持根据租后巡检实际情况维护承租方五级分类等级；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

1.业务经理-部门负责人-业务分管领导-法律合规岗-部门负责人；

#### 套打说明

#### 原型界面

### 归还验收单

#### 业务场景

客户正常结清合同或合同解除后，出租方对经营性租赁物进行归还验收，检查租赁物实际情况，检查每期租金是否收取完成，检查风险金等是否完成退款，验收完成后合同关闭。

#### 关键功能说明

经营性租赁归还验收单，可通过选单由经营性租赁交付确认单生成，相关字段（交付确认单编号、合同编号、承租方、合同期满时间）取自对应交付确认单，归还明细携带自交付确认单的租赁物清单；

归还验收时填报归还原因类型（合同正常结束或合同解除）

若归还原因类型选择“合同正常结束”，则归还验收提交时校验风险金是否退款完成、每期租金是否收取完成，若均未完成则不允许提交流程；

若归还原因类型选择“合同解除”，则归还验收提交时校验“归还原因说明”是否填写，若未填写则不允许提交流程；

归还验收单审核通过之后，归还验收单的租赁物清单的各租赁物的租赁物档案的租赁物地点更新为归还验收单的归还地点；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

#### 套打说明

打印《归还验收清单》

打印添加水印，水印格式为登录用户名+系统当前日期。

#### 原型界面

### 费用退款

#### 业务场景

合同中涉及到费用的退款，包括预付款退款、风险金退款、手续费用退款等，业务员发起费用退款核对退款金额、收款方、银行账号，提交进入退款审批流程

#### 关键功能说明

退款费用类型包括：风险金、手续费、服务费、违约金等其他费用；

选择合同后，根据实际收款信息填写实际退款日期、退款金额、结算方式、承租人收款银行账户等关键信息；

收款单审核后，可将实收金额反写到合同中的费用已收金额；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→业务部门负责人审批→合规与风险管理部负责人→会计→财务部门负责人→分管财务领导→总经理

#### 套打说明

《风险金返回申请单》《资金支付单》

#### 原型界面

### 到期租金通知单

#### 业务场景

租金到期前N天与租金到期当天，系统发送预警消息提醒项目对应业务经理。

#### 功能需求说明

根据查询条件查询出承付日期在开始日期至结束日期之间的期项，并携带相关信息到分录中；

系统预警：系统每日自动检索还款计划表中的回笼状态≠已回笼，当前日期=承付日期-N天或承付日期当天，则发送消息提醒业务经理；

件上传格式包括：word、excel、pdf等格式。

#### 审批流程

#### 套打说明

1.打印《租金到期通知单》

#### 原型界面

### 合同结清

#### 业务场景

合同结清分为：正常结束、提前结束、加速结束。市场发起合同结清，通过流程流程后，生成《合同结清证明》文件。

#### 功能需求说明

结清单审核依据系统参数控制：如款项必须收完、风险金完成退款；

合同结清后，自动发起抵质押物解押流程到资管经理处，发起风险金退款流程，并将合同结清证明携带到财务经理处；

结清方式有：正常结束、提前结束、加速结束、解除合同、其他；

提前结束和加速结束场景需要先进行提前还款申请，待租金全部回笼后，再发起合同结清流程；

合同结清后，该合同关联的单据（除抵质押物解押、风险金退款、费用收款）都不能够再进行更改；

合同结清后，支持打印生成合同结清证明；

支持附件上传格式包括：word、excel、pdf等格式。

#### 审批流程

业务经理提交→业务部门负责人→出纳→会计核算岗→财务部门负责人→审查岗→合规与风险管理部负责人

#### 套打说明

《正常结清说明书》、《结清证明书》

#### 系统实现

## 制裁诉讼

### 制裁诉讼

#### 制裁诉讼流程

#### 制裁诉讼流程说明

#### 诉讼申请

##### 业务场景

客户逾期未还款，公司可通过诉讼方式向客户追收相关款项。由审计纪检部收集相关材料发起诉讼申请，提交相关人员进行审批。

##### 功能需求说明

选择逾期的合同，带出相关信息：包括逾期期数、逾期租金、逾期利息、首次逾期时间、投放本金、风险金、租金总额、已收租金、未到期租金等；

记录违约客户需要向我方支付的赔偿金额和委托代理需要支付的委托代理费用。

##### 系统实现

#### 诉状

##### 业务场景

审计纪检部向法院提交诉状，记录原告、被告及起诉请求。

##### 功能需求说明

诉状通过选择诉讼自动生成，携带出合同编号、法院名称、诉讼日期、起诉标的、诉讼类型；

记录该诉讼案件被告人；

支持录入起诉请求：判令第一被告支付逾期租金、判令第一被告支付逾期利息、判令第一被告支付未到期租金、判令第一被告支付名义货价、判令第一被告承担本案诉讼费、判令第一被告对上述款项的偿付承担连带责任。

##### 系统实现

#### 起诉委托代理

##### 业务场景

诉讼过程中，公司可向委托代理机构委托诉讼，跟据合同约定内容发起对项目的诉讼申请，由审计纪检部在系统中记录委托诉讼信息。

##### 功能需求说明

起诉委托代理通过选择诉状自动生成，携带出合同编号、案件原告、案件被告、法院案号、诉讼日期；

记录委托代理需要支付的费用、委托代理机构代码和名称、代理人、签订日期、起诉原因、代理权限。

##### 系统实现

#### 诉讼费用

##### 业务场景

记录诉讼过程中产生的诉讼费用，由审计纪检部登记到系统中。

##### 功能需求说明

通过诉讼带出：合同编号、法院、案件原告、案件被告、被告人、诉讼、标的金额；

通过费用分录记录整个诉讼过程中需要支付的诉讼费用、费用名称、输入日期。

##### 系统实现

#### 财产保全

##### 业务场景

在诉讼过程中，我司向法院申请财产保全。面对争议财产或与案件有关的财产，依法采取的各种强制性保护措施，由审计纪检部中系统中记录标的物保全情况。

##### 功能需求说明

财产保证通过选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期；

记录申请财产保全的日期、保全日期、保全内容。

##### 系统实现

#### 法院受理

##### 业务场景

法院受理后，审计纪检部记录案件编号、受理状态（一审、二审、再审）、法院受理编号、开庭日期。

##### 功能需求说明

法院受理通过选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录法院受理信息：受理状态、公告费、诉讼费、缴纳日期、保全费、开庭日期、法院受理编号。

##### 系统实现

#### 案件调解

##### 业务场景

诉讼过程中，若被告与我司在法院经调解后达成调解，可在系统中记录案件调解情况。

##### 功能需求说明

##### 系统实现

案件调解通过选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录法院调解情况：调解金额、调解日期、期限、执行情况、我方承担诉讼保全费用、被告方承担诉讼保全费用。

#### 案件撤诉

##### 业务场景

法院受理后，由于各种原因需要撤诉，由审计纪检部登记撤诉及撤诉原因。

##### 功能需求说明

案件撤诉选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额，记录案件撤诉原因。

##### 系统实现

#### 法院判决

##### 业务场景

我司在收到法院的裁判文书后，由审计纪检部在系统中记录法院判决情况，并将法院判决书上传到系统中，包括一审判决、二审判决、再审判决。

##### 功能需求说明

记录法院最终的判决结果：判决次序、判决金额、我方承担保全诉讼费用、被告承担诉讼保全费用、判决日期、生效日期；

记录公司对该判决的意见即内容。

##### 系统实现

#### 自动履行

##### 业务场景

法院判决后，若被告自动履行裁判文书确定的义务，由审计纪检部在此记录履行情况。

##### 功能需求说明

自动履行选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录履行情况：履行日期、履行金额、履行实物、履行余额。

##### 系统实现

#### 申请执行

##### 业务场景

若被告未按照法院判决内容自动履行执行义务，我司可向法院申请强制执行，由审计纪检部在系统中记录申请强制执行的相关内容。

##### 功能需求说明

申请执行选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录执行情况：承办人、执行费、执行标的、案由、标的内容、依据、原因。

##### 系统实现

#### 执行情况

##### 业务场景

诉讼判决后，被执行人按照判决内容自动履行执行义务，纪检合规部门记录执行情况。

##### 功能需求说明

执行情况选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录执行情况：被执行人、执回现金金额。

##### 系统实现

#### 执行和解

##### 业务场景

案件执行过程中，被告与我司经沟通后达成和解，由审计纪检部在系统中记录和解情况、和解日期、金额、诉讼保全费用等。

##### 功能需求说明

执行情况选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录执行情况：和解金额、和解日期、期限、执行情况、我方承担诉讼保全费用、被告方承担诉讼保全费用。

##### 系统实现

#### 案件结案

##### 业务场景

诉讼案件结案后，由审计纪检部在系统中做结案处理。

##### 功能需求说明

案件结案选择诉状自动生成，携带出合同编号、案件编号、案件原告、案件被告、法院案号、诉讼日期、标的金额；

记录结案信息：执行人、民事裁定书编号、结案日期、执行状态、标的金额、申请人、与申请执行表的差额、结果。

##### 系统实现

## 资料管理

### 资料配置

#### 业务场景

在项目进行中，不同的环节会收取不同的资料，可使用资料配置单规范每个环节需要进行收集或者归档会使用到的文件等资料。

#### 功能需求说明

启用该配置时在单据页面保存时，会自动根据单据名称、租赁类型进行匹配，将已配置好的资料清单自动携带到单据页面；

勾选了是否必传后，在单据页面提交时需要校验附件是否上传。勾选了是否归档后，附件在单据页面上传并且单据审核通过后，在资料归档时，会自动携带到资料归档单中的归档清单；

支持配置资料清单的负责部门，后期资料移交时，根据部门带出需要移交的归档清单。

#### 系统实现

#### 审批流程

无

#### 套打说明

无

### 资料移交归档

#### 业务场景

合同签订后，需要对项目相关资料进行归档处理，由各部门负责将归档清单中的资料文件扫描上传到系统中。

#### 功能需求说明

各部门制作资料移交归档单时，选择租赁合同，系统自动带出本部门该合同需归档的清单及已上传的附件内容（如相关合同资料附件），填写页数和份数，支持对内容进行修改和补充，线下将归档内容移交至资产管理岗，并在系统提交流程，由资管流程中确认收到的资料，并进行扫描上传系统。

支持对附件分录进行新增和删减，新增的附件会根据所属单据类型自动反写到对应的单据附件分录；

如必有资料无法提供，需上传部门经理和分管领导的书面说明。

#### 系统实现

#### 审批流程

各部门发起资料移交→资管接收确认

#### 套打说明

无

### 资料借阅

#### 业务场景

在业务进行过程中，会对归档资料进行借出，可以使用资料借阅单发起借阅流程，经相关领导审批后，资管确认借出。

#### 功能需求说明

借出资料基于项目已归档资料进行借阅，选单选择对应资料归档单已归档的归档清单分录。（在选单选择资料归档时，需要过滤掉已借出的资料）；

考虑到在实际应用场景中，在选择借阅资料时，需要对不同项目的归档文件分录或者同个项目不同的归档单的归档文件分录同时借阅的情况；

需要在选择资料归档单时，对不同的资料归档单的归档清单分录，允许多选，并将选择的归档清单分录及其相关信息自动携带到资料借阅单中的借阅清单；

单据审核通过后自动反写资料归档单中已选择已借阅的资料的档案状态为已借阅；

允许通过删除按钮对分录行进行删除；

借出信息由借阅申请人（默认当前用户）填写，归还信息由后续的资料归还单自动反写。

#### 系统实现

#### 审批流程

无

#### 套打说明

无

### 资料归还

#### 业务场景

资料借阅后，由借阅人将资料进行归还，发起资料归还流程，由管理人员接收后确认归还。

#### 功能需求说明

支持对归档的资料考虑到会对不同的资料借阅单的分录进行归还的操作，在选择资料借阅单时，允许对不同的借阅单的借阅分录进行选择，并将选择的资料借阅分录和其信息自动携带到资料归还单中；

审核通过后，反写对应资料归档单的档案状态为已归档，反写资料借阅单是否已归还字段为是；

当资料归还审核通过后，将归还时间反写到对应的资料借阅单借阅清单分录中的实际归还时间字段；

当资料归还审核通过后，将归还人反写到对应的资料借阅单借阅清单分录中的归还人字段。

#### 系统实现

#### 审批流程

归还人提交→资管确认归还

#### 套打说明

无

### 资料查询

#### 业务场景

资管将文件归档之后，可通过资料查询单查询项目上传的附件资料及档案状态（未归档、已归档、无需归档）。

#### 功能需求说明

查询条件包括：合同、承租人、资料类别、资料名称、附件名称、单据名称；

支持根据查询条件查询相关附件及附件的档案状态；

支持导出查询结果。

#### 系统实现

#### 审批流程

无

#### 套打说明

无

## 系统接口

### 单点登录

#### 业务场景

在租赁业务系统中，提供业务中台系统的链接,通过此链接能够单点登录进入业务中台系统。

#### 功能需求说明

1.在租赁业务系统中，提供业务中台系统的链接,通过此链接能够单点登录进入业务中台系统，以新窗口的形式打开业务中台系统。

### 接口日志列表

#### 业务场景

将接口传递的数据及接口返回的内容在接口同步日志显示，方便接口信息跟踪。

#### 功能需求说明

接口同步日志包括：接口日志编码、接口名称、请求内容、接收内容、创建人、同步时间。

#### 系统实现

### 企查查

#### 业务场景

租赁公司在线下业务中使用企查查等平台获取客户、担保人信息，为在系统中实现对数据的快速录入以及数据的正确性，开发企信宝接口，在系统中直接从企查查平台获取客户、担保人真实信息。

#### 功能需求说明

在客户登记中录入客户名称后，点击旁边的下载按钮可获取客户工商信息；

支持模糊查询，若根据客户名称查询到多家，弹出查询的列表，客户手动选择正确的一家，获取其工商信息；

#### 系统实现

参数配置原型：

界面原型：

### 反洗钱对接

#### 业务场景

执行项目时，业务经理需要对客户进行反洗钱风险等级分析，系统将客户信息传输至反洗钱系统，反洗钱系统输出本次评定风险等级及下次评定有效期。

#### 功能需求说明

项目立项时，用户在提交项目立项信息时候，需调用反洗钱接口传输承租方客户信息至反洗钱接口中，反洗钱接口返回该承租方客户的本次评定风险等级及下次评定有效期；

项目申报时，用户在提交项目申报信息时候，需调用反洗钱接口传输承租方客户信息至反洗钱接口中，反洗钱接口返回该承租方客户的本次评定风险等级及下次评定有效期；

反洗钱等级可随时在业务单据中通过操作按钮的方式通过反洗钱接口获取该承租方客户的反洗钱等级，并将该承租方客户的反洗钱等级信息显示在承租方的客户信息中；

#### 系统实现

### 资金系统对接

#### 业务场景

自动获取银行账户余额，对资金进行管控，在业务系统租赁系统资金预测表中将对接财务系统资金模块的账户管理，自动获取财务系统资金模块账户管理中当前银行资金余额。

#### 标准关键功能说明

业务端通过接口自动获取当前银行的账户余额；

银行账户维护：可通过导入或手工新增方式维护公司业务相关银行账号；

#### 系统实现

### 北金租业务中台接口

#### 业务场景

通过北金租业务中台系统接口，获取北金租业务中台的客户基本信息、客户黑名单、客户信用评级、客户财报信息；同时将本系统的客户基本信息、客户财报信息通过此接口同步至北金租业务中台系统。

#### 功能需求说明

租赁业务系统通过北金租业务中台系统接口定期自动获取客户基本信息，记录至法人客户登记中，再由用户继续完善获取到的法人客户基本信息；

租赁业务系统通过北金租业务中台系统接口定期自动获取客户财报信息，记录至客户财报中，再由用户继续完善获取到的客户财报信息；

租赁业务系统通过北金租业务中台系统接口定期自动获取黑名单信息，记录至本系统的黑名单中，再由用户继续完善获取到的黑名单信息；

租赁业务系统通过北金租业务中台系统接口定期自动获取系统中的承租方客户的信用评级信息，记录至本系统的法人客户的评级信息中；

租赁业务系统定期通过接口向被北金租业务中台系统发送系统的承租方客户基本信息、客户财报信息。

#### 系统实现

### 钉钉接口

#### 业务场景

支持与钉钉移动办公软件集成，利用其消息、待办、移动应用等能力作为业务载体，通过配置即可与系统业务应用无缝集成，用户可以单点登录，随时随地处理业务，让企业的租赁业务高效流转。

#### 功能需求说明

租赁系统中的工作流审批待办事项通过钉钉接口推送至钉钉移动端；钉钉用户在钉钉移动端中查看租赁业务待办事项；

用户在钉钉移动端上点击待办事项后做审批，将审批结果回传至租赁系统；

3.租赁业务系统再将该工作流的下一步骤的待办事项回传至OA系统；租赁业务系统中查看工作流的审批结果、审批状态、流程结束状态（如为工作流程最后一步）。

#### 系统实现

1、配置钉钉登录认证参数（MC）

登录MC（金蝶云•苍穹管理中心），配置钉钉登录认证参数，有两个地方要配置，首先是集群参数配置，在MC【集群管理】-【配置信息】中配置第三方登录认证处理类：

还有环境公共配置项，在MC【集群管理】-【配置信息】中搜索authtication.apps，添加参数值kd.bos.login.thirdauth.app.dingding.DDAuthtication，若有多个值，用英文逗号隔开。

2、配置系统参数

在【系统服务云】-【配置工具】-【系统参数】中启用并配置钉钉参数，如下图所示。

3、同步人员信息

在【系统服务云】-【基础服务】-【人员管理】-【人员】中点击“钉钉同步”，将钉钉中的人员同步到苍穹中。

4、配置消息平台

用administrator登录苍穹，在【系统服务云】-【系统管理】-【消息平台】下的【消息渠道】列表中确定启用“钉钉”渠道。


| 序号 | 步骤 | 参与部门 | 活动描述 | 工作流 | 打印输出 |
| --- | --- | --- | --- | --- | --- |
| 1 | 诉讼申请 | 审计纪检部 | 客户发生逾期未还款时，公司可通过诉讼方式向客户追收相关款项。由审计纪检部在公司内部发起诉讼申请，提交相关人员进行审批。 | 无 | 无 |
| 2 | 诉状 | 审计纪检部 | 审计纪检部向法院提交诉状，记录原告、被告及起诉请求。 | 无 | 无 |
| 3 | 起诉委托代理 | 审计纪检部 | 诉讼过程中，公司可委托代理人进行诉讼，由审计纪检部在系统中记录委托诉讼信息。 | 无 | 无 |
| 4 | 诉讼费用 | 审计纪检部 | 记录诉讼过程中产生的诉讼费用，由审计纪检部登记到系统中。 | 无 | 无 |
| 5 | 财产保全 | 审计纪检部 | 在诉讼过程中，我司向法院申请财产保全，由审计纪检部中系统中记录财产保全内容。 | 无 | 无 |
| 6 | 法院受理 | 审计纪检部 | 法院受理后，审计纪检部记录案件编号、受理状态（一审、二审、再审）、法院受理编号、开庭日期。 | 无 | 无 |
| 7 | 案件调解 | 审计纪检部 | 诉讼过程中，若被告与我司在法院经调解后达成调解，可在系统中记录案件调解情况。 | 无 | 无 |
| 8 | 案件撤诉 | 审计纪检部 | 法院受理后，由于各种原因需要撤诉，由审计纪检部登记撤诉及撤诉原因。 | 无 | 无 |
| 9 | 法院判决 | 审计纪检部 | 我司在收到法院的裁判文书后，由审计纪检部在系统中记录法院判决情况，并将法院判决书上传到系统中，包括一审判决、二审判决、再审判决。 | 无 | 无 |
| 10 | 自动履行 | 审计纪检部 | 法院判决后，若被告自动履行裁判文书确定的义务，由审计纪检部在此记录履行情况。 | 无 | 无 |
| 11 | 执行和解 | 审计纪检部 | 案件执行过程中，被告与我司经沟通后达成和解，由审计纪检部在系统中记录和解情况、和解日期、金额、诉讼保全费用等。 | 无 | 无 |
| 12 | 申请执行 | 审计纪检部 | 若被告未按照法院判决内容自动履行执行义务，我司可向法院申请强制执行，由审计纪检部在系统中记录申请强制执行的相关内容。 | 无 | 无 |
| 13 | 执行情况 | 审计纪检部 | 由审计纪检部记录强制执行后的执行情况。 | 无 | 无 |
| 14 | 案件结案 | 审计纪检部 | 诉讼案件结案后，由审计纪检部在系统中做结案处理。 | 无 | 无 |
