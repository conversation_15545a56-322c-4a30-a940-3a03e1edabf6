"""
AI助手API端点 - 重构版
分离了文档解析和AI问答功能，代码更清晰
"""

from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
import json
import time
import traceback
import logging
import os
from datetime import datetime, date
import decimal
import asyncio
import httpx
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from langchain_community.utilities import SQLDatabase
import re
import base64
import io
from pathlib import Path

# from app.core.security import get_current_user  # 在需要时动态导入
from app.core.logger import get_logger
from app.core.config import settings
from app.services.document_parser import DocumentParser
from app.services.ai_chat import AIChatService
from app.services.knowledge_service import pmo_knowledge_service
from app.services.context_generator import smart_context_generator

logger = get_logger(__name__)
router = APIRouter()

# 创建一个不需要认证的路由器用于文件上传
file_router = APIRouter()

# ==================== 配置和初始化 ====================

# 金投大脑API配置
API_URL = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
BACKUP_API_URLS = [
    "http://**********:8000/v1/chat/completions",
    "http://10.0.10.42:8000/v1/chat/completions",
    "http://10.0.10.43:8000/v1/chat/completions"
]
API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
API_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

current_api_index = 0

def get_current_api_url():
    """获取当前API地址"""
    global current_api_index
    return BACKUP_API_URLS[current_api_index % len(BACKUP_API_URLS)]

def switch_to_next_api():
    """切换到下一个API地址"""
    global current_api_index
    if len(BACKUP_API_URLS) > 1:
        current_api_index = (current_api_index + 1) % len(BACKUP_API_URLS)
        new_api_url = get_current_api_url()
        logger.warning(f"切换到备用API地址: {new_api_url}")
        return new_api_url
    return get_current_api_url()

# 视觉模型配置（专门用于图片识别）
VISION_API_URL = os.getenv("VISION_API_URL", "http://**********:8000/v1/chat/completions")
VISION_API_KEY = os.getenv("VISION_API_KEY", "szjf@2025")
VISION_MODEL = os.getenv("VISION_MODEL", "/models/Qwen3-32B")

# 初始化服务实例
print(f"🔧 初始化DocumentParser，视觉模型配置: {VISION_API_URL}")
document_parser = DocumentParser(
    vision_api_url=VISION_API_URL,
    vision_api_key=VISION_API_KEY,
    vision_model=VISION_MODEL
)
print(f"✅ DocumentParser初始化完成，vision_api_url: {document_parser.vision_api_url}")

ai_chat_service = AIChatService(
    api_url=API_URL,
    api_key=API_KEY,
    model=API_MODEL,
    backup_urls=BACKUP_API_URLS
)

# ==================== 数据库相关功能 ====================

def get_database_connection():
    """获取数据库连接"""
    try:
        # 使用配置文件中的数据库设置
        DB_HOST = settings.DB_HOST
        DB_PORT = settings.DB_PORT
        DB_USER = settings.DB_USER
        DB_PASSWORD = settings.DB_PASSWORD
        DB_NAME = settings.DB_NAME

        connection_string = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        engine = create_engine(
            connection_string,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        logger.info(f"尝试连接数据库: {DB_HOST}:{DB_PORT}/{DB_NAME}")
        return engine
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")

@router.get("/status")
async def get_db_status(
    token: str = Query(None, description="认证令牌"),
    current_user: Dict = None
):
    """获取数据库连接状态"""
    try:
        # 如果提供了token参数，则使用token进行认证
        if token and not current_user:
            from app.core.security import decode_access_token
            try:
                payload = decode_access_token(token)
                if payload:
                    current_user = {"id": payload.get("sub"), "username": payload.get("username")}
            except Exception as e:
                logger.error(f"Token验证失败: {str(e)}")

        # 如果没有认证信息，返回错误
        if not current_user and not token:
            raise HTTPException(status_code=401, detail="未认证，请先登录")

        engine = get_database_connection()
        with engine.connect() as connection:
            result = connection.execute(text("SELECT DATABASE() as db_name"))
            db_name = result.fetchone()[0]
            logger.info(f"成功连接到数据库: {db_name}")
            return {
                "status": "ok",
                "database": db_name,
                "host": settings.DB_HOST,
                "tables_count": 8,  # 从测试中我们知道有8个表
                "timestamp": time.time()
            }
    except Exception as e:
        logger.error(f"数据库状态检查失败: {str(e)}")
        # 返回更详细的错误信息，但不抛出异常
        return {
            "status": "error",
            "message": f"数据库连接失败: {str(e)}",
            "host": settings.DB_HOST,
            "database": settings.DB_NAME,
            "timestamp": time.time()
        }

# ==================== 文件上传API ====================

@file_router.post("/upload-files")
async def upload_files(
    files: List[UploadFile] = File(...)
):
    """
    第一步：上传多个文件并解析内容，返回解析结果给前端显示
    支持Excel、Word、PDF、PPT、图片等格式
    """
    try:
        parsed_files = []

        for file in files:
            # 读取文件内容
            file_content = await file.read()

            # 验证文件大小（限制为10MB）
            if len(file_content) > 10 * 1024 * 1024:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件大小超过10MB限制",
                    "success": False
                })
                continue

            # 验证文件内容不为空
            if not file_content or len(file_content) == 0:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件内容为空",
                    "success": False
                })
                continue

            # 解析文档内容
            parsed_content = document_parser.parse_document(file_content, file.filename, file.content_type)

            # 如果是PDF，需要异步处理
            if parsed_content.startswith("PDF_TO_PROCESS:"):
                pdf_base64 = parsed_content.replace("PDF_TO_PROCESS:", "")
                try:
                    import base64
                    pdf_bytes = base64.b64decode(pdf_base64)
                    parsed_content = await document_parser.parse_pdf_file(pdf_bytes)
                except Exception as e:
                    logger.error(f"PDF异步解析失败: {str(e)}")
                    parsed_content = f"PDF解析失败: {str(e)}"

            # 如果是图片，需要异步处理
            elif parsed_content.startswith("IMAGE_TO_PROCESS:"):
                image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                try:
                    parsed_content = await document_parser.parse_image_with_vision_model(image_base64)
                except Exception as e:
                    parsed_content = f"图片解析失败: {str(e)}"

            # 如果是包含图片的PPT，需要异步处理图片识别
            elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                try:
                    parsed_content = await document_parser.process_ppt_with_images(parsed_content)
                except Exception as e:
                    logger.error(f"PPT图片处理失败: {str(e)}")
                    parsed_content = f"PPT解析失败: 图片处理错误 - {str(e)}"

            # 检查解析是否成功
            success = not parsed_content.startswith("文档解析失败") and not parsed_content.startswith("图片解析失败")

            parsed_files.append({
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(file_content),
                "content": parsed_content,  # 完整内容，前端自己决定显示多少
                "preview": parsed_content[:1000] + "..." if len(parsed_content) > 1000 else parsed_content,  # 预览内容
                "success": success,
                "error": None if success else parsed_content
            })

        return {
            "code": 200,
            "message": "文件解析完成",
            "data": {
                "files_count": len(parsed_files),
                "success_count": len([f for f in parsed_files if f["success"]]),
                "files": parsed_files
            }
        }

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

@file_router.post("/chat-with-files")
async def chat_with_files(request: dict):
    """
    第二步：接收已解析的文件内容和用户问题，调用AI模型回答
    """
    try:
        # 从请求中获取数据
        query = request.get("query", "")
        file_contents = request.get("file_contents", [])

        if not query.strip():
            raise HTTPException(status_code=400, detail="问题不能为空")

        if not file_contents:
            raise HTTPException(status_code=400, detail="没有文件内容")

        # 验证文件内容格式
        validated_contents = []
        for file_data in file_contents:
            if not isinstance(file_data, dict):
                continue

            filename = file_data.get("filename", "未知文件")
            content = file_data.get("content", "")

            if content and not content.startswith("文档解析失败"):
                validated_contents.append({
                    "filename": filename,
                    "content": content
                })

        if not validated_contents:
            raise HTTPException(status_code=400, detail="没有有效的文件内容")

        # 调用AI聊天服务
        result = await ai_chat_service.chat_with_files(query, validated_contents)

        if result["success"]:
            return {
                "code": 200,
                "message": "AI回答成功",
                "data": {
                    "answer": result.get("answer", ""),
                    "files_processed": len(validated_contents),
                    "query": query
                }
            }
        else:
            return {
                "code": 500,
                "message": "AI处理失败",
                "data": {
                    "error": result.get("error", "未知错误"),
                    "files_processed": len(validated_contents),
                    "query": query
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI聊天处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

# ==================== 流式聊天API ====================

async def error_stream(error_message):
    """生成错误消息流"""
    yield f"data: {json.dumps({'error': error_message})}\n\n"
    yield f"data: [DONE]\n\n"

@router.get("/chat/stream")
async def stream_chat(
    query: str = Query(..., description="用户查询"),
    token: str = Query(None, description="认证令牌"),
    current_user: Dict = None
):
    """流式聊天接口"""
    try:
        # 如果提供了token参数，则使用token进行认证
        if token and not current_user:
            from app.core.security import decode_access_token
            try:
                payload = decode_access_token(token)
                if payload:
                    current_user = {"id": payload.get("sub"), "username": payload.get("username")}
            except Exception as e:
                logger.error(f"Token验证失败: {str(e)}")

        # 如果没有认证信息，返回错误流
        if not current_user and not token:
            return StreamingResponse(
                error_stream("未认证，请先登录"),
                media_type="text/plain"
            )

        # 使用知识库进行智能问答
        return StreamingResponse(
            knowledge_based_chat(query),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        logger.error(f"流式聊天错误: {str(e)}")
        return StreamingResponse(
            error_stream(f"处理失败: {str(e)}"),
            media_type="text/event-stream"
        )

@router.get("/knowledge/status")
async def get_knowledge_status(token: str = Query(..., description="认证token")):
    """
    获取知识库状态
    """
    try:
        # 验证token
        from app.core.auth import verify_token
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的token")

        # 获取知识库状态
        cache_info = {}
        for key, timestamp in pmo_knowledge_service.last_update.items():
            cache_info[key] = {
                "last_update": datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                "age_seconds": int(time.time() - timestamp)
            }

        # 检查知识库是否可用
        knowledge_available = bool(pmo_knowledge_service.knowledge_cache.get('formatted_knowledge'))

        return {
            "status": "available" if knowledge_available else "not_ready",
            "cache_info": cache_info,
            "knowledge_size": len(pmo_knowledge_service.knowledge_cache.get('formatted_knowledge', '')),
            "entities_count": len(pmo_knowledge_service.knowledge_cache.get('entity_stats', {}))
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/knowledge/refresh")
async def refresh_knowledge(token: str = Query(..., description="认证token")):
    """
    刷新知识库
    """
    try:
        # 验证token
        from app.core.auth import verify_token
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的token")

        logger.info("手动刷新知识库...")

        # 强制刷新知识库
        knowledge = pmo_knowledge_service.get_knowledge_base(force_refresh=True)

        return {
            "status": "success",
            "message": "知识库刷新完成",
            "knowledge_size": len(knowledge),
            "refresh_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新知识库失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/knowledge/view")
async def view_knowledge(token: str = Query(..., description="认证token")):
    """
    查看知识库内容
    """
    try:
        # 验证token
        from app.core.auth import verify_token
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的token")

        # 获取知识库内容
        knowledge_content = pmo_knowledge_service.get_knowledge_base()

        # 获取详细的缓存信息
        cache_details = {}
        for key, data in pmo_knowledge_service.knowledge_cache.items():
            if isinstance(data, dict):
                cache_details[key] = {
                    "type": "dict",
                    "size": len(data),
                    "keys": list(data.keys()) if len(data) < 20 else f"{len(data)} keys"
                }
            elif isinstance(data, list):
                cache_details[key] = {
                    "type": "list",
                    "size": len(data),
                    "sample": data[:3] if len(data) > 0 else []
                }
            else:
                cache_details[key] = {
                    "type": type(data).__name__,
                    "size": len(str(data)) if hasattr(data, '__len__') else 'N/A'
                }

        return {
            "status": "success",
            "knowledge_content": knowledge_content,
            "cache_details": cache_details,
            "content_length": len(knowledge_content),
            "last_update": {
                key: datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                for key, timestamp in pmo_knowledge_service.last_update.items()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查看知识库失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/knowledge/clear")
async def clear_knowledge_cache(token: str = Query(..., description="认证token")):
    """
    清空知识库缓存
    """
    try:
        # 验证token
        from app.core.auth import verify_token
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的token")

        logger.info("手动清空知识库缓存...")

        # 记录清理前的状态
        old_cache_size = len(pmo_knowledge_service.knowledge_cache)
        old_memory_size = pmo_knowledge_service._calculate_cache_size()

        # 清空缓存
        pmo_knowledge_service.clear_cache()

        return {
            "status": "success",
            "message": "知识库缓存已清空",
            "cleared_items": old_cache_size,
            "freed_memory_mb": round(old_memory_size / 1024 / 1024, 2),
            "clear_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空知识库缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/knowledge/size")
async def get_knowledge_size(token: str = Query(..., description="认证token")):
    """
    获取知识库大小信息
    """
    try:
        # 验证token
        from app.core.auth import verify_token
        user_info = verify_token(token)
        if not user_info:
            raise HTTPException(status_code=401, detail="无效的token")

        # 计算缓存大小
        cache_size_bytes = pmo_knowledge_service._calculate_cache_size()
        cache_size_mb = cache_size_bytes / 1024 / 1024
        max_size_mb = pmo_knowledge_service.max_cache_size / 1024 / 1024

        # 获取各个缓存项的大小
        cache_breakdown = {}
        for key, value in pmo_knowledge_service.knowledge_cache.items():
            if isinstance(value, str):
                size = len(value.encode('utf-8'))
            elif isinstance(value, (dict, list)):
                import json
                size = len(json.dumps(value, ensure_ascii=False).encode('utf-8'))
            else:
                size = len(str(value).encode('utf-8'))

            cache_breakdown[key] = {
                "size_bytes": size,
                "size_mb": round(size / 1024 / 1024, 3)
            }

        return {
            "status": "success",
            "total_size_mb": round(cache_size_mb, 2),
            "max_size_mb": round(max_size_mb, 2),
            "usage_percentage": round((cache_size_mb / max_size_mb) * 100, 1),
            "cache_items": len(pmo_knowledge_service.knowledge_cache),
            "cache_breakdown": cache_breakdown,
            "needs_cleanup": cache_size_bytes > pmo_knowledge_service.max_cache_size
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库大小失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 基于知识库的聊天函数
async def knowledge_based_chat(query):
    """基于PMO知识库的智能聊天"""
    try:
        logger.info(f"开始处理知识库查询: {query[:50]}...")

        yield f"data: {json.dumps({'content': '正在分析您的问题...'})}\n\n"

        # 1. 生成智能上下文
        context = smart_context_generator.generate_context(query)
        logger.info(f"上下文生成完成，长度: {len(context)} 字符")

        yield f"data: {json.dumps({'content': '正在查询PMO系统数据...'})}\n\n"

        # 2. 构建完整的提示词（上下文 + 用户问题）
        full_prompt = f"""{context}

---
用户问题：{query}

请基于上述PMO系统数据准确回答用户问题。"""

        logger.info(f"完整提示词构建完成，长度: {len(full_prompt)} 字符")

        # 3. 调用AI生成回答
        async for content in stream_ai_response(full_prompt):
            yield f"data: {json.dumps({'content': content})}\n\n"

        yield f"data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"知识库聊天错误: {str(e)}")
        yield f"data: {json.dumps({'content': f'处理失败：{str(e)}'})}\n\n"
        yield f"data: [DONE]\n\n"

# 数据库查询相关函数（保留作为备用）
async def db_stream_chat(query):
    """处理数据库流式聊天"""
    try:
        logger.info(f"开始处理数据库查询: {query[:50]}...")

        # 连接数据库
        db_engine = get_db_engine()
        if not db_engine:
            yield f"data: {json.dumps({'content': '数据库连接失败，请检查配置'})}\n\n"
            yield f"data: [DONE]\n\n"
            return

        # 获取数据库表结构信息
        db_info = get_db_table_info(db_engine)
        logger.info(f"获取到数据库表结构信息: {len(db_info)} 字符")

        # 首先判断是否是数据库相关问题
        if is_database_query(query):
            # 生成SQL查询 - 使用改进的提示词
            sql_prompt = f"""你是一个SQL专家，需要根据用户的问题生成MySQL查询语句。

数据库信息:
{db_info}

系统中的投资主体/业务类型包括：
- 汽租
- 商租
- 金租
- 征信
- 资管
- 集团战略部
- 集团财务部
- 小贷
- 集团
- 不动产
- 担保
- 集团保全部
- 集团风控部
- 集团办公室
- 集团人力部
- 集团协同部
- 金服
- 外部主体
- 集团法规部
- 集团审计部
- 保理
- 财险

用户问题: {query}

查询处理指南:
1. 优先识别用户提到的投资主体（如上述列表中的项目），这是查询的核心关键信息
2. 如果用户提到了具体投资主体，请务必在查询中加入相关条件
3. 如果用户没有明确提及投资主体，但查询内容与特定投资主体相关，应该返回所有投资主体的相关数据
4. 其次识别项目名称、项目状态、时间范围等其他条件
5. 使用适当的JOIN连接相关表，确保数据完整性

SQL生成规则:
1. 只返回SQL查询语句，不要包含任何解释、思考过程或其他文本
2. 确保SQL语法正确，符合MySQL语法
3. 只使用表中存在的列
4. 使用适当的WHERE条件、JOIN和聚合函数
5. 限制结果数量，避免返回过多数据（使用LIMIT 50）
6. 如果用户问题无法通过SQL回答(比如问候、闲聊等)，只返回CANNOT_GENERATE_SQL
7. 不要使用<think>标签或其他XML/HTML标签

SQL查询(只返回SQL语句，不要有任何其他文本):"""
        else:
            # 非数据库问题，直接用AI回答
            yield f"data: {json.dumps({'content': '正在为您回答问题...'})}\n\n"

            general_prompt = f"""
你是PMO数据库助手，用户问了一个非数据库查询的问题。

用户问题: {query}

请友好地回答用户的问题，使用Markdown格式输出。如果是关于你的身份，请介绍你是PMO数据库助手，可以帮助查询项目数据、统计信息、投资主体信息等。

回答要求：
1. 使用Markdown格式
2. 语言友好、专业
3. 如果是问候，简洁回应
4. 如果询问功能，详细介绍能力
"""

            async for content in stream_ai_response(general_prompt):
                yield f"data: {json.dumps({'content': content})}\n\n"

            yield f"data: [DONE]\n\n"
            return

        yield f"data: {json.dumps({'content': '正在分析您的问题...'})}\n\n"

        # 调用AI生成SQL
        sql = await generate_sql_query(sql_prompt)

        if not sql or sql.strip() == "CANNOT_GENERATE_SQL":
            # 无法生成SQL，直接用AI回答
            yield f"data: {json.dumps({'content': '正在为您查找相关信息...'})}\n\n"

            general_prompt = f"""
你是一个数据库助手，用户问了一个问题，但无法转换为SQL查询。

用户问题: {query}

请礼貌地回答用户，如果这是关于数据库的问题，告诉用户需要更具体的信息；
如果是一般性问题，直接回答；如果是问候，友好地回应。
"""

            async for content in stream_ai_response(general_prompt):
                yield f"data: {json.dumps({'content': content})}\n\n"

            yield f"data: [DONE]\n\n"
            return

        logger.info(f"生成的SQL: {sql}")
        sql_content = f"生成的SQL查询：\n```sql\n{sql}\n```\n\n正在执行查询..."
        yield f"data: {json.dumps({'content': sql_content})}\n\n"

        # 执行SQL查询
        try:
            results = execute_sql_query(db_engine, sql)

            if isinstance(results, str) and "错误" in results:
                yield f"data: {json.dumps({'content': f'查询执行失败：{results}'})}\n\n"
            else:
                # 生成智能回答
                if results:
                    # 使用AI生成基于查询结果的回答
                    yield f"data: {json.dumps({'content': '正在分析查询结果...'})}\n\n"

                    answer_prompt = f"""基于以下信息回答用户问题，使用Markdown格式输出。

用户问题: {query}
SQL查询: {sql}
SQL结果: {json.dumps(results[:10], ensure_ascii=False)}

回答要求:
1. 使用Markdown格式，包含表格展示数据
2. 先简要回答用户问题
3. 然后用表格展示关键数据
4. 如果数据较多，只显示前10条并说明总数
5. 语言要专业、准确、友好

请基于查询结果详细回答用户问题："""

                    async for content in stream_ai_response(answer_prompt):
                        yield f"data: {json.dumps({'content': content})}\n\n"
                else:
                    no_data_message = "## 查询结果\n\n查询完成，但没有找到匹配的数据。\n\n💡 **建议：**\n- 检查查询条件是否正确\n- 尝试使用不同的关键词\n- 确认数据是否存在"
                    yield f"data: {json.dumps({'content': no_data_message})}\n\n"

        except Exception as e:
            logger.error(f"SQL执行错误: {str(e)}")
            yield f"data: {json.dumps({'content': f'查询执行失败：{str(e)}'})}\n\n"

        yield f"data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"数据库流式聊天错误: {str(e)}")
        yield f"data: {json.dumps({'content': f'处理失败：{str(e)}'})}\n\n"
        yield f"data: [DONE]\n\n"

def get_db_engine():
    """获取数据库引擎"""
    try:
        from app.core.config import settings

        # 构建数据库连接字符串
        if settings.DB_TYPE == "mysql":
            db_url = f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
        else:
            logger.error(f"不支持的数据库类型: {settings.DB_TYPE}")
            return None

        engine = create_engine(db_url, echo=False)
        return engine

    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return None

def get_db_table_info(engine):
    """获取数据库表结构信息"""
    try:
        with engine.connect() as conn:
            # 获取所有表名
            tables_query = "SHOW TABLES"
            tables_result = conn.execute(text(tables_query))
            tables = [row[0] for row in tables_result]

            db_info = "数据库表结构信息：\\n\\n"

            # 获取每个表的结构
            for table in tables[:10]:  # 限制表数量避免信息过多
                try:
                    desc_query = f"DESCRIBE {table}"
                    desc_result = conn.execute(text(desc_query))

                    db_info += f"表名: {table}\\n"
                    db_info += "字段信息:\\n"

                    for row in desc_result:
                        field_name = row[0]
                        field_type = row[1]
                        is_null = row[2]
                        key = row[3]
                        default = row[4]

                        db_info += f"  - {field_name}: {field_type}"
                        if key == "PRI":
                            db_info += " (主键)"
                        if is_null == "NO":
                            db_info += " (非空)"
                        db_info += "\\n"

                    db_info += "\\n"

                except Exception as e:
                    logger.error(f"获取表 {table} 结构失败: {str(e)}")
                    continue

            return db_info

    except Exception as e:
        logger.error(f"获取数据库表信息失败: {str(e)}")
        return "无法获取数据库表结构信息"

async def generate_sql_query(prompt):
    """调用AI生成SQL查询"""
    try:
        from app.core.config import settings

        # 使用金投大脑API生成SQL
        api_url = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
        api_key = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
        model = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1,
            "max_tokens": 1000
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(api_url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                sql = result["choices"][0]["message"]["content"].strip()

                # 清理SQL语句
                sql = clean_sql_query(sql)

                # 验证SQL是否有效
                if sql and not sql.upper().startswith(("SELECT", "SHOW", "DESCRIBE", "EXPLAIN")):
                    logger.warning(f"生成的SQL可能不安全: {sql}")
                    return None

                return sql
            else:
                logger.error(f"AI API调用失败: {response.status_code}")
                return None

    except Exception as e:
        logger.error(f"生成SQL查询失败: {str(e)}")
        return None

async def generate_answer(prompt):
    """调用AI生成回答"""
    try:
        # 使用金投大脑API生成回答
        api_url = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
        api_key = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
        model = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 4000
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(api_url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                return content
            else:
                logger.error(f"AI API调用失败: {response.status_code}")
                return "抱歉，生成回答时出现错误。"

    except Exception as e:
        logger.error(f"生成回答失败: {str(e)}")
        return "抱歉，生成回答时出现错误。"

async def stream_ai_response(prompt):
    """流式调用AI生成回答"""
    try:
        from app.core.config import settings

        api_url = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
        api_key = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
        model = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": True
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream("POST", api_url, headers=headers, json=data) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]
                            if data_str.strip() == "[DONE]":
                                break
                            try:
                                data_json = json.loads(data_str)
                                if "choices" in data_json and len(data_json["choices"]) > 0:
                                    delta = data_json["choices"][0].get("delta", {})
                                    content = delta.get("content", "")
                                    if content:
                                        yield content
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"AI服务调用失败: {response.status_code}"

    except Exception as e:
        logger.error(f"流式AI回答失败: {str(e)}")
        yield f"AI服务出现错误: {str(e)}"

def execute_sql_query(engine, sql):
    """执行SQL查询"""
    try:
        with engine.connect() as conn:
            # 安全检查：只允许SELECT、SHOW、DESCRIBE、EXPLAIN语句
            sql_upper = sql.upper().strip()
            if not sql_upper.startswith(("SELECT", "SHOW", "DESCRIBE", "EXPLAIN")):
                return "错误：只允许执行查询语句（SELECT、SHOW、DESCRIBE、EXPLAIN）"

            result = conn.execute(text(sql))

            # 获取列名
            columns = list(result.keys()) if hasattr(result, 'keys') else []

            # 获取数据
            rows = result.fetchall()

            # 转换为字典列表
            data = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    column_name = columns[i] if i < len(columns) else f"column_{i}"

                    # 处理特殊数据类型
                    if isinstance(value, decimal.Decimal):
                        row_dict[column_name] = float(value)
                    elif isinstance(value, (datetime, date)):
                        row_dict[column_name] = str(value)
                    else:
                        row_dict[column_name] = value

                data.append(row_dict)

            logger.info(f"SQL查询成功，返回 {len(data)} 行数据")
            return data

    except SQLAlchemyError as e:
        error_msg = f"SQL执行错误: {str(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"查询执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

def format_query_results(results):
    """格式化查询结果为Markdown表格"""
    try:
        if not results:
            return "查询结果为空"

        if isinstance(results, str):
            return results

        # 获取列名
        columns = list(results[0].keys()) if results else []

        if not columns:
            return "查询结果格式错误"

        # 构建Markdown表格
        markdown = "| " + " | ".join(columns) + " |\n"
        markdown += "| " + " | ".join(["---"] * len(columns)) + " |\n"

        # 添加数据行
        for row in results[:50]:  # 限制显示50行
            values = []
            for col in columns:
                value = row.get(col, "")
                # 处理None值和特殊字符
                if value is None:
                    value = ""
                else:
                    value = str(value).replace("|", "\\|").replace("\n", " ")
                values.append(value)

            markdown += "| " + " | ".join(values) + " |\n"

        # 如果结果超过50行，添加提示
        if len(results) > 50:
            markdown += f"\n*注：共 {len(results)} 行数据，仅显示前50行*"

        return markdown

    except Exception as e:
        logger.error(f"格式化查询结果失败: {str(e)}")
        return f"结果格式化失败: {str(e)}"

def is_database_query(query):
    """判断是否是数据库查询问题"""
    query_lower = query.lower()

    # 数据库查询关键词
    db_keywords = [
        '查询', '查找', '查看', '搜索', '统计', '计算', '分析',
        '多少', '几个', '数量', '总数', '合计', '平均',
        '项目', '用户', '数据', '记录', '信息', '列表',
        '最新', '最近', '今天', '昨天', '本月', '上月',
        '状态', '进度', '完成', '未完成', '逾期',
        '部门', '团队', '人员', '负责人'
    ]

    # 非数据库查询关键词
    non_db_keywords = [
        '你是', '什么模型', '介绍', '功能', '帮助',
        '你好', '谢谢', '再见', '怎么用', '如何使用'
    ]

    # 检查非数据库关键词
    for keyword in non_db_keywords:
        if keyword in query_lower:
            return False

    # 检查数据库关键词
    for keyword in db_keywords:
        if keyword in query_lower:
            return True

    # 默认认为是数据库查询
    return True


# ==================== 数据库聊天API ====================

@router.post("/db-chat")
async def db_chat(request: Request):
    """数据库聊天API - 完全参考SQL目录的实现"""
    try:
        # 解析请求数据
        body = await request.json()
        user_message = body.get("message", "").strip()

        if not user_message:
            raise HTTPException(status_code=400, detail="消息不能为空")

        logger.info(f"收到数据库查询请求: {user_message}")

        # 获取数据库连接
        db = get_db_connection()

        # 获取数据库表结构信息
        db_info = db.get_table_info()

        # 生成SQL查询
        sql_prompt = CHINESE_SQL_PROMPT.format(db_info=db_info, query=user_message)
        sql = await generate_sql_query(sql_prompt)

        if not sql:
            return {
                "response": "无法为您的问题生成有效的SQL查询，请尝试更明确的问题。",
                "sql": None,
                "results": None
            }

        # 执行SQL查询
        results = execute_sql(db, sql)
        if isinstance(results, str) and "错误" in results:
            return {
                "response": f"SQL执行出错: {results}",
                "sql": sql,
                "results": None
            }

        # 构造回答提示
        answer_prompt = f"""基于以下信息回答用户问题。

        用户问题: {user_message}
        SQL查询: {sql}
        SQL结果: {json.dumps(results, ensure_ascii=False)}

        如果SQL结果为空或无法回答用户问题，请礼貌地告知无法找到相关信息。
        如果SQL结果有数据，请基于这些数据详细回答用户问题，并以表格形式展示关键数据。
        """

        # 生成回答
        response_text = await generate_answer(answer_prompt)

        return {
            "response": response_text,
            "sql": sql,
            "results": results
        }

    except Exception as e:
        logger.error(f"数据库聊天API错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"处理请求时发生错误: {str(e)}")


async def generate_sql_from_question(question: str) -> Optional[str]:
    """根据问题生成SQL查询"""
    try:
        # 获取数据库表结构信息
        table_info = await get_database_schema()

        # 构建提示词
        prompt = CHINESE_SQL_PROMPT.format(db_info=table_info, query=question)

        # 调用AI生成SQL
        response = await generate_sql_query(prompt)

        if not response:
            return None

        # 验证SQL安全性
        if not is_safe_sql(response):
            logger.warning(f"不安全的SQL查询被拒绝: {response}")
            return None

        return response

    except Exception as e:
        logger.error(f"生成SQL查询失败: {str(e)}")
        return None


def get_db_connection():
    """创建数据库连接"""
    try:
        connection_string = f"mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
        db = SQLDatabase.from_uri(connection_string)
        logger.info(f"成功连接到数据库: {settings.DB_NAME}")
        return db
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库连接失败: {str(e)}")

async def get_database_schema() -> str:
    """获取数据库表结构信息"""
    try:
        db = get_db_connection()
        db_info = db.get_table_info()
        return db_info
    except Exception as e:
        logger.error(f"获取数据库结构失败: {str(e)}")
        return "无法获取数据库结构信息"


# 自定义中文SQL提示模板
CHINESE_SQL_PROMPT = """你是一个SQL专家，需要根据用户的问题生成MySQL查询语句。

数据库信息:
{db_info}

用户问题: {query}

根据用户问题，生成一个有效的SQL查询。注意：
1. 只返回SQL查询语句，不要包含任何解释、思考过程或其他文本
2. 确保SQL语法正确，符合MySQL语法
3. 只使用表中存在的列
4. 使用适当的WHERE条件、JOIN和聚合函数
5. 限制结果数量，避免返回过多数据
6. 如果用户问题无法通过SQL回答(比如问候、闲聊等)，只返回NULL，不要返回任何其他内容
7. 不要使用<think>标签或其他XML/HTML标签

SQL查询(只返回SQL语句，不要有任何其他文本):"""

def clean_sql_query(sql_text: str) -> str:
    """清理SQL查询语句"""
    if not sql_text:
        return ""

    # 移除<think>标签及其内容
    if "<think>" in sql_text:
        sql_text = re.sub(r'<think>[\s\S]*?</think>', '', sql_text)

    # 尝试提取SQL代码块
    if "```sql" in sql_text:
        sql_text = sql_text.split("```sql")[1].split("```")[0].strip()
    elif "```" in sql_text:
        sql_text = sql_text.split("```")[1].split("```")[0].strip()

    # 处理NULL返回
    if sql_text.strip().upper() == "NULL":
        logger.info("模型返回NULL，表示无法生成有效的SQL查询")
        return ""

    # 确保SQL不为空
    sql_text = sql_text.strip()
    if not sql_text:
        logger.info("生成的SQL为空")
        return ""

    logger.info(f"成功清理SQL: {sql_text}")
    return sql_text


def is_safe_sql(sql: str) -> bool:
    """检查SQL查询的安全性"""
    if not sql:
        return False

    sql_upper = sql.upper().strip()

    # 只允许SELECT查询
    if not sql_upper.startswith('SELECT'):
        return False

    # 禁止的关键词
    forbidden_keywords = [
        'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE',
        'TRUNCATE', 'REPLACE', 'GRANT', 'REVOKE', 'EXEC', 'EXECUTE'
    ]

    for keyword in forbidden_keywords:
        if keyword in sql_upper:
            return False

    return True


def execute_sql(db, sql):
    """执行SQL查询并返回结果"""
    try:
        if not sql or sql.strip().lower() == "null":
            return "无法生成有效的SQL查询"

        # 安全检查
        sql = sql.strip()
        if any(keyword in sql.lower() for keyword in ["drop", "delete", "truncate", "update", "insert", "alter", "create"]):
            return "出于安全原因，不允许执行修改数据库的操作"

        # 执行查询
        with db._engine.connect() as connection:
            result = connection.execute(text(sql))
            columns = result.keys()
            rows = [dict(zip(columns, row)) for row in result.fetchall()]

            # 限制返回的行数
            if len(rows) > 50:
                rows = rows[:50]
                rows.append({"注意": f"结果已截断，共有{len(rows)}行"})

            return rows
    except Exception as e:
        logger.error(f"SQL执行错误: {str(e)}, SQL: {sql}")
        return f"SQL执行错误: {str(e)}"

async def execute_sql_query(sql: str) -> Optional[List[Dict]]:
    """执行SQL查询"""
    try:
        db = get_db_connection()
        result = execute_sql(db, sql)

        if isinstance(result, str):
            # 如果返回字符串，说明有错误
            logger.error(f"SQL执行失败: {result}")
            return None

        return result

    except Exception as e:
        logger.error(f"执行SQL查询失败: {str(e)}")
        return None


async def generate_response_from_results(question: str, sql: str, results: Optional[List[Dict]]) -> str:
    """根据查询结果生成回答"""
    try:
        if results is None:
            return "抱歉，查询执行失败，请检查您的问题或稍后重试。"

        if len(results) == 0:
            return "查询执行成功，但没有找到符合条件的数据。"

        # 构建结果摘要
        result_count = len(results)

        if result_count <= 5:
            # 少量结果，可以详细描述
            summary = f"查询成功！找到了 {result_count} 条记录。"
        else:
            # 大量结果，只给出统计信息
            summary = f"查询成功！找到了 {result_count} 条记录，以下是查询结果的统计信息。"

        return summary

    except Exception as e:
        logger.error(f"生成回答失败: {str(e)}")
        return "查询完成，但生成回答时出现错误。"
