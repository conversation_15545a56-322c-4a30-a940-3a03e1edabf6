-- 更新督办管理表结构，添加软删除字段和操作日志表
-- 执行此脚本来更新现有数据库

-- 添加软删除字段到督办事项表
ALTER TABLE supervision_items
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER updated_at;

-- 添加索引
ALTER TABLE supervision_items
ADD INDEX idx_deleted_at (deleted_at);

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS supervision_operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operation_type ENUM('IMPORT', 'EXPORT', 'CREATE', 'UPDATE', 'DELETE') NOT NULL COMMENT '操作类型',
    operation_details JSON COMMENT '操作详情',
    file_name VARCHAR(255) COMMENT '文件名（导入导出时）',
    affected_records INT DEFAULT 0 COMMENT '影响的记录数',
    success_count INT DEFAULT 0 COMMENT '成功处理数量',
    error_count INT DEFAULT 0 COMMENT '错误数量',
    error_details TEXT COMMENT '错误详情',
    operator VARCHAR(50) DEFAULT 'system' COMMENT '操作人',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办管理操作日志表';

-- 更新现有查询，确保排除已删除的记录
-- 注意：这个脚本只是添加字段，应用程序代码已经更新了查询逻辑
