#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工时管理API路由
提供工时录入、查询、统计等功能的RESTful接口
"""

from fastapi import APIRouter, Query, Depends, HTTPException, Body
from typing import Optional, List
from pydantic import BaseModel
import json

# 导入云函数处理器
from app.cloud_functions.project_hours_management.index import main_handler
from app.core.security import get_current_user

router = APIRouter()

# 请求模型定义
class HoursData(BaseModel):
    project_code: str
    project_name: str
    investment_entity: str = ""
    hours_current: float = 0
    current_record_exists: bool = False
    hours_current_original: float = 0
    hours_prev1: float = 0
    hours_prev2: float = 0
    hours_prev3: float = 0
    hours_prev4: float = 0
    hours_prev5: float = 0
    prev1_record_exists: bool = False
    prev2_record_exists: bool = False
    prev3_record_exists: bool = False
    prev4_record_exists: bool = False
    prev5_record_exists: bool = False
    hours_prev1_original: float = 0
    hours_prev2_original: float = 0
    hours_prev3_original: float = 0
    hours_prev4_original: float = 0
    hours_prev5_original: float = 0

class SaveHoursRequest(BaseModel):
    UserID: str
    name: str
    year: int
    month: int
    hoursData: List[HoursData]
    department: str = ""
    prevYear1: Optional[int] = None
    prevMonth1: Optional[int] = None
    prevYear2: Optional[int] = None
    prevMonth2: Optional[int] = None
    prevYear3: Optional[int] = None
    prevMonth3: Optional[int] = None
    prevYear4: Optional[int] = None
    prevMonth4: Optional[int] = None
    prevYear5: Optional[int] = None
    prevMonth5: Optional[int] = None

@router.get("/hours")
def get_project_hours(
    user_id: str = Query(..., description="员工ID"),
    year: int = Query(..., description="年份"),
    month: int = Query(..., description="月份"),
    prev_year1: Optional[int] = Query(None, description="上个月年份"),
    prev_month1: Optional[int] = Query(None, description="上个月月份"),
    prev_year2: Optional[int] = Query(None, description="上上个月年份"),
    prev_month2: Optional[int] = Query(None, description="上上个月月份"),
    prev_year3: Optional[int] = Query(None, description="第三个月年份"),
    prev_month3: Optional[int] = Query(None, description="第三个月月份"),
    prev_year4: Optional[int] = Query(None, description="第四个月年份"),
    prev_month4: Optional[int] = Query(None, description="第四个月月份"),
    prev_year5: Optional[int] = Query(None, description="第五个月年份"),
    prev_month5: Optional[int] = Query(None, description="第五个月月份"),
    show_all_projects: bool = Query(False, description="是否显示所有项目"),
    current_user = Depends(get_current_user)
):
    """
    获取员工工时数据
    """
    try:
        # 构造云函数需要的参数
        event_data = {
            "function": "get_project_hours_for_employee",
            "action": "getProjectHoursForEmployee",
            "params": {
                "UserID": user_id,
                "year": year,
                "month": month,
                "prevYear1": prev_year1,
                "prevMonth1": prev_month1,
                "prevYear2": prev_year2,
                "prevMonth2": prev_month2,
                "prevYear3": prev_year3,
                "prevMonth3": prev_month3,
                "prevYear4": prev_year4,
                "prevMonth4": prev_month4,
                "prevYear5": prev_year5,
                "prevMonth5": prev_month5,
                "showAllProjects": show_all_projects
            }
        }
        
        # 调用云函数
        result = main_handler(event_data)
        
        # 检查结果
        if result.get("code") == 200:
            return result
        else:
            raise HTTPException(
                status_code=result.get("code", 500), 
                detail=result.get("message", "获取工时数据失败")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/hours")
def save_project_hours(
    request: SaveHoursRequest,
    current_user = Depends(get_current_user)
):
    """
    保存员工工时数据
    """
    try:
        # 构造云函数需要的参数
        event_data = {
            "function": "save_project_hours",
            "action": "saveProjectHours",
            "params": {
                "UserID": request.UserID,
                "name": request.name,
                "year": request.year,
                "month": request.month,
                "hoursData": [item.dict() for item in request.hoursData],
                "department": request.department,
                "updatedBy": current_user.get("UserID"),
                "prevYear1": request.prevYear1,
                "prevMonth1": request.prevMonth1,
                "prevYear2": request.prevYear2,
                "prevMonth2": request.prevMonth2,
                "prevYear3": request.prevYear3,
                "prevMonth3": request.prevMonth3,
                "prevYear4": request.prevYear4,
                "prevMonth4": request.prevMonth4,
                "prevYear5": request.prevYear5,
                "prevMonth5": request.prevMonth5,
            }
        }
        
        # 调用云函数
        result = main_handler(event_data)
        
        # 检查结果
        if result.get("code") == 200:
            return result
        else:
            raise HTTPException(
                status_code=result.get("code", 500), 
                detail=result.get("message", "保存工时数据失败")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/options")
def get_project_options(
    investment_entity: Optional[str] = Query(None, description="投资主体"),
    current_user = Depends(get_current_user)
):
    """
    获取项目选项
    """
    try:
        # 构造云函数需要的参数
        event_data = {
            "function": "get_project_options",
            "action": "getProjectOptions",
            "params": {
                "investment_entity": investment_entity
            }
        }
        
        # 调用云函数
        result = main_handler(event_data)
        
        # 检查结果
        if result.get("code") == 200:
            return result
        else:
            raise HTTPException(
                status_code=result.get("code", 500), 
                detail=result.get("message", "获取项目选项失败")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/employees/monthly-totals")
def get_employee_monthly_totals(
    months: List[str] = Query(..., description="月份列表，格式：YYYY-MM"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    include_project_details: bool = Query(False, description="是否包含项目详情"),
    current_user = Depends(get_current_user)
):
    """
    获取员工月度工时合计
    """
    try:
        # 构造云函数需要的参数
        event_data = {
            "function": "get_employee_monthly_totals",
            "action": "getEmployeeMonthlyTotals",
            "params": {
                "months": months,
                "userID": user_id,
                "includeProjectDetails": include_project_details
            }
        }
        
        # 调用云函数
        result = main_handler(event_data)
        
        # 检查结果
        if result.get("code") == 200:
            return result
        else:
            raise HTTPException(
                status_code=result.get("code", 500), 
                detail=result.get("message", "获取员工月度工时合计失败")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics/summary")
def get_timesheet_summary(
    year: int = Query(..., description="年份"),
    month: int = Query(..., description="月份"),
    department: Optional[str] = Query(None, description="部门"),
    current_user = Depends(get_current_user)
):
    """
    获取工时统计汇总
    """
    try:
        # 这里可以调用多个云函数来获取统计数据
        # 例如：总工时、项目分布、部门分布等
        
        # 构造月份列表（当前月和前5个月）
        months = []
        current_year = year
        current_month = month
        
        for i in range(6):
            month_str = f"{current_year}-{str(current_month).zfill(2)}"
            months.append(month_str)
            
            # 计算上一个月
            current_month -= 1
            if current_month <= 0:
                current_month = 12
                current_year -= 1
        
        # 获取员工月度统计
        event_data = {
            "function": "get_employee_monthly_totals",
            "action": "getEmployeeMonthlyTotals",
            "params": {
                "months": months,
                "includeProjectDetails": True
            }
        }
        
        result = main_handler(event_data)
        
        if result.get("code") == 200:
            # 处理统计数据
            employees_data = result.get("data", [])
            
            # 计算汇总统计
            summary = {
                "total_employees": len(employees_data),
                "total_hours": 0,
                "department_stats": {},
                "project_stats": {},
                "monthly_trends": {}
            }
            
            for employee in employees_data:
                # 如果指定了部门过滤
                if department and employee.get("department") != department:
                    continue
                
                # 统计部门工时
                dept = employee.get("department", "未分组")
                if dept not in summary["department_stats"]:
                    summary["department_stats"][dept] = {"employees": 0, "hours": 0}
                
                summary["department_stats"][dept]["employees"] += 1
                
                # 统计月度工时
                for month_str, hours in employee.get("monthly_hours", {}).items():
                    summary["total_hours"] += hours
                    summary["department_stats"][dept]["hours"] += hours
                    
                    if month_str not in summary["monthly_trends"]:
                        summary["monthly_trends"][month_str] = 0
                    summary["monthly_trends"][month_str] += hours
                
                # 统计项目工时
                for month_str, projects in employee.get("project_details", {}).items():
                    for project in projects:
                        project_code = project.get("project_code")
                        project_hours = project.get("working_hours", 0)
                        
                        if project_code not in summary["project_stats"]:
                            summary["project_stats"][project_code] = {
                                "project_name": project.get("project_name"),
                                "total_hours": 0,
                                "employees": set()
                            }
                        
                        summary["project_stats"][project_code]["total_hours"] += project_hours
                        summary["project_stats"][project_code]["employees"].add(employee.get("UserID"))
            
            # 转换set为count
            for project_code, stats in summary["project_stats"].items():
                stats["employee_count"] = len(stats["employees"])
                del stats["employees"]
            
            return {
                "code": 200,
                "data": summary,
                "message": "获取工时统计汇总成功"
            }
        else:
            raise HTTPException(
                status_code=result.get("code", 500), 
                detail=result.get("message", "获取工时统计汇总失败")
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
