| 取值单据：法人登记、项目立项 |  |  |  |  |  |  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |  |  |  |  |
| 接口字段 |  |  |  |  |  | 取值规则 |  |  |  |  |  |
| 中文名 | 字段名 | IT必填项 | 类型 | 说明 | 业务必填项 | 取值单据 | 取值字段 | 取值字段备注 | 字段默认格式 | 默认值 | 取值规则备注 |
| 客户号 | csnm | Y | varchar(32) |  |  | 法人登记 | 单据编号 |  |  |  |  |
| 客户名称 | ctnm | Y | varchar(128) |  | Y | 法人登记 | 客户名称 |  |  |  |  |
| 拼音/英文名称 | cten |  | varchar(128) |  |  |  |  |  |  | 传空值 |  |
| 业务类型 | busi_type |  | varchar(10) | 01:一般直租 |  | 项目立项 | 租赁形式 |  |  |  | 直租：取值为  01
回租：取值为  02
厂商租赁：取值为03 |
|  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  | 02:一般回组 |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  | 03:厂商租赁 |  |  |  |  |  |  |  |
| 证件类型 | citp | Y | varchar(32) |  |  | 法人登记 | 证件类型 |  |  |  | 统一社会信用代码：取值为610099
组织机构代码：取值为610001
其他：取值为639999 |
| 证件类型说明 | citp_nt |  | varchar(30) |  |  | 法人登记 | 证件类型 |  |  |  |  |
| 证件号码 | ctid | Y | varchar(50) |  | Y | 法人登记 | 证件号码 |  |  |  |  |
| 证件有效期 | ctid_edt |  | varchar(8) |  | Y | 法人登记 | 证件有效期 | 属于新加字段 | YYYYMMDD |  |  |
| 联系人姓名 | linkman |  | varchar(128) |  | Y | 法人登记 | 主要管理团队分录（取是联系人的分录）-姓名 | 属于新加字段：主要管理团队-是否联系人 |  |  |  |
| 联系人手机号 | linktel |  | varchar(11) |  | Y | 法人登记 | 主要管理团队分录（取是联系人的分录）-联系电话 | 属于新加字段：主要管理团队-是否联系人 |  |  |  |
| 联系人职务 | linkjob |  | varchar(100) |  | Y | 法人登记 | 主要管理团队分录（取是联系人的分录）-职务 | 属于新加字段：主要管理团队-是否联系人 |  |  |  |
| 联系人邮箱 | linkmail |  | varchar(32) |   | Y | 法人登记 | 主要管理团队分录（取是联系人的分录）-邮箱 | 属于新加字段：主要管理团队-是否联系人 |  |  |  |
| 联系人固定电话 | linkphone |  | varchar(20) |  | Y | 法人登记 | 主要管理团队分录（取是联系人的分录）-联系电话 | 属于新加字段：主要管理团队-是否联系人 |  |  |  |
| 承租人的电子邮件 | ceml |  | varchar(32) |  | Y |  |  |  |  | 传空值 |  |
| 承租人的行业类别 | ctvc |  | varchar(32) |  | Y | 法人登记 | 行业小类 | 属于新加字段 |  |  | 取行业小类最末级的4位编码 |
| 承租人的行业代码原值 | indu_code |  | varchar(10) |  |  | 法人登记 | 行业小类 | 属于新加字段 |  |  | 取行业小类最末级的4位编码 |
| 承租人的行业代码原值说明 | indu_code_nt |  | varchar(30) |  |  | 法人登记 | 所属行业、行业小类 |  |  |  | 取从所属行业大类到行业小类4个层级的描述，例：制造业->医药制造业->化学药品制剂制造->化学药品制剂制造 |
| 承租人的法定代表人姓名 | crnm |  | varchar(128) |  | Y | 法人登记 | 法定代表人 |  |  |  |  |
| 承租人的法定代表人身份证件类型 | crit |  | varchar(32) |  | Y | 法人登记 | 法定代表人身份证件类型 | 属于新加字段 |  |  | 身份证  取值：110001
军人证  取值：110007
护照  取值：110028
港澳身份证  取值：119019
台湾回乡证  取值：110021
其他   取值：139999 |
| 承租人的法定代表人证件类型说明 | crit_nt |  | varchar(30) |  | Y | 法人登记 | 法定代表人身份证件类型 | 属于新加字段 |  |  | 身份证  取值：居民身份证
军人证  取值：中国人民解放军军人身份证件
护照  取值：护照
港澳身份证  取值：港澳居民居住证
台湾回乡证  取值：台湾居民来往大陆通行证
其他   取值：其他类个人身份有效证件 |
| 承租人的法定代表人身份证件号码 | crid |  | varchar(50) |  | Y | 法人登记 | 法定代表人证件号 |  |  |  |  |
| 承租人的法定代表人证件有效期 | crid_edt |  | varchar(8) |  | Y | 法人登记 | 法定代表人证件有效期 | 属于新加字段 | YYYYMMDD |  |  |
| 注册国家 | country |  | varchar(32) |  | Y | 法人登记 | 所属区域-国家 |  |  |  | 通过国家和地区基础资料映射，取国家的英文简称 |
| 省 | mer_prov |  | varchar(10) |  | Y | 法人登记 | 所属区域-省 |  |  |  | 通过行政区划基础资料映射，取省级别的参考码 |
| 市 | mer_city |  | varchar(10) |  |  | 法人登记 | 所属区域-市 |  |  |  | 通过行政区划基础资料映射，取市级别的参考码 |
| 区县 | mer_area |  | varchar(10) |  | Y | 法人登记 | 所属区域-区县 |  |  |  | 通过行政区划基础资料映射，取区县级别参考码 |
| 详细地址 | address |  | varchar(200) |  | Y | 法人登记 | 注册地址 |  |  |  |  |
| 联系电话 | tel |  | varchar(20) |  | Y |  |  |  |  |  | 与联系人固定电话一致 |
| 成立日期 | fud_date |  | varchar(20) |  | Y | 法人登记 | 注册日期 |  | YYYYMMDD |  |  |
| 注册资本 | reg_cptl |  | varchar(16) |  | Y | 法人登记 | 注册资金 |  |  |  | 取值：以元为单位，将系统的万元转换为元，两位小数 |
| 注册资本金币种 | reg_cptl_code |  | varchar(3) |  |  |  |  |  |  | 默认：CNY |  |
| 经营范围 | remark_ctvc |  | varchar(600) |  | Y | 法人登记 | 经营范围 |  |  |  | 超长则截取 |
| 企业经济成份 | eecp |  | varchar(3) |  |  | 法人登记 | 客户经济成分（监） | 属于新加字段 |  |  | 取选中的选项的第二级的码值，如A01、A02、B01等 |
| 企业人数规模 | scale |  | char(3) |  | Y | 法人登记 | 从业人数 | 属于新加字段 |  |  | 按人数映射码值：
>=500  取 1
>200 且 <500 取2
>10 且 <=200 取3
<=10 取4 |
| 企业营收规模 | rvn_scale |  | char(3) |  |  | 法人登记 | 上年营业收入（监） | 属于新加字段 |  |  | 按营业收入映射码值：
>=10亿 取 1
>1亿 且 <10亿 取2
>2000万 且 <=1亿 取3
>100万 且 <=2000万 取4
<=100万 取5 |
| 组织机构类别 | crp_type |  | varchar(10) |  | Y | 法人登记 | 组织机构类别 |  |  |  | 企业：取值为1
机关：取值为2
事业单位：取值为3
社会团体：取值为4
个体工商户、其他：取值为5
1:企业
  0-大陆企业，3-中国香港公司，5-中国台湾公司，8-海外公司，

以上取值为1
-------------------------------------------------------------------------------
2:机关
  11-机关单位，

以上取值为2
------------------------------------------------------------------------------
3:事业单位
  4-事业单位

以上取值为3
---------------------------------------------------------------------------------
4:社会团体
   1-社会组织 
   9-律师事务所

以上取值为4
-----------------------------------------------------------------------------------
5:其他组织机构
   6-基金会，7-（民办）医院，，10-（民办）学校 ，-1-其他

以上取值为5
 |
| 企业总资产 | tast |  | decimal(20,2) |  |  | 法人登记 | 总资产（监） | 属于新加字段 |  |  |  |
| 企业总负债-资产负债表的负债合计 | tlbt |  | decimal(20,2) |  |  | 法人登记 | 总负债（监） | 属于新加字段 |  |  |  |
| 经办人姓名 | agency_ctnm |  | varchar(32) |  | Y |  |  |  |  | 传空值 |  |
| 经办人证件类型 | agency_citp |  | varchar(32) |  | Y |  |  |  |  | 传空值 |  |
| 经办人证件类型说明 | agency_citp_nt |  | varchar(30) |  |  |  |  |  |  | 传空值 |  |
| 经办人证件号码 | agency_ctid |  | varchar(50) |  | Y |  |  |  |  | 传空值 |  |
| 经办人证件有效期限 | agency_edt |  | varchar(8) |  |  |  |  |  |  | 传空值 |  |
| 控股股东或实际控制人姓名 | majority_shareholder_ctnm |  | varchar(32) |  |  | 法人登记 | 实际控制人 |  |  |  |  |
| 控股股东或实际控制人证件类型 | majority_shareholder_citp |  | varchar(32) |  |  | 法人登记 | 实际控制人证件类型 | 属于新加字段 |  |  | 如实际控制人类型为企业，则按如下取值：
统一信用代码  取值：610099
组织机构代码 取值：610001
工商注册号  取值：639999
纳税识别号  取值：639999
机关和其他事业单位登记号取值：639999

如实际控制人类型为个人，则按如下取值：
身份证  取值：110001
军人证  取值：110007
护照  取值：110028
港澳身份证  取值：119019
台湾回乡证  取值：110021
其他   取值：139999 |
| 控股股东或实际控制人证件类型说明 | majority_shareholder_citp_nt |  | varchar(30) |  |  | 法人登记 | 实际控制人证件类型 | 属于新加字段 |  |  | 身份证  取值：居民身份证
军人证  取值：中国人民解放军军人身份证件
护照  取值：护照
港澳身份证  取值：港澳居民居住证
台湾回乡证  取值：台湾居民来往大陆通行证
其他   取值：其他类个人身份有效证件 |
| 控股股东或实际控制人证件号码 | majority_shareholder_ctid |  | varchar(50) |  |  | 法人登记 | 实际控制人证件号码 | 属于新加字段 |  |  |  |
| 控股股东或实际控制人证件有效期限 | majority_shareholder_edt |  | varchar(8) |  |  | 法人登记 | 实际控制人证件有效期限 | 属于新加字段 | YYYYMMDD |  |  |
| 负责人姓名 | head_ctnm |  | varchar(32) |  |  |  |  |  |  |  | 与承租人的法定代表人姓名一致 |
| 负责人证件类型 | head_citp |  | varchar(32) |  |  |  |  |  |  |  | 与承租人的法定代表人身份证件类型一致 |
| 负责人证件类型说明 | head_citp_nt |  | varchar(30) |  |  |  |  |  |  |  | 与承租人的法定代表人证件类型说明一致 |
| 负责人证件号码 | head_ctid |  | varchar(50) |  |  |  |  |  |  |  | 与承租人的法定代表人身份证件号码一致 |
| 负责人证件有效期限 | head_edt |  | varchar(8) |  |  |  |  |  | YYYYMMDD |  | 与承租人的法定代表人证件有效期一致 |
| 建立渠道 | certification |  | varchar(6) |  |  | 法人登记 | 建立渠道 |  |  |  | 取客户来源基础资料的码值
面对面渠道 取1
网络渠道-pc注册 取2
网络渠道-手机注册 取3
代理渠道 取4
其他渠道 取9 |
| 注册地IP地址 | ip_code |  | varchar(18) |  |  |  |  |  |  | 传空值 |  |
| 注册设备MAC地址 | mac_info |  | varchar(18) |  |  |  |  |  |  | 传空值 |  |
| 注册设备号 | dev_info |  | varchar(18) |  |  |  |  |  |  | 传空值 |  |
| 境内外标识 | bord_flag |  | char(2) |  |  |  |  |  |  | 11 |  |
| 建立业务日期 | rgdt |  | varchar(8) |  | Y | 法人登记 | 首次建立租赁关系日期 | 属于新加字段 | YYYYMMDD |  | 如首次建立租赁关系日期为空，则传空值 |
| 终止业务日期 | cls_dt |  | varchar(8) |  | Y |  |  |  |  | 传空值 |  |
| 管理部门 | mbrc | Y | varchar(10) |  |  | 项目立项 | 业务部门 |  |  |  | 从人员组织中，取部门对应的行政组织编码 |
| 客户经理 | cmgr |  | varchar(128) |  |  | 项目立项 | 项目经理或业务经理 |  |  |  | 从人员组织中，取登录系统的用户名 |
| 客户风险标签 | reals |  | varchar(3) |  |  |  |  |  |  | 传空值 |  |
| 股权结构复杂度 |  complex |  | varchar(3) |  |  |  |  |  |  | 传空值 |  |
| 股权可辨识度 |  clear |  | varchar(3) |  |  |  |  |  |  | 传空值 |  |
| 备注 | remark |  | varchar(512) |  |  |  |  |  |  | 传空值 |  |
| 关系人 | relation |  | List<Map> | 可传空 |  |  |  |  |  |  |  |