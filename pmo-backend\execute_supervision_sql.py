#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行督办管理SQL脚本
"""

import pymysql

def execute_sql_file():
    """执行SQL文件"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',  # 使用正确的数据库名
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        # 读取SQL文件
        with open('supervision_tables.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("📄 SQL文件读取成功")
        
        # 分割SQL语句（简单分割，按分号分割）
        sql_statements = []
        current_statement = ""
        in_delimiter = False
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue
                
            if line.startswith('DELIMITER'):
                in_delimiter = not in_delimiter
                continue
                
            current_statement += line + '\n'
            
            if not in_delimiter and line.endswith(';'):
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        # 执行SQL语句
        with connection.cursor() as cursor:
            for i, statement in enumerate(sql_statements):
                if statement:
                    try:
                        print(f"📋 执行SQL语句 {i+1}/{len(sql_statements)}")
                        cursor.execute(statement)
                        connection.commit()
                        print(f"✅ SQL语句 {i+1} 执行成功")
                    except Exception as e:
                        print(f"❌ SQL语句 {i+1} 执行失败: {str(e)}")
                        print(f"语句内容: {statement[:100]}...")
                        # 继续执行其他语句
        
        # 检查创建结果
        with connection.cursor() as cursor:
            # 检查表是否创建成功
            cursor.execute("SHOW TABLES LIKE 'supervision_items'")
            if cursor.fetchone():
                print("✅ supervision_items 表创建成功")
            
            cursor.execute("SHOW TABLES LIKE 'companies'")
            if cursor.fetchone():
                print("✅ companies 表创建成功")
            
            cursor.execute("SHOW TABLES LIKE 'company_progress'")
            if cursor.fetchone():
                print("✅ company_progress 表创建成功")
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) as count FROM supervision_items")
            items_count = cursor.fetchone()[0]
            print(f"📊 督办事项数量: {items_count}")
            
            cursor.execute("SELECT COUNT(*) as count FROM companies")
            companies_count = cursor.fetchone()[0]
            print(f"🏢 公司数量: {companies_count}")
            
            cursor.execute("SELECT COUNT(*) as count FROM company_progress")
            progress_count = cursor.fetchone()[0]
            print(f"📈 进度记录数量: {progress_count}")
        
        print("🎉 督办管理表创建完成！")
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    execute_sql_file()
