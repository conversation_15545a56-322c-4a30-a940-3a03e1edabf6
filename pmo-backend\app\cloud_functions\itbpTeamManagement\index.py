#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ITBP团队管理云函数

功能：综合团队成员查询和更新功能
接口：HTTP GET/POST
参数：
  - action: 操作类型 (getMembers/updateMembers)
  - entity: 投资主体
  - [updateMembers时] operations: 包含add/update/delete操作的对象

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "members": [团队成员列表] 或 
    "updated": true/false
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime, date

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 添加自定义JSON编码器处理date类型
class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

# 数据库配置
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', 3306)),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'pmo'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def get_team_members(entity):
    """
    获取团队成员列表
    :param entity: 投资主体
    :return: 团队成员列表
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 查询指定投资主体的团队成员
            cursor.execute("""
                SELECT id, NAME, UserID, staff_category, 
                       start_time, end_time
                FROM itbp
                WHERE investment_entity = %s
                ORDER BY staff_category, NAME
            """, (entity,))
            
            members = cursor.fetchall()
            
            # 处理日期格式
            for member in members:
                if 'start_time' in member and member['start_time']:
                    if isinstance(member['start_time'], (date, datetime)):
                        member['start_time'] = member['start_time'].strftime('%Y-%m-%d')
                
                if 'end_time' in member and member['end_time']:
                    if isinstance(member['end_time'], (date, datetime)):
                        member['end_time'] = member['end_time'].strftime('%Y-%m-%d')
            
            # 去除重复的成员（相同UserID和相同staff_category视为重复）
            unique_members = []
            seen_keys = set()
            
            for member in members:
                # 生成唯一键：UserID + staff_category
                key = f"{member.get('UserID', '')}_{member.get('staff_category', '')}"
                
                if key not in seen_keys:
                    seen_keys.add(key)
                    unique_members.append(member)
            
            # 获取用户详细信息
            for member in unique_members:
                if member['UserID']:
                    try:
                        # 查询用户表获取额外信息
                        cursor.execute("""
                            SELECT company_name, department_name, LaborCost
                            FROM users
                            WHERE UserID = %s
                        """, (member['UserID'],))
                        
                        user_info = cursor.fetchone()
                        if user_info:
                            # 更新成员信息
                            member.update(user_info)
                    except Exception as e:
                        logger.warning(f"获取用户 {member['UserID']} 详细信息失败: {str(e)}")
            
            return unique_members
    except Exception as e:
        logger.error(f"获取团队成员失败: {str(e)}")
        raise e
    finally:
        conn.close()

def update_team_members(entity, operations):
    """
    更新团队成员
    :param entity: 投资主体
    :param operations: 操作数据，包含add/update/delete操作
    :return: 是否更新成功
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 处理添加操作
            if 'add' in operations and operations['add']:
                for member in operations['add']:
                    # 检查是否已存在相同UserID和staff_category的成员
                    user_id = member.get('UserID')
                    staff_category = member.get('staff_category')
                    
                    if user_id and staff_category is not None:
                        check_sql = """
                            SELECT id FROM itbp 
                            WHERE investment_entity = %s AND UserID = %s AND staff_category = %s
                        """
                        cursor.execute(check_sql, (entity, user_id, staff_category))
                        existing = cursor.fetchone()
                        
                        if existing:
                            # 已存在，跳过添加
                            logger.info(f"跳过添加已存在的成员: UserID={user_id}, staff_category={staff_category}")
                            continue
                    
                    # 准备插入数据
                    insert_data = {
                        'investment_entity': entity,
                        'NAME': member.get('NAME'),
                        'UserID': member.get('UserID'),
                        'staff_category': member.get('staff_category'),
                        'start_time': member.get('start_time'),
                        'end_time': member.get('end_time')
                    }
                    
                    # 构建SQL
                    fields = ', '.join(insert_data.keys())
                    placeholders = ', '.join(['%s'] * len(insert_data))
                    
                    cursor.execute(f"""
                        INSERT INTO itbp ({fields})
                        VALUES ({placeholders})
                    """, tuple(insert_data.values()))
                    
                    logger.info(f"添加成员成功: UserID={user_id}, NAME={member.get('NAME')}")
            
            # 处理更新操作
            if 'update' in operations and operations['update']:
                for member in operations['update']:
                    if 'id' not in member:
                        continue
                    
                    # 准备更新数据
                    update_data = {}
                    if 'NAME' in member:
                        update_data['NAME'] = member['NAME']
                    if 'UserID' in member:
                        update_data['UserID'] = member['UserID']
                    if 'staff_category' in member:
                        update_data['staff_category'] = member['staff_category']
                    if 'start_time' in member:
                        update_data['start_time'] = member['start_time']
                    if 'end_time' in member:
                        update_data['end_time'] = member['end_time']
                    
                    # 构建SQL
                    if update_data:
                        set_clause = ', '.join([f"{field} = %s" for field in update_data])
                        params = list(update_data.values()) + [member['id']]
                        
                        cursor.execute(f"""
                            UPDATE itbp
                            SET {set_clause}
                            WHERE id = %s
                        """, params)
                        
                        logger.info(f"更新成员成功: ID={member['id']}, 影响行数={cursor.rowcount}")
            
            # 处理删除操作
            if 'delete' in operations and operations['delete']:
                for member in operations['delete']:
                    if 'id' not in member:
                        logger.warning(f"删除成员缺少ID字段: {member}")
                        continue
                    
                    # 记录删除操作日志
                    member_id = member['id']
                    logger.info(f"删除成员 ID: {member_id}")
                    
                    # 执行删除
                    delete_sql = "DELETE FROM itbp WHERE id = %s"
                    cursor.execute(delete_sql, (member_id,))
                    
                    # 检查是否删除成功
                    affected_rows = cursor.rowcount
                    logger.info(f"删除成员影响行数: {affected_rows}")
                    
                    if affected_rows == 0:
                        logger.warning(f"未找到要删除的成员 ID: {member_id}")
            
            # 提交事务
            conn.commit()
            
            return True
    except Exception as e:
        logger.error(f"更新团队成员失败: {str(e)}")
        conn.rollback()
        raise e
    finally:
        conn.close()

def get_users(search_text=None):
    """
    获取用户列表
    :param search_text: 搜索文本，可以是用户名、ID、公司或部门
    :return: 用户列表
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 构建SQL查询
            sql = """
                SELECT UserID, name, company_name, department_name, LaborCost
                FROM users
                WHERE is_disabled IS NULL OR is_disabled = 'N'
            """
            params = []
            
            # 添加搜索条件
            if search_text:
                sql += """ AND (
                    UserID LIKE %s OR
                    name LIKE %s OR
                    company_name LIKE %s OR
                    department_name LIKE %s
                )"""
                search_pattern = f"%{search_text}%"
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
            
            # 添加排序
            sql += " ORDER BY UserID ASC"
            
            # 执行查询
            cursor.execute(sql, params)
            users = cursor.fetchall()
            
            return users
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        raise e
    finally:
        conn.close()

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析请求参数
        if 'queryString' in event:
            # GET请求
            query_params = event['queryString']
            action = query_params.get('action')
            entity = query_params.get('entity')
            
            if action == 'getUsers':
                # 获取用户列表
                search_text = query_params.get('search')
                users = get_users(search_text)
                
                return {
                    'statusCode': 200,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 200,
                        'message': 'success',
                        'data': {
                            'users': users
                        }
                    }, ensure_ascii=False, cls=DateEncoder)
                }
            
            if not entity:
                return {
                    'statusCode': 400,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 400,
                        'message': '缺少必要参数: entity',
                        'error': 'Missing required parameter: entity'
                    }, ensure_ascii=False)
                }
            
            if action == 'getMembers':
                # 获取团队成员
                members = get_team_members(entity)
                
                return {
                    'statusCode': 200,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 200,
                        'message': 'success',
                        'data': {
                            'members': members
                        }
                    }, ensure_ascii=False, cls=DateEncoder)
                }
        elif 'body' in event:
            # POST请求
            body = json.loads(event['body']) if isinstance(event['body'], str) else event['body']
            
            action = body.get('action')
            entity = body.get('entity')
            operations = body.get('operations')
            
            if not entity or not operations:
                return {
                    'statusCode': 400,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 400,
                        'message': '缺少必要参数: entity 或 operations',
                        'error': 'Missing required parameters'
                    }, ensure_ascii=False)
                }
            
            if action == 'updateMembers':
                # 更新团队成员
                success = update_team_members(entity, operations)
                
                return {
                    'statusCode': 200,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 200,
                        'message': 'success',
                        'data': {
                            'updated': success
                        }
                    }, ensure_ascii=False)
                }
        
        # 未知操作
        return {
            'statusCode': 400,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 400,
                'message': '未知操作',
                'error': 'Unknown action'
            }, ensure_ascii=False)
        }
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'服务器错误: {str(e)}',
                'error': str(e)
            }, ensure_ascii=False)
        } 