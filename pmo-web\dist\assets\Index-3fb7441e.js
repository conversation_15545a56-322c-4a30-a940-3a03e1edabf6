import{J as k,_ as re,c as u,r as F,o as ne,d as n,L as de,e as h,f as M,h as t,w as o,E as c,g as y,j as m,F as I,D as E,N as T,B as ue,t as ie,m as pe}from"./index-76121fa4.js";import{a as ce}from"./project-0b1ee259.js";function me(i={}){return k({url:"/timesheet/list",method:"get",params:i})}function _e(i){return k({url:"/timesheet",method:"post",data:i})}function ge(i){return k({url:`/timesheet/${i.id}`,method:"put",data:i})}function fe(i){return k({url:`/timesheet/${i}`,method:"delete"})}const ve={class:"timesheet"},he={class:"card-header"},ye={class:"header-actions"},be={class:"filter-bar"},we={class:"pagination"},ke={class:"dialog-footer"},je={__name:"Index",setup(i){const j=u(!1),V=u(!1),D=u([]),x=u([]),S=u(0),C=u(1),Y=u(10),f=u(!1),b=u("add"),w=u(null),z=u(null),p=F({project:"",dateRange:[]}),s=F({date:"",project_id:"",task:"",hours:8}),$={date:[{required:!0,message:"请选择日期",trigger:"change"}],project_id:[{required:!0,message:"请选择项目",trigger:"change"}],task:[{required:!0,message:"请输入任务内容",trigger:"blur"}],hours:[{required:!0,message:"请输入工时",trigger:"change"}]},_=async()=>{var a,e;j.value=!0;try{const d={project_id:p.project,start_date:p.dateRange[0]||"",end_date:p.dateRange[1]||"",page:C.value,pageSize:Y.value},r=await me(d);r&&r.code===200?(D.value=((a=r.data)==null?void 0:a.items)||[],S.value=((e=r.data)==null?void 0:e.total)||D.value.length):c({message:(r==null?void 0:r.message)||"获取工时数据失败",type:"warning"})}catch(d){c({message:d.message||"获取工时数据失败",type:"error"})}finally{j.value=!1}},q=async()=>{try{const a=await ce();a&&a.code===200?x.value=(a.data||[]).map(e=>({label:e.name||e.project_name,value:e.id||e.project_code})):console.warn("获取项目选项失败:",a==null?void 0:a.message)}catch(a){console.error("获取项目选项失败:",a)}},N=()=>{_()},O=()=>{b.value="add",P(),f.value=!0},P=()=>{z.value=null,s.date=new Date().toISOString().split("T")[0],s.project_id="",s.task="",s.hours=8,w.value&&w.value.resetFields()},L=async()=>{w.value&&await w.value.validate(async a=>{if(a){V.value=!0;try{const e=b.value==="add"?_e:ge,d={id:z.value,date:s.date,project_id:s.project_id,task:s.task,hours:s.hours},r=await e(d);r&&r.code===200?(c({message:b.value==="add"?"工时添加成功":"工时更新成功",type:"success"}),f.value=!1,_()):c({message:(r==null?void 0:r.message)||"操作失败",type:"warning"})}catch(e){c({message:e.message||"操作失败",type:"error"})}finally{V.value=!1}}})},A=a=>{b.value="edit",z.value=a.id,s.date=a.date,s.project_id=a.project_id,s.task=a.task,s.hours=a.hours,f.value=!0},J=a=>{pe.confirm("确定要删除这条工时记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await fe(a.id);e&&e.code===200?(c({message:"删除成功",type:"success"}),_()):c({message:(e==null?void 0:e.message)||"删除失败",type:"warning"})}catch(e){c({message:e.message||"删除失败",type:"error"})}}).catch(()=>{})},G=a=>{switch(a){case"approved":return"success";case"pending":return"warning";case"rejected":return"danger";default:return"info"}},H=a=>{switch(a){case"approved":return"已审批";case"pending":return"待审批";case"rejected":return"已拒绝";case"draft":return"草稿";default:return"未知"}},K=a=>{Y.value=a,_()},Q=a=>{C.value=a,_()};return ne(()=>{q(),_()}),(a,e)=>{const d=n("el-button"),r=n("el-option"),B=n("el-select"),g=n("el-form-item"),R=n("el-date-picker"),U=n("el-form"),v=n("el-table-column"),W=n("el-tag"),X=n("el-table"),Z=n("el-pagination"),ee=n("el-card"),te=n("el-input"),ae=n("el-input-number"),le=n("el-dialog"),oe=de("loading");return h(),M("div",ve,[t(ee,{class:"timesheet-card"},{header:o(()=>[y("div",he,[e[10]||(e[10]=y("span",null,"工时录入",-1)),y("div",ye,[t(d,{type:"primary",size:"small",onClick:N},{default:o(()=>e[8]||(e[8]=[m("刷新")])),_:1,__:[8]}),t(d,{type:"success",size:"small",onClick:O},{default:o(()=>e[9]||(e[9]=[m("新增工时")])),_:1,__:[9]})])])]),default:o(()=>[y("div",be,[t(U,{inline:!0,model:p},{default:o(()=>[t(g,{label:"项目"},{default:o(()=>[t(B,{modelValue:p.project,"onUpdate:modelValue":e[0]||(e[0]=l=>p.project=l),clearable:"",placeholder:"选择项目"},{default:o(()=>[(h(!0),M(I,null,E(x.value,l=>(h(),T(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"日期范围"},{default:o(()=>[t(R,{modelValue:p.dateRange,"onUpdate:modelValue":e[1]||(e[1]=l=>p.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(g,null,{default:o(()=>[t(d,{type:"primary",onClick:_},{default:o(()=>e[11]||(e[11]=[m("查询")])),_:1,__:[11]})]),_:1})]),_:1},8,["model"])]),ue((h(),T(X,{data:D.value,border:"",style:{width:"100%"}},{default:o(()=>[t(v,{prop:"date",label:"日期",width:"120"}),t(v,{prop:"project_name",label:"项目名称"}),t(v,{prop:"task",label:"任务内容"}),t(v,{prop:"hours",label:"工时(小时)",width:"100"}),t(v,{prop:"status",label:"状态",width:"100"},{default:o(l=>[t(W,{type:G(l.row.status)},{default:o(()=>[m(ie(H(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(v,{label:"操作",width:"150"},{default:o(l=>[t(d,{size:"small",type:"primary",disabled:l.row.status==="approved",onClick:se=>A(l.row)},{default:o(()=>e[12]||(e[12]=[m(" 编辑 ")])),_:2,__:[12]},1032,["disabled","onClick"]),t(d,{size:"small",type:"danger",disabled:l.row.status==="approved",onClick:se=>J(l.row)},{default:o(()=>e[13]||(e[13]=[m(" 删除 ")])),_:2,__:[13]},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])),[[oe,j.value]]),y("div",we,[t(Z,{"current-page":C.value,"page-sizes":[10,20,50,100],"page-size":Y.value,layout:"total, sizes, prev, pager, next, jumper",total:S.value,onSizeChange:K,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1}),t(le,{modelValue:f.value,"onUpdate:modelValue":e[7]||(e[7]=l=>f.value=l),title:b.value==="add"?"新增工时":"编辑工时",width:"600px"},{footer:o(()=>[y("span",ke,[t(d,{onClick:e[6]||(e[6]=l=>f.value=!1)},{default:o(()=>e[14]||(e[14]=[m("取消")])),_:1,__:[14]}),t(d,{type:"primary",loading:V.value,onClick:L},{default:o(()=>e[15]||(e[15]=[m("确认")])),_:1,__:[15]},8,["loading"])])]),default:o(()=>[t(U,{ref_key:"formRef",ref:w,model:s,rules:$,"label-width":"100px"},{default:o(()=>[t(g,{label:"日期",prop:"date"},{default:o(()=>[t(R,{modelValue:s.date,"onUpdate:modelValue":e[2]||(e[2]=l=>s.date=l),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(g,{label:"项目",prop:"project_id"},{default:o(()=>[t(B,{modelValue:s.project_id,"onUpdate:modelValue":e[3]||(e[3]=l=>s.project_id=l),placeholder:"选择项目",style:{width:"100%"}},{default:o(()=>[(h(!0),M(I,null,E(x.value,l=>(h(),T(r,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"任务内容",prop:"task"},{default:o(()=>[t(te,{modelValue:s.task,"onUpdate:modelValue":e[4]||(e[4]=l=>s.task=l),type:"textarea",rows:3,placeholder:"请输入任务内容"},null,8,["modelValue"])]),_:1}),t(g,{label:"工时(小时)",prop:"hours"},{default:o(()=>[t(ae,{modelValue:s.hours,"onUpdate:modelValue":e[5]||(e[5]=l=>s.hours=l),min:.5,max:24,step:.5,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},xe=re(je,[["__scopeId","data-v-6e433775"]]);export{xe as default};
