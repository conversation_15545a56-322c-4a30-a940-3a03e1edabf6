import{J as h,_ as se,c as i,r as T,o as re,d,L as ue,e as w,f as U,h as t,w as o,E as _,g as k,j as m,F as E,D as M,N as W,B as de,t as ie,m as pe}from"./index-76121fa4.js";import{a as ce}from"./project-0b1ee259.js";function _e(r={}){return h({url:"/report/weekly/list",method:"get",params:r})}function me(r){return h({url:"/report/weekly",method:"post",data:r})}function fe(r){return h({url:`/report/weekly/${r.id}`,method:"put",data:r})}function ge(r){return h({url:`/report/weekly/${r}`,method:"delete"})}function N(r){if(!r)return"";const g=r.getFullYear(),v=new Date(g,0,1),b=Math.ceil(((r-v)/864e5+v.getDay()+1)/7);return`${g}年第${b}周`}const ve={class:"weekly-report"},ye={class:"card-header"},we={class:"header-actions"},ke={class:"filter-bar"},be={class:"pagination"},Ve={class:"dialog-footer"},je={__name:"Weekly",setup(r){const g=i(!1),v=i(!1),b=i([]),C=i([]),F=i(0),D=i(1),z=i(10),y=i(!1),V=i("add"),j=i(null),Y=i(null),p=T({week:"",project:""}),n=T({week:"",project_id:"",content:"",next_plan:"",issues:""}),q={week:[{required:!0,message:"请选择周次",trigger:"change"}],project_id:[{required:!0,message:"请选择项目",trigger:"change"}],content:[{required:!0,message:"请输入工作内容",trigger:"blur"}]},f=async()=>{var a,e;g.value=!0;try{const u={week:p.week?N(p.week):"",project_id:p.project,page:D.value,pageSize:z.value},s=await _e(u);s&&s.code===200?(b.value=((a=s.data)==null?void 0:a.items)||[],F.value=((e=s.data)==null?void 0:e.total)||b.value.length):_({message:(s==null?void 0:s.message)||"获取周报数据失败",type:"warning"})}catch(u){_({message:u.message||"获取周报数据失败",type:"error"})}finally{g.value=!1}},O=async()=>{try{const a=await ce();a&&a.code===200?C.value=(a.data||[]).map(e=>({label:e.name||e.project_name,value:e.id||e.project_code})):console.warn("获取项目选项失败:",a==null?void 0:a.message)}catch(a){console.error("获取项目选项失败:",a)}},P=()=>{f()},I=()=>{V.value="add",L(),y.value=!0},L=()=>{Y.value=null,n.week=new Date,n.project_id="",n.content="",n.next_plan="",n.issues="",j.value&&j.value.resetFields()},A=async()=>{j.value&&await j.value.validate(async a=>{if(a){v.value=!0;try{const e=V.value==="add"?me:fe,u={id:Y.value,week:N(n.week),project_id:n.project_id,content:n.content,next_plan:n.next_plan,issues:n.issues},s=await e(u);s&&s.code===200?(_({message:V.value==="add"?"周报添加成功":"周报更新成功",type:"success"}),y.value=!1,f()):_({message:(s==null?void 0:s.message)||"操作失败",type:"warning"})}catch(e){_({message:e.message||"操作失败",type:"error"})}finally{v.value=!1}}})},J=a=>{V.value="edit",Y.value=a.id,n.week=new Date,n.project_id=a.project_id,n.content=a.content,n.next_plan=a.next_plan||"",n.issues=a.issues||"",y.value=!0},G=a=>{pe.confirm("确定要删除这条周报记录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await ge(a.id);e&&e.code===200?(_({message:"删除成功",type:"success"}),f()):_({message:(e==null?void 0:e.message)||"删除失败",type:"warning"})}catch(e){_({message:e.message||"删除失败",type:"error"})}}).catch(()=>{})},H=a=>{switch(a){case"approved":return"success";case"pending":return"warning";case"rejected":return"danger";default:return"info"}},K=a=>{switch(a){case"approved":return"已审批";case"pending":return"待审批";case"rejected":return"已拒绝";case"draft":return"草稿";default:return"未知"}},Q=a=>{z.value=a,f()},X=a=>{D.value=a,f()};return re(()=>{O(),f()}),(a,e)=>{const u=d("el-button"),s=d("el-date-picker"),c=d("el-form-item"),S=d("el-option"),$=d("el-select"),R=d("el-form"),x=d("el-table-column"),Z=d("el-tag"),ee=d("el-table"),te=d("el-pagination"),le=d("el-card"),B=d("el-input"),ae=d("el-dialog"),oe=ue("loading");return w(),U("div",ve,[t(le,{class:"report-card"},{header:o(()=>[k("div",ye,[e[11]||(e[11]=k("span",null,"周报录入",-1)),k("div",we,[t(u,{type:"primary",size:"small",onClick:P},{default:o(()=>e[9]||(e[9]=[m("刷新")])),_:1,__:[9]}),t(u,{type:"success",size:"small",onClick:I},{default:o(()=>e[10]||(e[10]=[m("新增周报")])),_:1,__:[10]})])])]),default:o(()=>[k("div",ke,[t(R,{inline:!0,model:p},{default:o(()=>[t(c,{label:"周次"},{default:o(()=>[t(s,{modelValue:p.week,"onUpdate:modelValue":e[0]||(e[0]=l=>p.week=l),type:"week",format:"YYYY 第 ww 周",placeholder:"选择周"},null,8,["modelValue"])]),_:1}),t(c,{label:"项目"},{default:o(()=>[t($,{modelValue:p.project,"onUpdate:modelValue":e[1]||(e[1]=l=>p.project=l),clearable:"",placeholder:"选择项目"},{default:o(()=>[(w(!0),U(E,null,M(C.value,l=>(w(),W(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(c,null,{default:o(()=>[t(u,{type:"primary",onClick:f},{default:o(()=>e[12]||(e[12]=[m("查询")])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),de((w(),W(ee,{data:b.value,border:"",style:{width:"100%"}},{default:o(()=>[t(x,{prop:"week",label:"周次",width:"120"}),t(x,{prop:"project_name",label:"项目名称"}),t(x,{prop:"content",label:"工作内容"}),t(x,{prop:"status",label:"状态",width:"100"},{default:o(l=>[t(Z,{type:H(l.row.status)},{default:o(()=>[m(ie(K(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(x,{label:"操作",width:"150"},{default:o(l=>[t(u,{size:"small",type:"primary",disabled:l.row.status==="approved",onClick:ne=>J(l.row)},{default:o(()=>e[13]||(e[13]=[m(" 编辑 ")])),_:2,__:[13]},1032,["disabled","onClick"]),t(u,{size:"small",type:"danger",disabled:l.row.status==="approved",onClick:ne=>G(l.row)},{default:o(()=>e[14]||(e[14]=[m(" 删除 ")])),_:2,__:[14]},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])),[[oe,g.value]]),k("div",be,[t(te,{"current-page":D.value,"page-sizes":[10,20,50,100],"page-size":z.value,layout:"total, sizes, prev, pager, next, jumper",total:F.value,onSizeChange:Q,onCurrentChange:X},null,8,["current-page","page-size","total"])])]),_:1}),t(ae,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=l=>y.value=l),title:V.value==="add"?"新增周报":"编辑周报",width:"600px"},{footer:o(()=>[k("span",Ve,[t(u,{onClick:e[7]||(e[7]=l=>y.value=!1)},{default:o(()=>e[15]||(e[15]=[m("取消")])),_:1,__:[15]}),t(u,{type:"primary",loading:v.value,onClick:A},{default:o(()=>e[16]||(e[16]=[m("确认")])),_:1,__:[16]},8,["loading"])])]),default:o(()=>[t(R,{ref_key:"formRef",ref:j,model:n,rules:q,"label-width":"100px"},{default:o(()=>[t(c,{label:"周次",prop:"week"},{default:o(()=>[t(s,{modelValue:n.week,"onUpdate:modelValue":e[2]||(e[2]=l=>n.week=l),type:"week",format:"YYYY 第 ww 周",placeholder:"选择周",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(c,{label:"项目",prop:"project_id"},{default:o(()=>[t($,{modelValue:n.project_id,"onUpdate:modelValue":e[3]||(e[3]=l=>n.project_id=l),placeholder:"选择项目",style:{width:"100%"}},{default:o(()=>[(w(!0),U(E,null,M(C.value,l=>(w(),W(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"工作内容",prop:"content"},{default:o(()=>[t(B,{modelValue:n.content,"onUpdate:modelValue":e[4]||(e[4]=l=>n.content=l),type:"textarea",rows:5,placeholder:"请输入本周工作内容"},null,8,["modelValue"])]),_:1}),t(c,{label:"下周计划",prop:"next_plan"},{default:o(()=>[t(B,{modelValue:n.next_plan,"onUpdate:modelValue":e[5]||(e[5]=l=>n.next_plan=l),type:"textarea",rows:3,placeholder:"请输入下周工作计划"},null,8,["modelValue"])]),_:1}),t(c,{label:"问题与风险",prop:"issues"},{default:o(()=>[t(B,{modelValue:n.issues,"onUpdate:modelValue":e[6]||(e[6]=l=>n.issues=l),type:"textarea",rows:3,placeholder:"请输入遇到的问题与风险"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ce=se(je,[["__scopeId","data-v-f476b1be"]]);export{Ce as default};
