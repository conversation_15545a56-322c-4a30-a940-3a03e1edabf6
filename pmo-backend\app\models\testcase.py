#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用例模型 - 参考禅道zt_case表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class TestCase:
    """测试用例模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.product: int = 0
        self.branch: int = 0
        self.module: int = 0
        self.story: int = 0
        self.storyVersion: int = 1
        self.title: str = ""
        self.precondition: Optional[str] = None
        self.keywords: str = ""
        self.pri: int = 3
        self.type: str = "feature"
        self.stage: str = ""
        self.method: str = "manual"
        self.status: str = "normal"
        self.subStatus: str = ""
        self.color: str = ""
        self.frequency: int = 1
        self.order: int = 0
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.reviewedBy: str = ""
        self.reviewedDate: Optional[datetime] = None
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.version: int = 1
        self.linkCase: str = ""
        self.fromBug: int = 0
        self.fromCaseID: int = 0
        self.fromCaseVersion: int = 1
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, case_id: int) -> Optional['TestCase']:
        """根据ID获取测试用例"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT c.*, p.name as product_name
                FROM zt_case c
                LEFT JOIN zt_product p ON c.product = p.id
                WHERE c.id = %s AND c.deleted = '0'
            """, (case_id,))
            
            row = cursor.fetchone()
            if row:
                case = cls()
                case._load_from_dict(row)
                return case
            return None
            
        except Exception as e:
            print(f"获取测试用例失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_cases(cls, page: int = 1, page_size: int = 20, product_id: int = 0, 
                     status: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取测试用例列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["c.deleted = '0'"]
            params = []
            
            if product_id > 0:
                where_conditions.append("c.product = %s")
                params.append(product_id)
            
            if status:
                where_conditions.append("c.status = %s")
                params.append(status)
            
            if keyword:
                where_conditions.append("(c.title LIKE %s OR c.keywords LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_case c WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT c.id, c.title, c.pri, c.type, c.stage, c.method, c.status,
                       c.openedBy, c.openedDate, c.product, p.name as product_name
                FROM zt_case c
                LEFT JOIN zt_product p ON c.product = p.id
                WHERE {where_clause}
                ORDER BY c.`order` ASC, c.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            cases = cursor.fetchall()
            
            return {
                "cases": cases,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取测试用例列表失败: {e}")
            return {"cases": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self, steps: List[Dict[str, str]] = None) -> bool:
        """创建测试用例"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入测试用例
            cursor.execute("""
                INSERT INTO zt_case (
                    vision, product, branch, module, story, storyVersion, title,
                    precondition, keywords, pri, type, stage, method, status,
                    subStatus, color, frequency, `order`, openedBy, openedDate, version
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s
                )
            """, (
                self.vision, self.product, self.branch, self.module, self.story,
                self.storyVersion, self.title, self.precondition, self.keywords,
                self.pri, self.type, self.stage, self.method, self.status,
                self.subStatus, self.color, self.frequency, self.order,
                self.openedBy, self.version
            ))
            
            self.id = cursor.lastrowid
            
            # 插入测试步骤
            if steps:
                for step in steps:
                    cursor.execute("""
                        INSERT INTO zt_casestep (parent, version, type, `desc`, expect)
                        VALUES (%s, %s, 'step', %s, %s)
                    """, (self.id, self.version, step.get('desc', ''), step.get('expect', '')))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建测试用例失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self, steps: List[Dict[str, str]] = None) -> bool:
        """更新测试用例信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 更新测试用例基本信息
            cursor.execute("""
                UPDATE zt_case SET 
                    title = %s, precondition = %s, keywords = %s, pri = %s, type = %s,
                    stage = %s, method = %s, status = %s, frequency = %s, `order` = %s,
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (
                self.title, self.precondition, self.keywords, self.pri, self.type,
                self.stage, self.method, self.status, self.frequency, self.order,
                self.lastEditedBy, self.id
            ))
            
            # 更新测试步骤
            if steps is not None:
                # 删除现有步骤
                cursor.execute("""
                    DELETE FROM zt_casestep WHERE parent = %s AND version = %s
                """, (self.id, self.version))
                
                # 插入新步骤
                for step in steps:
                    cursor.execute("""
                        INSERT INTO zt_casestep (parent, version, type, `desc`, expect)
                        VALUES (%s, %s, 'step', %s, %s)
                    """, (self.id, self.version, step.get('desc', ''), step.get('expect', '')))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新测试用例失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除测试用例"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_case SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除测试用例失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_steps(self, version: int = None) -> List[Dict[str, Any]]:
        """获取测试用例步骤"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if version is None:
                version = self.version
            
            cursor.execute("""
                SELECT * FROM zt_casestep 
                WHERE parent = %s AND version = %s
                ORDER BY id ASC
            """, (self.id, version))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取测试用例步骤失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def review(self, reviewedBy: str) -> bool:
        """评审测试用例"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_case SET 
                    reviewedBy = %s, reviewedDate = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (reviewedBy, reviewedBy, self.id))
            
            self.reviewedBy = reviewedBy
            self.reviewedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"评审测试用例失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self, include_steps: bool = False) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            "id": self.id,
            "vision": self.vision,
            "product": self.product,
            "module": self.module,
            "story": self.story,
            "title": self.title,
            "precondition": self.precondition,
            "keywords": self.keywords,
            "pri": self.pri,
            "type": self.type,
            "stage": self.stage,
            "method": self.method,
            "status": self.status,
            "subStatus": self.subStatus,
            "color": self.color,
            "frequency": self.frequency,
            "order": self.order,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "reviewedBy": self.reviewedBy,
            "reviewedDate": self.reviewedDate.isoformat() if self.reviewedDate else None,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "version": self.version,
            "linkCase": self.linkCase,
            "fromBug": self.fromBug,
            "deleted": self.deleted
        }
        
        if include_steps:
            data["steps"] = self.get_steps()
        
        return data


class TestTask:
    """测试任务模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.product: int = 0
        self.execution: int = 0
        self.build: str = ""
        self.owner: str = ""
        self.pri: int = 3
        self.begin: Optional[date] = None
        self.end: Optional[date] = None
        self.realBegan: Optional[date] = None
        self.realEnd: Optional[date] = None
        self.status: str = "wait"
        self.subStatus: str = ""
        self.desc: Optional[str] = None
        self.report: Optional[str] = None
        self.mailto: Optional[str] = None
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.closedBy: str = ""
        self.closedDate: Optional[datetime] = None
        self.canceledBy: str = ""
        self.canceledDate: Optional[datetime] = None
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, task_id: int) -> Optional['TestTask']:
        """根据ID获取测试任务"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, p.name as product_name
                FROM zt_testtask t
                LEFT JOIN zt_product p ON t.product = p.id
                WHERE t.id = %s AND t.deleted = '0'
            """, (task_id,))
            
            row = cursor.fetchone()
            if row:
                task = cls()
                task._load_from_dict(row)
                return task
            return None
            
        except Exception as e:
            print(f"获取测试任务失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_tasks(cls, page: int = 1, page_size: int = 20, product_id: int = 0, 
                     status: str = "") -> Dict[str, Any]:
        """获取测试任务列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["t.deleted = '0'"]
            params = []
            
            if product_id > 0:
                where_conditions.append("t.product = %s")
                params.append(product_id)
            
            if status:
                where_conditions.append("t.status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_testtask t WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT t.id, t.name, t.build, t.owner, t.pri, t.begin, t.end,
                       t.status, t.openedBy, t.openedDate, t.product, p.name as product_name
                FROM zt_testtask t
                LEFT JOIN zt_product p ON t.product = p.id
                WHERE {where_clause}
                ORDER BY t.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            tasks = cursor.fetchall()
            
            return {
                "tasks": tasks,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取测试任务列表失败: {e}")
            return {"tasks": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "product": self.product,
            "execution": self.execution,
            "build": self.build,
            "owner": self.owner,
            "pri": self.pri,
            "begin": self.begin.isoformat() if self.begin else None,
            "end": self.end.isoformat() if self.end else None,
            "realBegan": self.realBegan.isoformat() if self.realBegan else None,
            "realEnd": self.realEnd.isoformat() if self.realEnd else None,
            "status": self.status,
            "subStatus": self.subStatus,
            "desc": self.desc,
            "report": self.report,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "closedBy": self.closedBy,
            "closedDate": self.closedDate.isoformat() if self.closedDate else None,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "deleted": self.deleted
        }
