from fastapi import APIRouter

from app.api.endpoints import auth, project, users, dashboard, timesheet, reports, options, team, ai_assistant, project_archive, archive_management, dynamic_knowledge, simple_supervision, ticket_integration, ai_chat
# 临时注释掉project_management，调试用
# from app.api.endpoints import project_management

# 添加调试信息
print("📦 导入工时管理模块...")
try:
    print(f"工时管理路由包含的端点: {[route.path for route in timesheet.router.routes]}")
    print("✅ 工时管理模块导入成功")
except Exception as e:
    print(f"❌ 工时管理模块导入失败: {e}")
from app.api import planning

api_router = APIRouter()

# 添加各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(project.router, prefix="/project", tags=["项目管理"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["仪表盘"])
api_router.include_router(ai_assistant.router, prefix="/ai", tags=["AI助手"])
# 添加文件上传路由（不需要认证）
api_router.include_router(ai_assistant.file_router, prefix="/ai", tags=["AI助手文件"])
# 添加一个额外的路由，让AI助手同时支持/db路径
api_router.include_router(ai_assistant.router, prefix="/db", tags=["数据库助手"])
api_router.include_router(team.router, prefix="/team", tags=["团队管理"])

# 添加工时管理路由
print("🔧 注册工时管理路由...")
api_router.include_router(timesheet.router, prefix="/timesheet", tags=["工时管理"])
print("✅ 工时管理路由注册完成")

# 删除不存在的project_hours模块引用
api_router.include_router(options.router, prefix="/options", tags=["选项数据"], dependencies=[])  # 移除认证依赖
api_router.include_router(planning.router, prefix="", tags=["项目规划"])  # planning路由已经包含/planning前缀
api_router.include_router(project_archive.router, prefix="/archive", tags=["项目档案管理"])  # 新增项目档案路由
api_router.include_router(simple_supervision.router, prefix="/supervision", tags=["督办管理"])  # 督办管理路由
api_router.include_router(archive_management.router, prefix="/archive-management", tags=["档案清单管理"])  # 新增档案清单管理路由
api_router.include_router(dynamic_knowledge.router, prefix="/dynamic-knowledge", tags=["动态知识库"])  # 新增动态知识库路由
api_router.include_router(ticket_integration.router, prefix="/ticket-integration", tags=["工单系统集成"])  # 新增工单系统集成路由
api_router.include_router(ai_chat.router, prefix="/ai-chat", tags=["AI智能问答"])  # 新增AI智能问答路由
# 临时注释掉project_management路由
# api_router.include_router(project_management.router, prefix="/project-management", tags=["项目管理"])  # 新增完整项目管理路由

# 打印所有注册的路由
print("📋 所有注册的路由:")
for route in api_router.routes:
    if hasattr(route, 'path') and hasattr(route, 'methods'):
        print(f"  {list(route.methods)[0] if route.methods else 'GET'} {route.path}")
    elif hasattr(route, 'path_regex'):
        print(f"  MOUNT {route.path_regex.pattern}")