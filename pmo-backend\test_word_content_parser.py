#!/usr/bin/env python3
"""
测试专门的Word文档正文提取器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def create_realistic_doc_content():
    """创建更真实的.doc文件内容"""
    # OLE文件头
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    
    # 模拟OLE结构信息（应该被过滤掉）
    ole_structure = b'Root Entry\x00SummaryInformation\x00DocumentSummaryInformation\x00WordDocument\x00'
    
    # 真实的中文文档内容（UTF-16LE编码）
    chinese_content = """
2025年科技条线半年工作报告发言稿
各位领导，下面我就2025年上半年科技条线的工作情况向大家做简要汇报。
一、主要工作成果
1. 数字化转型取得重要进展
2. 技术创新能力持续提升
3. 信息安全保障体系不断完善
二、存在的问题和挑战
1. 技术人才储备不足
2. 系统集成度有待提高
三、下半年工作计划
1. 加强人才队伍建设
2. 推进核心技术攻关
""".encode('utf-16le')
    
    # 英文内容
    english_content = b"Project Management Office Annual Report 2025\nTechnology Innovation and Digital Transformation"
    
    # 组合成模拟的.doc文件
    mock_content = (
        ole_header + 
        b'\x00' * 100 +  # 填充
        ole_structure +
        b'\x00' * 200 +
        chinese_content + 
        b'\x00' * 100 + 
        english_content + 
        b'\x00' * 500
    )
    
    return mock_content

def test_word_content_extraction():
    """测试Word文档正文提取"""
    print("🔧 测试Word文档正文提取...")
    
    parser = DocumentParser()
    mock_doc = create_realistic_doc_content()
    
    print(f"模拟.doc文件大小: {len(mock_doc)} 字节")
    
    # 测试专门的Word文档正文提取
    print("\n=== 专门的Word文档正文提取 ===")
    try:
        result = parser._extract_word_document_content_only(mock_doc)
        print(f"正文提取结果:\n{result}")
        
        # 检查是否包含OLE结构信息
        if 'Root Entry' in result or 'SummaryInformation' in result:
            print("❌ 警告：结果中包含OLE结构信息")
        else:
            print("✅ 成功过滤掉OLE结构信息")
            
    except Exception as e:
        print(f"正文提取失败: {str(e)}")

def test_individual_scanners():
    """测试各个扫描器"""
    print("\n🔧 测试各个扫描器...")
    
    parser = DocumentParser()
    mock_doc = create_realistic_doc_content()
    
    # 测试中文内容扫描
    print("\n--- 中文内容扫描 ---")
    try:
        chinese_results = parser._scan_for_chinese_content(mock_doc)
        print(f"中文扫描结果: {chinese_results}")
    except Exception as e:
        print(f"中文扫描失败: {str(e)}")
    
    # 测试英文内容扫描
    print("\n--- 英文内容扫描 ---")
    try:
        english_results = parser._scan_for_english_content(mock_doc)
        print(f"英文扫描结果: {english_results}")
    except Exception as e:
        print(f"英文扫描失败: {str(e)}")
    
    # 测试文档正文提取
    print("\n--- 文档正文提取 ---")
    try:
        body_results = parser._extract_document_body_only(mock_doc)
        print(f"正文提取结果: {body_results}")
    except Exception as e:
        print(f"正文提取失败: {str(e)}")

def test_ole_structure_filter():
    """测试OLE结构信息过滤"""
    print("\n🔧 测试OLE结构信息过滤...")
    
    parser = DocumentParser()
    
    # 测试各种文本
    test_texts = [
        "Root Entry",  # 应该被过滤
        "SummaryInformation",  # 应该被过滤
        "2025年科技条线半年工作报告",  # 应该保留
        "Project Management Office",  # 应该保留
        "eyJoZGlkIjoiOGYzNzJjNDQw",  # Base64，应该被过滤
        "各位领导，下面我就工作情况做汇报",  # 应该保留
        "WPS Office_12",  # 应该被过滤
    ]
    
    for text in test_texts:
        is_structure = parser._is_ole_structure_info(text)
        status = "过滤" if is_structure else "保留"
        print(f"{text} -> {status}")

def test_complete_parsing():
    """测试完整的解析流程"""
    print("\n🔧 测试完整的解析流程...")
    
    parser = DocumentParser()
    mock_doc = create_realistic_doc_content()
    
    try:
        result = parser._parse_old_doc_file(mock_doc)
        print(f"完整解析结果:\n{result}")
        
        # 检查结果质量
        if len(result) > 100:
            print("✅ 提取到足够的内容")
        else:
            print("❌ 提取的内容过少")
            
        if '2025年科技条线' in result:
            print("✅ 成功提取到中文标题")
        else:
            print("❌ 未能提取到中文标题")
            
        if 'Root Entry' not in result:
            print("✅ 成功过滤掉OLE结构信息")
        else:
            print("❌ 仍包含OLE结构信息")
            
    except Exception as e:
        print(f"完整解析失败: {str(e)}")

if __name__ == "__main__":
    print("🔧 测试专门的Word文档正文提取器")
    print("=" * 60)
    
    try:
        # 测试Word文档正文提取
        test_word_content_extraction()
        
        # 测试各个扫描器
        test_individual_scanners()
        
        # 测试OLE结构信息过滤
        test_ole_structure_filter()
        
        # 测试完整解析流程
        test_complete_parsing()
        
        print("\n✅ 所有测试完成")
        print("💡 现在可以重新启动后端并测试实际的.doc文件")
        print("\n🚀 新解析器的特点:")
        print("1. 专门提取Word文档正文内容")
        print("2. 自动过滤OLE结构信息")
        print("3. 智能识别中文和英文内容")
        print("4. 避免提取元数据和乱码")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
