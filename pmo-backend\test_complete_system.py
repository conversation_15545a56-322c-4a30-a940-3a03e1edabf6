#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统功能测试
"""

import requests
import json
import time

def test_api_endpoints():
    """测试所有API端点"""
    print("🧪 测试API端点...")
    
    base_url = "http://127.0.0.1:8001/api/v1/new-supervision"
    
    tests = [
        {
            'name': '获取督办事项列表',
            'method': 'GET',
            'url': f'{base_url}/items',
            'expected_keys': ['data', 'companies']
        },
        {
            'name': '获取导出Excel',
            'method': 'GET', 
            'url': f'{base_url}/export-excel',
            'check_content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
    ]
    
    results = []
    
    for test in tests:
        print(f"\n📋 测试: {test['name']}")
        try:
            if test['method'] == 'GET':
                response = requests.get(test['url'])
            
            if response.status_code == 200:
                print(f"  ✅ 状态码: {response.status_code}")
                
                # 检查内容类型
                if 'check_content_type' in test:
                    content_type = response.headers.get('content-type', '')
                    if test['check_content_type'] in content_type:
                        print(f"  ✅ 内容类型正确: {content_type}")
                        results.append(True)
                    else:
                        print(f"  ❌ 内容类型错误: {content_type}")
                        results.append(False)
                else:
                    # 检查JSON响应
                    data = response.json()
                    if 'expected_keys' in test:
                        missing_keys = [key for key in test['expected_keys'] if key not in data]
                        if not missing_keys:
                            print(f"  ✅ 响应包含所有必要字段")
                            results.append(True)
                        else:
                            print(f"  ❌ 响应缺少字段: {missing_keys}")
                            results.append(False)
                    else:
                        print(f"  ✅ 响应格式正确")
                        results.append(True)
            else:
                print(f"  ❌ 状态码错误: {response.status_code}")
                print(f"  错误信息: {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 API测试结果: {sum(results)}/{len(results)} 通过 ({success_rate:.1f}%)")
    return success_rate >= 80

def test_status_update():
    """测试状态更新功能"""
    print("\n🔄 测试状态更新功能...")
    
    try:
        # 首先获取督办事项列表
        response = requests.get("http://127.0.0.1:8001/api/v1/new-supervision/items")
        if response.status_code != 200:
            print("❌ 无法获取督办事项列表")
            return False
        
        data = response.json()
        items = data.get('data', [])
        companies = data.get('companies', [])
        
        if not items or not companies:
            print("❌ 没有督办事项或公司数据")
            return False
        
        # 选择第一个督办事项和第一个公司进行测试
        test_item = items[0]
        test_company = companies[0]
        
        print(f"测试督办事项: {test_item.get('work_theme')}")
        print(f"测试公司: {test_company.get('company_name')}")
        
        # 测试状态更新
        update_data = {
            'supervision_item_id': test_item['id'],
            'company_id': test_company['id'],
            'status': '√',
            'existing_problems': '测试问题描述',
            'next_plan': '测试下一步计划'
        }
        
        response = requests.put(
            "http://127.0.0.1:8001/api/v1/new-supervision/status",
            json=update_data
        )
        
        if response.status_code == 200:
            print("✅ 状态更新成功")
            return True
        else:
            print(f"❌ 状态更新失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 状态更新测试失败: {str(e)}")
        return False

def test_database_consistency():
    """测试数据库一致性"""
    print("\n🔍 测试数据库一致性...")
    
    try:
        response = requests.get("http://127.0.0.1:8001/api/v1/new-supervision/items")
        if response.status_code != 200:
            print("❌ 无法获取数据")
            return False
        
        data = response.json()
        items = data.get('data', [])
        companies = data.get('companies', [])
        
        print(f"督办事项数量: {len(items)}")
        print(f"公司数量: {len(companies)}")
        
        # 检查每个督办事项是否有对应的公司状态
        for item in items:
            company_statuses = item.get('company_statuses', {})
            print(f"督办事项 '{item.get('work_theme')}' 有 {len(company_statuses)} 个公司状态")
            
            # 检查是否所有活跃公司都有状态
            active_companies = [c for c in companies if c.get('is_active')]
            missing_statuses = []
            
            for company in active_companies:
                company_code = company.get('company_code')
                if company_code not in company_statuses:
                    missing_statuses.append(company_code)
            
            if missing_statuses:
                print(f"  ❌ 缺少公司状态: {missing_statuses}")
                return False
            else:
                print(f"  ✅ 所有公司都有状态记录")
        
        print("✅ 数据库一致性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库一致性检查失败: {str(e)}")
        return False

def test_performance():
    """测试系统性能"""
    print("\n⚡ 测试系统性能...")
    
    try:
        # 测试API响应时间
        start_time = time.time()
        response = requests.get("http://127.0.0.1:8001/api/v1/new-supervision/items")
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        if response.status_code == 200:
            print(f"✅ API响应时间: {response_time:.2f}ms")
            
            if response_time < 1000:  # 小于1秒
                print("✅ 响应时间良好")
                return True
            else:
                print("⚠️ 响应时间较慢")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整系统功能测试...")
    print("=" * 60)
    
    test_results = []
    
    # 1. API端点测试
    test_results.append(test_api_endpoints())
    
    # 2. 状态更新测试
    test_results.append(test_status_update())
    
    # 3. 数据库一致性测试
    test_results.append(test_database_consistency())
    
    # 4. 性能测试
    test_results.append(test_performance())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "API端点测试",
        "状态更新测试", 
        "数据库一致性测试",
        "性能测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    success_count = sum(test_results)
    total_count = len(test_results)
    success_rate = success_count / total_count * 100
    
    print(f"\n🎯 总体测试结果: {success_count}/{total_count} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 系统测试通过！系统运行正常。")
    else:
        print("⚠️ 系统测试未完全通过，需要进一步检查。")
    
    return success_rate >= 80

if __name__ == "__main__":
    main()
