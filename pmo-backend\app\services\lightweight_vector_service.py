#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级向量知识库服务 - 基于Chroma + text2vec
内存占用小，部署简单，专为中文优化
"""

import os
import json
import logging
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime
import tempfile

try:
    import chromadb
    from chromadb.config import Settings
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from app.core.logger import get_logger

logger = get_logger(__name__)

class LightweightVectorService:
    """轻量级向量知识库服务 - 已禁用以提升启动速度"""

    def __init__(self):
        # 完全禁用向量服务
        self.client = None
        self.collection = None
        self.embedding_model = None
        self.is_initialized = True  # 直接标记为已初始化
        logger.info("✅ 向量服务已禁用（提升启动速度）")
    
    def _quick_initialize(self):
        """快速初始化，只初始化数据库连接"""
        try:
            if not CHROMA_AVAILABLE:
                logger.warning("⚠️ ChromaDB未安装，请运行: pip install chromadb")
                return

            # 初始化ChromaDB客户端（持久化存储）
            self.client = chromadb.PersistentClient(
                path=self.data_dir,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            logger.info(f"✅ ChromaDB初始化成功，数据目录: {self.data_dir}")
            self.is_initialized = True

        except Exception as e:
            logger.error(f"❌ ChromaDB初始化失败: {str(e)}")
            self.client = None

    def _lazy_load_model(self):
        """延迟加载embedding模型"""
        if self.embedding_model is not None:
            return True

        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("⚠️ sentence-transformers未安装，请运行: pip install sentence-transformers")
            return False

        try:
            # 设置国内镜像源
            import os
            os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

            # 简化模型列表，优先使用轻量级模型
            models = [
                "sentence-transformers/all-MiniLM-L6-v2",  # 轻量级英文模型
                "shibing624/text2vec-base-chinese"  # 中文模型
            ]

            for model_name in models:
                try:
                    logger.info(f"🔄 延迟加载模型: {model_name}")
                    self.embedding_model = SentenceTransformer(model_name)
                    logger.info(f"✅ 成功加载模型: {model_name}")

                    # 获取或创建集合
                    self._get_or_create_collection()
                    return True
                except Exception as e:
                    logger.warning(f"⚠️ 模型 {model_name} 加载失败: {str(e)}")
                    continue

            logger.error("❌ 所有模型都加载失败")
            return False

        except Exception as e:
            logger.error(f"❌ 模型加载失败: {str(e)}")
            return False
    
    def _get_or_create_collection(self):
        """获取或创建向量集合"""
        try:
            # 尝试获取现有集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"📦 使用现有集合: {self.collection_name}")
            except:
                # 创建新集合
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "PMO知识库向量集合"}
                )
                logger.info(f"✅ 创建新集合: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"❌ 创建向量集合失败: {str(e)}")
    
    def _chunk_text(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """智能文本分块（轻量级版本）"""
        chunks = []
        
        # 简单按段落和句子分割
        paragraphs = text.split('\n\n')
        
        current_chunk = ""
        chunk_id = 0
        
        for paragraph in paragraphs:
            # 如果段落太长，按句子分割
            if len(paragraph) > self.chunk_size:
                sentences = paragraph.split('。')
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                        chunks.append({
                            "text": current_chunk.strip() + "。",
                            "chunk_id": chunk_id,
                            "metadata": {**metadata, "chunk_id": chunk_id}
                        })
                        chunk_id += 1
                        current_chunk = sentence
                    else:
                        current_chunk += sentence + "。" if current_chunk else sentence
            else:
                if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                    chunks.append({
                        "text": current_chunk.strip(),
                        "chunk_id": chunk_id,
                        "metadata": {**metadata, "chunk_id": chunk_id}
                    })
                    chunk_id += 1
                    current_chunk = paragraph
                else:
                    current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        # 添加最后一个块
        if current_chunk.strip():
            chunks.append({
                "text": current_chunk.strip(),
                "chunk_id": chunk_id,
                "metadata": {**metadata, "chunk_id": chunk_id}
            })
        
        return chunks
    
    def add_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """添加知识到向量库 - 已禁用"""
        logger.info("向量服务已禁用，跳过知识添加")
        return True
        
        try:
            # 清空现有数据（重建索引）
            try:
                self.client.delete_collection(self.collection_name)
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "PMO知识库向量集合"}
                )
                logger.info("🔄 重建向量集合")
            except:
                pass
            
            documents = []
            metadatas = []
            ids = []
            
            # 处理格式化的知识库数据
            if "formatted_knowledge" in knowledge_data:
                text = knowledge_data["formatted_knowledge"]
                metadata = {
                    "type": "formatted_knowledge",
                    "timestamp": datetime.now().isoformat(),
                    "source": "pmo_system"
                }
                chunks = self._chunk_text(text, metadata)
                
                for chunk in chunks:
                    # 生成唯一ID
                    chunk_hash = hashlib.md5(chunk["text"].encode()).hexdigest()
                    chunk_id = f"knowledge_{chunk['chunk_id']}_{chunk_hash[:8]}"
                    
                    documents.append(chunk["text"])
                    metadatas.append(chunk["metadata"])
                    ids.append(chunk_id)
            
            # 批量添加到集合
            if documents:
                # 分批处理，避免内存占用过大
                batch_size = 50
                for i in range(0, len(documents), batch_size):
                    batch_docs = documents[i:i+batch_size]
                    batch_metas = metadatas[i:i+batch_size]
                    batch_ids = ids[i:i+batch_size]
                    
                    self.collection.add(
                        documents=batch_docs,
                        metadatas=batch_metas,
                        ids=batch_ids
                    )
                
                logger.info(f"✅ 成功添加 {len(documents)} 个知识块到轻量级向量库")
                return True
            
        except Exception as e:
            logger.error(f"❌ 添加知识到轻量级向量库失败: {str(e)}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """语义搜索知识 - 已禁用"""
        logger.info("向量服务已禁用，返回空结果")
        return []
    
    def get_relevant_context(self, query: str, max_context_length: int = 1500) -> str:
        """获取查询相关的上下文 - 已禁用"""
        return ""
    
    def get_stats(self) -> Dict[str, Any]:
        """获取向量库统计信息"""
        if not self.client or not self.collection:
            return {"status": "unavailable"}
        
        try:
            count = self.collection.count()
            return {
                "status": "available",
                "total_documents": count,
                "model_name": "text2vec-base-chinese",
                "storage_path": self.data_dir,
                "memory_usage": "< 500MB"
            }
        except Exception as e:
            logger.error(f"❌ 获取轻量级向量库统计失败: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return (self.client is not None and 
                self.embedding_model is not None and 
                self.collection is not None)

# 全局实例
lightweight_vector_service = LightweightVectorService()
