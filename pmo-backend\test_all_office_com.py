#!/usr/bin/env python3
"""
测试全部Office文档的VB.NET/COM解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_office_availability():
    """测试Office应用程序可用性"""
    print("🔧 测试Office应用程序可用性...")
    
    office_apps = {
        "Word": "Word.Application",
        "Excel": "Excel.Application", 
        "PowerPoint": "PowerPoint.Application"
    }
    
    available_apps = {}
    
    try:
        import win32com.client
        print("✅ win32com模块已安装")
        
        for app_name, app_class in office_apps.items():
            try:
                app = win32com.client.Dispatch(app_class)
                app.Visible = False
                print(f"✅ {app_name}应用程序可用")
                app.Quit()
                available_apps[app_name] = True
            except Exception as e:
                print(f"❌ {app_name}应用程序不可用: {str(e)}")
                available_apps[app_name] = False
                
    except ImportError:
        print("❌ win32com模块未安装")
        return {}
    
    return available_apps

def create_test_files():
    """创建测试用的Office文件内容"""
    test_files = {}
    
    # Word文档测试内容
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    word_content = "VB.NET/COM Word解析测试\n这是一个测试用的Word文档\n包含中文内容".encode('utf-16le')
    test_files['word'] = ole_header + b'\x00' * 200 + word_content + b'\x00' * 500
    
    # Excel文档测试内容（简单的OLE格式）
    excel_content = ole_header + b'\x00' * 500 + "测试Excel内容".encode('utf-16le') + b'\x00' * 500
    test_files['excel'] = excel_content
    
    # PowerPoint文档测试内容
    ppt_content = ole_header + b'\x00' * 500 + "测试PowerPoint内容".encode('utf-16le') + b'\x00' * 500
    test_files['ppt'] = ppt_content
    
    return test_files

def test_word_com_parsing():
    """测试Word COM解析"""
    print("\n🔧 测试Word COM解析...")
    
    parser = DocumentParser()
    test_files = create_test_files()
    
    try:
        result = parser._parse_old_doc_file(test_files['word'])
        if result and "Word文档内容" in result:
            print("✅ Word COM解析成功")
            print(f"解析结果预览: {result[:200]}...")
        else:
            print("❌ Word COM解析失败或返回空内容")
            print(f"实际结果: {result[:200]}...")
    except Exception as e:
        print(f"❌ Word COM解析异常: {str(e)}")

def test_excel_com_parsing():
    """测试Excel COM解析"""
    print("\n🔧 测试Excel COM解析...")
    
    parser = DocumentParser()
    test_files = create_test_files()
    
    try:
        result = parser.parse_excel_file(test_files['excel'])
        if result and len(result.strip()) > 10:
            print("✅ Excel COM解析成功")
            print(f"解析结果预览: {result[:200]}...")
        else:
            print("❌ Excel COM解析失败或返回空内容")
            print(f"实际结果: {result[:200]}...")
    except Exception as e:
        print(f"❌ Excel COM解析异常: {str(e)}")

def test_ppt_com_parsing():
    """测试PowerPoint COM解析"""
    print("\n🔧 测试PowerPoint COM解析...")
    
    parser = DocumentParser()
    test_files = create_test_files()
    
    try:
        result = parser.parse_ppt_file(test_files['ppt'])
        if result and len(result.strip()) > 10:
            print("✅ PowerPoint COM解析成功")
            print(f"解析结果预览: {result[:200]}...")
        else:
            print("❌ PowerPoint COM解析失败或返回空内容")
            print(f"实际结果: {result[:200]}...")
    except Exception as e:
        print(f"❌ PowerPoint COM解析异常: {str(e)}")

def test_powershell_scripts():
    """测试PowerShell脚本生成"""
    print("\n🔧 测试PowerShell脚本生成...")
    
    parser = DocumentParser()
    
    # 测试Word PowerShell脚本
    try:
        word_script = parser._create_powershell_doc_parser()
        if word_script and len(word_script) > 100:
            print("✅ Word PowerShell脚本生成成功")
        else:
            print("❌ Word PowerShell脚本生成失败")
    except Exception as e:
        print(f"❌ Word PowerShell脚本生成异常: {str(e)}")
    
    # 测试Excel PowerShell脚本
    try:
        excel_script = parser._create_powershell_excel_parser()
        if excel_script and len(excel_script) > 100:
            print("✅ Excel PowerShell脚本生成成功")
        else:
            print("❌ Excel PowerShell脚本生成失败")
    except Exception as e:
        print(f"❌ Excel PowerShell脚本生成异常: {str(e)}")
    
    # 测试PowerPoint PowerShell脚本
    try:
        ppt_script = parser._create_powershell_ppt_parser()
        if ppt_script and len(ppt_script) > 100:
            print("✅ PowerPoint PowerShell脚本生成成功")
        else:
            print("❌ PowerPoint PowerShell脚本生成失败")
    except Exception as e:
        print(f"❌ PowerPoint PowerShell脚本生成异常: {str(e)}")

def test_backup_methods():
    """测试备用解析方法"""
    print("\n🔧 测试备用解析方法...")
    
    parser = DocumentParser()
    test_files = create_test_files()
    
    # 测试Excel备用方法
    try:
        result = parser._parse_excel_with_python_libs(test_files['excel'])
        print(f"Excel备用方法结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"Excel备用方法异常: {str(e)}")
    
    # 测试PowerPoint备用方法
    try:
        result = parser._parse_ppt_with_python_libs(test_files['ppt'])
        print(f"PowerPoint备用方法结果: {'成功' if result else '失败'}")
    except Exception as e:
        print(f"PowerPoint备用方法异常: {str(e)}")

def show_parsing_priority():
    """显示解析优先级"""
    print("\n📋 Office文档解析优先级:")
    
    priorities = {
        "Word文档": [
            "1. Office COM对象解析（最稳定）",
            "2. PowerShell脚本解析",
            "3. 专门的Word文档正文提取（备用）",
            "4. textract等其他方法"
        ],
        "Excel文档": [
            "1. Excel COM对象解析（最稳定）",
            "2. PowerShell脚本解析",
            "3. openpyxl + xlrd（备用）"
        ],
        "PowerPoint文档": [
            "1. PowerPoint COM对象解析（最稳定）",
            "2. PowerShell脚本解析", 
            "3. python-pptx（备用）"
        ]
    }
    
    for doc_type, priority_list in priorities.items():
        print(f"\n{doc_type}:")
        for priority in priority_list:
            print(f"  {priority}")

if __name__ == "__main__":
    print("🚀 全部Office文档VB.NET/COM解析功能测试")
    print("=" * 70)
    
    try:
        # 测试Office应用程序可用性
        available_apps = test_office_availability()
        
        # 测试PowerShell脚本生成
        test_powershell_scripts()
        
        # 如果Office应用程序可用，测试COM解析
        if available_apps.get('Word', False):
            test_word_com_parsing()
        
        if available_apps.get('Excel', False):
            test_excel_com_parsing()
        
        if available_apps.get('PowerPoint', False):
            test_ppt_com_parsing()
        
        # 测试备用方法
        test_backup_methods()
        
        # 显示解析优先级
        show_parsing_priority()
        
        print("\n✅ 所有测试完成")
        
        if any(available_apps.values()):
            print("💡 现在可以重新启动后端并测试实际的Office文件")
            print("\n🚀 VB.NET/COM解析器的优势:")
            print("1. 直接调用Microsoft Office进行解析")
            print("2. 完美支持所有Office格式")
            print("3. 自动处理编码和格式问题")
            print("4. 提取完整、干净的文档内容")
            print("5. 支持复杂的Office功能（公式、图表、动画等）")
        else:
            print("⚠️  需要安装Microsoft Office才能使用COM解析")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
