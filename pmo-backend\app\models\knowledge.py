#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库模型 - 参考禅道zt_knowledge表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Knowledge:
    """知识库模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.lib: int = 0
        self.title: str = ""
        self.category: str = ""
        self.tags: str = ""
        self.summary: Optional[str] = None
        self.content: Optional[str] = None
        self.author: str = ""
        self.reviewer: str = ""
        self.status: str = "draft"
        self.priority: int = 3
        self.views: int = 0
        self.likes: int = 0
        self.addedBy: str = ""
        self.addedDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.reviewedBy: str = ""
        self.reviewedDate: Optional[datetime] = None
        self.publishedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, knowledge_id: int) -> Optional['Knowledge']:
        """根据ID获取知识"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT k.*, l.name as lib_name
                FROM zt_knowledge k
                LEFT JOIN zt_doclib l ON k.lib = l.id
                WHERE k.id = %s AND k.deleted = '0'
            """, (knowledge_id,))
            
            row = cursor.fetchone()
            if row:
                knowledge = cls()
                knowledge._load_from_dict(row)
                return knowledge
            return None
            
        except Exception as e:
            print(f"获取知识失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_knowledge(cls, page: int = 1, page_size: int = 20, lib_id: int = 0, 
                         category: str = "", status: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取知识列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["k.deleted = '0'"]
            params = []
            
            if lib_id > 0:
                where_conditions.append("k.lib = %s")
                params.append(lib_id)
            
            if category:
                where_conditions.append("k.category = %s")
                params.append(category)
            
            if status:
                where_conditions.append("k.status = %s")
                params.append(status)
            
            if keyword:
                where_conditions.append("(k.title LIKE %s OR k.tags LIKE %s OR k.summary LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_knowledge k WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT k.id, k.title, k.category, k.tags, k.summary, k.author,
                       k.status, k.priority, k.views, k.likes, k.addedBy, k.addedDate,
                       k.lib, l.name as lib_name
                FROM zt_knowledge k
                LEFT JOIN zt_doclib l ON k.lib = l.id
                WHERE {where_clause}
                ORDER BY k.priority ASC, k.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            knowledge_list = cursor.fetchall()
            
            return {
                "knowledge": knowledge_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取知识列表失败: {e}")
            return {"knowledge": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建知识"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO zt_knowledge (
                    lib, title, category, tags, summary, content, author,
                    status, priority, addedBy, addedDate
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                )
            """, (
                self.lib, self.title, self.category, self.tags, self.summary,
                self.content, self.author, self.status, self.priority, self.addedBy
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建知识失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新知识信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_knowledge SET 
                    title = %s, category = %s, tags = %s, summary = %s,
                    content = %s, author = %s, status = %s, priority = %s,
                    editedBy = %s, editedDate = NOW()
                WHERE id = %s
            """, (
                self.title, self.category, self.tags, self.summary,
                self.content, self.author, self.status, self.priority,
                self.editedBy, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新知识失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除知识"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_knowledge SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除知识失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def publish(self, publishedBy: str) -> bool:
        """发布知识"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_knowledge SET 
                    status = 'published', reviewedBy = %s, reviewedDate = NOW(),
                    publishedDate = NOW(), editedBy = %s, editedDate = NOW()
                WHERE id = %s
            """, (publishedBy, publishedBy, self.id))
            
            self.status = 'published'
            self.reviewedBy = publishedBy
            self.reviewedDate = datetime.now()
            self.publishedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"发布知识失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def increase_views(self) -> bool:
        """增加浏览次数"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_knowledge SET views = views + 1 WHERE id = %s
            """, (self.id,))
            
            self.views += 1
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"增加浏览次数失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def like(self) -> bool:
        """点赞"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_knowledge SET likes = likes + 1 WHERE id = %s
            """, (self.id,))
            
            self.likes += 1
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"点赞失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "lib": self.lib,
            "title": self.title,
            "category": self.category,
            "tags": self.tags,
            "summary": self.summary,
            "content": self.content,
            "author": self.author,
            "reviewer": self.reviewer,
            "status": self.status,
            "priority": self.priority,
            "views": self.views,
            "likes": self.likes,
            "addedBy": self.addedBy,
            "addedDate": self.addedDate.isoformat() if self.addedDate else None,
            "editedBy": self.editedBy,
            "editedDate": self.editedDate.isoformat() if self.editedDate else None,
            "reviewedBy": self.reviewedBy,
            "reviewedDate": self.reviewedDate.isoformat() if self.reviewedDate else None,
            "publishedDate": self.publishedDate.isoformat() if self.publishedDate else None,
            "deleted": self.deleted
        }
