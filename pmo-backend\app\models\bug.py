#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缺陷模型 - 参考禅道zt_bug表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Bug:
    """缺陷模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.vision: str = "rnd"
        self.product: int = 0
        self.branch: int = 0
        self.module: int = 0
        self.execution: int = 0
        self.plan: int = 0
        self.story: int = 0
        self.storyVersion: int = 1
        self.task: int = 0
        self.toTask: int = 0
        self.toStory: int = 0
        self.title: str = ""
        self.keywords: str = ""
        self.severity: int = 3
        self.pri: int = 3
        self.type: str = "codeerror"
        self.os: str = ""
        self.browser: str = ""
        self.hardware: str = ""
        self.found: str = ""
        self.steps: Optional[str] = None
        self.status: str = "active"
        self.subStatus: str = ""
        self.color: str = ""
        self.confirmed: str = "1"
        self.activatedCount: int = 0
        self.activatedDate: Optional[datetime] = None
        self.feedbackBy: str = ""
        self.notifyEmail: str = ""
        self.mailto: Optional[str] = None
        self.openedBy: str = ""
        self.openedDate: Optional[datetime] = None
        self.openedBuild: str = ""
        self.assignedTo: str = ""
        self.assignedDate: Optional[datetime] = None
        self.resolvedBy: str = ""
        self.resolution: str = ""
        self.resolvedBuild: str = ""
        self.resolvedDate: Optional[datetime] = None
        self.closedBy: str = ""
        self.closedDate: Optional[datetime] = None
        self.duplicateBug: int = 0
        self.linkBug: str = ""
        self.feedback: int = 0
        self.result: int = 0
        self.repo: int = 0
        self.mr: int = 0
        self.entry: str = ""
        self.lines: str = ""
        self.v1: str = ""
        self.v2: str = ""
        self.repoType: str = ""
        self.testtask: int = 0
        self.lastEditedBy: str = ""
        self.lastEditedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, bug_id: int) -> Optional['Bug']:
        """根据ID获取缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT b.*, p.name as product_name
                FROM zt_bug b
                LEFT JOIN zt_product p ON b.product = p.id
                WHERE b.id = %s AND b.deleted = '0'
            """, (bug_id,))
            
            row = cursor.fetchone()
            if row:
                bug = cls()
                bug._load_from_dict(row)
                return bug
            return None
            
        except Exception as e:
            print(f"获取缺陷失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_bugs(cls, page: int = 1, page_size: int = 20, product_id: int = 0, 
                    status: str = "", keyword: str = "") -> Dict[str, Any]:
        """获取缺陷列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["b.deleted = '0'"]
            params = []
            
            if product_id > 0:
                where_conditions.append("b.product = %s")
                params.append(product_id)
            
            if status:
                where_conditions.append("b.status = %s")
                params.append(status)
            
            if keyword:
                where_conditions.append("(b.title LIKE %s OR b.keywords LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword])
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_bug b WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT b.id, b.title, b.severity, b.pri, b.type, b.status, 
                       b.openedBy, b.openedDate, b.assignedTo, b.product,
                       p.name as product_name
                FROM zt_bug b
                LEFT JOIN zt_product p ON b.product = p.id
                WHERE {where_clause}
                ORDER BY b.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            bugs = cursor.fetchall()
            
            return {
                "bugs": bugs,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取缺陷列表失败: {e}")
            return {"bugs": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入缺陷
            cursor.execute("""
                INSERT INTO zt_bug (
                    vision, product, branch, module, execution, plan, story, storyVersion,
                    task, title, keywords, severity, pri, type, os, browser, hardware,
                    found, steps, status, subStatus, color, confirmed, openedBy, 
                    openedDate, openedBuild, assignedTo, mailto
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s
                )
            """, (
                self.vision, self.product, self.branch, self.module, self.execution,
                self.plan, self.story, self.storyVersion, self.task, self.title,
                self.keywords, self.severity, self.pri, self.type, self.os,
                self.browser, self.hardware, self.found, self.steps, self.status,
                self.subStatus, self.color, self.confirmed, self.openedBy,
                self.openedBuild, self.assignedTo, self.mailto
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新缺陷信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET 
                    title = %s, keywords = %s, severity = %s, pri = %s, type = %s,
                    steps = %s, status = %s, assignedTo = %s, 
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (
                self.title, self.keywords, self.severity, self.pri, self.type,
                self.steps, self.status, self.assignedTo, self.lastEditedBy, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def assign(self, assignedTo: str, assignedBy: str) -> bool:
        """指派缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET 
                    assignedTo = %s, assignedDate = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (assignedTo, assignedBy, self.id))
            
            self.assignedTo = assignedTo
            self.assignedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"指派缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def resolve(self, resolvedBy: str, resolution: str = "", resolvedBuild: str = "") -> bool:
        """解决缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET 
                    status = 'resolved', resolvedBy = %s, resolvedDate = NOW(),
                    resolution = %s, resolvedBuild = %s,
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (resolvedBy, resolution, resolvedBuild, resolvedBy, self.id))
            
            self.status = 'resolved'
            self.resolvedBy = resolvedBy
            self.resolvedDate = datetime.now()
            self.resolution = resolution
            self.resolvedBuild = resolvedBuild
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"解决缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def close(self, closedBy: str) -> bool:
        """关闭缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET 
                    status = 'closed', closedBy = %s, closedDate = NOW(),
                    lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (closedBy, closedBy, self.id))
            
            self.status = 'closed'
            self.closedBy = closedBy
            self.closedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"关闭缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def activate(self, activatedBy: str) -> bool:
        """激活缺陷"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_bug SET 
                    status = 'active', activatedCount = activatedCount + 1,
                    activatedDate = NOW(), lastEditedBy = %s, lastEditedDate = NOW()
                WHERE id = %s
            """, (activatedBy, self.id))
            
            self.status = 'active'
            self.activatedCount += 1
            self.activatedDate = datetime.now()
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"激活缺陷失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "vision": self.vision,
            "product": self.product,
            "module": self.module,
            "story": self.story,
            "task": self.task,
            "title": self.title,
            "keywords": self.keywords,
            "severity": self.severity,
            "pri": self.pri,
            "type": self.type,
            "os": self.os,
            "browser": self.browser,
            "hardware": self.hardware,
            "found": self.found,
            "steps": self.steps,
            "status": self.status,
            "subStatus": self.subStatus,
            "color": self.color,
            "confirmed": self.confirmed,
            "activatedCount": self.activatedCount,
            "activatedDate": self.activatedDate.isoformat() if self.activatedDate else None,
            "openedBy": self.openedBy,
            "openedDate": self.openedDate.isoformat() if self.openedDate else None,
            "openedBuild": self.openedBuild,
            "assignedTo": self.assignedTo,
            "assignedDate": self.assignedDate.isoformat() if self.assignedDate else None,
            "resolvedBy": self.resolvedBy,
            "resolution": self.resolution,
            "resolvedBuild": self.resolvedBuild,
            "resolvedDate": self.resolvedDate.isoformat() if self.resolvedDate else None,
            "closedBy": self.closedBy,
            "closedDate": self.closedDate.isoformat() if self.closedDate else None,
            "lastEditedBy": self.lastEditedBy,
            "lastEditedDate": self.lastEditedDate.isoformat() if self.lastEditedDate else None,
            "deleted": self.deleted
        }
