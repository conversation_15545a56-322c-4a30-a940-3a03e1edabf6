import re
from typing import Any, Optional
from app.core.errors import ValidationError

def validate_required(value: Any, field_name: str) -> None:
    """
    验证必填字段
    
    Args:
        value: 字段值
        field_name: 字段名称
        
    Raises:
        ValidationError: 当字段为空时抛出
    """
    if value is None or (isinstance(value, str) and value.strip() == ""):
        raise ValidationError(f"{field_name}不能为空")

def validate_length(value: str, field_name: str, min_length: int = 0, max_length: Optional[int] = None) -> None:
    """
    验证字符串长度
    
    Args:
        value: 字段值
        field_name: 字段名称
        min_length: 最小长度
        max_length: 最大长度
        
    Raises:
        ValidationError: 当长度不符合要求时抛出
    """
    if value is None:
        return
    
    if min_length > 0 and len(value) < min_length:
        raise ValidationError(f"{field_name}长度不能小于{min_length}个字符")
    
    if max_length is not None and len(value) > max_length:
        raise ValidationError(f"{field_name}长度不能超过{max_length}个字符")

def validate_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """
    验证手机号格式
    
    Args:
        phone: 手机号
        
    Returns:
        是否为有效手机号
    """
    if not phone:
        return False
    
    # 中国大陆手机号格式
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def validate_number_range(value: float, field_name: str, min_value: Optional[float] = None, max_value: Optional[float] = None) -> None:
    """
    验证数值范围
    
    Args:
        value: 字段值
        field_name: 字段名称
        min_value: 最小值
        max_value: 最大值
        
    Raises:
        ValidationError: 当数值不在范围内时抛出
    """
    if value is None:
        return
    
    if min_value is not None and value < min_value:
        raise ValidationError(f"{field_name}不能小于{min_value}")
    
    if max_value is not None and value > max_value:
        raise ValidationError(f"{field_name}不能大于{max_value}") 