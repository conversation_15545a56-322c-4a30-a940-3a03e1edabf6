import{_ as le,u as oe,a as ne,c as h,p as z,o as re,d as u,L as ie,e as d,f as p,g as s,h as a,w as l,k as m,B as de,N as D,G as V,F as B,E as q,j as f,a3 as ue,V as O,$ as ce,t as c,y as _e,P as pe,a0 as ve,Q as me,D as Q,S as fe,T as ye}from"./index-76121fa4.js";import{g as ge}from"./project-0b1ee259.js";import{u as E,a as be}from"./xlsx-32f1569e.js";const he={class:"investment-details"},we={class:"page-header"},ke={class:"header-left"},xe={class:"header-right"},Ce={class:"details-content"},je={class:"stats-header"},Fe={class:"stats-container"},De={class:"stat-card"},Ne={class:"stat-icon project-icon"},ze={class:"stat-content"},Ie={class:"stat-value"},Ve={class:"stat-card"},Be={class:"stat-icon budget-icon"},Ee={class:"stat-content"},Te={class:"stat-value"},Se={class:"stat-card"},Ae={class:"stat-icon implementation-icon"},Le={class:"stat-content"},Me={class:"stat-value"},Pe={class:"stat-card"},$e={class:"stat-icon delayed-icon"},Re={class:"stat-content"},qe={class:"stat-value"},Oe={class:"table-header"},Qe={class:"table-title"},Ue={key:0,class:"project-count"},Ge={class:"table-actions"},We={key:0},He={key:0},Je={key:1},Ke={key:2},Xe={__name:"InvestmentDetails",setup(Ye){const U=oe(),T=ne(),F=h(!1),N=h(null),r=h([]),v=h(""),w=h(""),k=h(""),G=z(()=>!r.value||r.value.length===0?0:r.value.reduce((t,e)=>{const o=parseFloat(e.budget||0)||0;return t+o},0).toFixed(2)),W=z(()=>!r.value||r.value.length===0?0:r.value.filter(t=>t.current_progress&&t.current_progress!=="未开工").reduce((t,e)=>{const o=parseFloat(e.budget||0)||0;return t+o},0).toFixed(2)),H=z(()=>!r.value||r.value.length===0?0:r.value.filter(t=>t.delayed).reduce((t,e)=>{const o=parseFloat(e.budget||0)||0;return t+o},0).toFixed(2)),I=h([{prop:"budget",label:"预算金额(万元)",width:"120",visible:!0},{prop:"start_date",label:"开工日期",width:"120",visible:!0},{prop:"end_date",label:"完工日期",width:"120",visible:!0},{prop:"delayed",label:"是否逾期",width:"80",visible:!0},{prop:"responsible_department",label:"责任部门",width:"120",visible:!0},{prop:"project_manager",label:"项目经理",width:"100",visible:!0},{prop:"project_status",label:"项目状态",width:"100",visible:!0},{prop:"project_type",label:"项目类型",width:"100",visible:!0},{prop:"project_priority",label:"项目优先级",width:"100",visible:!1},{prop:"remarks",label:"备注",width:"200",visible:!1}]),S=z(()=>I.value.filter(t=>t.visible));re(()=>{v.value=T.query.entity||"",w.value=T.query.category||"全部",A()});async function A(){F.value=!0,k.value="";try{const t={};v.value&&(t.entity=v.value),w.value&&w.value!=="全部"&&(t.category=w.value==="开工"?"implementation":w.value==="逾期"?"delayed":"");const e=await ge(t);console.log("API返回数据:",e);let o=[];e&&e.data?o=Array.isArray(e.data)?e.data:e.data.projects||[]:e&&e.projects?o=e.projects:Array.isArray(e)&&(o=e),r.value=o.map(n=>({project_code:n.project_code||"",project_name:n.project_name||"",investment_entity:n.investment_entity||"",current_progress:n.current_progress||"",budget:n.budget||0,start_date:n.start_date||"",end_date:n.end_date||"",delayed:!!n.delayed,responsible_department:n.responsible_department||"",project_manager:n.project_manager||"",project_status:n.project_status||"",project_type:n.project_type||"",project_priority:n.project_priority||"",remarks:n.remarks||"",project_name_1:n.project_name_1||"",...n})),console.log("处理后的项目列表:",r.value),N.value={entity:v.value,category:w.value,projects:r.value}}catch(t){console.error("获取项目数据失败:",t),t.message&&(t.message.includes("数据库连接")||t.message.includes("MySQL")||t.message.includes("timed out"))?k.value="数据库连接暂时不可用，请稍后再试":k.value="获取项目数据失败",r.value=[],N.value=null}finally{F.value=!1}}function J(){U.push("/dashboard")}function K(t){const e=I.value.find(o=>o.prop===t);e&&(e.visible=!e.visible)}function L(){try{const t=r.value.map(y=>{const g={};return g.项目编号=y.project_code,g.项目名称=y.project_name,g.投资主体=y.investment_entity,g.当前阶段=y.current_progress,S.value.forEach(x=>{let b=y[x.prop];M(x.prop)?b=P(b):x.prop==="delayed"&&(b=b?"是":"否"),g[x.label]=b}),g}),e=E.json_to_sheet(t),o=E.book_new();E.book_append_sheet(o,e,"项目列表");const _=`${v.value?`${v.value}-项目列表`:"项目列表"}-${new Date().toISOString().split("T")[0]}.xlsx`;be(o,_),q.success("导出成功")}catch(t){console.error("导出Excel失败:",t),q.error("导出Excel失败，请稍后重试")}}function X(t,e,o,n){const _={fontSize:"12px",padding:"4px 0"};return!e||!e.property||!t||e.property==="delayed"&&t.delayed&&(_.color="#F56C6C"),_}function Y({row:t,rowIndex:e}){return t&&t.delayed?"delayed-row":""}function Z(t){return t?t==="未开工"?"info":t==="已完工"?"success":"warning":""}function M(t){return["start_date","end_date","plan_start_date","plan_end_date"].includes(t)}function P(t){if(!t)return"";try{const e=new Date(t);return isNaN(e.getTime())?t:e.toISOString().split("T")[0]}catch{return t}}function ee(t,e){return t==null?"":["budget","actual_cost"].includes(e)&&!isNaN(parseFloat(t))?parseFloat(t).toFixed(2):t}return(t,e)=>{const o=u("el-button"),n=u("el-alert"),_=u("el-icon"),y=u("el-card"),g=u("el-checkbox"),x=u("el-dropdown-item"),b=u("el-dropdown-menu"),te=u("el-dropdown"),C=u("el-table-column"),$=u("el-tag"),se=u("el-table"),R=u("el-empty"),ae=ie("loading");return d(),p("div",he,[s("div",we,[s("div",ke,[a(o,{onClick:J,icon:m(ue),size:"small"},{default:l(()=>e[1]||(e[1]=[f("返回")])),_:1,__:[1]},8,["icon"]),e[2]||(e[2]=s("h2",null,"投资详情",-1))]),s("div",xe,[a(o,{type:"primary",size:"small",onClick:L,icon:m(O)},{default:l(()=>e[3]||(e[3]=[f("导出Excel")])),_:1,__:[3]},8,["icon"]),a(o,{type:"primary",size:"small",onClick:A,icon:m(ce)},{default:l(()=>e[4]||(e[4]=[f("刷新数据")])),_:1,__:[4]},8,["icon"])])]),de((d(),p("div",Ce,[k.value?(d(),D(n,{key:0,title:k.value,type:"error",description:"请稍后再试或联系管理员","show-icon":"",closable:!1,style:{"margin-bottom":"20px"}},null,8,["title"])):V("",!0),!F.value&&N.value?(d(),p(B,{key:1},[a(y,{class:"stats-card",shadow:"hover"},{default:l(()=>[s("div",je,[s("h3",null,c(v.value||"全部")+" - 项目数据概览",1)]),s("div",Fe,[s("div",De,[s("div",Ne,[a(_,null,{default:l(()=>[a(m(_e))]),_:1})]),s("div",ze,[e[5]||(e[5]=s("div",{class:"stat-title"},"项目总数",-1)),s("div",Ie,c(r.value.length),1)])]),s("div",Ve,[s("div",Be,[a(_,null,{default:l(()=>[a(m(pe))]),_:1})]),s("div",Ee,[e[6]||(e[6]=s("div",{class:"stat-title"},"总投资金额",-1)),s("div",Te,c(G.value)+"万",1)])]),s("div",Se,[s("div",Ae,[a(_,null,{default:l(()=>[a(m(ve))]),_:1})]),s("div",Le,[e[7]||(e[7]=s("div",{class:"stat-title"},"开工金额",-1)),s("div",Me,c(W.value)+"万",1)])]),s("div",Pe,[s("div",$e,[a(_,null,{default:l(()=>[a(m(me))]),_:1})]),s("div",Re,[e[8]||(e[8]=s("div",{class:"stat-title"},"逾期金额",-1)),s("div",qe,c(H.value)+"万",1)])])])]),_:1}),a(y,{class:"project-table-card",shadow:"hover"},{header:l(()=>[s("div",Oe,[s("div",Qe,[f(c(v.value?`${v.value} - 项目列表`:"所有项目列表")+" ",1),r.value.length>0?(d(),p("span",Ue,"(共 "+c(r.value.length)+" 个项目)",1)):V("",!0)]),s("div",Ge,[a(te,{onCommand:K,trigger:"click"},{dropdown:l(()=>[a(b,null,{default:l(()=>[(d(!0),p(B,null,Q(I.value,i=>(d(),D(x,{key:i.prop,command:i.prop},{default:l(()=>[a(g,{modelValue:i.visible,"onUpdate:modelValue":j=>i.visible=j,onClick:e[0]||(e[0]=fe(()=>{},["stop"]))},{default:l(()=>[f(c(i.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["command"]))),128))]),_:1})]),default:l(()=>[a(o,{size:"small"},{default:l(()=>[e[9]||(e[9]=f(" 显示/隐藏列 ")),a(_,{class:"el-icon--right"},{default:l(()=>[a(m(ye))]),_:1})]),_:1,__:[9]})]),_:1}),a(o,{type:"primary",size:"small",onClick:L,icon:m(O)},{default:l(()=>e[10]||(e[10]=[f("导出Excel")])),_:1,__:[10]},8,["icon"])])])]),default:l(()=>[r.value.length>0?(d(),p("div",We,[a(se,{data:r.value,border:"",stripe:"","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",fontWeight:"bold",fontSize:"13px"},"cell-style":X,"row-class-name":Y,style:{width:"100%"},size:"small",height:"calc(100vh - 350px)"},{default:l(()=>[a(C,{prop:"project_code",label:"项目编号",width:"120",fixed:"left"}),a(C,{prop:"project_name",label:"项目名称","min-width":"180",fixed:"left"}),a(C,{prop:"investment_entity",label:"投资主体",width:"120"}),a(C,{prop:"current_progress",label:"当前阶段",width:"120"},{default:l(i=>[a($,{type:Z(i.row.current_progress),size:"small"},{default:l(()=>[f(c(i.row.current_progress),1)]),_:2},1032,["type"])]),_:1}),(d(!0),p(B,null,Q(S.value,i=>(d(),D(C,{key:i.prop,prop:i.prop,label:i.label,width:i.width,"show-overflow-tooltip":!0},{default:l(j=>[M(i.prop)?(d(),p("span",He,c(P(j.row[i.prop])),1)):i.prop==="delayed"?(d(),p("span",Je,[a($,{type:j.row.delayed?"danger":"success",size:"small"},{default:l(()=>[f(c(j.row.delayed?"是":"否"),1)]),_:2},1032,["type"])])):(d(),p("span",Ke,c(ee(j.row[i.prop],i.prop)),1))]),_:2},1032,["prop","label","width"]))),128)),a(C,{prop:"project_name_1",label:"任务名称1",width:"180"})]),_:1},8,["data"])])):(d(),D(R,{key:1,description:"暂无项目数据"}))]),_:1})],64)):!F.value&&!N.value&&!k.value?(d(),D(R,{key:2,description:"暂无数据"})):V("",!0)])),[[ae,F.value]])])}}},st=le(Xe,[["__scopeId","data-v-58bb14f7"]]);export{st as default};
