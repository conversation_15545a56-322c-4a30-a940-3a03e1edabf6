"""
文档解析服务模块
支持Excel、Word、PDF、PPT、图片等多种格式的文档解析
"""

import base64
import json
import asyncio
import os
import io
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Optional
import aiohttp
import httpx
from PIL import Image

from app.core.logger import get_logger

logger = get_logger(__name__)

# 图像处理设置
MAX_IMAGE_SIZE = (1024, 1024)  # 最大图像尺寸，超过此尺寸将进行压缩
MAX_QUALITY = 85  # 最大质量，压缩后的JPEG质量
MIN_IMAGE_SIZE = (320, 320)  # 如果图片小于此尺寸，则不进行压缩

def compress_image(base64_image: str, max_size=MAX_IMAGE_SIZE, quality=MAX_QUALITY) -> str:
    """
    压缩Base64编码的图像，以减少大小和分辨率

    Args:
        base64_image: Base64编码的图像数据
        max_size: 最大图像尺寸 (宽度, 高度)
        quality: JPEG压缩质量 (1-100)

    Returns:
        压缩后的Base64编码图像数据
    """
    try:
        # 提取图像数据部分
        if "base64," in base64_image:
            parts = base64_image.split("base64,", 1)
            if len(parts) == 2:
                header, encoded = parts
                header = header + "base64,"
            else:
                header = "data:image/jpeg;base64,"
                encoded = base64_image
        else:
            header = "data:image/jpeg;base64,"
            encoded = base64_image

        # 解码Base64
        image_data = base64.b64decode(encoded)

        # 打开图像
        image = Image.open(io.BytesIO(image_data))

        # 获取原始尺寸
        original_width, original_height = image.size
        logger.info(f"原始图像尺寸: {original_width}x{original_height}")

        # 检查是否需要压缩 - 图像太小则不压缩
        if (original_width <= MIN_IMAGE_SIZE[0] and original_height <= MIN_IMAGE_SIZE[1]):
            logger.info(f"图像尺寸太小 ({original_width}x{original_height})，不进行压缩")
            return base64_image

        # 检查是否需要压缩 - 图像已在合适范围
        if original_width <= max_size[0] and original_height <= max_size[1]:
            logger.info("图像尺寸在允许范围内，无需压缩")
            return base64_image

        # 计算新尺寸，保持宽高比
        ratio = min(max_size[0] / original_width, max_size[1] / original_height)
        new_width = int(original_width * ratio)
        new_height = int(original_height * ratio)

        # 调整图像大小
        resized_image = image.resize((new_width, new_height), Image.LANCZOS)
        logger.info(f"调整后的图像尺寸: {new_width}x{new_height}")

        # 保存为JPEG格式
        output = io.BytesIO()
        resized_image.save(output, format='JPEG', quality=quality, optimize=True)

        # 编码为Base64
        compressed_data = base64.b64encode(output.getvalue()).decode('utf-8')

        # 返回完整的Base64编码图像
        compressed_result = f"{header}{compressed_data}"
        logger.info(f"压缩比: {len(compressed_result) / len(base64_image):.2f}")

        return compressed_result

    except Exception as e:
        logger.error(f"压缩图像时出错: {e}")
        # 如果压缩失败，返回原始图像
        return base64_image

class DocumentParser:
    """文档解析器类"""
    
    def __init__(self, vision_api_url: str = None, vision_api_key: str = None, vision_model: str = None):
        """初始化文档解析器"""
        # 使用MonkeyOCR API配置
        self.monkey_ocr_url = os.getenv("MONKEY_OCR_URL", "http://10.0.10.42:8670")
        # 保留原有配置作为备用
        self.vision_api_url = vision_api_url or os.getenv("VISION_API_URL", "http://10.0.10.41:8000/v1/chat/completions")
        self.vision_api_key = vision_api_key or os.getenv("VISION_API_KEY", "szjf@2025")
        self.vision_model = vision_model or os.getenv("VISION_MODEL", "/models/Qwen3-32B")

        # 初始化性能优化组件
        self._init_performance_optimizations()

    def _init_performance_optimizations(self):
        """初始化性能优化组件"""
        try:
            # 初始化文档缓存
            from .document_cache import get_document_cache
            self.cache = get_document_cache()
            logger.info("✅ 文档缓存初始化完成")

            # 快速启动模式：延迟初始化COM对象池
            self.com_pool = None
            logger.info("✅ Office COM对象池延迟初始化（提升启动速度）")

        except Exception as e:
            logger.warning(f"性能优化组件初始化失败: {str(e)}")
            self.cache = None
            self.com_pool = None

    def _convert_to_markdown_table(self, table_data: List[List[str]]) -> str:
        """将表格数据转换为Markdown表格格式"""
        if not table_data:
            return ""

        markdown_lines = []

        # 添加表头
        if table_data:
            header = table_data[0]
            markdown_lines.append("| " + " | ".join(str(cell) for cell in header) + " |")
            # 添加分隔线
            markdown_lines.append("| " + " | ".join("---" for _ in header) + " |")

            # 添加数据行
            for row in table_data[1:]:
                # 确保行的列数与表头一致
                while len(row) < len(header):
                    row.append("")
                markdown_lines.append("| " + " | ".join(str(cell) for cell in row[:len(header)]) + " |")

        return "\n".join(markdown_lines)

    def _format_content_as_markdown(self, content: str, document_type: str = "文档") -> str:
        """将VB/COM解析的纯文本内容格式化为Markdown"""
        try:
            if not content or not content.strip():
                return f"# {document_type}内容\n\n📄 文档内容为空"

            lines = content.split('\n')
            markdown_lines = [f"# {document_type}内容\n"]

            current_section = []
            in_table = False
            table_rows = []

            for line in lines:
                line = line.strip()

                # 跳过空行
                if not line:
                    if current_section:
                        current_section.append("")
                    continue

                # 检测工作表/幻灯片标题
                if line.startswith("=== ") and line.endswith(" ==="):
                    # 先处理之前的内容
                    if current_section:
                        markdown_lines.extend(current_section)
                        current_section = []

                    # 添加二级标题
                    title = line.replace("=== ", "").replace(" ===", "")
                    markdown_lines.append(f"## {title}\n")
                    continue

                # 检测表格行（包含制表符或多个空格分隔的数据）
                if '\t' in line or '  ' in line:
                    # 可能是表格数据
                    if '\t' in line:
                        cells = [cell.strip() for cell in line.split('\t')]
                    else:
                        cells = [cell.strip() for cell in line.split('  ') if cell.strip()]

                    if len(cells) > 1:  # 至少两列才认为是表格
                        if not in_table:
                            # 开始新表格
                            if current_section:
                                markdown_lines.extend(current_section)
                                current_section = []
                            in_table = True
                            table_rows = []

                        table_rows.append(cells)
                        continue

                # 如果之前在处理表格，现在遇到非表格行，结束表格
                if in_table:
                    if table_rows:
                        markdown_table = self._convert_to_markdown_table(table_rows)
                        markdown_lines.append(markdown_table)
                        markdown_lines.append("")
                    in_table = False
                    table_rows = []

                # 检测可能的标题行（短行，包含特定关键词）
                if (len(line) < 50 and
                    any(keyword in line for keyword in ['第', '章', '节', '项目', '编号', '标题', '主题', '：', ':']) and
                    not any(char in line for char in ['。', '，', '、'])):
                    # 先处理之前的内容
                    if current_section:
                        markdown_lines.extend(current_section)
                        current_section = []

                    # 添加三级标题
                    markdown_lines.append(f"### {line}\n")
                    continue

                # 普通文本行
                current_section.append(line)

            # 处理最后的表格
            if in_table and table_rows:
                markdown_table = self._convert_to_markdown_table(table_rows)
                markdown_lines.append(markdown_table)
                markdown_lines.append("")

            # 处理剩余的文本内容
            if current_section:
                markdown_lines.extend(current_section)

            result = '\n\n'.join([line for line in markdown_lines if line.strip()])
            return result if result.strip() else f"# {document_type}内容\n\n📄 文档解析完成，但内容为空"

        except Exception as e:
            logger.error(f"Markdown格式化失败: {str(e)}")
            return f"# {document_type}内容\n\n{content}"
    
    def parse_document(self, file_content: bytes, filename: str, content_type: str) -> str:
        """统一文档解析接口 - 支持缓存和性能优化"""
        import time
        import base64

        file_ext = Path(filename).suffix.lower()

        try:
            # 检查文件是否为空
            if not file_content or len(file_content) == 0:
                return f"文件内容为空: {filename}"

            # 检查缓存
            if hasattr(self, 'cache') and self.cache:
                cached_result = self.cache.get_cached_result(file_content, filename)
                if cached_result:
                    logger.info(f"🚀 使用缓存结果: {filename}")
                    return cached_result

            # 记录解析开始时间
            start_time = time.time()
            
            if file_ext in ['.xlsx', '.xls']:
                return self.parse_excel_file(file_content)
            elif file_ext in ['.docx', '.doc']:
                return self.parse_word_file(file_content)
            elif file_ext in ['.pptx', '.ppt']:
                ppt_result = self.parse_ppt_file(file_content)
                # 检查是否包含需要视觉识别的图片
                if ppt_result.startswith("PPT_WITH_IMAGES:"):
                    return ppt_result  # 返回特殊标记，在上层处理图片识别
                return ppt_result
            elif file_ext == '.pdf':
                # PDF需要使用MonkeyOCR异步处理
                pdf_base64 = base64.b64encode(file_content).decode('utf-8')
                return f"PDF_TO_PROCESS:{pdf_base64}"
            elif file_ext in ['.txt', '.csv']:
                # 对于CSV文件，特殊处理转换为制表符格式
                if file_ext == '.csv':
                    try:
                        text_content = file_content.decode('utf-8')
                        lines = text_content.strip().split('\n')
                        content_lines = []
                        
                        for line in lines:
                            if line.strip():
                                # 将CSV格式转换为制表符分隔
                                row_text = line.replace(',', '\t')
                                content_lines.append(row_text)
                        
                        return '\n'.join(content_lines)
                    except:
                        pass
                
                return self.parse_text_file(file_content, filename)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                # 图片需要转换为base64后调用视觉模型
                image_base64 = f"data:{content_type};base64,{base64.b64encode(file_content).decode('utf-8')}"
                # 这里返回一个标记，实际处理在异步函数中
                return f"IMAGE_TO_PROCESS:{image_base64}"
            else:
                result = f"不支持的文件格式: {file_ext}"

            # 计算解析时间并缓存结果
            if result and not result.startswith("不支持的文件格式"):
                parse_time = time.time() - start_time

                # 缓存结果（排除需要异步处理的情况）
                if (hasattr(self, 'cache') and self.cache and
                    not result.startswith(("PDF_TO_PROCESS:", "IMAGE_TO_PROCESS:", "PPT_WITH_IMAGES:"))):
                    self.cache.cache_result(file_content, filename, content_type or "", result, parse_time)

                logger.info(f"📄 文档解析完成: {filename} (耗时: {parse_time:.2f}s)")

            return result

        except Exception as e:
            logger.error(f"文档解析失败: {str(e)}")
            return f"文档解析失败: {str(e)} (文件: {filename})"
    
    def parse_excel_file(self, file_content: bytes) -> str:
        """VB.NET/COM Excel解析 - 直接调用Microsoft Excel"""
        try:
            # 检查文件是否为空
            if not file_content or len(file_content) == 0:
                return "文件内容为空"

            # 方法1: 使用Excel COM对象解析（最稳定）
            try:
                result = self._parse_excel_with_com(file_content)
                if result and len(result.strip()) > 20:
                    return result
            except Exception as e:
                logger.info(f"Excel COM解析失败: {str(e)}")

            # 方法2: 使用PowerShell脚本解析Excel
            try:
                result = self._parse_excel_with_powershell(file_content)
                if result and len(result.strip()) > 20:
                    # 格式化为Markdown
                    return self._format_content_as_markdown(result, "Excel文档")
            except Exception as e:
                logger.info(f"Excel PowerShell解析失败: {str(e)}")

            # 方法3: 备用方案 - openpyxl + xlrd
            try:
                result = self._parse_excel_with_python_libs(file_content)
                if result and len(result.strip()) > 20:
                    return result
            except Exception as e:
                logger.info(f"Excel Python库解析失败: {str(e)}")

            # 如果所有方法都失败
            return f"Excel解析失败: 文件格式不支持或文件损坏。支持的格式: .xlsx, .xls"
            
        except ImportError as e:
            logger.error(f"Excel解析库未安装: {str(e)}")
            return f"Excel解析失败: 缺少必要库，请安装: pip install openpyxl xlrd"
        except Exception as e:
            logger.error(f"Excel解析失败: {str(e)}")
            return f"Excel解析失败: {str(e)}"
    
    def parse_word_file(self, file_content: bytes) -> str:
        """解析Word文件内容"""
        try:
            from docx import Document
            from io import BytesIO
            import zipfile

            # 首先检查文件是否为有效的ZIP格式（.docx文件）
            try:
                # 尝试作为ZIP文件打开
                with zipfile.ZipFile(BytesIO(file_content), 'r') as zip_file:
                    # 检查是否包含Word文档的必要文件
                    if 'word/document.xml' not in zip_file.namelist():
                        return "## Word解析失败\n\n❌ 文件不是有效的Word文档格式"
            except zipfile.BadZipFile:
                # 如果不是ZIP文件，可能是老版本的.doc文件
                return self._parse_old_doc_file(file_content)

            # 解析.docx文件
            doc = Document(BytesIO(file_content))
            markdown_content = ["# Word文档内容\n"]

            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # 根据段落样式判断是否为标题
                    text = paragraph.text.strip()
                    if paragraph.style.name.startswith('Heading'):
                        level = int(paragraph.style.name.split()[-1]) if paragraph.style.name.split()[-1].isdigit() else 1
                        markdown_content.append(f"{'#' * (level + 1)} {text}")
                    else:
                        markdown_content.append(text)

            # 提取表格数据并转换为Markdown表格
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    if any(cell for cell in row_data):  # 如果行不为空
                        table_data.append(row_data)

                if table_data:
                    markdown_content.append("\n" + self._convert_to_markdown_table(table_data) + "\n")

            result = '\n\n'.join(markdown_content)
            return result if result.strip() else "## Word文档\n\n📄 文档内容为空"

        except ImportError as e:
            logger.error(f"python-docx模块未安装: {str(e)}")
            return "## Word解析失败\n\n❌ 缺少python-docx模块，请安装: `pip install python-docx`"
        except Exception as e:
            logger.error(f"Word解析失败: {str(e)}")
            return f"## Word解析失败\n\n❌ {str(e)}"

    def _parse_old_doc_file(self, file_content: bytes) -> str:
        """尝试解析老版本的.doc文件和WPS文档"""

        # 检测文件类型
        file_type = self._detect_old_doc_type(file_content)
        logger.info(f"检测到老版本文档类型: {file_type}")

        # 方法1: 尝试使用docx2txt（支持一些.doc文件）
        try:
            import docx2txt
            from io import BytesIO

            text = docx2txt.process(BytesIO(file_content))
            if text and text.strip():
                # 格式化为Markdown
                lines = text.split('\n')
                markdown_lines = ["# Word文档内容（老版本格式）\n"]

                for line in lines:
                    line = line.strip()
                    if line:
                        markdown_lines.append(line)

                return '\n\n'.join(markdown_lines)

        except ImportError:
            logger.info("docx2txt模块未安装")
        except Exception as e:
            logger.info(f"docx2txt解析失败: {str(e)}")

        # 方法1.5: 尝试使用oletools直接解析OLE文档
        try:
            from oletools import olefile
            from io import BytesIO

            if olefile.isOleFile(file_content):
                logger.info("检测到OLE文件，尝试使用oletools解析")
                ole = olefile.OleFileIO(file_content)

                # 尝试提取Word文档流
                text_content = self._extract_ole_word_text(ole)
                ole.close()

                if text_content:
                    return f"# Word文档内容（OLE格式）\n\n{text_content}"

        except ImportError:
            logger.info("oletools模块未安装")
        except Exception as e:
            logger.info(f"oletools解析失败: {str(e)}")

        # 方法2: 尝试使用oletools解析OLE格式文件（如果可用）
        try:
            # 检查是否为OLE格式
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                text_content = self._extract_ole_text(file_content)
                if text_content:
                    return f"# Word文档内容（OLE格式）\n\n{text_content}"

        except Exception as e:
            logger.info(f"OLE解析失败: {str(e)}")

        # 方法3: 尝试RTF格式解析
        if file_type == "rtf" or b'{\\rtf' in file_content[:100]:
            try:
                text = self._parse_rtf_content(file_content)
                if text:
                    return f"# RTF文档内容\n\n{text}"
            except Exception as e:
                logger.info(f"RTF解析失败: {str(e)}")

        # 方法4: 尝试高级二进制文档解析
        try:
            extracted_text = self._advanced_binary_text_extraction(file_content)
            if extracted_text:
                return f"# 文档内容（高级提取）\n\n{extracted_text}"
        except Exception as e:
            logger.info(f"高级文本提取失败: {str(e)}")

        # 方法5: 尝试简单的文本提取
        try:
            extracted_text = self._extract_readable_text(file_content)
            if extracted_text:
                return f"# 文档内容（文本提取）\n\n{extracted_text}"
        except Exception as e:
            logger.info(f"文本提取失败: {str(e)}")

        # 如果所有方法都失败
        return self._generate_doc_failure_message(file_type)

    def _detect_old_doc_type(self, file_content: bytes) -> str:
        """检测老版本文档类型"""
        try:
            # 检查文件头
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                return "ole_doc"  # OLE格式的.doc文件
            elif file_content.startswith(b'{\\rtf'):
                return "rtf"  # RTF格式
            elif b'Microsoft Office Word' in file_content[:1024]:
                return "doc"
            elif b'WPS' in file_content[:1024]:
                return "wps"
            else:
                return "unknown"
        except Exception:
            return "unknown"

    def _extract_ole_text(self, file_content: bytes) -> str:
        """从OLE格式文件中提取文本"""
        try:
            # 这里可以实现更复杂的OLE文本提取
            # 目前使用简单的方法
            return self._extract_readable_text(file_content)
        except Exception as e:
            logger.error(f"OLE文本提取失败: {str(e)}")
            return ""

    def _extract_ole_word_text(self, ole) -> str:
        """从OLE Word文档中提取文本"""
        try:
            # 尝试读取Word文档的主要文本流
            text_parts = []

            # Word文档的常见流名称
            stream_names = [
                'WordDocument',
                '1Table',
                '0Table',
                'Data',
                'CompObj',
                '\x01CompObj'
            ]

            for stream_name in stream_names:
                try:
                    if ole._olestream_size(stream_name) > 0:
                        stream_data = ole._olestream_read(stream_name)
                        if stream_data:
                            # 尝试从流中提取可读文本
                            readable_text = self._extract_text_from_stream(stream_data)
                            if readable_text:
                                text_parts.append(readable_text)
                except Exception:
                    continue

            if text_parts:
                return '\n\n'.join(text_parts)
            else:
                # 如果无法从流中提取，尝试整体文本提取
                return self._extract_readable_text(ole.save_stream())

        except Exception as e:
            logger.error(f"OLE Word文本提取失败: {str(e)}")
            return ""

    def _extract_text_from_stream(self, stream_data: bytes) -> str:
        """从OLE流数据中提取文本"""
        try:
            import re

            # 方法1: 尝试查找Word文档的文本表
            word_text = self._extract_word_text_table(stream_data)
            if word_text:
                return word_text

            # 方法2: 尝试多种编码解析
            best_text = ""
            best_score = 0

            encodings = ['utf-16le', 'utf-16be', 'utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']

            for encoding in encodings:
                try:
                    text = stream_data.decode(encoding, errors='ignore')

                    # 计算文本质量分数
                    score = self._calculate_text_quality(text)

                    if score > best_score:
                        # 提取有意义的文本片段
                        fragments = self._extract_meaningful_fragments(text)
                        if fragments:
                            best_text = '\n\n'.join(fragments)
                            best_score = score

                except UnicodeDecodeError:
                    continue

            return best_text

        except Exception as e:
            logger.error(f"流文本提取失败: {str(e)}")
            return ""

    def _extract_word_text_table(self, stream_data: bytes) -> str:
        """尝试从Word文档流中提取文本表"""
        try:
            # Word文档的文本通常存储在特定的偏移位置
            # 尝试查找文本表的起始位置

            text_fragments = []

            # 查找可能的文本起始标记
            for i in range(0, len(stream_data) - 20, 1):
                # 检查是否是文本块的开始
                chunk = stream_data[i:i+200]

                # 尝试UTF-16LE解码（Word常用编码）
                try:
                    text = chunk.decode('utf-16le', errors='strict')
                    # 检查是否包含有效的文本字符
                    if self._is_valid_text(text):
                        # 扩展读取
                        extended_chunk = stream_data[i:min(i+2000, len(stream_data))]
                        extended_text = extended_chunk.decode('utf-16le', errors='ignore')

                        # 清理文本
                        cleaned = self._clean_word_text(extended_text)
                        if cleaned and len(cleaned) > 10:
                            text_fragments.append(cleaned)

                except UnicodeDecodeError:
                    continue

            if text_fragments:
                # 去重并合并
                unique_fragments = []
                seen = set()
                for fragment in text_fragments:
                    if fragment not in seen and len(fragment) > 15:
                        seen.add(fragment)
                        unique_fragments.append(fragment)

                return '\n\n'.join(unique_fragments[:10])

            return ""

        except Exception as e:
            logger.error(f"Word文本表提取失败: {str(e)}")
            return ""

    def _is_valid_text(self, text: str) -> bool:
        """检查文本是否有效"""
        if len(text) < 5:
            return False

        # 计算可打印字符的比例
        printable_count = sum(1 for c in text if c.isprintable() or c in '\n\r\t')
        printable_ratio = printable_count / len(text)

        # 检查是否包含字母或中文
        has_letters = any(c.isalpha() or '\u4e00' <= c <= '\u9fff' for c in text)

        return printable_ratio > 0.7 and has_letters

    def _clean_word_text(self, text: str) -> str:
        """清理Word文档提取的文本"""
        import re

        # 移除控制字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

        # 移除过多的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除明显的乱码模式
        text = re.sub(r'[^\w\s\u4e00-\u9fff\.,;:!?()""''""''—–-]', '', text)

        # 分割成行并过滤
        lines = text.split('\n')
        valid_lines = []

        for line in lines:
            line = line.strip()
            if len(line) > 3 and not line.isspace():
                # 检查行是否主要由有意义的字符组成
                meaningful_chars = sum(1 for c in line if c.isalnum() or '\u4e00' <= c <= '\u9fff')
                if meaningful_chars / len(line) > 0.5:
                    valid_lines.append(line)

        return '\n'.join(valid_lines)

    def _calculate_text_quality(self, text: str) -> int:
        """计算文本质量分数"""
        if not text:
            return 0

        score = 0

        # 可打印字符比例
        printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
        score += int(printable_ratio * 50)

        # 字母和中文字符比例
        alpha_ratio = sum(1 for c in text if c.isalpha() or '\u4e00' <= c <= '\u9fff') / len(text)
        score += int(alpha_ratio * 30)

        # 单词数量
        words = len(text.split())
        score += min(words, 20)

        return score

    def _extract_meaningful_fragments(self, text: str) -> list:
        """提取有意义的文本片段"""
        import re

        fragments = []

        # 查找连续的有意义文本
        patterns = [
            r'[a-zA-Z\u4e00-\u9fff][a-zA-Z\u4e00-\u9fff\s\.,;:!?()""''""''—–-]{10,}',  # 一般文本
            r'[\u4e00-\u9fff]+[^\x00-\x1f]*[\u4e00-\u9fff]*',  # 中文文本
            r'[A-Z][a-z]+(?:\s+[A-Za-z]+)*',  # 英文句子
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                match = match.strip()
                if len(match) > 10 and self._is_meaningful_fragment(match):
                    fragments.append(match)

        # 去重并排序
        unique_fragments = list(set(fragments))
        unique_fragments.sort(key=len, reverse=True)

        return unique_fragments[:15]

    def _is_meaningful_fragment(self, fragment: str) -> bool:
        """检查片段是否有意义"""
        # 检查是否包含足够的字母或中文字符
        meaningful_chars = sum(1 for c in fragment if c.isalpha() or '\u4e00' <= c <= '\u9fff')

        return meaningful_chars >= 5 and meaningful_chars / len(fragment) > 0.3

    def _advanced_binary_text_extraction(self, file_content: bytes) -> str:
        """高级二进制文档文本提取"""
        try:
            import struct
            import re

            # 方法1: 查找Word文档的文本块
            text_blocks = []

            # Word文档的文本通常以特定模式存储
            # 尝试查找Unicode文本块
            for i in range(0, len(file_content) - 100, 2):
                try:
                    # 尝试解码为UTF-16LE
                    chunk = file_content[i:i+100]
                    text = chunk.decode('utf-16le', errors='ignore')

                    # 检查是否包含有意义的文本
                    if len(text) > 5 and any(c.isalpha() or '\u4e00' <= c <= '\u9fff' for c in text):
                        # 扩展读取更多文本
                        extended_chunk = file_content[i:min(i+1000, len(file_content))]
                        extended_text = extended_chunk.decode('utf-16le', errors='ignore')

                        # 清理文本
                        cleaned_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', extended_text)
                        if len(cleaned_text.strip()) > 10:
                            text_blocks.append(cleaned_text.strip())

                except UnicodeDecodeError:
                    continue

            # 方法2: 查找ASCII文本块
            ascii_blocks = []
            current_text = ""

            for byte in file_content:
                if 32 <= byte <= 126 or byte in [9, 10, 13]:  # 可打印ASCII + 制表符、换行
                    current_text += chr(byte)
                else:
                    if len(current_text.strip()) > 10:
                        ascii_blocks.append(current_text.strip())
                    current_text = ""

            # 添加最后一个文本块
            if len(current_text.strip()) > 10:
                ascii_blocks.append(current_text.strip())

            # 方法3: 查找中文文本（UTF-8编码）
            chinese_blocks = []
            try:
                # 尝试整体UTF-8解码
                full_text = file_content.decode('utf-8', errors='ignore')
                chinese_matches = re.findall(r'[\u4e00-\u9fff]+[^\x00-\x1f]*[\u4e00-\u9fff]*', full_text)
                for match in chinese_matches:
                    if len(match.strip()) > 5:
                        chinese_blocks.append(match.strip())
            except:
                pass

            # 合并所有提取的文本
            all_blocks = []

            # 去重并过滤
            seen = set()
            for block in text_blocks + ascii_blocks + chinese_blocks:
                block = block.strip()
                if len(block) > 10 and block not in seen:
                    # 进一步清理
                    cleaned_block = re.sub(r'\s+', ' ', block)  # 合并多个空格
                    cleaned_block = re.sub(r'[^\w\s\u4e00-\u9fff\.,;:!?()""''""''—–-]', '', cleaned_block)

                    if len(cleaned_block) > 10:
                        seen.add(block)
                        all_blocks.append(cleaned_block)

            if all_blocks:
                # 按长度排序，优先显示较长的文本块
                all_blocks.sort(key=len, reverse=True)
                return '\n\n'.join(all_blocks[:15])  # 最多15个文本块

            return ""

        except Exception as e:
            logger.error(f"高级二进制文本提取失败: {str(e)}")
            return ""

    def _parse_rtf_content(self, file_content: bytes) -> str:
        """解析RTF格式内容"""
        try:
            import re

            # 解码RTF内容
            text = file_content.decode('utf-8', errors='ignore')

            # 移除RTF控制字符
            text = re.sub(r'\\[a-z]+\d*\s?', '', text)
            text = re.sub(r'[{}]', '', text)
            text = re.sub(r'\\\w+', '', text)

            # 清理并格式化
            lines = []
            for line in text.split('\n'):
                line = line.strip()
                if len(line) > 3:  # 只保留有意义的行
                    lines.append(line)

            return '\n\n'.join(lines[:100])  # 最多100行

        except Exception as e:
            logger.error(f"RTF解析失败: {str(e)}")
            return ""

    def _extract_readable_text(self, file_content: bytes) -> str:
        """从二进制内容中提取可读文本"""
        try:
            import string
            import re

            # 尝试多种编码，优先中文编码
            encodings = ['utf-16le', 'utf-16be', 'utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1', 'cp1252']

            best_text = ""
            best_score = 0

            for encoding in encodings:
                try:
                    text = file_content.decode(encoding, errors='ignore')

                    # 改进的文本片段提取
                    # 1. 提取中文文本片段（3个字符以上）
                    chinese_fragments = re.findall(r'[\u4e00-\u9fff]{3,}', text)

                    # 2. 提取英文文本片段（5个字符以上）
                    english_fragments = re.findall(r'[a-zA-Z]{5,}', text)

                    # 3. 提取混合文本片段（包含中英文和标点）
                    mixed_fragments = re.findall(r'[\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】]{10,}', text)

                    # 合并所有片段
                    all_fragments = chinese_fragments + english_fragments + mixed_fragments

                    if all_fragments:
                        # 计算文本质量分数
                        chinese_count = sum(len(re.findall(r'[\u4e00-\u9fff]', frag)) for frag in all_fragments)
                        english_count = sum(len(re.findall(r'[a-zA-Z]', frag)) for frag in all_fragments)
                        total_chars = chinese_count + english_count

                        # 优先选择包含更多有意义字符的结果
                        score = total_chars

                        if score > best_score:
                            best_score = score
                            # 清理和格式化文本
                            cleaned_fragments = []
                            seen_fragments = set()

                            for fragment in all_fragments[:30]:  # 最多30个片段
                                fragment = fragment.strip()
                                # 去重和过滤
                                if len(fragment) > 5 and fragment not in seen_fragments:
                                    # 进一步清理
                                    fragment = re.sub(r'\s+', ' ', fragment)  # 合并多个空格
                                    fragment = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】-]', '', fragment)  # 移除特殊字符

                                    if len(fragment.strip()) > 5:
                                        cleaned_fragments.append(fragment.strip())
                                        seen_fragments.add(fragment)

                            if cleaned_fragments:
                                best_text = '\n\n'.join(cleaned_fragments)

                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.debug(f"编码 {encoding} 解析失败: {str(e)}")
                    continue

            return best_text

        except Exception as e:
            logger.error(f"文本提取失败: {str(e)}")
            return ""

    def _generate_doc_failure_message(self, file_type: str) -> str:
        """生成文档解析失败的消息"""
        message = "## Word解析失败\n\n"

        if file_type == "ole_doc":
            message += "❌ 检测到老版本.doc文件（OLE格式）\n\n"
            message += "💡 **解决方案：**\n"
            message += "1. 将文件另存为.docx格式后重新上传\n"
            message += "2. 或者安装额外的解析模块：`pip install docx2txt oletools`\n"
        elif file_type == "wps":
            message += "❌ 检测到WPS文档格式\n\n"
            message += "💡 **解决方案：**\n"
            message += "1. 在WPS中将文件另存为.docx格式\n"
            message += "2. 或者导出为PDF格式后上传\n"
        elif file_type == "rtf":
            message += "❌ RTF格式解析失败\n\n"
            message += "💡 **解决方案：**\n"
            message += "1. 将RTF文件另存为.docx格式\n"
        else:
            message += "❌ 不支持的文档格式或文件损坏\n\n"
            message += "💡 **建议：**\n"
            message += "1. 确保文件是有效的Word文档\n"
            message += "2. 尝试将文件另存为.docx格式\n"
            message += "3. 检查文件是否损坏\n"

        return message
    
    async def parse_pdf_file(self, file_content: bytes) -> str:
        """使用MonkeyOCR解析PDF文件内容 - 完全按照官方API用法"""
        try:
            logger.info(f"开始PDF解析，文件大小: {len(file_content)} 字节，MonkeyOCR API: {self.monkey_ocr_url}")

            # 统一设置超时时间为10分钟，给MonkeyOCR充足的处理时间
            file_size_mb = len(file_content) / 1024 / 1024
            timeout_seconds = 600  # 统一设置为10分钟超时，确保复杂文档有足够处理时间

            logger.info(f"PDF文件大小: {file_size_mb:.1f}MB，设置超时时间: {timeout_seconds}秒（10分钟）")

            timeout = aiohttp.ClientTimeout(total=timeout_seconds)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 按照官方API用法：PDF文件必须使用 /parse 端点
                logger.info("使用MonkeyOCR /parse端点解析PDF")
                markdown_content = await self.monkey_ocr_pdf_parse(session, file_content)

                if markdown_content and markdown_content.strip():
                    # MonkeyOCR默认输出就是标准Markdown格式，直接使用，不要额外处理
                    logger.info(f"MonkeyOCR PDF解析成功，提取内容长度: {len(markdown_content)}")
                    return markdown_content
                else:
                    logger.warning("MonkeyOCR未从PDF中提取到内容，尝试传统解析")
                    return self.fallback_pdf_parse(file_content)

        except Exception as e:
            logger.error(f"MonkeyOCR PDF解析异常: {str(e)}", exc_info=True)
            # 如果MonkeyOCR失败，回退到传统PDF解析
            logger.info("开始使用传统PDF解析方法")
            return self.fallback_pdf_parse(file_content)

    def fallback_pdf_parse(self, file_content: bytes) -> str:
        """传统PDF解析方法（备用）"""
        try:
            # 尝试多种PDF解析方法
            logger.info("使用传统方法解析PDF文件")

            # 方法1: 尝试使用pdfplumber
            try:
                import pdfplumber
                from io import BytesIO
                logger.info("尝试使用pdfplumber解析PDF")
                return self._parse_with_pdfplumber(file_content)
            except ImportError:
                logger.info("pdfplumber模块未安装")
            except Exception as e:
                logger.warning(f"pdfplumber解析失败: {str(e)}")

            # 方法2: 尝试使用PyPDF2
            try:
                import PyPDF2
                from io import BytesIO
                logger.info("尝试使用PyPDF2解析PDF")
                return self._parse_with_pypdf2(file_content)
            except ImportError:
                logger.info("PyPDF2模块未安装")
            except Exception as e:
                logger.warning(f"PyPDF2解析失败: {str(e)}")

            # 方法3: 尝试使用pdfminer
            try:
                from pdfminer.high_level import extract_text
                logger.info("尝试使用pdfminer解析PDF")
                return self._parse_with_pdfminer(file_content)
            except ImportError:
                logger.info("pdfminer模块未安装")
            except Exception as e:
                logger.warning(f"pdfminer解析失败: {str(e)}")

            return "PDF传统解析失败: 所有解析方法都不可用"

        except Exception as e:
            logger.error(f"PDF传统解析异常: {str(e)}")
            return f"PDF传统解析失败: {str(e)}"

    def _parse_with_pdfplumber(self, file_content: bytes) -> str:
        """使用pdfplumber解析PDF"""
        try:
            import pdfplumber
            from io import BytesIO

            markdown_content = ["# PDF文档内容\n"]

            with pdfplumber.open(BytesIO(file_content)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    markdown_content.append(f"## 第 {page_num} 页\n")

                    # 提取文本
                    text = page.extract_text()
                    if text:
                        markdown_content.append(text)
                        markdown_content.append("\n")

                    # 提取表格
                    tables = page.extract_tables()
                    for table_num, table in enumerate(tables, 1):
                        if table:
                            markdown_content.append(f"### 表格 {table_num}\n")
                            for row in table:
                                if row:
                                    row_text = " | ".join(str(cell) if cell else "" for cell in row)
                                    markdown_content.append(f"| {row_text} |")
                            markdown_content.append("\n")

            return "\n".join(markdown_content)

        except Exception as e:
            logger.error(f"pdfplumber解析失败: {str(e)}")
            raise e

    def _parse_with_pypdf2(self, file_content: bytes) -> str:
        """使用PyPDF2解析PDF"""
        try:
            import PyPDF2
            from io import BytesIO

            markdown_content = ["# PDF文档内容\n"]

            pdf_reader = PyPDF2.PdfReader(BytesIO(file_content))

            for page_num, page in enumerate(pdf_reader.pages, 1):
                markdown_content.append(f"## 第 {page_num} 页\n")
                text = page.extract_text()
                if text:
                    markdown_content.append(text)
                    markdown_content.append("\n")

            return "\n".join(markdown_content)

        except Exception as e:
            logger.error(f"PyPDF2解析失败: {str(e)}")
            raise e

    def _parse_with_pdfminer(self, file_content: bytes) -> str:
        """使用pdfminer解析PDF"""
        try:
            from pdfminer.high_level import extract_text
            from io import BytesIO

            text = extract_text(BytesIO(file_content))

            if text:
                return f"# PDF文档内容\n\n{text}"
            else:
                return "PDF文档内容为空"

        except Exception as e:
            logger.error(f"pdfminer解析失败: {str(e)}")
            raise e
            markdown_content = ["# PDF文档内容\n"]

            with pdfplumber.open(BytesIO(file_content)) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    # 添加页面标题
                    if len(pdf.pages) > 1:
                        markdown_content.append(f"## 第 {page_num} 页")

                    # 提取文本
                    text = page.extract_text()
                    if text:
                        # 将文本按段落分割
                        paragraphs = text.split('\n\n')
                        for paragraph in paragraphs:
                            if paragraph.strip():
                                markdown_content.append(paragraph.strip())

                    # 提取表格并转换为Markdown表格
                    tables = page.extract_tables()
                    for table in tables:
                        table_data = []
                        for row in table:
                            if row and any(cell for cell in row):
                                row_data = [str(cell).strip() if cell else '' for cell in row]
                                table_data.append(row_data)

                        if table_data:
                            markdown_content.append("\n" + self._convert_to_markdown_table(table_data) + "\n")

            result = '\n\n'.join(markdown_content)
            logger.info(f"传统PDF解析完成，提取内容长度: {len(result)}")
            return result if result.strip() else "PDF文件中未找到可提取的内容"

        except ImportError as e:
            logger.error(f"pdfplumber模块未安装: {str(e)}")
            return f"PDF解析失败: 缺少pdfplumber模块，请安装: pip install pdfplumber"
        except Exception as e:
            logger.error(f"传统PDF解析失败: {str(e)}")
            return f"PDF解析失败: {str(e)}"

    def format_pdf_content_as_markdown(self, content: str) -> str:
        """格式化PDF内容为Markdown"""
        try:
            # 如果内容已经是Markdown格式，进行优化
            if any(marker in content for marker in ['#', '##', '###', '|', '-']):
                # 添加文档标题
                formatted_content = f"# PDF文档内容\n\n{content}"
            else:
                # 如果是纯文本，格式化为Markdown
                lines = content.split('\n')
                markdown_lines = ["# PDF文档内容\n"]

                current_section = []
                for line in lines:
                    line = line.strip()
                    if line:
                        # 检测标题行
                        if len(line) < 100 and any(char in line for char in ['第', '章', '节', '项目', '编号', '：', ':']):
                            if current_section:
                                markdown_lines.extend(current_section)
                                current_section = []
                            markdown_lines.append(f"## {line}\n")
                        else:
                            current_section.append(f"{line}\n")
                    else:
                        current_section.append("\n")

                # 添加剩余内容
                if current_section:
                    markdown_lines.extend(current_section)

                formatted_content = ''.join(markdown_lines)

            return formatted_content

        except Exception as e:
            logger.error(f"PDF内容格式化失败: {str(e)}")
            return f"# PDF文档内容\n\n{content}"

    def parse_text_file(self, file_content: bytes, filename: str) -> str:
        """解析文本文件内容"""
        try:
            # 检查文件是否为空
            if not file_content or len(file_content) == 0:
                return "文件内容为空"
            
            import chardet
            
            # 检测编码
            detected = chardet.detect(file_content)
            encoding = detected.get('encoding')
            
            # 如果检测不到编码，使用默认编码
            if not encoding:
                encoding = 'utf-8'
            
            # 解码文本
            text = file_content.decode(encoding)
            return text
        except ImportError as e:
            logger.error(f"chardet模块未安装: {str(e)}")
            # 如果没有chardet，尝试常见编码
            try:
                return file_content.decode('utf-8')
            except:
                try:
                    return file_content.decode('gbk')
                except:
                    return f"文本解析失败: 编码检测失败，请安装chardet模块"
        except Exception as e:
            logger.error(f"文本解析失败: {str(e)}")
            return f"文本解析失败: {str(e)}"

    def parse_ppt_file(self, file_content: bytes) -> str:
        """VB.NET/COM PowerPoint解析 - 直接调用Microsoft PowerPoint"""
        try:
            # 检查文件是否为空
            if not file_content or len(file_content) == 0:
                return "文件内容为空"

            logger.info("开始解析PowerPoint文件...")

            # 方法1: 使用PowerPoint COM对象解析（最稳定）
            try:
                logger.info("尝试使用PowerPoint COM解析")
                result = self._parse_ppt_with_com(file_content)
                if result and len(result.strip()) > 20:
                    logger.info(f"PowerPoint COM解析成功，内容长度: {len(result)}")
                    # 检查是否包含需要视觉识别的图片
                    if "[图片内容_需要OCR识别]" in result:
                        return f"PPT_WITH_IMAGES_COM:{result}"
                    # 格式化为Markdown
                    return self._format_content_as_markdown(result, "PowerPoint文档")
                else:
                    logger.info("PowerPoint COM解析返回空内容")
            except Exception as e:
                logger.info(f"PowerPoint COM解析失败: {str(e)}")

            # 方法2: 使用PowerShell脚本解析PowerPoint
            try:
                logger.info("尝试使用PowerShell脚本解析PowerPoint")
                result = self._parse_ppt_with_powershell(file_content)
                if result and len(result.strip()) > 20:
                    logger.info(f"PowerPoint PowerShell解析成功，内容长度: {len(result)}")
                    # 格式化为Markdown
                    return self._format_content_as_markdown(result, "PowerPoint文档")
                else:
                    logger.info("PowerPoint PowerShell解析返回空内容")
            except Exception as e:
                logger.info(f"PowerPoint PowerShell解析失败: {str(e)}")

            # 方法3: 备用方案 - python-pptx
            try:
                logger.info("尝试使用Python库解析PowerPoint")
                result = self._parse_ppt_with_python_libs(file_content)
                if result and len(result.strip()) > 20:
                    logger.info(f"PowerPoint Python库解析成功，内容长度: {len(result)}")
                    return result
                else:
                    logger.info("PowerPoint Python库解析返回空内容")
            except Exception as e:
                logger.info(f"PowerPoint Python库解析失败: {str(e)}")

            # 如果所有方法都失败
            logger.warning("所有PowerPoint解析方法都失败")
            return "PowerPoint解析失败: 文件格式不支持或文件损坏"

            prs = Presentation(BytesIO(file_content))
            content_lines = []
            image_tasks = []  # 存储需要视觉识别的图片

            for slide_num, slide in enumerate(prs.slides, 1):
                # 添加幻灯片标题
                content_lines.append(f"=== 幻灯片 {slide_num} ===")

                for shape in slide.shapes:
                    # 1. 处理文本内容
                    if hasattr(shape, "text") and shape.text.strip():
                        text = shape.text.strip()
                        content_lines.append(text)

                    # 2. 处理表格
                    if shape.has_table:
                        table = shape.table
                        for row in table.rows:
                            row_values = []
                            for cell in row.cells:
                                cell_text = cell.text.strip() if cell.text else ''
                                row_values.append(cell_text)

                            if any(val for val in row_values):  # 跳过空行
                                row_text = '\t'.join(row_values)
                                content_lines.append(row_text)

                    # 3. 处理图片（关键改进：用视觉大模型识别）
                    if hasattr(shape, 'image') and shape.image:
                        try:
                            # 提取图片数据
                            image_stream = shape.image.blob
                            image_base64 = base64.b64encode(image_stream).decode('utf-8')

                            # 构造图片数据URL
                            image_data_url = f"data:image/png;base64,{image_base64}"

                            # 标记需要视觉识别的图片
                            image_tasks.append({
                                'slide_num': slide_num,
                                'image_data': image_data_url,
                                'placeholder': f"[幻灯片{slide_num}_图片内容_待识别]"
                            })

                            # 先添加占位符
                            content_lines.append(f"[幻灯片{slide_num}_图片内容_待识别]")

                        except Exception as img_error:
                            logger.warning(f"图片提取失败: {str(img_error)}")
                            content_lines.append(f"[幻灯片{slide_num}_图片内容: 提取失败]")

                content_lines.append("")  # 幻灯片之间添加空行

            # 处理文本内容
            text_content = '\n'.join(content_lines)

            # 如果有图片需要识别，返回特殊标记
            if image_tasks:
                # 将图片任务信息编码到结果中
                import json
                image_info = json.dumps(image_tasks)
                return f"PPT_WITH_IMAGES:{image_info}|||{text_content}"

            return text_content

        except ImportError as e:
            logger.error(f"python-pptx模块未安装: {str(e)}")
            return f"PPT解析失败: 缺少python-pptx模块，请安装: pip install python-pptx"
        except Exception as e:
            logger.error(f"PPT解析失败: {str(e)}")
            return f"PPT解析失败: {str(e)}"

    async def parse_image_with_vision_model(self, image_base64: str, prompt: str = None) -> str:
        """使用MonkeyOCR解析图片内容 - 完全按照官方API用法"""
        try:
            logger.info(f"使用MonkeyOCR解析图片，API地址: {self.monkey_ocr_url}")

            # 将base64转换为字节数据
            import base64
            import aiohttp
            import io

            # 处理base64数据
            if image_base64.startswith('data:image'):
                image_base64 = image_base64.split(',')[1]

            image_bytes = base64.b64decode(image_base64)

            # 按照官方API用法：图片使用 /ocr/text 和 /ocr/table
            # 设置10分钟超时，确保复杂图片有足够处理时间
            timeout = aiohttp.ClientTimeout(total=600)  # 10分钟超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 1. 文本提取 - 使用 /ocr/text
                text_content = await self.monkey_ocr_text_extraction(session, image_bytes)

                # 2. 表格提取 - 使用 /ocr/table
                table_content = await self.monkey_ocr_table_extraction(session, image_bytes)

                # 合并内容
                if text_content or table_content:
                    combined_content = self.format_monkey_ocr_content(text_content, table_content)
                    logger.info(f"MonkeyOCR图片解析成功，内容长度: {len(combined_content)}")
                    return combined_content
                else:
                    logger.warning("MonkeyOCR未提取到任何内容")
                    return "图片中未检测到文本内容"

        except Exception as e:
            logger.error(f"MonkeyOCR图片解析失败: {str(e)}")
            return f"图片解析失败: {str(e)}"

    async def monkey_ocr_pdf_parse(self, session: aiohttp.ClientSession, file_bytes: bytes) -> str:
        """MonkeyOCR PDF解析 - 使用官方 /parse 端点"""
        try:
            import zipfile
            import io

            data = aiohttp.FormData()
            data.add_field('file',
                          io.BytesIO(file_bytes),
                          filename='document.pdf',
                          content_type='application/pdf')

            # 使用/parse端点进行PDF解析
            async with session.post(f"{self.monkey_ocr_url}/parse", data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"MonkeyOCR返回结果: {result}")

                    # 根据官方API，返回的是ParseResponse格式
                    if result.get('success', False):
                        # 如果有content字段，直接返回
                        if 'content' in result and result['content']:
                            content = result['content']
                            logger.info("MonkeyOCR PDF解析成功，直接获取content")
                            return content
                        # 如果没有content，尝试从files中读取
                        elif 'files' in result and result['files']:
                            logger.info("MonkeyOCR PDF解析成功，从files字段读取结果")
                            files = result['files']
                            download_url = result.get('download_url', '')

                            # 查找.md文件
                            md_file = None
                            if isinstance(files, list):
                                for file in files:
                                    if isinstance(file, str) and file.endswith('.md'):
                                        md_file = file
                                        break

                            if download_url and md_file:
                                # 尝试下载zip文件并提取.md文件内容
                                try:
                                    zip_url = f"{self.monkey_ocr_url}{download_url}"
                                    logger.info(f"尝试下载MonkeyOCR结果文件: {zip_url}")

                                    async with session.get(zip_url) as zip_response:
                                        if zip_response.status == 200:
                                            zip_content = await zip_response.read()
                                            logger.info(f"zip文件下载成功，大小: {len(zip_content)} 字节")

                                            # 解压zip文件并读取内容
                                            with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
                                                logger.info(f"zip文件内容: {zip_file.namelist()}")

                                                # 优先尝试读取content_list.json文件（结构化数据）
                                                content_list_file = None
                                                md_file = None

                                                for file_name in zip_file.namelist():
                                                    if 'content_list.json' in file_name:
                                                        content_list_file = file_name
                                                    elif file_name.endswith('.md'):
                                                        md_file = file_name

                                                # 直接使用.md文件，MonkeyOCR的输出对AI分析已经足够好

                                                # 读取.md文件（MonkeyOCR的原始输出，适合AI分析）
                                                if md_file:
                                                    logger.info(f"读取文件: {md_file}")
                                                    with zip_file.open(md_file) as md_file_obj:
                                                        content = md_file_obj.read().decode('utf-8')
                                                        logger.info(f"成功从zip文件读取.md内容，长度: {len(content)}")
                                                        return content

                                                logger.warning("zip文件中未找到可用的内容文件")
                                                return "PDF解析成功，但zip文件中未找到可用内容"
                                        else:
                                            error_text = await zip_response.text()
                                            logger.warning(f"无法下载MonkeyOCR结果文件: {zip_response.status} - {error_text}")
                                except Exception as e:
                                    logger.warning(f"下载或解析MonkeyOCR结果文件异常: {str(e)}")
                                    import traceback
                                    logger.warning(f"异常详情: {traceback.format_exc()}")

                            # 如果无法读取文件，返回文件列表信息
                            logger.warning(f"无法读取MonkeyOCR解析结果文件: {md_file}")
                            return f"PDF解析成功，生成文件: {', '.join(files) if isinstance(files, list) else str(files)}"
                        else:
                            logger.warning("MonkeyOCR返回成功但没有content或files字段")
                            return "PDF解析成功，但返回格式异常"
                    else:
                        error_msg = result.get('message', 'Unknown error')
                        logger.warning(f"MonkeyOCR PDF解析失败: {error_msg}")
                        return ""
                else:
                    error_text = await response.text()
                    logger.warning(f"MonkeyOCR PDF解析失败: {response.status} - {error_text}")
                    return ""

        except Exception as e:
            logger.warning(f"MonkeyOCR PDF解析异常: {str(e)}")
            import traceback
            logger.warning(f"异常详情: {traceback.format_exc()}")
            return ""

    async def monkey_ocr_text_extraction(self, session: aiohttp.ClientSession, image_bytes: bytes) -> str:
        """MonkeyOCR文本提取 - 对应官方的-t text"""
        try:
            data = aiohttp.FormData()
            data.add_field('file',
                          io.BytesIO(image_bytes),
                          filename='image.jpg',
                          content_type='image/jpeg')

            async with session.post(f"{self.monkey_ocr_url}/ocr/text", data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get('content', '')
                    logger.info("MonkeyOCR文本提取成功")
                    return content
                else:
                    logger.warning(f"MonkeyOCR文本提取失败: {response.status}")
                    return ""

        except Exception as e:
            logger.warning(f"MonkeyOCR文本提取异常: {str(e)}")
            return ""

    async def monkey_ocr_table_extraction(self, session: aiohttp.ClientSession, image_bytes: bytes) -> str:
        """MonkeyOCR表格提取 - 对应官方的-t table"""
        try:
            data = aiohttp.FormData()
            data.add_field('file',
                          io.BytesIO(image_bytes),
                          filename='image.jpg',
                          content_type='image/jpeg')

            async with session.post(f"{self.monkey_ocr_url}/ocr/table", data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get('content', '')
                    logger.info("MonkeyOCR表格提取成功")
                    return content
                else:
                    logger.warning(f"MonkeyOCR表格提取失败: {response.status}")
                    return ""

        except Exception as e:
            logger.warning(f"MonkeyOCR表格提取异常: {str(e)}")
            return ""

    def format_monkey_ocr_content(self, text_content: str, table_content: str = "") -> str:
        """格式化MonkeyOCR提取的内容 - 基于官方输出格式优化"""
        try:
            # MonkeyOCR默认输出就是Markdown格式，主要做内容合并和优化
            markdown_parts = []

            # 添加文档标题
            markdown_parts.append("# 文档内容")

            # 处理文本内容
            if text_content and text_content.strip():
                # MonkeyOCR输出的内容通常已经是良好的Markdown格式
                if text_content.startswith('#'):
                    # 如果已经有标题，直接使用
                    markdown_parts.append(text_content)
                else:
                    # 否则添加一个二级标题
                    markdown_parts.append("## 文本内容")
                    markdown_parts.append(text_content)

            # 处理表格内容
            if table_content and table_content.strip():
                markdown_parts.append("## 表格内容")
                # MonkeyOCR的表格输出通常已经是Markdown格式
                markdown_parts.append(table_content)

            result = '\n\n'.join(markdown_parts)
            logger.info(f"MonkeyOCR内容格式化完成，长度: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"MonkeyOCR内容格式化失败: {str(e)}")
            # 如果格式化失败，返回原始内容
            return f"# 文档内容\n\n{text_content}\n\n{table_content if table_content else ''}"

    async def process_ppt_with_images(self, parsed_content: str) -> str:
        """处理包含图片的PPT内容"""
        try:
            import json
            # 分离图片信息和文本内容
            parts = parsed_content.replace("PPT_WITH_IMAGES:", "").split("|||", 1)
            image_tasks = json.loads(parts[0])
            text_content = parts[1] if len(parts) > 1 else ""

            # 处理每个图片
            for task in image_tasks:
                try:
                    # 用视觉大模型识别图片（使用简洁prompt，要求Markdown格式）
                    image_result = await self.parse_image_with_vision_model(
                        task['image_data'],
                        "提取图片文字，以Markdown格式输出。表格用Markdown表格格式。"
                    )

                    # 替换占位符
                    text_content = text_content.replace(
                        task['placeholder'],
                        f"[幻灯片{task['slide_num']}_图片内容]: {image_result}"
                    )

                except Exception as img_error:
                    logger.error(f"PPT图片识别失败: {str(img_error)}")
                    text_content = text_content.replace(
                        task['placeholder'],
                        f"[幻灯片{task['slide_num']}_图片内容]: 识别失败 - {str(img_error)}"
                    )

            return text_content

        except Exception as e:
            logger.error(f"PPT图片处理失败: {str(e)}")
            return f"PPT解析失败: 图片处理错误 - {str(e)}"

    def _parse_old_ppt_file(self, file_content: bytes) -> str:
        """尝试解析老版本的.ppt文件"""
        logger.info("检测到老版本PPT文件，尝试解析...")

        # 检测文件类型
        if file_content.startswith(b'\xd0\xcf\x11\xe0'):
            file_type = "ole_ppt"
        else:
            file_type = "unknown"

        logger.info(f"检测到PPT文件类型: {file_type}")

        # 方法1: 尝试使用oletools解析OLE格式PPT
        try:
            from oletools import olefile
            from io import BytesIO

            if olefile.isOleFile(file_content):
                logger.info("检测到OLE PPT文件，尝试使用oletools解析")
                ole = olefile.OleFileIO(file_content)

                # 尝试提取PPT文档流
                text_content = self._extract_ole_ppt_text(ole)
                ole.close()

                if text_content:
                    return f"# PowerPoint文档内容（OLE格式）\n\n{text_content}"

        except ImportError:
            logger.info("oletools模块未安装")
        except Exception as e:
            logger.info(f"oletools PPT解析失败: {str(e)}")

        # 方法2: 尝试通用OLE文本提取
        try:
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                text_content = self._extract_readable_text(file_content)
                if text_content and len(text_content.strip()) > 10:
                    return f"# PowerPoint文档内容（OLE格式）\n\n{text_content}"

        except Exception as e:
            logger.info(f"OLE PPT解析失败: {str(e)}")

        # 方法3: 尝试高级二进制文档解析
        try:
            extracted_text = self._advanced_binary_text_extraction(file_content)
            if extracted_text:
                return f"# PowerPoint文档内容（高级提取）\n\n{extracted_text}"

        except Exception as e:
            logger.info(f"高级PPT提取失败: {str(e)}")

        # 如果所有方法都失败
        return self._generate_ppt_failure_message(file_type)

    def _extract_ole_ppt_text(self, ole) -> str:
        """从OLE PowerPoint文档中提取文本"""
        try:
            text_parts = []

            # PowerPoint文档的常见流名称
            stream_names = [
                'PowerPoint Document',
                'Current User',
                'Pictures',
                'CompObj',
                '\x01CompObj',
                'DocumentSummaryInformation',
                'SummaryInformation'
            ]

            for stream_name in stream_names:
                try:
                    if ole._olestream_size(stream_name) > 0:
                        stream_data = ole._olestream_read(stream_name)
                        if stream_data:
                            # 尝试从流中提取可读文本
                            readable_text = self._extract_text_from_stream(stream_data)
                            if readable_text:
                                text_parts.append(readable_text)
                except Exception:
                    continue

            if text_parts:
                return '\n\n'.join(text_parts)
            else:
                # 如果无法从流中提取，尝试整体文本提取
                return self._extract_readable_text(ole.save_stream())

        except Exception as e:
            logger.error(f"OLE PPT文本提取失败: {str(e)}")
            return ""

    def _generate_ppt_failure_message(self, file_type: str) -> str:
        """生成PPT解析失败的消息"""
        message = "## PowerPoint解析失败\n\n"

        if file_type == "ole_ppt":
            message += "❌ 检测到老版本.ppt文件（OLE格式）\n\n"
            message += "💡 **解决方案：**\n"
            message += "1. 将文件另存为.pptx格式后重新上传\n"
            message += "2. 或者安装额外的解析模块：`pip install oletools`\n"
            message += "3. 也可以导出为PDF格式后上传\n"
        else:
            message += "❌ 不支持的PowerPoint格式或文件损坏\n\n"
            message += "💡 **建议：**\n"
            message += "1. 确保文件是有效的PowerPoint文档\n"
            message += "2. 尝试将文件另存为.pptx格式\n"
            message += "3. 检查文件是否损坏\n"

        return message

    def _parse_old_doc_file(self, file_content: bytes) -> str:
        """专门解析Word文档正文内容 - 避免提取OLE结构信息"""
        logger.info("开始解析老版本DOC文件...")

        # 检测文件类型
        if file_content.startswith(b'\xd0\xcf\x11\xe0'):
            file_type = "ole_doc"
        else:
            file_type = "unknown"

        logger.info(f"检测到DOC文件类型: {file_type}")

        # 方法1: 使用VB.NET/PowerShell解析（最稳定，直接调用Word）
        try:
            result = self._parse_with_office_com(file_content)
            if result and len(result.strip()) > 20:
                # 格式化为Markdown
                return self._format_content_as_markdown(result, "Word文档")
        except Exception as e:
            logger.info(f"Office COM解析失败: {str(e)}")

        # 方法2: 使用PowerShell脚本解析
        try:
            result = self._parse_with_powershell_script(file_content)
            if result and len(result.strip()) > 20:
                # 格式化为Markdown
                return self._format_content_as_markdown(result, "Word文档")
        except Exception as e:
            logger.info(f"PowerShell解析失败: {str(e)}")

        # 方法3: 专门的Word文档正文提取（备用）
        try:
            result = self._extract_word_document_content_only(file_content)
            if result and len(result.strip()) > 20:
                return f"# Word文档内容\n\n{result}"
        except Exception as e:
            logger.info(f"Word文档正文提取失败: {str(e)}")

        # 方法4: 尝试使用textract
        try:
            result = self._parse_with_textract(file_content)
            if result and len(result.strip()) > 20:
                return result
        except Exception as e:
            logger.info(f"textract解析失败: {str(e)}")

        # 方法3: 专门的中文文本提取
        try:
            chinese_text = self._extract_chinese_text_only(file_content)
            if chinese_text and len(chinese_text.strip()) > 10:
                return f"# Word文档内容\n\n{chinese_text}"
        except Exception as e:
            logger.info(f"中文文本提取失败: {str(e)}")

        # 方法4: 使用docx2txt（如果可用）
        try:
            import docx2txt
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                text_content = docx2txt.process(temp_file_path)
                if text_content and text_content.strip():
                    cleaned = self._clean_docx2txt_output(text_content)
                    if cleaned:
                        return f"# Word文档内容\n\n{cleaned}"
            finally:
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except ImportError:
            logger.info("docx2txt模块未安装")
        except Exception as e:
            logger.info(f"docx2txt解析失败: {str(e)}")

        # 方法5: 简单的文本提取作为最后手段
        try:
            simple_text = self._simple_text_extraction(file_content)
            if simple_text and len(simple_text.strip()) > 10:
                return f"# Word文档内容（简单提取）\n\n{simple_text}"
        except Exception as e:
            logger.info(f"简单文本提取失败: {str(e)}")

        # 如果所有方法都失败
        return self._generate_doc_failure_message(file_type)

    def _extract_ole_doc_text(self, ole) -> str:
        """从OLE Word文档中提取文本"""
        try:
            text_parts = []

            # Word文档的常见流名称
            stream_names = [
                'WordDocument',
                '1Table',
                '0Table',
                'Data',
                'CompObj',
                '\x01CompObj',
                'DocumentSummaryInformation',
                'SummaryInformation'
            ]

            for stream_name in stream_names:
                try:
                    if ole._olestream_size(stream_name) > 0:
                        stream_data = ole._olestream_read(stream_name)
                        if stream_data:
                            # 尝试从流中提取可读文本
                            readable_text = self._extract_text_from_stream(stream_data)
                            if readable_text:
                                text_parts.append(readable_text)
                except Exception:
                    continue

            if text_parts:
                return '\n\n'.join(text_parts)
            else:
                # 如果无法从流中提取，尝试整体文本提取
                return self._extract_readable_text(ole.save_stream())

        except Exception as e:
            logger.error(f"OLE DOC文本提取失败: {str(e)}")
            return ""

    def _extract_text_from_stream(self, stream_data: bytes) -> str:
        """从OLE流中提取文本"""
        try:
            # 尝试多种编码解析流数据
            encodings = ['utf-16le', 'utf-16be', 'utf-8', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings:
                try:
                    text = stream_data.decode(encoding, errors='ignore')
                    # 提取有意义的文本片段
                    import re
                    fragments = re.findall(r'[\u4e00-\u9fff\w\s]{5,}', text)
                    if fragments:
                        return ' '.join(fragments[:10])  # 最多10个片段
                except:
                    continue

            return ""
        except Exception as e:
            logger.error(f"流文本提取失败: {str(e)}")
            return ""

    def _advanced_binary_text_extraction(self, file_content: bytes) -> str:
        """高级二进制文档文本提取"""
        try:
            import re

            # 尝试查找文本模式
            text_patterns = [
                # 中文文本模式
                rb'[\xe4-\xe9][\x80-\xbf][\x80-\xbf]+',
                # 英文文本模式
                rb'[a-zA-Z\s]{10,}',
                # 混合文本模式
                rb'[\x20-\x7e\xe4-\xe9\x80-\xbf]{10,}'
            ]

            extracted_texts = []

            for pattern in text_patterns:
                matches = re.findall(pattern, file_content)
                for match in matches[:20]:  # 最多20个匹配
                    try:
                        # 尝试解码
                        for encoding in ['utf-8', 'gbk', 'gb2312']:
                            try:
                                text = match.decode(encoding)
                                if len(text.strip()) > 5:
                                    extracted_texts.append(text.strip())
                                    break
                            except:
                                continue
                    except:
                        continue

            if extracted_texts:
                # 去重并返回
                unique_texts = list(dict.fromkeys(extracted_texts))
                return '\n\n'.join(unique_texts[:10])

            return ""

        except Exception as e:
            logger.error(f"高级文本提取失败: {str(e)}")
            return ""

    def _clean_extracted_text(self, text: str) -> str:
        """清理提取的文本，去除乱码和无意义字符"""
        try:
            import re

            if not text or not text.strip():
                return ""

            # 1. 基本清理
            text = text.strip()

            # 2. 去除明显的乱码字符（非中英文、数字、常见标点）
            # 保留中文、英文、数字、常见标点符号
            cleaned_text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,，。！？；：""''（）【】\-\+\=\*\/\%\@\#\$\&\|\\\n\r\t]', '', text)

            # 3. 合并多个空白字符
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

            # 4. 分行处理，去除过短或明显乱码的行
            lines = cleaned_text.split('\n')
            valid_lines = []

            for line in lines:
                line = line.strip()
                if len(line) < 2:  # 跳过过短的行
                    continue

                # 检查是否包含有意义的内容（中文或英文单词）
                if re.search(r'[\u4e00-\u9fff]', line) or re.search(r'[a-zA-Z]{2,}', line):
                    valid_lines.append(line)

            if valid_lines:
                return '\n\n'.join(valid_lines)

            return ""

        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return text

    def _smart_text_extraction(self, file_content: bytes) -> str:
        """智能文本模式识别和提取"""
        try:
            import re

            # 方法1: 查找UTF-16编码的中文文本
            chinese_texts = []
            try:
                # 扫描文件，查找UTF-16LE编码的中文文本
                for i in range(0, len(file_content) - 100, 2):
                    chunk = file_content[i:i+200]
                    try:
                        text = chunk.decode('utf-16le', errors='ignore')
                        # 查找中文字符
                        chinese_matches = re.findall(r'[\u4e00-\u9fff]+[^\x00-\x1f]*', text)
                        for match in chinese_matches:
                            if len(match.strip()) > 3:
                                chinese_texts.append(match.strip())
                    except:
                        continue

                if chinese_texts:
                    # 去重并合并
                    unique_chinese = list(dict.fromkeys(chinese_texts))
                    combined = '\n\n'.join(unique_chinese[:15])
                    cleaned = self._clean_extracted_text(combined)
                    if cleaned and len(cleaned) > 50:
                        return cleaned

            except Exception as e:
                logger.debug(f"UTF-16中文提取失败: {str(e)}")

            # 方法2: 查找GBK编码的中文文本
            try:
                for encoding in ['gbk', 'gb2312', 'gb18030']:
                    text = file_content.decode(encoding, errors='ignore')
                    chinese_matches = re.findall(r'[\u4e00-\u9fff]+[^\x00-\x1f]*[\u4e00-\u9fff]*', text)
                    if chinese_matches:
                        valid_matches = [m.strip() for m in chinese_matches if len(m.strip()) > 5]
                        if valid_matches:
                            combined = '\n\n'.join(valid_matches[:10])
                            cleaned = self._clean_extracted_text(combined)
                            if cleaned and len(cleaned) > 50:
                                return cleaned

            except Exception as e:
                logger.debug(f"GBK中文提取失败: {str(e)}")

            # 方法3: 使用改进的通用文本提取
            return self._extract_readable_text(file_content)

        except Exception as e:
            logger.error(f"智能文本提取失败: {str(e)}")
            return ""

    def _extract_ole_doc_content(self, file_content: bytes) -> str:
        """改进的OLE文档内容提取"""
        try:
            import struct

            # Word文档的文本通常存储在特定的位置
            # 尝试查找文本模式
            text_parts = []

            # 方法1: 查找UTF-16编码的文本
            try:
                # 查找可能的UTF-16文本段
                for i in range(0, len(file_content) - 100, 2):
                    chunk = file_content[i:i+200]
                    try:
                        # 尝试UTF-16LE解码
                        text = chunk.decode('utf-16le', errors='ignore')
                        # 过滤掉控制字符，保留有意义的文本
                        clean_text = ''.join(c for c in text if c.isprintable() or c in '\n\r\t')
                        if len(clean_text) > 10 and any(c.isalnum() or '\u4e00' <= c <= '\u9fff' for c in clean_text):
                            text_parts.append(clean_text.strip())
                    except:
                        continue

                if text_parts:
                    # 去重并合并
                    unique_parts = list(dict.fromkeys(text_parts))
                    combined_text = '\n'.join(unique_parts[:20])  # 最多20段
                    cleaned = self._clean_extracted_text(combined_text)
                    if cleaned and len(cleaned) > 50:
                        return cleaned

            except Exception as e:
                logger.debug(f"UTF-16提取失败: {str(e)}")

            # 方法2: 查找GB2312/GBK编码的文本
            try:
                for encoding in ['gbk', 'gb2312', 'gb18030']:
                    for i in range(0, len(file_content) - 100, 1):
                        chunk = file_content[i:i+200]
                        try:
                            text = chunk.decode(encoding, errors='ignore')
                            clean_text = ''.join(c for c in text if c.isprintable() or c in '\n\r\t')
                            if len(clean_text) > 10 and any('\u4e00' <= c <= '\u9fff' for c in clean_text):
                                text_parts.append(clean_text.strip())
                        except:
                            continue

                if text_parts:
                    unique_parts = list(dict.fromkeys(text_parts))
                    combined_text = '\n'.join(unique_parts[:15])
                    cleaned = self._clean_extracted_text(combined_text)
                    if cleaned and len(cleaned) > 50:
                        return cleaned

            except Exception as e:
                logger.debug(f"GBK提取失败: {str(e)}")

            # 方法3: 使用改进的通用文本提取
            return self._extract_readable_text(file_content)

        except Exception as e:
            logger.error(f"OLE文档内容提取失败: {str(e)}")
            return ""

    def _extract_ole_doc_text_improved(self, ole) -> str:
        """改进的OLE Word文档文本提取"""
        try:
            text_parts = []

            # Word文档的常见流名称（更全面）
            stream_names = [
                'WordDocument',
                '1Table',
                '0Table',
                'Data',
                'CompObj',
                '\x01CompObj',
                'DocumentSummaryInformation',
                'SummaryInformation',
                '\x05DocumentSummaryInformation',
                '\x05SummaryInformation'
            ]

            for stream_name in stream_names:
                try:
                    if ole._olestream_size(stream_name) > 0:
                        stream_data = ole._olestream_read(stream_name)
                        if stream_data and len(stream_data) > 10:
                            # 使用改进的文本提取
                            readable_text = self._extract_text_from_ole_stream(stream_data)
                            if readable_text and len(readable_text.strip()) > 10:
                                text_parts.append(readable_text.strip())
                except Exception:
                    continue

            if text_parts:
                # 去重并合并
                unique_parts = list(dict.fromkeys(text_parts))
                combined_text = '\n\n'.join(unique_parts)
                return self._clean_extracted_text(combined_text)

            return ""

        except Exception as e:
            logger.error(f"改进OLE Word文本提取失败: {str(e)}")
            return ""

    def _extract_text_from_ole_stream(self, stream_data: bytes) -> str:
        """从OLE流中提取文本 - 改进版"""
        try:
            import re

            text_parts = []

            # 尝试多种编码，优先中文编码
            encodings = [
                'utf-16le', 'utf-16be',  # Word常用的Unicode编码
                'gbk', 'gb2312', 'gb18030',  # 中文编码
                'utf-8', 'latin1', 'cp1252'  # 其他编码
            ]

            for encoding in encodings:
                try:
                    # 尝试解码整个流
                    text = stream_data.decode(encoding, errors='ignore')

                    # 提取有意义的文本片段
                    # 1. 中文文本（3个字符以上）
                    chinese_matches = re.findall(r'[\u4e00-\u9fff]{3,}[^\x00-\x1f]*', text)

                    # 2. 英文文本（5个字符以上的单词）
                    english_matches = re.findall(r'[a-zA-Z]{5,}[a-zA-Z\s\.,!?;:]*', text)

                    # 3. 混合文本（包含中英文的句子）
                    mixed_matches = re.findall(r'[\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】]{10,}', text)

                    # 合并所有匹配
                    all_matches = chinese_matches + english_matches + mixed_matches

                    if all_matches:
                        # 清理和过滤
                        for match in all_matches:
                            cleaned = match.strip()
                            # 过滤掉明显的乱码和控制字符
                            if len(cleaned) > 5 and not re.search(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', cleaned):
                                text_parts.append(cleaned)

                    # 如果找到足够的文本，优先返回
                    if len(text_parts) > 3:
                        break

                except Exception:
                    continue

            if text_parts:
                # 去重并返回
                unique_parts = list(dict.fromkeys(text_parts))
                return '\n\n'.join(unique_parts[:10])  # 最多10个片段

            return ""

        except Exception as e:
            logger.error(f"OLE流文本提取失败: {str(e)}")
            return ""

    def _extract_chinese_text_only(self, file_content: bytes) -> str:
        """专门提取中文文本，避免乱码"""
        try:
            import re

            valid_texts = []

            # 方法1: 查找UTF-16LE编码的中文文本
            for i in range(0, len(file_content) - 10, 2):
                try:
                    # 读取较小的块避免乱码
                    chunk = file_content[i:i+50]
                    text = chunk.decode('utf-16le', errors='ignore')

                    # 只保留包含中文字符的文本
                    chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
                    for chars in chinese_chars:
                        if len(chars) >= 2:  # 至少2个中文字符
                            valid_texts.append(chars)

                except:
                    continue

            # 方法2: 查找GBK编码的中文文本
            try:
                text = file_content.decode('gbk', errors='ignore')
                chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
                for chars in chinese_chars:
                    if len(chars) >= 2:
                        valid_texts.append(chars)
            except:
                pass

            # 方法3: 查找GB2312编码的中文文本
            try:
                text = file_content.decode('gb2312', errors='ignore')
                chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
                for chars in chinese_chars:
                    if len(chars) >= 2:
                        valid_texts.append(chars)
            except:
                pass

            # 去重并组合
            if valid_texts:
                # 去重
                unique_texts = []
                seen = set()
                for text in valid_texts:
                    if text not in seen and len(text) >= 2:
                        seen.add(text)
                        unique_texts.append(text)

                # 组合成段落
                if unique_texts:
                    return '\n\n'.join(unique_texts[:30])  # 最多30个文本片段

            return ""

        except Exception as e:
            logger.error(f"中文文本提取失败: {str(e)}")
            return ""

    def _clean_docx2txt_output(self, text: str) -> str:
        """清理docx2txt的输出"""
        try:
            import re

            if not text or not text.strip():
                return ""

            # 移除明显的乱码字符
            # 只保留中文、英文、数字、常见标点符号
            cleaned = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】\-\n\r\t]', '', text)

            # 分行处理
            lines = cleaned.split('\n')
            valid_lines = []

            for line in lines:
                line = line.strip()
                if len(line) < 2:
                    continue

                # 检查是否包含有意义的内容
                if re.search(r'[\u4e00-\u9fff]', line) or re.search(r'[a-zA-Z]{3,}', line):
                    valid_lines.append(line)

            if valid_lines:
                return '\n\n'.join(valid_lines)

            return ""

        except Exception as e:
            logger.error(f"docx2txt输出清理失败: {str(e)}")
            return text

    def _simple_text_extraction(self, file_content: bytes) -> str:
        """简单的文本提取作为最后手段"""
        try:
            import re

            # 尝试多种编码，但只保留有意义的文本
            encodings = ['utf-16le', 'gbk', 'gb2312', 'utf-8']

            for encoding in encodings:
                try:
                    text = file_content.decode(encoding, errors='ignore')

                    # 查找有意义的文本片段
                    # 1. 中文文本
                    chinese_matches = re.findall(r'[\u4e00-\u9fff]{2,}', text)

                    # 2. 英文单词
                    english_matches = re.findall(r'[a-zA-Z]{3,}', text)

                    # 3. 数字
                    number_matches = re.findall(r'\d{2,}', text)

                    all_matches = chinese_matches + english_matches + number_matches

                    if all_matches:
                        # 去重并限制数量
                        unique_matches = list(dict.fromkeys(all_matches))
                        if len(unique_matches) >= 3:  # 至少3个有意义的片段
                            return '\n'.join(unique_matches[:20])

                except:
                    continue

            return ""

        except Exception as e:
            logger.error(f"简单文本提取失败: {str(e)}")
            return ""

    def _parse_with_textract(self, file_content: bytes) -> str:
        """使用textract解析.doc文件"""
        try:
            import textract
            import tempfile
            import os

            logger.info("尝试使用textract解析.doc文件")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # 使用textract解析
                extracted_text = textract.process(temp_file_path)

                # 解码文本
                if isinstance(extracted_text, bytes):
                    # 尝试多种编码
                    for encoding in ['utf-8', 'gbk', 'gb2312', 'latin1']:
                        try:
                            text_content = extracted_text.decode(encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        text_content = extracted_text.decode('utf-8', errors='ignore')
                else:
                    text_content = str(extracted_text)

                # 清理文本
                cleaned_text = self._clean_textract_output(text_content)

                if cleaned_text and len(cleaned_text.strip()) > 10:
                    logger.info(f"textract解析成功，提取内容长度: {len(cleaned_text)}")
                    return f"# Word文档内容\n\n{cleaned_text}"
                else:
                    logger.warning("textract提取的内容为空或过短")
                    return ""

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        except ImportError:
            logger.info("textract模块未安装")
            return ""
        except Exception as e:
            logger.error(f"textract解析失败: {str(e)}")
            return ""

    def _clean_textract_output(self, text: str) -> str:
        """清理textract的输出"""
        try:
            import re

            if not text or not text.strip():
                return ""

            # 分行处理
            lines = text.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()
                if len(line) < 2:
                    continue

                # 移除明显的乱码行
                # 检查是否包含有意义的字符
                meaningful_chars = len(re.findall(r'[\u4e00-\u9fff\w]', line))
                if meaningful_chars / len(line) > 0.3:  # 至少30%是有意义的字符
                    cleaned_lines.append(line)

            if cleaned_lines:
                return '\n\n'.join(cleaned_lines)

            return ""

        except Exception as e:
            logger.error(f"textract输出清理失败: {str(e)}")
            return text

    def _parse_ole_doc_improved(self, file_content: bytes) -> str:
        """改进的OLE文档解析"""
        try:
            import struct
            import re

            logger.info("使用改进的OLE解析方法")

            # 检查OLE文件头
            if not file_content.startswith(b'\xd0\xcf\x11\xe0'):
                return ""

            # 尝试多种方法提取文本
            text_parts = []

            # 方法1: 查找Word文档的文本表
            word_text = self._extract_word_text_table_improved(file_content)
            if word_text:
                text_parts.append(word_text)

            # 方法2: 扫描整个文件查找文本模式
            scanned_text = self._scan_for_text_patterns(file_content)
            if scanned_text:
                text_parts.append(scanned_text)

            # 方法3: 使用oletools（如果可用）
            ole_text = self._extract_with_oletools_improved(file_content)
            if ole_text:
                text_parts.append(ole_text)

            # 合并和清理结果
            if text_parts:
                combined_text = '\n\n'.join(text_parts)
                cleaned_text = self._clean_ole_extracted_text(combined_text)

                if cleaned_text and len(cleaned_text.strip()) > 20:
                    logger.info(f"改进OLE解析成功，提取内容长度: {len(cleaned_text)}")
                    return f"# Word文档内容（OLE解析）\n\n{cleaned_text}"

            return ""

        except Exception as e:
            logger.error(f"改进OLE解析失败: {str(e)}")
            return ""

    def _extract_word_text_table_improved(self, file_content: bytes) -> str:
        """改进的Word文档文本表提取"""
        try:
            import re

            # Word文档的文本通常存储在特定的偏移位置
            # 尝试在常见位置查找文本
            text_fragments = []

            # 扫描文件，查找可能的文本区域
            for offset in range(0, len(file_content) - 100, 512):
                chunk = file_content[offset:offset + 2048]

                # 尝试UTF-16LE解码
                try:
                    text = chunk.decode('utf-16le', errors='ignore')
                    # 查找连续的中文或英文文本
                    matches = re.findall(r'[\u4e00-\u9fff\w\s]{5,}', text)
                    for match in matches:
                        if len(match.strip()) > 3:
                            text_fragments.append(match.strip())
                except:
                    pass

                # 尝试GBK解码
                try:
                    text = chunk.decode('gbk', errors='ignore')
                    matches = re.findall(r'[\u4e00-\u9fff\w\s]{5,}', text)
                    for match in matches:
                        if len(match.strip()) > 3:
                            text_fragments.append(match.strip())
                except:
                    pass

            # 去重并返回
            if text_fragments:
                unique_fragments = list(dict.fromkeys(text_fragments))
                return '\n'.join(unique_fragments[:20])

            return ""

        except Exception as e:
            logger.error(f"Word文本表提取失败: {str(e)}")
            return ""

    def _scan_for_text_patterns(self, file_content: bytes) -> str:
        """扫描文件查找文本模式"""
        try:
            import re

            text_patterns = []

            # 模式1: 查找UTF-16编码的中文文本
            utf16_pattern = rb'[\x00-\x7f][\x4e-\x9f]'  # UTF-16LE中文模式
            matches = re.finditer(utf16_pattern, file_content)

            for match in list(matches)[:50]:  # 限制匹配数量
                start = max(0, match.start() - 20)
                end = min(len(file_content), match.end() + 100)
                chunk = file_content[start:end]

                try:
                    text = chunk.decode('utf-16le', errors='ignore')
                    chinese_text = re.findall(r'[\u4e00-\u9fff]+', text)
                    for ct in chinese_text:
                        if len(ct) >= 2:
                            text_patterns.append(ct)
                except:
                    pass

            # 模式2: 查找ASCII文本
            ascii_pattern = rb'[a-zA-Z]{3,}[\x00\x20-\x7e]*[a-zA-Z]{3,}'
            matches = re.finditer(ascii_pattern, file_content)

            for match in list(matches)[:30]:
                try:
                    text = match.group().decode('ascii', errors='ignore')
                    if len(text.strip()) > 5:
                        text_patterns.append(text.strip())
                except:
                    pass

            if text_patterns:
                unique_patterns = list(dict.fromkeys(text_patterns))
                return '\n'.join(unique_patterns[:15])

            return ""

        except Exception as e:
            logger.error(f"文本模式扫描失败: {str(e)}")
            return ""

    def _extract_with_oletools_improved(self, file_content: bytes) -> str:
        """使用oletools的改进提取 - 专门提取Word文档正文"""
        try:
            from oletools import olefile

            if not olefile.isOleFile(file_content):
                return ""

            ole = olefile.OleFileIO(file_content)

            # 专门解析WordDocument流中的文本表
            try:
                if ole._olestream_size.get('WordDocument'):
                    word_doc_stream = ole._olestream_data['WordDocument']

                    # 解析Word文档的FIB (File Information Block)
                    text_content = self._parse_word_document_stream(word_doc_stream, ole)
                    if text_content:
                        ole.close()
                        return text_content

            except Exception as e:
                logger.debug(f"WordDocument流解析失败: {str(e)}")

            # 如果WordDocument流解析失败，尝试直接从文本表提取
            try:
                if ole._olestream_size.get('1Table'):
                    table_stream = ole._olestream_data['1Table']
                    text_content = self._extract_text_from_table_stream(table_stream)
                    if text_content:
                        ole.close()
                        return text_content

                if ole._olestream_size.get('0Table'):
                    table_stream = ole._olestream_data['0Table']
                    text_content = self._extract_text_from_table_stream(table_stream)
                    if text_content:
                        ole.close()
                        return text_content

            except Exception as e:
                logger.debug(f"Table流解析失败: {str(e)}")

            ole.close()
            return ""

        except ImportError:
            logger.info("oletools模块未安装")
            return ""
        except Exception as e:
            logger.error(f"oletools改进提取失败: {str(e)}")
            return ""

    def _parse_word_document_stream(self, word_doc_stream: bytes, ole) -> str:
        """解析Word文档流，提取文本表位置"""
        try:
            import struct

            # Word文档的FIB结构
            # 前32字节是FIB的基本信息
            if len(word_doc_stream) < 32:
                return ""

            # 读取FIB头部
            fib_header = struct.unpack('<HH', word_doc_stream[0:4])

            # 查找文本表的位置
            # Word文档的文本通常存储在特定偏移位置
            text_parts = []

            # 尝试从不同位置读取文本
            offsets = [0x200, 0x400, 0x800, 0x1000, 0x2000, 0x4000]

            for offset in offsets:
                if offset < len(word_doc_stream):
                    chunk = word_doc_stream[offset:offset+1000]

                    # 尝试UTF-16LE解码（Word常用编码）
                    try:
                        text = chunk.decode('utf-16le', errors='ignore')
                        import re

                        # 查找连续的中文或英文文本
                        chinese_matches = re.findall(r'[\u4e00-\u9fff]{2,}', text)
                        english_matches = re.findall(r'[a-zA-Z]{3,}', text)

                        for match in chinese_matches + english_matches:
                            if len(match.strip()) > 2:
                                text_parts.append(match.strip())

                    except:
                        continue

            if text_parts:
                # 去重并返回
                unique_parts = list(dict.fromkeys(text_parts))
                return '\n\n'.join(unique_parts[:20])

            return ""

        except Exception as e:
            logger.error(f"Word文档流解析失败: {str(e)}")
            return ""

    def _extract_text_from_table_stream(self, table_stream: bytes) -> str:
        """从文本表流中提取文本"""
        try:
            import re

            text_parts = []

            # Word的文本表通常包含格式化的文本数据
            # 尝试多种编码解析
            encodings = ['utf-16le', 'utf-16be', 'gbk', 'gb2312', 'utf-8']

            for encoding in encodings:
                try:
                    text = table_stream.decode(encoding, errors='ignore')

                    # 查找有意义的文本片段
                    # 1. 中文文本
                    chinese_matches = re.findall(r'[\u4e00-\u9fff]{2,}', text)

                    # 2. 英文单词
                    english_matches = re.findall(r'[a-zA-Z]{3,}', text)

                    # 3. 数字
                    number_matches = re.findall(r'\d{4}', text)  # 年份等

                    all_matches = chinese_matches + english_matches + number_matches

                    for match in all_matches:
                        if len(match.strip()) > 2:
                            text_parts.append(match.strip())

                except:
                    continue

            if text_parts:
                # 去重并限制数量
                unique_parts = list(dict.fromkeys(text_parts))
                return '\n\n'.join(unique_parts[:15])

            return ""

        except Exception as e:
            logger.error(f"文本表流提取失败: {str(e)}")
            return ""

    def _extract_word_document_content_only(self, file_content: bytes) -> str:
        """专门提取Word文档正文内容，避免OLE结构信息和乱码"""
        try:
            import re
            import struct

            logger.info("使用专门的Word文档正文提取方法")

            # 检查OLE文件头
            if not file_content.startswith(b'\xd0\xcf\x11\xe0'):
                return ""

            text_parts = []

            # 方法1: 扫描整个文件，查找连续的中文文本
            chinese_texts = self._scan_for_chinese_content(file_content)
            if chinese_texts:
                text_parts.extend(chinese_texts)

            # 方法2: 查找英文文本内容
            english_texts = self._scan_for_english_content(file_content)
            if english_texts:
                text_parts.extend(english_texts)

            # 方法3: 使用oletools但只提取正文
            ole_content = self._extract_document_body_only(file_content)
            if ole_content:
                text_parts.extend(ole_content)

            # 智能清理和去重
            if text_parts:
                cleaned_parts = self._intelligent_text_cleaning(text_parts)

                if cleaned_parts:
                    return '\n\n'.join(cleaned_parts[:20])

            return ""

        except Exception as e:
            logger.error(f"Word文档正文提取失败: {str(e)}")
            return ""

    def _intelligent_text_cleaning(self, text_parts: list) -> list:
        """智能文本清理，去除乱码但保留有意义的内容"""
        try:
            import re

            cleaned_parts = []
            seen = set()

            for part in text_parts:
                part = part.strip()
                if len(part) < 2:
                    continue

                # 1. 过滤掉明显的OLE结构信息
                if self._is_ole_structure_info(part):
                    continue

                # 2. 过滤掉纯乱码文本
                if self._is_pure_gibberish(part):
                    continue

                # 3. 清理混合文本中的乱码字符
                cleaned_part = self._clean_mixed_text(part)

                # 4. 检查清理后的文本是否有意义
                if len(cleaned_part) > 2 and cleaned_part not in seen:
                    cleaned_parts.append(cleaned_part)
                    seen.add(cleaned_part)

            return cleaned_parts

        except Exception as e:
            logger.error(f"智能文本清理失败: {str(e)}")
            return text_parts

    def _is_pure_gibberish(self, text: str) -> bool:
        """判断是否为纯乱码文本"""
        try:
            import re

            # 如果文本太短，不判断为乱码
            if len(text) < 3:
                return False

            # 计算有意义字符的比例
            meaningful_chars = len(re.findall(r'[\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】]', text))
            total_chars = len(text)

            if total_chars == 0:
                return True

            meaningful_ratio = meaningful_chars / total_chars

            # 如果有意义字符比例低于60%，认为是乱码
            if meaningful_ratio < 0.6:
                return True

            # 检查是否包含过多的生僻字或特殊字符
            rare_chars = len(re.findall(r'[鄥燆鵒縹剉錧鷁緥臽礠茤釼褃]', text))
            if rare_chars > len(text) * 0.3:  # 生僻字超过30%
                return True

            return False

        except Exception:
            return False

    def _clean_mixed_text(self, text: str) -> str:
        """清理混合文本中的乱码字符"""
        try:
            import re

            # 定义常见的乱码字符模式
            gibberish_patterns = [
                r'[鄥燆鵒縹剉錧鷁緥臽礠茤釼褃]',  # 特定的乱码字符
                r'[\u4e00-\u9fff]{1}(?=[\u4e00-\u9fff]{0,2}[a-zA-Z])',  # 单个中文字符后跟英文
                r'[^\u4e00-\u9fff\w\s\.,，。！？；：""''（）【】\-]',  # 其他非常用字符
            ]

            cleaned_text = text

            # 逐个应用清理模式
            for pattern in gibberish_patterns:
                cleaned_text = re.sub(pattern, '', cleaned_text)

            # 清理多余的空白字符
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
            cleaned_text = cleaned_text.strip()

            # 如果清理后文本太短，返回原文本
            if len(cleaned_text) < len(text) * 0.3:
                return text

            return cleaned_text

        except Exception:
            return text

    def _scan_for_chinese_content(self, file_content: bytes) -> list:
        """扫描文件查找中文内容 - 改进版，更精准识别有意义的中文"""
        try:
            import re

            chinese_texts = []

            # 以较小的步长扫描文件
            step = 50  # 减小步长，提高精度
            for i in range(0, len(file_content) - 100, step):
                chunk = file_content[i:i+300]  # 增大块大小

                # 尝试UTF-16LE解码
                try:
                    text = chunk.decode('utf-16le', errors='ignore')
                    # 查找有意义的中文文本模式
                    meaningful_texts = self._extract_meaningful_chinese(text)
                    chinese_texts.extend(meaningful_texts)
                except:
                    pass

                # 尝试GBK解码
                try:
                    text = chunk.decode('gbk', errors='ignore')
                    meaningful_texts = self._extract_meaningful_chinese(text)
                    chinese_texts.extend(meaningful_texts)
                except:
                    pass

            # 去重并返回
            unique_texts = list(dict.fromkeys(chinese_texts))
            return unique_texts[:30]  # 限制数量

        except Exception as e:
            logger.error(f"中文内容扫描失败: {str(e)}")
            return []

    def _extract_meaningful_chinese(self, text: str) -> list:
        """从文本中提取有意义的中文内容"""
        try:
            import re

            meaningful_texts = []

            # 模式1: 查找包含常见词汇的中文文本
            common_patterns = [
                r'[\u4e00-\u9fff]*年[\u4e00-\u9fff]*',  # 包含"年"的文本
                r'[\u4e00-\u9fff]*工作[\u4e00-\u9fff]*',  # 包含"工作"的文本
                r'[\u4e00-\u9fff]*报告[\u4e00-\u9fff]*',  # 包含"报告"的文本
                r'[\u4e00-\u9fff]*项目[\u4e00-\u9fff]*',  # 包含"项目"的文本
                r'[\u4e00-\u9fff]*科技[\u4e00-\u9fff]*',  # 包含"科技"的文本
                r'[\u4e00-\u9fff]*建设[\u4e00-\u9fff]*',  # 包含"建设"的文本
                r'[\u4e00-\u9fff]*情况[\u4e00-\u9fff]*',  # 包含"情况"的文本
                r'[\u4e00-\u9fff]*领导[\u4e00-\u9fff]*',  # 包含"领导"的文本
                r'[\u4e00-\u9fff]*大家[\u4e00-\u9fff]*',  # 包含"大家"的文本
            ]

            for pattern in common_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) >= 3 and not self._is_pure_gibberish(match):
                        meaningful_texts.append(match.strip())

            # 模式2: 查找连续的常用中文字符（避免生僻字）
            common_chinese_pattern = r'[一-龯]{3,}'  # 常用中文字符范围
            matches = re.findall(common_chinese_pattern, text)
            for match in matches:
                if len(match) >= 3 and not self._contains_too_many_rare_chars(match):
                    meaningful_texts.append(match.strip())

            # 模式3: 查找中文句子片段
            sentence_patterns = [
                r'[\u4e00-\u9fff]{2,}[，。！？；：]',  # 以标点结尾的中文
                r'第[一二三四五六七八九十\d]+[部分章节条]',  # 章节标题
                r'[\u4e00-\u9fff]{2,}的[\u4e00-\u9fff]{2,}',  # "...的..."结构
            ]

            for pattern in sentence_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) >= 3 and not self._is_pure_gibberish(match):
                        meaningful_texts.append(match.strip())

            return meaningful_texts

        except Exception as e:
            logger.error(f"有意义中文提取失败: {str(e)}")
            return []

    def _contains_too_many_rare_chars(self, text: str) -> bool:
        """检查是否包含过多生僻字"""
        try:
            # 定义一些明显的乱码字符
            rare_chars = '鄥燆鵒縹剉錧鷁緥臽礠茤釼褃鯹鯺鯻鯼鯽鯾鯿鰀鰁鰂鰃鰄鰅鰆鰇鰈鰉鰊鰋鰌鰍鰎鰏'

            rare_count = sum(1 for char in text if char in rare_chars)

            # 如果生僻字超过20%，认为是乱码
            return rare_count > len(text) * 0.2

        except Exception:
            return False

    def _scan_for_english_content(self, file_content: bytes) -> list:
        """扫描文件查找英文内容"""
        try:
            import re

            english_texts = []

            # 以较小的步长扫描文件
            step = 100
            for i in range(0, len(file_content) - 100, step):
                chunk = file_content[i:i+200]

                # 尝试UTF-8解码
                try:
                    text = chunk.decode('utf-8', errors='ignore')
                    # 查找连续的英文单词（至少3个字符）
                    matches = re.findall(r'[a-zA-Z]{3,}(?:\s+[a-zA-Z]{3,})*', text)
                    for match in matches:
                        if len(match.strip()) >= 5:  # 至少5个字符的短语
                            english_texts.append(match.strip())
                except:
                    pass

                # 尝试latin1解码
                try:
                    text = chunk.decode('latin1', errors='ignore')
                    matches = re.findall(r'[a-zA-Z]{3,}(?:\s+[a-zA-Z]{3,})*', text)
                    for match in matches:
                        if len(match.strip()) >= 5:
                            english_texts.append(match.strip())
                except:
                    pass

            # 去重并返回
            unique_texts = list(dict.fromkeys(english_texts))
            return unique_texts[:20]  # 限制数量

        except Exception as e:
            logger.error(f"英文内容扫描失败: {str(e)}")
            return []

    def _extract_document_body_only(self, file_content: bytes) -> list:
        """使用oletools但只提取文档正文"""
        try:
            from oletools import olefile

            if not olefile.isOleFile(file_content):
                return []

            ole = olefile.OleFileIO(file_content)
            body_texts = []

            # 只查找包含正文的流，避免元数据流
            content_streams = ['WordDocument', '1Table', '0Table']

            for stream_name in content_streams:
                try:
                    if ole._olestream_size.get(stream_name):
                        stream_data = ole._olestream_data[stream_name]

                        # 从流中提取正文内容
                        content = self._extract_body_from_stream(stream_data)
                        if content:
                            body_texts.extend(content)

                except Exception as e:
                    logger.debug(f"流 {stream_name} 正文提取失败: {str(e)}")
                    continue

            ole.close()
            return body_texts

        except ImportError:
            logger.info("oletools模块未安装")
            return []
        except Exception as e:
            logger.error(f"文档正文提取失败: {str(e)}")
            return []

    def _extract_body_from_stream(self, stream_data: bytes) -> list:
        """从流中提取正文内容"""
        try:
            import re

            body_texts = []

            # 尝试多种编码
            encodings = ['utf-16le', 'gbk', 'gb2312', 'utf-8']

            for encoding in encodings:
                try:
                    text = stream_data.decode(encoding, errors='ignore')

                    # 查找有意义的文本片段
                    # 1. 中文句子（包含标点符号）
                    chinese_sentences = re.findall(r'[\u4e00-\u9fff][^。！？]*[。！？]', text)
                    for sentence in chinese_sentences:
                        if len(sentence) > 5:
                            body_texts.append(sentence.strip())

                    # 2. 中文短语（不包含标点）
                    chinese_phrases = re.findall(r'[\u4e00-\u9fff]{5,}', text)
                    for phrase in chinese_phrases:
                        if len(phrase) > 5:
                            body_texts.append(phrase.strip())

                    # 3. 英文句子
                    english_sentences = re.findall(r'[A-Z][a-zA-Z\s]{10,}[.!?]', text)
                    for sentence in english_sentences:
                        if len(sentence.strip()) > 10:
                            body_texts.append(sentence.strip())

                except:
                    continue

            return body_texts[:15]  # 限制数量

        except Exception as e:
            logger.error(f"流正文提取失败: {str(e)}")
            return []

    def _is_ole_structure_info(self, text: str) -> bool:
        """判断是否为OLE结构信息"""
        try:
            # 常见的OLE结构关键词
            ole_keywords = [
                'Root Entry', 'SummaryInformation', 'DocumentSummaryInformation',
                'WordDocument', 'CompObj', 'WPS Office', 'KSOProductBuildVer',
                'KSOTemplateDocerSaveRecord', 'WpsCustomData', 'Table',
                'Normal', 'eyJoZGlkIjoiOGYzNzJjNDQw'  # Base64编码的元数据
            ]

            for keyword in ole_keywords:
                if keyword in text:
                    return True

            # 检查是否为乱码（包含大量非打印字符）
            printable_chars = sum(1 for c in text if c.isprintable())
            if len(text) > 0 and printable_chars / len(text) < 0.7:
                return True

            return False

        except Exception:
            return True  # 如果无法判断，认为是结构信息

    def _parse_with_office_com(self, file_content: bytes) -> str:
        """使用Office COM对象解析.doc文件"""
        try:
            import tempfile
            import os
            import subprocess

            logger.info("尝试使用Office COM解析.doc文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过COM解析")
                return ""

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_input:
                temp_input.write(file_content)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_output:
                temp_output_path = temp_output.name

            try:
                # 尝试使用win32com
                try:
                    import win32com.client

                    word = None
                    doc = None

                    try:
                        # 创建Word应用程序对象
                        word = win32com.client.Dispatch("Word.Application")
                        word.Visible = False
                        word.DisplayAlerts = 0

                        # 打开文档
                        doc = word.Documents.Open(temp_input_path, ReadOnly=True)

                        # 提取文本内容
                        content = doc.Content.Text

                        # 清理文本
                        content = self._clean_office_extracted_text(content)

                        if content and len(content.strip()) > 10:
                            logger.info(f"COM解析成功，提取内容长度: {len(content)}")
                            return content

                    finally:
                        # 清理资源
                        if doc:
                            doc.Close(SaveChanges=False)
                        if word:
                            word.Quit()

                except ImportError:
                    logger.info("win32com模块未安装")
                except Exception as e:
                    logger.info(f"win32com解析失败: {str(e)}")

                return ""

            finally:
                # 清理临时文件
                for path in [temp_input_path, temp_output_path]:
                    try:
                        if os.path.exists(path):
                            os.unlink(path)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Office COM解析失败: {str(e)}")
            return ""

    def _parse_with_powershell_script(self, file_content: bytes) -> str:
        """使用PowerShell脚本解析.doc文件"""
        try:
            import tempfile
            import os
            import subprocess

            logger.info("尝试使用PowerShell脚本解析.doc文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过PowerShell解析")
                return ""

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_input:
                temp_input.write(file_content)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_output:
                temp_output_path = temp_output.name

            # 创建PowerShell脚本
            ps_script = self._create_powershell_doc_parser()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.ps1', mode='w', encoding='utf-8') as ps_file:
                ps_file.write(ps_script)
                ps_script_path = ps_file.name

            try:
                # 执行PowerShell脚本
                cmd = [
                    'powershell.exe',
                    '-ExecutionPolicy', 'Bypass',
                    '-File', ps_script_path,
                    '-InputFile', temp_input_path,
                    '-OutputFile', temp_output_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # 读取输出文件
                    if os.path.exists(temp_output_path):
                        with open(temp_output_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if content and len(content.strip()) > 10:
                            logger.info(f"PowerShell解析成功，提取内容长度: {len(content)}")
                            return content.strip()
                else:
                    logger.info(f"PowerShell脚本执行失败: {result.stderr}")

                return ""

            finally:
                # 清理临时文件
                for path in [temp_input_path, temp_output_path, ps_script_path]:
                    try:
                        if os.path.exists(path):
                            os.unlink(path)
                    except:
                        pass

        except Exception as e:
            logger.error(f"PowerShell解析失败: {str(e)}")
            return ""

    def _create_powershell_doc_parser(self) -> str:
        """创建PowerShell的.doc文件解析器脚本"""
        return '''
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,

    [Parameter(Mandatory=$true)]
    [string]$OutputFile
)

try {
    # 创建Word应用程序对象
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $word.DisplayAlerts = 0  # 不显示警告

    # 打开文档
    $doc = $word.Documents.Open($InputFile, $false, $true)  # 只读模式

    # 提取文本内容
    $content = $doc.Content.Text

    # 清理文本
    $content = $content -replace "`r", "`n"  # 替换回车符
    $content = $content -replace "`f", "`n"  # 替换分页符
    $content = $content -replace "[`u0007]", ""  # 移除表格结束符

    # 移除多余的空行
    $lines = $content -split "`n" | Where-Object { $_.Trim() -ne "" }
    $cleanContent = $lines -join "`n"

    # 保存到输出文件
    [System.IO.File]::WriteAllText($OutputFile, $cleanContent, [System.Text.Encoding]::UTF8)

    Write-Host "PowerShell解析完成"

} catch {
    Write-Host "PowerShell解析失败: $($_.Exception.Message)"
    exit 1
} finally {
    # 清理资源
    if ($doc) {
        $doc.Close([ref]$false)
    }
    if ($word) {
        $word.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    }
}
'''

    def _clean_office_extracted_text(self, text: str) -> str:
        """清理从Office提取的文本"""
        try:
            if not text or not text.strip():
                return ""

            # 替换特殊字符
            text = text.replace('\r', '\n')  # 回车符
            text = text.replace('\x07', '')  # 表格结束符
            text = text.replace('\x0c', '\n')  # 分页符
            text = text.replace('\x0b', '\n')  # 垂直制表符

            # 分行处理
            lines = text.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()
                if len(line) > 0:
                    cleaned_lines.append(line)

            # 合并成段落
            if cleaned_lines:
                return '\n\n'.join(cleaned_lines)

            return ""

        except Exception as e:
            logger.error(f"Office文本清理失败: {str(e)}")
            return text

    def _parse_excel_with_com(self, file_content: bytes) -> str:
        """使用Excel COM对象解析Excel文件"""
        try:
            import tempfile
            import os

            logger.info("尝试使用Excel COM解析Excel文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过Excel COM解析")
                return ""

            # 确定文件扩展名
            file_ext = '.xlsx'
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                file_ext = '.xls'  # OLE格式，老版本Excel

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # 尝试使用win32com
                import win32com.client

                excel = None
                workbook = None

                try:
                    # 创建Excel应用程序对象
                    excel = win32com.client.Dispatch("Excel.Application")
                    excel.Visible = False
                    excel.DisplayAlerts = False

                    # 打开工作簿
                    workbook = excel.Workbooks.Open(temp_file_path)

                    content_lines = []

                    # 遍历所有工作表
                    for sheet_idx in range(1, workbook.Sheets.Count + 1):
                        sheet = workbook.Sheets(sheet_idx)
                        sheet_name = sheet.Name

                        content_lines.append(f"=== 工作表: {sheet_name} ===")

                        # 获取使用区域
                        used_range = sheet.UsedRange
                        if used_range:
                            # 获取数据
                            data = used_range.Value

                            if data:
                                # 处理单行数据
                                if not isinstance(data, (list, tuple)) or (len(data) > 0 and not isinstance(data[0], (list, tuple))):
                                    if isinstance(data, (list, tuple)):
                                        data = [data]
                                    else:
                                        data = [[data]]

                                # 转换为表格
                                table_data = []
                                for row in data:
                                    if isinstance(row, (list, tuple)):
                                        row_values = [str(cell) if cell is not None else '' for cell in row]
                                    else:
                                        row_values = [str(row) if row is not None else '']
                                    table_data.append(row_values)

                                # 转换为Markdown表格
                                if table_data:
                                    markdown_table = self._convert_to_markdown_table(table_data)
                                    content_lines.append(markdown_table)

                        content_lines.append("")  # 工作表之间添加空行

                    if content_lines:
                        logger.info(f"Excel COM解析成功，提取内容长度: {len(''.join(content_lines))}")
                        # 添加文档标题并格式化为标准Markdown
                        markdown_content = ["# Excel文档内容\n"] + content_lines
                        return '\n\n'.join(markdown_content)

                finally:
                    # 清理资源
                    if workbook:
                        workbook.Close(SaveChanges=False)
                    if excel:
                        excel.Quit()

            except ImportError:
                logger.info("win32com模块未安装")
            except Exception as e:
                logger.info(f"Excel COM解析失败: {str(e)}")

            finally:
                # 清理临时文件
                try:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                except:
                    pass

            return ""

        except Exception as e:
            logger.error(f"Excel COM解析失败: {str(e)}")
            return ""

    def _parse_excel_with_powershell(self, file_content: bytes) -> str:
        """使用PowerShell脚本解析Excel文件"""
        try:
            import tempfile
            import os
            import subprocess

            logger.info("尝试使用PowerShell脚本解析Excel文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过Excel PowerShell解析")
                return ""

            # 确定文件扩展名
            file_ext = '.xlsx'
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                file_ext = '.xls'

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_input:
                temp_input.write(file_content)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_output:
                temp_output_path = temp_output.name

            # 创建PowerShell脚本
            ps_script = self._create_powershell_excel_parser()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.ps1', mode='w', encoding='utf-8') as ps_file:
                ps_file.write(ps_script)
                ps_script_path = ps_file.name

            try:
                # 执行PowerShell脚本
                cmd = [
                    'powershell.exe',
                    '-ExecutionPolicy', 'Bypass',
                    '-File', ps_script_path,
                    '-InputFile', temp_input_path,
                    '-OutputFile', temp_output_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # 读取输出文件
                    if os.path.exists(temp_output_path):
                        with open(temp_output_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if content and len(content.strip()) > 10:
                            logger.info(f"Excel PowerShell解析成功，提取内容长度: {len(content)}")
                            return content.strip()
                else:
                    logger.info(f"Excel PowerShell脚本执行失败: {result.stderr}")

                return ""

            finally:
                # 清理临时文件
                for path in [temp_input_path, temp_output_path, ps_script_path]:
                    try:
                        if os.path.exists(path):
                            os.unlink(path)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Excel PowerShell解析失败: {str(e)}")
            return ""

    def _parse_ppt_with_com(self, file_content: bytes) -> str:
        """使用PowerPoint COM对象解析PPT文件"""
        try:
            import tempfile
            import os

            logger.info("尝试使用PowerPoint COM解析PPT文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过PowerPoint COM解析")
                return ""

            # 确定文件扩展名
            file_ext = '.pptx'
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                file_ext = '.ppt'  # OLE格式，老版本PowerPoint

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # 尝试使用win32com
                import win32com.client

                ppt = None
                presentation = None

                try:
                    # 创建PowerPoint应用程序对象
                    ppt = win32com.client.Dispatch("PowerPoint.Application")

                    # 尝试不同的打开方式
                    presentation = None

                    # 方法1: 尝试只读模式打开
                    try:
                        presentation = ppt.Presentations.Open(temp_file_path, ReadOnly=True, Untitled=False, WithWindow=False)
                        logger.info("PowerPoint文件打开成功（只读模式）")
                    except Exception as e1:
                        logger.info(f"只读模式打开失败: {str(e1)}")

                        # 方法2: 尝试普通模式打开
                        try:
                            presentation = ppt.Presentations.Open(temp_file_path)
                            logger.info("PowerPoint文件打开成功（普通模式）")
                        except Exception as e2:
                            logger.info(f"普通模式打开失败: {str(e2)}")
                            raise e2

                    content_lines = []

                    # 遍历所有幻灯片
                    for slide_idx in range(1, presentation.Slides.Count + 1):
                        slide = presentation.Slides(slide_idx)

                        content_lines.append(f"=== 幻灯片 {slide_idx} ===")

                        # 遍历幻灯片中的所有形状
                        for shape_idx in range(1, slide.Shapes.Count + 1):
                            shape = slide.Shapes(shape_idx)

                            try:
                                # 处理文本内容
                                if hasattr(shape, 'TextFrame') and shape.TextFrame.HasText:
                                    text_content = shape.TextFrame.TextRange.Text.strip()
                                    if text_content:
                                        content_lines.append(text_content)

                                # 处理表格
                                if hasattr(shape, 'Table') and shape.HasTable:
                                    table = shape.Table
                                    for row_idx in range(1, table.Rows.Count + 1):
                                        row_values = []
                                        for col_idx in range(1, table.Columns.Count + 1):
                                            try:
                                                cell = table.Cell(row_idx, col_idx)
                                                cell_text = cell.Shape.TextFrame.TextRange.Text.strip()
                                                row_values.append(cell_text)
                                            except:
                                                row_values.append('')

                                        if any(val for val in row_values):
                                            row_text = '\t'.join(row_values)
                                            content_lines.append(row_text)

                                # 处理图片（标记需要进一步处理）
                                if shape.Type == 13:  # msoLinkedPicture or msoPicture
                                    content_lines.append(f"[幻灯片{slide_idx}_图片内容_需要OCR识别]")

                            except Exception as shape_error:
                                logger.debug(f"形状处理失败: {str(shape_error)}")
                                continue

                        content_lines.append("")  # 幻灯片之间添加空行

                    if content_lines:
                        logger.info(f"PowerPoint COM解析成功，提取内容长度: {len(''.join(content_lines))}")
                        return '\n'.join(content_lines)

                finally:
                    # 清理资源
                    if presentation:
                        presentation.Close()
                    if ppt:
                        ppt.Quit()

            except ImportError:
                logger.info("win32com模块未安装")
            except Exception as e:
                logger.info(f"PowerPoint COM解析失败: {str(e)}")

            finally:
                # 清理临时文件
                try:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                except:
                    pass

            return ""

        except Exception as e:
            logger.error(f"PowerPoint COM解析失败: {str(e)}")
            return ""

    def _parse_ppt_with_powershell(self, file_content: bytes) -> str:
        """使用PowerShell脚本解析PowerPoint文件"""
        try:
            import tempfile
            import os
            import subprocess

            logger.info("尝试使用PowerShell脚本解析PowerPoint文件")

            # 检查是否在Windows环境
            if os.name != 'nt':
                logger.info("非Windows环境，跳过PowerPoint PowerShell解析")
                return ""

            # 确定文件扩展名
            file_ext = '.pptx'
            if file_content.startswith(b'\xd0\xcf\x11\xe0'):
                file_ext = '.ppt'

            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_input:
                temp_input.write(file_content)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_output:
                temp_output_path = temp_output.name

            # 创建PowerShell脚本
            ps_script = self._create_powershell_ppt_parser()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.ps1', mode='w', encoding='utf-8') as ps_file:
                ps_file.write(ps_script)
                ps_script_path = ps_file.name

            try:
                # 执行PowerShell脚本
                cmd = [
                    'powershell.exe',
                    '-ExecutionPolicy', 'Bypass',
                    '-File', ps_script_path,
                    '-InputFile', temp_input_path,
                    '-OutputFile', temp_output_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # 读取输出文件
                    if os.path.exists(temp_output_path):
                        with open(temp_output_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        if content and len(content.strip()) > 10:
                            logger.info(f"PowerPoint PowerShell解析成功，提取内容长度: {len(content)}")
                            return content.strip()
                else:
                    logger.info(f"PowerPoint PowerShell脚本执行失败: {result.stderr}")

                return ""

            finally:
                # 清理临时文件
                for path in [temp_input_path, temp_output_path, ps_script_path]:
                    try:
                        if os.path.exists(path):
                            os.unlink(path)
                    except:
                        pass

        except Exception as e:
            logger.error(f"PowerPoint PowerShell解析失败: {str(e)}")
            return ""

    def _create_powershell_excel_parser(self) -> str:
        """创建PowerShell的Excel文件解析器脚本"""
        return '''
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,

    [Parameter(Mandatory=$true)]
    [string]$OutputFile
)

try {
    # 创建Excel应用程序对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false

    # 打开工作簿
    $workbook = $excel.Workbooks.Open($InputFile)

    $content = @()

    # 遍历所有工作表
    foreach ($worksheet in $workbook.Worksheets) {
        $content += "=== 工作表: $($worksheet.Name) ==="

        # 获取使用区域
        $usedRange = $worksheet.UsedRange
        if ($usedRange) {
            $rowCount = $usedRange.Rows.Count
            $colCount = $usedRange.Columns.Count

            # 读取数据
            for ($row = 1; $row -le $rowCount; $row++) {
                $rowData = @()
                for ($col = 1; $col -le $colCount; $col++) {
                    $cellValue = $usedRange.Cells.Item($row, $col).Value2
                    if ($cellValue -eq $null) {
                        $rowData += ""
                    } else {
                        $rowData += $cellValue.ToString()
                    }
                }
                $content += ($rowData -join "`t")
            }
        }
        $content += ""
    }

    # 保存到输出文件
    $contentText = $content -join "`n"
    [System.IO.File]::WriteAllText($OutputFile, $contentText, [System.Text.Encoding]::UTF8)

    Write-Host "Excel PowerShell解析完成"

} catch {
    Write-Host "Excel PowerShell解析失败: $($_.Exception.Message)"
    exit 1
} finally {
    # 清理资源
    if ($workbook) {
        $workbook.Close($false)
    }
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
'''

    def _create_powershell_ppt_parser(self) -> str:
        """创建PowerShell的PowerPoint文件解析器脚本"""
        return '''
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,

    [Parameter(Mandatory=$true)]
    [string]$OutputFile
)

try {
    # 创建PowerPoint应用程序对象
    $ppt = New-Object -ComObject PowerPoint.Application
    # 不设置Visible属性，避免权限问题

    # 打开演示文稿
    $presentation = $ppt.Presentations.Open($InputFile, $true, $true, $false)

    $content = @()

    # 遍历所有幻灯片
    for ($slideIndex = 1; $slideIndex -le $presentation.Slides.Count; $slideIndex++) {
        $slide = $presentation.Slides.Item($slideIndex)
        $content += "=== 幻灯片 $slideIndex ==="

        # 遍历幻灯片中的所有形状
        foreach ($shape in $slide.Shapes) {
            try {
                # 处理文本内容
                if ($shape.HasTextFrame -and $shape.TextFrame.HasText) {
                    $textContent = $shape.TextFrame.TextRange.Text.Trim()
                    if ($textContent) {
                        $content += $textContent
                    }
                }

                # 处理表格
                if ($shape.HasTable) {
                    $table = $shape.Table
                    for ($row = 1; $row -le $table.Rows.Count; $row++) {
                        $rowData = @()
                        for ($col = 1; $col -le $table.Columns.Count; $col++) {
                            try {
                                $cell = $table.Cell($row, $col)
                                $cellText = $cell.Shape.TextFrame.TextRange.Text.Trim()
                                $rowData += $cellText
                            } catch {
                                $rowData += ""
                            }
                        }
                        if ($rowData -join "" -ne "") {
                            $content += ($rowData -join "`t")
                        }
                    }
                }

                # 处理图片
                if ($shape.Type -eq 13) {  # msoLinkedPicture or msoPicture
                    $content += "[幻灯片${slideIndex}_图片内容_需要OCR识别]"
                }
            } catch {
                # 忽略单个形状的错误，继续处理下一个
            }
        }
        $content += ""
    }

    # 保存到输出文件
    $contentText = $content -join "`n"
    [System.IO.File]::WriteAllText($OutputFile, $contentText, [System.Text.Encoding]::UTF8)

    Write-Host "PowerPoint PowerShell解析完成"

} catch {
    Write-Host "PowerPoint PowerShell解析失败: $($_.Exception.Message)"
    exit 1
} finally {
    # 清理资源
    if ($presentation) {
        $presentation.Close()
    }
    if ($ppt) {
        $ppt.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($ppt) | Out-Null
    }
}
'''

    def _parse_excel_with_python_libs(self, file_content: bytes) -> str:
        """使用Python库解析Excel文件（备用方案）"""
        try:
            from io import BytesIO

            content_lines = []

            # 方法1: 使用openpyxl解析.xlsx文件
            if file_content.startswith(b'PK') or file_content.startswith(b'\x50\x4b'):
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(BytesIO(file_content), data_only=True)
                    ws = wb.active

                    max_row = ws.max_row
                    max_col = ws.max_column

                    for row_idx in range(1, max_row + 1):
                        row_values = []
                        for col_idx in range(1, max_col + 1):
                            cell = ws.cell(row=row_idx, column=col_idx)
                            cell_value = cell.value

                            if cell_value is None:
                                row_values.append('')
                            elif isinstance(cell_value, (int, float)):
                                if isinstance(cell_value, float) and cell_value.is_integer():
                                    row_values.append(str(int(cell_value)))
                                else:
                                    row_values.append(str(cell_value))
                            else:
                                row_values.append(str(cell_value))

                        content_lines.append(row_values)

                    if content_lines:
                        return self._convert_to_markdown_table(content_lines)

                except Exception as e:
                    logger.warning(f"openpyxl解析失败: {str(e)}")

            # 方法2: 使用xlrd解析.xls文件
            try:
                import xlrd
                workbook = xlrd.open_workbook(file_contents=file_content)
                sheet = workbook.sheet_by_index(0)

                for row_idx in range(sheet.nrows):
                    row_values = []

                    for col_idx in range(sheet.ncols):
                        cell_value = sheet.cell_value(row_idx, col_idx)

                        if cell_value is None or cell_value == '':
                            row_values.append('')
                        elif isinstance(cell_value, float) and cell_value.is_integer():
                            row_values.append(str(int(cell_value)))
                        else:
                            row_values.append(str(cell_value))

                    content_lines.append(row_values)

                if content_lines:
                    return self._convert_to_markdown_table(content_lines)

            except Exception as e:
                logger.warning(f"xlrd解析失败: {str(e)}")

            return ""

        except Exception as e:
            logger.error(f"Excel Python库解析失败: {str(e)}")
            return ""

    def _parse_ppt_with_python_libs(self, file_content: bytes) -> str:
        """使用Python库解析PowerPoint文件（备用方案）"""
        try:
            from pptx import Presentation
            from io import BytesIO
            import base64
            import zipfile

            # 首先检查文件是否为有效的ZIP格式（.pptx文件）
            try:
                with zipfile.ZipFile(BytesIO(file_content), 'r') as zip_file:
                    if 'ppt/presentation.xml' not in zip_file.namelist():
                        return self._parse_old_ppt_file(file_content)
            except zipfile.BadZipFile:
                return self._parse_old_ppt_file(file_content)

            prs = Presentation(BytesIO(file_content))
            content_lines = []
            image_tasks = []

            for slide_num, slide in enumerate(prs.slides, 1):
                content_lines.append(f"=== 幻灯片 {slide_num} ===")

                for shape in slide.shapes:
                    # 处理文本内容
                    if hasattr(shape, "text") and shape.text.strip():
                        text = shape.text.strip()
                        content_lines.append(text)

                    # 处理表格
                    if shape.has_table:
                        table = shape.table
                        for row in table.rows:
                            row_values = []
                            for cell in row.cells:
                                cell_text = cell.text.strip() if cell.text else ''
                                row_values.append(cell_text)

                            if any(val for val in row_values):
                                row_text = '\t'.join(row_values)
                                content_lines.append(row_text)

                    # 处理图片
                    if hasattr(shape, 'image') and shape.image:
                        try:
                            image_stream = shape.image.blob
                            image_base64 = base64.b64encode(image_stream).decode('utf-8')
                            image_data_url = f"data:image/png;base64,{image_base64}"

                            image_tasks.append({
                                'slide_num': slide_num,
                                'image_data': image_data_url,
                                'placeholder': f"[幻灯片{slide_num}_图片内容_待识别]"
                            })

                            content_lines.append(f"[幻灯片{slide_num}_图片内容_待识别]")

                        except Exception as img_error:
                            logger.warning(f"图片提取失败: {str(img_error)}")
                            content_lines.append(f"[幻灯片{slide_num}_图片内容: 提取失败]")

                content_lines.append("")

            text_content = '\n'.join(content_lines)

            # 如果有图片需要识别，返回特殊标记
            if image_tasks:
                import json
                image_info = json.dumps(image_tasks)
                return f"PPT_WITH_IMAGES:{image_info}|||{text_content}"

            return text_content

        except ImportError as e:
            logger.error(f"python-pptx模块未安装: {str(e)}")
            return f"PPT解析失败: 缺少python-pptx模块，请安装: pip install python-pptx"
        except Exception as e:
            logger.error(f"PowerPoint Python库解析失败: {str(e)}")
            return ""

    def _clean_ole_extracted_text(self, text: str) -> str:
        """清理OLE提取的文本"""
        try:
            import re

            if not text or not text.strip():
                return ""

            # 分行处理
            lines = text.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()
                if len(line) < 2:
                    continue

                # 过滤明显的乱码
                # 检查中文字符比例
                chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', line))
                english_chars = len(re.findall(r'[a-zA-Z]', line))
                total_meaningful = chinese_chars + english_chars

                if total_meaningful > 0 and total_meaningful / len(line) > 0.5:
                    cleaned_lines.append(line)

            if cleaned_lines:
                return '\n\n'.join(cleaned_lines)

            return ""

        except Exception as e:
            logger.error(f"OLE文本清理失败: {str(e)}")
            return text
