# 开源组件许可证声明

本项目使用了以下开源组件，特此声明其许可证信息：

## 核心依赖

### Qdrant
- **许可证**: Apache License 2.0
- **版权**: Copyright 2021 Qdrant
- **用途**: 向量数据库
- **商用**: ✅ 允许商业使用

### sentence-transformers
- **许可证**: Apache License 2.0
- **版权**: Copyright 2019 UKP Lab, Technische Universität Darmstadt
- **用途**: 文本向量化
- **商用**: ✅ 允许商业使用

### text2vec-base-chinese
- **许可证**: Apache License 2.0
- **版权**: Copyright 2021 shibing624
- **用途**: 中文语义向量模型
- **商用**: ✅ 允许商业使用

### FastAPI
- **许可证**: MIT License
- **版权**: Copyright 2018 Sebas<PERSON><PERSON> Ramírez
- **用途**: Web框架
- **商用**: ✅ 允许商业使用

### PyTorch
- **许可证**: BSD 3-Clause License
- **版权**: Copyright 2016 Facebook, Inc
- **用途**: 深度学习框架
- **商用**: ✅ 允许商业使用

## 许可证全文

### Apache License 2.0
```
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
```

### MIT License
```
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 商用声明

本项目基于上述开源组件构建，所有组件均允许商业使用。在商业部署时：

1. **保留许可证声明**: 必须在产品文档中包含此许可证声明
2. **遵守许可条款**: 按照各组件的许可证条款使用
3. **数据安全**: 确保客户数据的安全和隐私保护
4. **本地部署**: 建议采用本地部署方式，避免数据泄露风险

## 免责声明

本软件按"现状"提供，不提供任何明示或暗示的保证。使用者需自行承担使用风险。

---

**更新日期**: 2025-07-27
**版本**: 1.0.0
