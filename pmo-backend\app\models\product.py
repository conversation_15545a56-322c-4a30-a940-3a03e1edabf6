#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品模型 - 参考禅道zt_product表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
import pymysql
from app.core.database import get_db_connection, close_db_connection

class Product:
    """产品模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.program: int = 0
        self.name: str = ""
        self.code: str = ""
        self.bind: str = "0"
        self.line: int = 0
        self.type: str = "normal"
        self.status: str = "normal"
        self.subStatus: str = ""
        self.desc: Optional[str] = None
        self.PO: str = ""
        self.QD: str = ""
        self.RD: str = ""
        self.feedback: str = ""
        self.acl: str = "open"
        self.whitelist: Optional[str] = None
        self.reviewer: Optional[str] = None
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.createdVersion: str = ""
        self.order: int = 0
        self.vision: str = "rnd"
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, product_id: int) -> Optional['Product']:
        """根据ID获取产品"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_product 
                WHERE id = %s AND deleted = '0'
            """, (product_id,))
            
            row = cursor.fetchone()
            if row:
                product = cls()
                product._load_from_dict(row)
                return product
            return None
            
        except Exception as e:
            print(f"获取产品失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_products(cls, page: int = 1, page_size: int = 20, keyword: str = "", status: str = "") -> Dict[str, Any]:
        """获取产品列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["deleted = '0'"]
            params = []
            
            if keyword:
                where_conditions.append("(name LIKE %s OR code LIKE %s OR `desc` LIKE %s)")
                like_keyword = f"%{keyword}%"
                params.extend([like_keyword, like_keyword, like_keyword])
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_product WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT p.*, pl.name as line_name
                FROM zt_product p
                LEFT JOIN zt_productline pl ON p.line = pl.id
                WHERE {where_clause}
                ORDER BY p.`order` ASC, p.id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            products = cursor.fetchall()
            
            return {
                "products": products,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取产品列表失败: {e}")
            return {"products": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建产品"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查产品名称是否已存在
            cursor.execute("SELECT id FROM zt_product WHERE name = %s AND deleted = '0'", (self.name,))
            if cursor.fetchone():
                raise ValueError(f"产品 {self.name} 已存在")
            
            # 检查产品代号是否已存在
            if self.code:
                cursor.execute("SELECT id FROM zt_product WHERE code = %s AND deleted = '0'", (self.code,))
                if cursor.fetchone():
                    raise ValueError(f"产品代号 {self.code} 已存在")
            
            # 插入产品
            cursor.execute("""
                INSERT INTO zt_product (
                    program, name, code, bind, line, type, status, subStatus, `desc`,
                    PO, QD, RD, feedback, acl, whitelist, reviewer, createdBy, 
                    createdDate, createdVersion, `order`, vision
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s
                )
            """, (
                self.program, self.name, self.code, self.bind, self.line, self.type,
                self.status, self.subStatus, self.desc, self.PO, self.QD, self.RD,
                self.feedback, self.acl, self.whitelist, self.reviewer, self.createdBy,
                self.createdVersion, self.order, self.vision
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建产品失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新产品信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_product SET 
                    name = %s, code = %s, line = %s, type = %s, status = %s,
                    `desc` = %s, PO = %s, QD = %s, RD = %s, acl = %s, `order` = %s
                WHERE id = %s
            """, (
                self.name, self.code, self.line, self.type, self.status,
                self.desc, self.PO, self.QD, self.RD, self.acl, self.order, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新产品失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除产品"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查是否有关联的需求
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_story 
                WHERE product = %s AND deleted = '0'
            """, (self.id,))
            if cursor.fetchone()['count'] > 0:
                raise ValueError("该产品下还有需求，无法删除")
            
            cursor.execute("""
                UPDATE zt_product SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除产品失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_stories(self, page: int = 1, page_size: int = 20, status: str = "") -> Dict[str, Any]:
        """获取产品下的需求"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["product = %s", "deleted = '0'"]
            params = [self.id]
            
            if status:
                where_conditions.append("status = %s")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_sql = f"SELECT COUNT(*) as total FROM zt_story WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = f"""
                SELECT id, title, type, category, pri, status, stage, 
                       openedBy, openedDate, assignedTo, estimate
                FROM zt_story 
                WHERE {where_clause}
                ORDER BY `order` ASC, id DESC
                LIMIT %s OFFSET %s
            """
            params.extend([page_size, offset])
            cursor.execute(list_sql, params)
            
            stories = cursor.fetchall()
            
            return {
                "stories": stories,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            print(f"获取产品需求失败: {e}")
            return {"stories": [], "total": 0, "page": page, "page_size": page_size, "total_pages": 0}
        finally:
            if conn:
                close_db_connection(conn)

    def get_plans(self) -> List[Dict[str, Any]]:
        """获取产品计划"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_productplan 
                WHERE product = %s AND deleted = '0'
                ORDER BY `order` ASC, id DESC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取产品计划失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def get_releases(self) -> List[Dict[str, Any]]:
        """获取产品发布"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_release 
                WHERE product = %s AND deleted = '0'
                ORDER BY date DESC, id DESC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取产品发布失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def get_modules(self) -> List[Dict[str, Any]]:
        """获取产品模块"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_module 
                WHERE root = %s AND deleted = '0'
                ORDER BY grade ASC, `order` ASC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取产品模块失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "program": self.program,
            "name": self.name,
            "code": self.code,
            "bind": self.bind,
            "line": self.line,
            "type": self.type,
            "status": self.status,
            "subStatus": self.subStatus,
            "desc": self.desc,
            "PO": self.PO,
            "QD": self.QD,
            "RD": self.RD,
            "feedback": self.feedback,
            "acl": self.acl,
            "whitelist": self.whitelist,
            "reviewer": self.reviewer,
            "createdBy": self.createdBy,
            "createdDate": self.createdDate.isoformat() if self.createdDate else None,
            "createdVersion": self.createdVersion,
            "order": self.order,
            "vision": self.vision,
            "deleted": self.deleted
        }


class ProductLine:
    """产品线模型类"""

    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.type: str = "product"
        self.desc: Optional[str] = None
        self.order: int = 0
        self.deleted: str = "0"

    @classmethod
    def get_all_lines(cls) -> List['ProductLine']:
        """获取所有产品线"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM zt_productline
                WHERE deleted = '0'
                ORDER BY `order` ASC, id ASC
            """)

            rows = cursor.fetchall()
            lines = []
            for row in rows:
                line = cls()
                line._load_from_dict(row)
                lines.append(line)

            return lines

        except Exception as e:
            print(f"获取产品线列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "desc": self.desc,
            "order": self.order,
            "deleted": self.deleted
        }
