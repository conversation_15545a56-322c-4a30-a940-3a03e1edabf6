#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新项目详情云函数

功能：根据项目编码更新项目信息
接口：HTTP POST
参数：
  - project_code：项目编码（必填）
  - 其他需要更新的字段

返回数据：
{
  "code": 200,
  "message": "success",
  "data": {
    "updated": true
  }
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def update_project_details(data):
    """
    更新项目详情
    :param data: 包含项目代码和需要更新的字段
    :return: 更新结果
    """
    if not data or 'project_code' not in data:
        raise ValueError("项目编码不能为空")
        
    project_code = data.pop('project_code')
    
    if not data:
        return {"updated": False, "message": "没有提供需要更新的字段"}
        
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建更新SQL
            fields = []
            values = []
            
            for key, value in data.items():
                fields.append(f"{key} = %s")
                values.append(value)
                
            if not fields:
                return {"updated": False, "message": "没有提供有效的更新字段"}
                
            sql = f"""
            UPDATE Project_Account_Book
            SET {", ".join(fields)}
            WHERE project_code = %s
            """
            
            values.append(project_code)
            
            # 执行更新
            cursor.execute(sql, values)
            conn.commit()
            
            # 检查是否有记录被更新
            if cursor.rowcount > 0:
                # 记录变更日志
                log_changes(conn, project_code, data)
                return {"updated": True, "message": "项目信息已更新"}
            else:
                return {"updated": False, "message": f"未找到项目编码为 {project_code} 的记录"}
                
    except Exception as e:
        logger.error(f"更新项目详情错误: {str(e)}")
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()

def log_changes(conn, project_code, changes):
    """
    记录项目变更日志
    :param conn: 数据库连接
    :param project_code: 项目编码
    :param changes: 变更信息
    """
    try:
        with conn.cursor() as cursor:
            # 获取项目名称
            cursor.execute("SELECT project_name FROM Project_Account_Book WHERE project_code = %s", (project_code,))
            project = cursor.fetchone()
            
            if not project:
                logger.warning(f"未找到项目编码为 {project_code} 的项目记录，无法记录变更日志")
                return
                
            project_name = project.get('project_name', '')
            
            # 获取操作用户信息
            user_id = changes.pop('UserID', '') if 'UserID' in changes else ''
            name = changes.pop('name', '') if 'name' in changes else ''
            
            # 记录变更日志
            for field, new_value in changes.items():
                # 获取原值
                cursor.execute(f"SELECT {field} FROM Project_Account_Book WHERE project_code = %s", (project_code,))
                result = cursor.fetchone()
                previous_value = result.get(field, None) if result else None
                
                # 只有值发生变化时才记录
                if previous_value != new_value:
                    sql = """
                    INSERT INTO change_log 
                    (project_code, project_name, field, UserID, name, previous_value, new_value, change_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                    """
                    cursor.execute(sql, (
                        project_code, project_name, field, user_id, name, 
                        str(previous_value) if previous_value is not None else '',
                        str(new_value) if new_value is not None else ''
                    ))
            
            conn.commit()
            logger.info(f"成功记录项目 {project_code} 的变更日志")
    except Exception as e:
        logger.error(f"记录变更日志错误: {str(e)}")
        conn.rollback()

def parse_request_body(event):
    """解析请求体"""
    if 'body' in event:
        try:
            return json.loads(event['body'])
        except json.JSONDecodeError:
            return {}
    return {}

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析请求体
        data = parse_request_body(event)
        
        if not data:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({
                    'code': 400,
                    'message': '请求体不能为空',
                    'data': None
                }, ensure_ascii=False)
            }
        
        # 更新项目详情
        result = update_project_details(data)
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 200,
                'message': 'success',
                'data': result
            }, ensure_ascii=False)
        }
    except ValueError as e:
        return {
            'statusCode': 400,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 400,
                'message': str(e),
                'data': None
            }, ensure_ascii=False)
        }
    except Exception as e:
        logger.error(f"处理请求错误: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'更新项目详情失败: {str(e)}',
                'data': None
            }, ensure_ascii=False)
        } 