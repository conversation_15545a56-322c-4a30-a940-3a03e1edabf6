#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户组模型 - 参考禅道zt_group表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import pymysql
import json
from app.core.database import get_db_connection, close_db_connection

class Group:
    """用户组模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.role: str = ""
        self.desc: str = ""
        self.acl: str = ""
        self.deleted: str = "0"
        self.created_at: Optional[datetime] = None

    @classmethod
    def get_by_id(cls, group_id: int) -> Optional['Group']:
        """根据ID获取用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_group 
                WHERE id = %s AND deleted = '0'
            """, (group_id,))
            
            row = cursor.fetchone()
            if row:
                group = cls()
                group._load_from_dict(row)
                return group
            return None
            
        except Exception as e:
            print(f"获取用户组失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_groups(cls) -> List['Group']:
        """获取所有用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_group 
                WHERE deleted = '0'
                ORDER BY id ASC
            """)
            
            rows = cursor.fetchall()
            groups = []
            for row in rows:
                group = cls()
                group._load_from_dict(row)
                groups.append(group)
            
            return groups
            
        except Exception as e:
            print(f"获取用户组列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_user_groups(cls, account: str) -> List['Group']:
        """获取用户所属的用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT g.* FROM zt_group g
                INNER JOIN zt_usergroup ug ON g.id = ug.group_id
                WHERE ug.account = %s AND g.deleted = '0'
                ORDER BY g.id ASC
            """, (account,))
            
            rows = cursor.fetchall()
            groups = []
            for row in rows:
                group = cls()
                group._load_from_dict(row)
                groups.append(group)
            
            return groups
            
        except Exception as e:
            print(f"获取用户组失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def create(self) -> bool:
        """创建用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查用户组名称是否已存在
            cursor.execute("""
                SELECT id FROM zt_group 
                WHERE name = %s AND deleted = '0'
            """, (self.name,))
            if cursor.fetchone():
                raise ValueError(f"用户组 {self.name} 已存在")
            
            # 插入用户组
            cursor.execute("""
                INSERT INTO zt_group (
                    name, role, `desc`, acl, created_at
                ) VALUES (
                    %s, %s, %s, %s, NOW()
                )
            """, (
                self.name, self.role, self.desc, self.acl
            ))
            
            self.id = cursor.lastrowid
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"创建用户组失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def update(self) -> bool:
        """更新用户组信息"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE zt_group SET 
                    name = %s, role = %s, `desc` = %s, acl = %s
                WHERE id = %s
            """, (
                self.name, self.role, self.desc, self.acl, self.id
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"更新用户组失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def delete(self) -> bool:
        """软删除用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查是否有用户关联
            cursor.execute("""
                SELECT COUNT(*) as count FROM zt_usergroup 
                WHERE group_id = %s
            """, (self.id,))
            if cursor.fetchone()['count'] > 0:
                raise ValueError("该用户组下还有用户，无法删除")
            
            cursor.execute("""
                UPDATE zt_group SET deleted = '1' WHERE id = %s
            """, (self.id,))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除用户组失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def add_user(self, account: str) -> bool:
        """添加用户到用户组"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查用户是否已在组中
            cursor.execute("""
                SELECT 1 FROM zt_usergroup 
                WHERE account = %s AND group_id = %s
            """, (account, self.id))
            if cursor.fetchone():
                return True  # 已存在，直接返回成功
            
            # 添加用户到组
            cursor.execute("""
                INSERT INTO zt_usergroup (account, group_id) 
                VALUES (%s, %s)
            """, (account, self.id))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"添加用户到用户组失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def remove_user(self, account: str) -> bool:
        """从用户组移除用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM zt_usergroup 
                WHERE account = %s AND group_id = %s
            """, (account, self.id))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"从用户组移除用户失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_users(self) -> List[Dict[str, Any]]:
        """获取用户组中的用户"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT u.id, u.account, u.realname, u.email, u.role, u.created_at
                FROM zt_user u
                INNER JOIN zt_usergroup ug ON u.account = ug.account
                WHERE ug.group_id = %s AND u.deleted = '0'
                ORDER BY u.id ASC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取用户组用户失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def set_permissions(self, permissions: List[Dict[str, str]]) -> bool:
        """设置用户组权限"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 删除现有权限
            cursor.execute("""
                DELETE FROM zt_grouppriv WHERE group_id = %s
            """, (self.id,))
            
            # 添加新权限
            for perm in permissions:
                cursor.execute("""
                    INSERT INTO zt_grouppriv (group_id, module, method) 
                    VALUES (%s, %s, %s)
                """, (self.id, perm['module'], perm['method']))
            
            conn.commit()
            return True
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"设置用户组权限失败: {e}")
            return False
        finally:
            if conn:
                close_db_connection(conn)

    def get_permissions(self) -> List[Dict[str, str]]:
        """获取用户组权限"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT module, method FROM zt_grouppriv 
                WHERE group_id = %s
                ORDER BY module, method
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取用户组权限失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "role": self.role,
            "desc": self.desc,
            "acl": self.acl,
            "deleted": self.deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
