#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
user_project_management 云函数
统一处理用户账户管理、用户数据管理、项目工时管理等功能
合并了原 userManagement、saveProjectHoursData 和 get_project_hours_data 三个云函数的功能
"""

import json
import os
import pymysql
import hashlib
import re
import secrets
import hmac
import base64
from datetime import datetime

# 配置 PyMySQL 以禁用 cryptography
pymysql.install_as_MySQLdb()
# 禁用 ssl 验证，避免需要 cryptography
pymysql._auth.sha256_password = None 
pymysql._auth.caching_sha2_password = None
print("已禁用 PyMySQL 中的 cryptography 依赖")

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

#===========================
# 密码处理相关函数
#===========================

def generate_salt():
    """生成随机盐值"""
    return secrets.token_hex(16)

def hash_password(password, salt=None):
    """
    使用PBKDF2算法哈希密码
    :param password: 明文密码
    :param salt: 盐值，如果为None则生成新的盐值
    :return: salt:hash格式的密码哈希
    """
    if salt is None:
        salt = generate_salt()
    
    key = hashlib.pbkdf2_hmac(
        'sha512', 
        password.encode('utf-8'), 
        salt.encode('utf-8'), 
        1000,
        dklen=64
    )
    
    password_hash = base64.b64encode(key).decode('utf-8')
    return f"{salt}:{password_hash}"

def verify_password(password, stored_hash):
    """
    验证密码是否匹配
    :param password: 明文密码
    :param stored_hash: 存储的密码哈希（salt:hash格式）
    :return: 密码是否匹配
    """
    try:
        salt, hash_value = stored_hash.split(':')
        password_hash = hash_password(password, salt)
        return password_hash == stored_hash
    except Exception as e:
        print(f"密码验证错误: {str(e)}")
        return False

#===========================
# 用户账户管理相关函数
#===========================

def handle_logout(event):
    """
    处理用户注销请求
    :param event: 请求参数，必须包含userId
    :return: 操作结果
    """
    user_id = event.get('userId')
    if not user_id:
        return {'code': 400, 'message': '缺少必要参数：userId'}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 更新用户openid为空
            sql = "UPDATE users SET openid = '' WHERE UserID = %s"
            result = cursor.execute(sql, (user_id,))
            connection.commit()
            
            if result > 0:
                return {'code': 200, 'message': '注销成功'}
            else:
                return {'code': 404, 'message': '用户不存在'}
    except Exception as e:
        print(f"注销用户失败: {str(e)}")
        return {'code': 500, 'message': f'操作失败: {str(e)}'}
    finally:
        if connection:
            connection.close()

def handle_change_password(event):
    """
    处理用户密码修改请求
    :param event: 请求参数，必须包含userId、oldPassword和newPassword
    :return: 操作结果
    """
    user_id = event.get('userId')
    old_password = event.get('oldPassword')
    new_password = event.get('newPassword')
    
    if not user_id or not old_password or not new_password:
        return {'code': 400, 'message': '缺少必要参数'}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 查询用户
            sql = "SELECT password_hash FROM users WHERE UserID = %s"
            cursor.execute(sql, (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return {'code': 404, 'message': '用户不存在'}
            
            # 验证旧密码
            if not verify_password(old_password, user['password_hash']):
                return {'code': 400, 'message': '原密码不正确'}
            
            # 生成新密码哈希
            new_password_hash = hash_password(new_password)
            
            # 更新密码
            update_sql = "UPDATE users SET password_hash = %s WHERE UserID = %s"
            result = cursor.execute(update_sql, (new_password_hash, user_id))
            connection.commit()
            
            if result > 0:
                return {'code': 200, 'message': '密码修改成功'}
            else:
                return {'code': 500, 'message': '密码修改失败'}
                
    except Exception as e:
        print(f"修改密码失败: {str(e)}")
        return {'code': 500, 'message': f'操作失败: {str(e)}'}
    finally:
        if connection:
            connection.close()

def handle_reset_password(event):
    """
    处理管理员重置用户密码请求
    :param event: 请求参数，必须包含userId、newPassword和adminUserId
    :return: 操作结果
    """
    user_id = event.get('userId')
    new_password = event.get('newPassword')
    admin_user_id = event.get('adminUserId')
    
    if not user_id or not new_password or not admin_user_id:
        return {'code': 400, 'message': '缺少必要参数'}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 验证管理员权限
            sql = "SELECT role FROM users WHERE UserID = %s"
            cursor.execute(sql, (admin_user_id,))
            admin = cursor.fetchone()
            
            if not admin or admin['role'] != 1:
                return {'code': 403, 'message': '无权执行此操作'}
            
            # 直接使用明文密码，不进行哈希处理
            new_password_value = new_password
            
            # 更新用户密码
            update_sql = "UPDATE users SET password_hash = %s WHERE UserID = %s"
            result = cursor.execute(update_sql, (new_password_value, user_id))
            connection.commit()
            
            if result > 0:
                return {'code': 200, 'message': '密码重置成功'}
            else:
                return {'code': 404, 'message': '用户不存在'}
                
    except Exception as e:
        print(f"重置密码失败: {str(e)}")
        return {'code': 500, 'message': f'操作失败: {str(e)}'}
    finally:
        if connection:
            connection.close()

#===========================
# 用户数据管理相关函数
#===========================

def handle_update_user(event):
    """
    处理用户信息更新/创建请求
    :param event: 请求参数，必须包含userData
    :return: 操作结果
    """
    user_data = event.get('userData')
    original_data = event.get('originalData')
    updated_by = event.get('updatedBy')
    
    if not user_data or not isinstance(user_data, dict):
        return {'code': 400, 'message': '缺少用户数据或格式错误'}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 检查用户是否已存在
            sql = "SELECT * FROM users WHERE UserID = %s"
            cursor.execute(sql, (user_data.get('UserID'),))
            existing_user = cursor.fetchone()
            
            try:
                # 开始事务
                connection.begin()
                
                if existing_user:
                    # 用户存在，执行更新
                    update_fields = [
                        'name',
                        'username',
                        'company_name',
                        'department_name',
                        'LaborCost',
                        'role',
                        'is_disabled'
                    ]
                    
                    update_sql = f"""
                        UPDATE users 
                        SET {', '.join([f'{field} = %s' for field in update_fields])}
                        WHERE UserID = %s
                    """
                    
                    update_params = [
                        user_data.get('name'),
                        user_data.get('username'),
                        user_data.get('company_name'),
                        user_data.get('department_name'),
                        float(user_data.get('LaborCost', 0)),
                        int(user_data.get('role', 3)),
                        user_data.get('is_disabled', 'N'),
                        user_data.get('UserID')
                    ]
                    
                    cursor.execute(update_sql, update_params)
                    
                    result = {
                        'id': existing_user['id'],
                        'message': '用户信息更新成功',
                        'UserID': user_data.get('UserID')
                    }
                else:
                    # 用户不存在，创建新用户
                    # 如果有密码字段，直接使用明文密码
                    password_value = ''
                    if user_data.get('password'):
                        password_value = user_data.get('password')
                    
                    insert_fields = [
                        'username',
                        'password_hash',
                        'openid',
                        'name',
                        'UserID',
                        'company_name',
                        'department_name',
                        'LaborCost',
                        'role',
                        'is_disabled'
                    ]
                    
                    insert_sql = f"""
                        INSERT INTO users ({', '.join(insert_fields)})
                        VALUES ({', '.join(['%s'] * len(insert_fields))})
                    """
                    
                    insert_params = [
                        user_data.get('username') or user_data.get('UserID', '').lower(),
                        password_value,
                        user_data.get('openid', ''),
                        user_data.get('name'),
                        user_data.get('UserID'),
                        user_data.get('company_name'),
                        user_data.get('department_name'),
                        float(user_data.get('LaborCost', 0)),
                        int(user_data.get('role', 3)),
                        user_data.get('is_disabled', 'N')
                    ]
                    
                    cursor.execute(insert_sql, insert_params)
                    
                    result = {
                        'id': connection.insert_id(),
                        'message': '用户创建成功',
                        'UserID': user_data.get('UserID')
                    }
                
                # 记录变更日志
                try:
                    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    log_sql = """
                        INSERT INTO change_log 
                        (name, UserID, previous_value, new_value, change_time) 
                        VALUES (%s, %s, %s, %s, %s)
                    """
                    
                    log_params = [
                        updated_by,
                        user_data.get('UserID'),
                        json.dumps(original_data) if original_data else '新建用户',
                        json.dumps(user_data),
                        now
                    ]
                    
                    cursor.execute(log_sql, log_params)
                except Exception as log_error:
                    print(f"记录变更日志失败: {str(log_error)}")
                    # 继续执行，不影响主要操作
                
                # 提交事务
                connection.commit()
                
                return {
                    'code': 200,
                    'data': result,
                    'message': result['message']
                }
                
            except Exception as tx_error:
                # 回滚事务
                connection.rollback()
                raise tx_error
                
    except Exception as e:
        print(f"更新用户信息失败: {str(e)}")
        return {
            'code': 500,
            'message': f'更新用户信息失败: {str(e)}',
            'error': str(e)
        }
    finally:
        if connection:
            connection.close()

def handle_delete_user(event):
    """
    处理删除用户请求
    :param event: 请求参数，必须包含userId和adminUserId
    :return: 操作结果
    """
    user_id = event.get('userId')
    admin_user_id = event.get('adminUserId')
    
    if not user_id:
        return {'code': 400, 'message': '缺少必要参数：userId'}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            try:
                # 开始事务
                connection.begin()
                
                # 注释掉管理员权限验证
                # admin_sql = "SELECT role FROM users WHERE UserID = %s"
                # cursor.execute(admin_sql, (admin_user_id,))
                # admin = cursor.fetchone()
                
                # if not admin or admin['role'] != 1:
                #     return {'code': 403, 'message': '无权删除用户'}
                
                # 查询要删除的用户
                user_sql = "SELECT * FROM users WHERE UserID = %s"
                cursor.execute(user_sql, (user_id,))
                user = cursor.fetchone()
                
                if not user:
                    return {'code': 404, 'message': '用户不存在'}
                
                # 记录删除日志
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_sql = """
                    INSERT INTO change_log 
                    (name, UserID, previous_value, new_value, change_time) 
                    VALUES (%s, %s, %s, %s, %s)
                """
                
                log_params = [
                    admin_user_id or 'system',  # 如果没有提供adminUserId，则使用'system'
                    user_id,
                    json.dumps(user),
                    '用户已删除',
                    now
                ]
                
                cursor.execute(log_sql, log_params)
                
                # 删除用户
                delete_sql = "DELETE FROM users WHERE UserID = %s"
                result = cursor.execute(delete_sql, (user_id,))
                
                # 提交事务
                connection.commit()
                
                return {
                    'code': 200,
                    'data': {
                        'affectedRows': result,
                        'message': '用户删除成功'
                    },
                    'message': '用户删除成功'
                }
                
            except Exception as tx_error:
                # 回滚事务
                connection.rollback()
                raise tx_error
                
    except Exception as e:
        print(f"删除用户失败: {str(e)}")
        return {
            'code': 500,
            'message': f'删除用户失败: {str(e)}',
            'error': str(e)
        }
    finally:
        if connection:
            connection.close()

#===========================
# 项目工时相关函数
#===========================

def handle_save_project_hours(event):
    """
    处理保存项目工时数据请求
    :param event: 请求参数，必须包含items
    :return: 操作结果
    """
    items = event.get('items')
    
    if not items or not isinstance(items, list):
        return {'code': 400, 'message': '缺少项目工时数据或格式错误'}
    
    result = {'success': 0, 'failed': 0, 'failedItems': []}
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            try:
                # 开始事务
                connection.begin()
                
                for item in items:
                    try:
                        project_code = item.get('project_code')
                        project_name = item.get('project_name')
                        department = item.get('department')
                        name = item.get('name')
                        user_id = item.get('UserID')
                        month = item.get('month')
                        working_hours = item.get('working_hours')
                        
                        sql = """
                            INSERT INTO project_hours 
                            (project_code, project_name, department, name, UserID, month, working_hours) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE 
                            project_name = VALUES(project_name),
                            department = VALUES(department),
                            name = VALUES(name),
                            working_hours = VALUES(working_hours)
                        """
                        
                        cursor.execute(sql, [
                            project_code,
                            project_name,
                            department,
                            name,
                            user_id,
                            month,
                            working_hours
                        ])
                        
                        result['success'] += 1
                    except Exception as item_error:
                        print(f"保存项目工时项失败: {str(item_error)}")
                        result['failed'] += 1
                        result['failedItems'].append({
                            'item': item,
                            'error': str(item_error)
                        })
                
                # 提交事务
                connection.commit()
                
                return {
                    'code': 200,
                    'data': result,
                    'message': '保存成功'
                }
                
            except Exception as tx_error:
                # 回滚事务
                connection.rollback()
                raise tx_error
                
    except Exception as e:
        print(f"保存项目工时数据失败: {str(e)}")
        return {
            'code': 500,
            'message': f'保存失败: {str(e)}',
            'error': str(e)
        }
    finally:
        if connection:
            connection.close()

#===========================
# 数据查询相关函数
#===========================

def handle_get_users_list(params):
    """
    处理获取用户列表请求
    :param params: 请求参数，包含查询条件
    :return: 用户列表及按部门分组的数据
    """
    query_params = params.get('queryParams', {})
    sort_by = params.get('sortBy', 'UserID')  # 默认按UserID排序
    sort_order = params.get('sortOrder', 'asc')  # 默认升序排列
    
    print(f"获取用户列表 - 参数: {query_params}, 排序: {sort_by} {sort_order}")
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 构建查询语句
            sql = 'SELECT * FROM users WHERE 1=1'
            values = []
            
            # 构建查询条件 
            if query_params.get('UserID'):
                sql += ' AND UserID = %s'
                values.append(query_params.get('UserID'))
            
            if query_params.get('department_name'):
                sql += ' AND department_name = %s'
                values.append(query_params.get('department_name'))
            
            if query_params.get('role'):
                sql += ' AND role = %s'
                values.append(int(query_params.get('role')))
            elif query_params.get('role_filter') == 'not_admin':
                # 管理员可以选择所有role不等于1的用户
                sql += ' AND role != 1'
            elif query_params.get('role_filter') == 'not_manager':
                # 部门管理员可以选择role不等于1或2的用户
                sql += ' AND role NOT IN (1, 2)'
            
            if query_params.get('is_disabled') == 'N':
                sql += ' AND (is_disabled IS NULL OR is_disabled = "N")'
            elif query_params.get('is_disabled') == 'Y':
                sql += ' AND is_disabled = "Y"'
            else:
                # 默认只显示未禁用的用户
                sql += ' AND (is_disabled IS NULL OR is_disabled = "N")'
            
            # 搜索条件
            if query_params.get('search_text'):
                search_text = f"%{query_params.get('search_text')}%"
                sql += ' AND (name LIKE %s OR UserID LIKE %s OR username LIKE %s OR company_name LIKE %s OR department_name LIKE %s)'
                values.extend([search_text, search_text, search_text, search_text, search_text])
                print(f"添加搜索条件: {search_text}")
            
            # 添加排序 - 按指定字段排序
            if sort_by == 'UserID':
                # 对于UserID字段，尝试按数字部分排序
                sql += f' ORDER BY UserID {"ASC" if sort_order.lower() == "asc" else "DESC"}'
            else:
                sql += f' ORDER BY {sort_by} {"ASC" if sort_order.lower() == "asc" else "DESC"}'
            
            # 不限制返回数量，返回所有匹配的用户
            # sql += ' LIMIT 100'  # 移除限制
            
            print(f'执行SQL: {sql}, 参数: {values}')
            
            # 执行查询
            cursor.execute(sql, values)
            rows = cursor.fetchall()
            
            print(f"查询到 {len(rows)} 条用户记录")
            
            # 将数据格式化为列表
            formatted_users = []
            for user in rows:
                formatted_user = {
                    'id': user.get('id'),
                    'UserID': user.get('UserID', ''),
                    'name': user.get('name', ''),
                    'username': user.get('username', ''),
                    'company_name': user.get('company_name', ''),
                    'department_name': user.get('department_name', ''),
                    'LaborCost': user.get('LaborCost', 0),
                    'role': user.get('role', 3),
                    'is_disabled': user.get('is_disabled', 'N')
                }
                formatted_users.append(formatted_user)
            
            # 打印第一个用户信息以便调试
            if formatted_users:
                first_user = formatted_users[0]
                print(f"第一个用户信息: UserID={first_user['UserID']}, name={first_user['name']}, company={first_user['company_name']}")
            
            # 按部门分组用户
            department_groups = {}
            
            for user in formatted_users:
                dept = user.get('department_name') or '未分组'
                if dept not in department_groups:
                    department_groups[dept] = []
                department_groups[dept].append(user)
            
            # 转换为数组格式，便于前端处理
            grouped_data = []
            for dept, users in department_groups.items():
                grouped_data.append({
                    'department_name': dept,
                    'name': dept,
                    'users': users
                })
            
            # 构建响应数据
            response_data = {
                'code': 200,
                'data': formatted_users,
                'groupedData': grouped_data,
                'message': '获取用户列表成功'
            }
            
            # 序列化为JSON，确保中文正确处理
            response_json = json.dumps(response_data, ensure_ascii=False)
            
            # 返回结果
            return {
                'statusCode': 200,
                'headers': {
                    'Content-Type': 'application/json; charset=utf-8',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': response_json
            }
            
    except Exception as e:
        print(f"获取用户列表失败: {str(e)}")
        error_json = json.dumps({
            'code': 500,
            'message': f'获取用户列表失败: {str(e)}',
            'error': str(e)
        }, ensure_ascii=False)
        
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json; charset=utf-8',
                'Access-Control-Allow-Origin': '*'
            },
            'body': error_json
        }
    finally:
        if connection:
            connection.close()

def handle_get_project_hours_data(params):
    """
    处理获取项目工时数据请求
    :param params: 请求参数，包含用户ID和月份
    :return: 项目工时数据
    """
    user_id = params.get('UserID')
    month = params.get('month')
    
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            sql = 'SELECT * FROM project_hours WHERE 1=1'
            values = []
            
            if user_id:
                sql += ' AND UserID = %s'
                values.append(user_id)
            
            if month:
                sql += ' AND month = %s'
                values.append(month)
            
            # 排序
            sql += ' ORDER BY project_name ASC'
            
            # 执行查询
            cursor.execute(sql, values)
            rows = cursor.fetchall()
            
            return {
                'code': 200,
                'data': rows,
                'message': '获取数据成功'
            }
            
    except Exception as e:
        print(f"获取项目工时数据失败: {str(e)}")
        return {
            'code': 500,
            'message': f'获取数据失败: {str(e)}',
            'error': str(e)
        }
    finally:
        if connection:
            connection.close()

def handle_get_department_list():
    """
    处理获取部门列表请求
    :return: 部门列表，包含完整的部门信息
    """
    try:
        connection = get_connection()
        with connection.cursor() as cursor:
            # 获取基本部门列表（从users表中提取）
            sql_departments = 'SELECT DISTINCT department_name FROM users WHERE department_name IS NOT NULL AND department_name != "" ORDER BY department_name ASC'
            cursor.execute(sql_departments)
            department_rows = cursor.fetchall()
            departments = [row.get('department_name') for row in department_rows]
            
            # 获取基本公司列表（从users表中提取）
            sql_companies = 'SELECT DISTINCT company_name FROM users WHERE company_name IS NOT NULL AND company_name != "" ORDER BY company_name ASC'
            cursor.execute(sql_companies)
            company_rows = cursor.fetchall()
            companies = [row.get('company_name') for row in company_rows]
            
            # 获取部门与公司的关联关系
            sql_dept_company = '''
                SELECT DISTINCT department_name, company_name 
                FROM users 
                WHERE department_name IS NOT NULL AND department_name != "" 
                      AND company_name IS NOT NULL AND company_name != ""
                ORDER BY company_name ASC, department_name ASC
            '''
            cursor.execute(sql_dept_company)
            relation_rows = cursor.fetchall()
            
            # 构建部门与公司的关联数据
            dept_company_relations = []
            for row in relation_rows:
                dept_company_relations.append({
                    'department': row.get('department_name', ''),
                    'company': row.get('company_name', '')
                })
            
            # 构建按公司分组的部门数据
            company_departments = {}
            for relation in dept_company_relations:
                company = relation.get('company')
                department = relation.get('department')
                
                if company not in company_departments:
                    company_departments[company] = []
                
                if department not in company_departments[company]:
                    company_departments[company].append(department)
            
            # 转换为数组格式
            grouped_departments = []
            for company, depts in company_departments.items():
                grouped_departments.append({
                    'company': company,
                    'departments': depts
                })
            
            return {
                'code': 200,
                'data': {
                    'departments': departments,
                    'companies': companies,
                    'relations': dept_company_relations,
                    'groupedDepartments': grouped_departments
                },
                'message': '获取部门列表成功'
            }
            
    except Exception as e:
        print(f"获取部门列表失败: {str(e)}")
        return {
            'code': 500,
            'message': f'获取部门列表失败: {str(e)}',
            'error': str(e)
        }
    finally:
        if connection:
            connection.close()

#===========================
# 云函数入口
#===========================

def main_handler(event, context):
    """
    云函数入口函数
    :param event: 事件对象
    :param context: 上下文对象
    :return: 处理结果
    """
    print(f"接收到请求: {event}")
    
    try:
        # 解析请求参数
        action = event.get('action')
        
        # 根据action分发到不同的处理函数
        if action == 'logout':
            return handle_logout(event)
        elif action == 'changePassword':
            return handle_change_password(event)
        elif action == 'resetPassword':
            return handle_reset_password(event)
        elif action == 'updateUser':
            return handle_update_user(event)
        elif action == 'deleteUser':
            return handle_delete_user(event)
        elif action == 'saveProjectHours':
            return handle_save_project_hours(event)
        elif action == 'getProjectHoursData':
            return handle_get_project_hours_data(event)
        elif action == 'getUsersList':
            return handle_get_users_list(event)
        elif action == 'getDepartmentList':
            return handle_get_department_list()
        else:
            print(f"未知的操作类型: {action}")
            return {'code': 400, 'message': f'未知的操作类型: {action}'}
    except Exception as e:
        import traceback
        print(f"处理请求失败: {str(e)}")
        print(traceback.format_exc())
        return {'code': 500, 'message': f'处理请求失败: {str(e)}', 'error': str(e)} 