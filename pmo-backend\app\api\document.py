#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理API - 第六个功能模块：文档和知识库管理
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.models.user import User
from app.models.document import DocumentLib, Document
from app.models.knowledge import Knowledge
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/doc", tags=["文档管理"])

# 请求模型
class DocLibCreateRequest(BaseModel):
    type: str = "product"
    product: int = 0
    project: int = 0
    name: str
    acl: str = "open"
    order: int = 0

class DocumentCreateRequest(BaseModel):
    lib: int
    title: str
    keywords: str = ""
    type: str = "text"
    content: str = ""
    contentType: str = "html"
    url: str = ""
    product: int = 0
    project: int = 0
    module: int = 0
    order: int = 0

class DocumentUpdateRequest(BaseModel):
    title: Optional[str] = None
    keywords: Optional[str] = None
    type: Optional[str] = None
    content: Optional[str] = None
    contentType: Optional[str] = None
    url: Optional[str] = None
    status: Optional[str] = None
    order: Optional[int] = None

class KnowledgeCreateRequest(BaseModel):
    lib: int
    title: str
    category: str = ""
    tags: str = ""
    summary: str = ""
    content: str = ""
    author: str = ""
    priority: int = 3

class KnowledgeUpdateRequest(BaseModel):
    title: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    summary: Optional[str] = None
    content: Optional[str] = None
    author: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[int] = None

# 权限检查
def check_doc_permission(current_user: User = Depends(get_current_user)):
    """检查文档权限"""
    if current_user.role not in ["admin", "po", "pm", "dev"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员、产品经理、项目经理或开发者权限"
        )
    return current_user

# 文档库管理API
@router.get("/libs")
async def get_doc_libs(
    lib_type: str = "",
    product_id: int = 0,
    project_id: int = 0,
    current_user: User = Depends(get_current_user)
):
    """获取文档库列表"""
    try:
        libs = DocumentLib.get_all_libs(lib_type=lib_type, product_id=product_id, project_id=project_id)
        return success_response(libs, "获取文档库列表成功")
    except Exception as e:
        return error_response(f"获取文档库列表失败: {str(e)}")

@router.get("/libs/{lib_id}")
async def get_doc_lib(lib_id: int, current_user: User = Depends(get_current_user)):
    """获取文档库详情"""
    try:
        lib = DocumentLib.get_by_id(lib_id)
        if not lib:
            return error_response("文档库不存在", status_code=404)
        
        return success_response(lib.to_dict(), "获取文档库详情成功")
    except Exception as e:
        return error_response(f"获取文档库详情失败: {str(e)}")

# 文档管理API
@router.get("/documents")
async def get_documents(
    page: int = 1,
    page_size: int = 20,
    lib_id: int = 0,
    doc_type: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取文档列表"""
    try:
        result = Document.get_all_docs(
            page=page, page_size=page_size, 
            lib_id=lib_id, doc_type=doc_type, keyword=keyword
        )
        return success_response(result, "获取文档列表成功")
    except Exception as e:
        return error_response(f"获取文档列表失败: {str(e)}")

@router.get("/documents/{doc_id}")
async def get_document(doc_id: int, current_user: User = Depends(get_current_user)):
    """获取文档详情"""
    try:
        doc = Document.get_by_id(doc_id)
        if not doc:
            return error_response("文档不存在", status_code=404)
        
        # 增加浏览次数
        doc.increase_views()
        
        return success_response(doc.to_dict(), "获取文档详情成功")
    except Exception as e:
        return error_response(f"获取文档详情失败: {str(e)}")

@router.post("/documents")
async def create_document(
    request: DocumentCreateRequest,
    current_user: User = Depends(check_doc_permission)
):
    """创建文档"""
    try:
        # 检查文档库是否存在
        lib = DocumentLib.get_by_id(request.lib)
        if not lib:
            return error_response("文档库不存在", status_code=404)
        
        doc = Document()
        doc.lib = request.lib
        doc.title = request.title
        doc.keywords = request.keywords
        doc.type = request.type
        doc.content = request.content
        doc.contentType = request.contentType
        doc.url = request.url
        doc.product = request.product
        doc.project = request.project
        doc.module = request.module
        doc.order = request.order
        doc.addedBy = current_user.account
        doc.status = "normal"
        
        if doc.create():
            return success_response(doc.to_dict(), "创建文档成功")
        else:
            return error_response("创建文档失败")
            
    except Exception as e:
        return error_response(f"创建文档失败: {str(e)}")

@router.put("/documents/{doc_id}")
async def update_document(
    doc_id: int,
    request: DocumentUpdateRequest,
    current_user: User = Depends(check_doc_permission)
):
    """更新文档信息"""
    try:
        doc = Document.get_by_id(doc_id)
        if not doc:
            return error_response("文档不存在", status_code=404)
        
        # 更新文档信息
        if request.title is not None:
            doc.title = request.title
        if request.keywords is not None:
            doc.keywords = request.keywords
        if request.type is not None:
            doc.type = request.type
        if request.content is not None:
            doc.content = request.content
        if request.contentType is not None:
            doc.contentType = request.contentType
        if request.url is not None:
            doc.url = request.url
        if request.status is not None:
            doc.status = request.status
        if request.order is not None:
            doc.order = request.order
        
        doc.editedBy = current_user.account
        
        if doc.update():
            return success_response(doc.to_dict(), "更新文档信息成功")
        else:
            return error_response("更新文档信息失败")
            
    except Exception as e:
        return error_response(f"更新文档信息失败: {str(e)}")

@router.delete("/documents/{doc_id}")
async def delete_document(
    doc_id: int,
    current_user: User = Depends(check_doc_permission)
):
    """删除文档"""
    try:
        doc = Document.get_by_id(doc_id)
        if not doc:
            return error_response("文档不存在", status_code=404)
        
        if doc.delete():
            return success_response(None, "删除文档成功")
        else:
            return error_response("删除文档失败")
            
    except Exception as e:
        return error_response(f"删除文档失败: {str(e)}")

# 知识库管理API
@router.get("/knowledge")
async def get_knowledge_list(
    page: int = 1,
    page_size: int = 20,
    lib_id: int = 0,
    category: str = "",
    status: str = "",
    keyword: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取知识列表"""
    try:
        result = Knowledge.get_all_knowledge(
            page=page, page_size=page_size, 
            lib_id=lib_id, category=category, status=status, keyword=keyword
        )
        return success_response(result, "获取知识列表成功")
    except Exception as e:
        return error_response(f"获取知识列表失败: {str(e)}")

@router.get("/knowledge/{knowledge_id}")
async def get_knowledge(knowledge_id: int, current_user: User = Depends(get_current_user)):
    """获取知识详情"""
    try:
        knowledge = Knowledge.get_by_id(knowledge_id)
        if not knowledge:
            return error_response("知识不存在", status_code=404)
        
        # 增加浏览次数
        knowledge.increase_views()
        
        return success_response(knowledge.to_dict(), "获取知识详情成功")
    except Exception as e:
        return error_response(f"获取知识详情失败: {str(e)}")

@router.post("/knowledge")
async def create_knowledge(
    request: KnowledgeCreateRequest,
    current_user: User = Depends(check_doc_permission)
):
    """创建知识"""
    try:
        # 检查文档库是否存在
        lib = DocumentLib.get_by_id(request.lib)
        if not lib:
            return error_response("文档库不存在", status_code=404)
        
        knowledge = Knowledge()
        knowledge.lib = request.lib
        knowledge.title = request.title
        knowledge.category = request.category
        knowledge.tags = request.tags
        knowledge.summary = request.summary
        knowledge.content = request.content
        knowledge.author = request.author or current_user.realname
        knowledge.priority = request.priority
        knowledge.addedBy = current_user.account
        knowledge.status = "draft"
        
        if knowledge.create():
            return success_response(knowledge.to_dict(), "创建知识成功")
        else:
            return error_response("创建知识失败")
            
    except Exception as e:
        return error_response(f"创建知识失败: {str(e)}")

@router.put("/knowledge/{knowledge_id}")
async def update_knowledge(
    knowledge_id: int,
    request: KnowledgeUpdateRequest,
    current_user: User = Depends(check_doc_permission)
):
    """更新知识信息"""
    try:
        knowledge = Knowledge.get_by_id(knowledge_id)
        if not knowledge:
            return error_response("知识不存在", status_code=404)
        
        # 更新知识信息
        if request.title is not None:
            knowledge.title = request.title
        if request.category is not None:
            knowledge.category = request.category
        if request.tags is not None:
            knowledge.tags = request.tags
        if request.summary is not None:
            knowledge.summary = request.summary
        if request.content is not None:
            knowledge.content = request.content
        if request.author is not None:
            knowledge.author = request.author
        if request.status is not None:
            knowledge.status = request.status
        if request.priority is not None:
            knowledge.priority = request.priority
        
        knowledge.editedBy = current_user.account
        
        if knowledge.update():
            return success_response(knowledge.to_dict(), "更新知识信息成功")
        else:
            return error_response("更新知识信息失败")
            
    except Exception as e:
        return error_response(f"更新知识信息失败: {str(e)}")

@router.delete("/knowledge/{knowledge_id}")
async def delete_knowledge(
    knowledge_id: int,
    current_user: User = Depends(check_doc_permission)
):
    """删除知识"""
    try:
        knowledge = Knowledge.get_by_id(knowledge_id)
        if not knowledge:
            return error_response("知识不存在", status_code=404)
        
        if knowledge.delete():
            return success_response(None, "删除知识成功")
        else:
            return error_response("删除知识失败")
            
    except Exception as e:
        return error_response(f"删除知识失败: {str(e)}")

@router.post("/knowledge/{knowledge_id}/publish")
async def publish_knowledge(
    knowledge_id: int,
    current_user: User = Depends(check_doc_permission)
):
    """发布知识"""
    try:
        knowledge = Knowledge.get_by_id(knowledge_id)
        if not knowledge:
            return error_response("知识不存在", status_code=404)
        
        if knowledge.publish(current_user.account):
            return success_response(knowledge.to_dict(), "发布知识成功")
        else:
            return error_response("发布知识失败")
            
    except Exception as e:
        return error_response(f"发布知识失败: {str(e)}")

@router.post("/knowledge/{knowledge_id}/like")
async def like_knowledge(
    knowledge_id: int,
    current_user: User = Depends(get_current_user)
):
    """点赞知识"""
    try:
        knowledge = Knowledge.get_by_id(knowledge_id)
        if not knowledge:
            return error_response("知识不存在", status_code=404)
        
        if knowledge.like():
            return success_response(knowledge.to_dict(), "点赞成功")
        else:
            return error_response("点赞失败")
            
    except Exception as e:
        return error_response(f"点赞失败: {str(e)}")
