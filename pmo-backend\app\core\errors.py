from fastapi import HTTPException, status
from typing import Any, Dict, Optional

class PMOError(Exception):
    """PMO系统基础异常类"""
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.detail = detail
        super().__init__(self.message)

    def to_http_exception(self) -> HTTPException:
        """转换为FastAPI的HTTPException"""
        return HTTPException(
            status_code=self.status_code,
            detail={
                "message": self.message,
                "detail": self.detail
            }
        )

class DatabaseError(PMOError):
    """数据库操作异常"""
    def __init__(
        self,
        message: str = "数据库操作失败",
        detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR, detail)

class NotFoundError(PMOError):
    """资源未找到异常"""
    def __init__(
        self,
        message: str = "资源未找到",
        detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status.HTTP_404_NOT_FOUND, detail)

class ValidationError(PMOError):
    """数据验证异常"""
    def __init__(
        self,
        message: str = "数据验证失败",
        detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status.HTTP_422_UNPROCESSABLE_ENTITY, detail)

class AuthenticationError(PMOError):
    """认证异常"""
    def __init__(
        self,
        message: str = "认证失败",
        detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status.HTTP_401_UNAUTHORIZED, detail)

class AuthorizationError(PMOError):
    """授权异常"""
    def __init__(
        self,
        message: str = "没有操作权限",
        detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status.HTTP_403_FORBIDDEN, detail) 