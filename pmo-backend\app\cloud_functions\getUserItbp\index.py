#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取用户ITBP信息云函数
完全参考Python桌面版本的实现
"""

import json
import pymysql
import os
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', '3306')),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'kanban2'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_user_itbp_info(user_id):
    """
    获取用户在ITBP表中的条线和职责信息
    完全参考Python桌面版本的实现
    """
    try:
        print(f"开始获取用户(ID:{user_id})的ITBP信息")
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        
        with conn.cursor() as cursor:
            # 查询ITBP表中的用户信息
            sql = """
            SELECT investment_entity, staff_category, UserID
            FROM itbp 
            WHERE UserID = %s
            """
            cursor.execute(sql, (user_id,))
            itbp_records = cursor.fetchall()
            
            print(f"从ITBP表查询到 {len(itbp_records)} 条记录")
            
            # 如果没有ITBP记录，尝试根据用户角色推断权限
            if not itbp_records:
                print(f"用户 {user_id} 在ITBP表中没有记录，尝试根据角色推断权限")
                
                # 查询用户信息
                user_sql = "SELECT role, department_name FROM users WHERE UserID = %s"
                cursor.execute(user_sql, (user_id,))
                user_info = cursor.fetchone()
                
                if user_info:
                    user_role = user_info.get('role')
                    department = user_info.get('department_name')
                    
                    print(f"用户角色: {user_role}, 部门: {department}")
                    
                    # 超级管理员可以访问所有条线
                    if user_role == 1:
                        print("超级管理员，获取所有投资主体")
                        # 获取所有投资主体
                        entity_sql = """
                        SELECT DISTINCT investment_entity 
                        FROM Project_Account_Book 
                        WHERE investment_entity IS NOT NULL 
                        AND investment_entity != ''
                        """
                        cursor.execute(entity_sql)
                        entities = cursor.fetchall()
                        
                        itbp_records = []
                        for entity in entities:
                            itbp_records.append({
                                'investment_entity': entity['investment_entity'],
                                'staff_category': '0',  # 超级管理员
                                'UserID': user_id
                            })
                        
                        print(f"为超级管理员生成 {len(itbp_records)} 个条线权限")
                    
                    # 部门管理员根据部门映射条线
                    elif user_role == 2:
                        print("部门管理员，根据部门映射条线")
                        # 部门管理员可以看到所有投资主体的项目，但有编辑限制
                        # 获取所有投资主体
                        entity_sql = """
                        SELECT DISTINCT investment_entity
                        FROM Project_Account_Book
                        WHERE investment_entity IS NOT NULL
                        AND investment_entity != ''
                        """
                        cursor.execute(entity_sql)
                        entities = cursor.fetchall()

                        itbp_records = []
                        for entity in entities:
                            itbp_records.append({
                                'investment_entity': entity['investment_entity'],
                                'staff_category': '1',  # 部门管理员
                                'UserID': user_id
                            })

                        print(f"为部门管理员生成 {len(itbp_records)} 个条线权限")
        
        conn.close()
        
        return itbp_records
        
    except Exception as e:
        print(f"获取用户ITBP信息失败: {str(e)}")
        return []

def get_user_service_lines(user_id):
    """
    获取用户服务的条线列表
    完全参考Python桌面版本的实现
    """
    try:
        print(f"开始获取用户(ID:{user_id})的服务条线列表")
        
        # 获取用户的ITBP信息
        itbp_info = get_user_itbp_info(user_id)
        
        if not itbp_info:
            print("用户没有ITBP信息，返回空条线列表")
            return []
        
        # 提取条线信息
        service_lines = []
        for item in itbp_info:
            entity = item.get('investment_entity')
            if entity and entity not in service_lines:
                service_lines.append(entity)
        
        print(f"用户 {user_id} 的服务条线: {service_lines}")
        return service_lines
        
    except Exception as e:
        print(f"获取用户服务条线失败: {str(e)}")
        return []

def main_handler(event, context):
    """
    云函数主处理函数
    """
    try:
        print(f"接收到请求: {event}")
        
        # 解析请求参数
        query_string = event.get('queryString', {})
        action = query_string.get('action', 'getUserItbp')
        user_id = query_string.get('UserID', '')
        
        if not user_id:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'code': 400,
                    'message': '缺少UserID参数',
                    'data': None
                }, ensure_ascii=False)
            }
        
        if action == 'getUserItbp':
            # 获取用户ITBP信息
            itbp_info = get_user_itbp_info(user_id)
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'code': 200,
                    'message': '获取用户ITBP信息成功',
                    'data': itbp_info
                }, ensure_ascii=False)
            }
        
        elif action == 'getUserServiceLines':
            # 获取用户服务条线
            service_lines = get_user_service_lines(user_id)
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'code': 200,
                    'message': '获取用户服务条线成功',
                    'data': service_lines
                }, ensure_ascii=False)
            }
        
        else:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'code': 400,
                    'message': f'不支持的操作: {action}',
                    'data': None
                }, ensure_ascii=False)
            }
    
    except Exception as e:
        print(f"处理请求失败: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'code': 500,
                'message': f'服务器内部错误: {str(e)}',
                'data': None
            }, ensure_ascii=False)
        }

# 兼容不同的云函数调用方式
def handler(event, context):
    return main_handler(event, context)

if __name__ == "__main__":
    # 本地测试
    test_event = {
        'queryString': {
            'action': 'getUserServiceLines',
            'UserID': 'na10000003'
        }
    }
    result = main_handler(test_event, None)
    print(json.dumps(result, indent=2, ensure_ascii=False))
