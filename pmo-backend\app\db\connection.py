import pymysql
import sqlite3
from pymysql.cursors import DictCursor
from contextlib import contextmanager
from app.core.config import settings
from app.core.logger import get_logger

logger = get_logger(__name__)

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': settings.DB_HOST,
    'port': settings.DB_PORT,
    'user': settings.DB_USER,
    'password': settings.DB_PASSWORD,
    'database': settings.DB_NAME,
    'charset': 'utf8mb4',
    'cursorclass': DictCursor
}

def get_db_connection():
    """获取数据库连接 - 与云函数保持一致的接口"""
    try:
        if settings.DB_TYPE.lower() == 'mysql':
            return pymysql.connect(**MYSQL_CONFIG)
        elif settings.DB_TYPE.lower() == 'sqlite':
            import os
            # 确保使用绝对路径
            if os.path.isabs(settings.SQLITE_FILE):
                db_path = settings.SQLITE_FILE
            else:
                # 相对于项目根目录
                db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), settings.SQLITE_FILE)

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row  # 使用类字典行工厂
            return conn
        else:
            raise ValueError(f"不支持的数据库类型: {settings.DB_TYPE}")
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

@contextmanager
def db_transaction():
    """数据库事务上下文管理器"""
    conn = None
    try:
        conn = get_db_connection()
        yield conn
        if settings.DB_TYPE.lower() == 'mysql':
            conn.commit()
        elif settings.DB_TYPE.lower() == 'sqlite':
            conn.commit()
    except Exception as e:
        if conn:
            if settings.DB_TYPE.lower() == 'mysql':
                conn.rollback()
            elif settings.DB_TYPE.lower() == 'sqlite':
                conn.rollback()
        logger.error(f"数据库事务错误: {str(e)}")
        raise e
    finally:
        if conn:
            conn.close() 