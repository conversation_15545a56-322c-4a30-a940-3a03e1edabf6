#!/usr/bin/env python3
"""
性能优化相关API端点
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from app.core.auth import get_current_user
from app.services.document_parser import DocumentParser
from app.services.document_cache import get_document_cache
from app.services.office_com_pool import get_office_com_pool

logger = logging.getLogger(__name__)

router = APIRouter()

# 全局文档解析器实例
document_parser = DocumentParser()

@router.get("/cache/stats")
async def get_cache_stats(current_user: dict = Depends(get_current_user)):
    """获取文档缓存统计信息"""
    try:
        cache = get_document_cache()
        stats = cache.get_cache_stats()
        
        return {
            "code": 200,
            "message": "缓存统计获取成功",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取缓存统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")

@router.post("/cache/clear")
async def clear_cache(current_user: dict = Depends(get_current_user)):
    """清空文档缓存"""
    try:
        cache = get_document_cache()
        cache.clear_cache()
        
        return {
            "code": 200,
            "message": "缓存已清空",
            "data": {}
        }
        
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")

@router.post("/cache/cleanup")
async def cleanup_old_cache(
    max_age_days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """清理过期缓存"""
    try:
        cache = get_document_cache()
        cache.remove_old_cache(max_age_days)
        
        return {
            "code": 200,
            "message": f"已清理超过{max_age_days}天的缓存",
            "data": {}
        }
        
    except Exception as e:
        logger.error(f"清理缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")

@router.get("/com-pool/status")
async def get_com_pool_status(current_user: dict = Depends(get_current_user)):
    """获取COM对象池状态"""
    try:
        com_pool = get_office_com_pool()
        
        status = {
            "initialized": com_pool.initialized,
            "pool_size": com_pool.pool_size,
            "word_pool_size": com_pool.word_pool.qsize(),
            "excel_pool_size": com_pool.excel_pool.qsize(),
            "ppt_pool_size": com_pool.ppt_pool.qsize(),
        }
        
        return {
            "code": 200,
            "message": "COM对象池状态获取成功",
            "data": status
        }
        
    except Exception as e:
        logger.error(f"获取COM对象池状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取COM对象池状态失败: {str(e)}")

@router.post("/com-pool/initialize")
async def initialize_com_pool(current_user: dict = Depends(get_current_user)):
    """初始化COM对象池"""
    try:
        com_pool = get_office_com_pool()
        
        if not com_pool.initialized:
            com_pool.initialize()
            
        return {
            "code": 200,
            "message": "COM对象池初始化完成",
            "data": {
                "initialized": com_pool.initialized,
                "pool_size": com_pool.pool_size
            }
        }
        
    except Exception as e:
        logger.error(f"初始化COM对象池失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"初始化COM对象池失败: {str(e)}")

@router.post("/com-pool/cleanup")
async def cleanup_com_pool(current_user: dict = Depends(get_current_user)):
    """清理COM对象池"""
    try:
        com_pool = get_office_com_pool()
        com_pool.cleanup()
        
        return {
            "code": 200,
            "message": "COM对象池已清理",
            "data": {}
        }
        
    except Exception as e:
        logger.error(f"清理COM对象池失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理COM对象池失败: {str(e)}")

@router.get("/performance/summary")
async def get_performance_summary(current_user: dict = Depends(get_current_user)):
    """获取性能优化总结"""
    try:
        # 获取缓存统计
        cache = get_document_cache()
        cache_stats = cache.get_cache_stats()
        
        # 获取COM池状态
        com_pool = get_office_com_pool()
        com_status = {
            "initialized": com_pool.initialized,
            "pool_size": com_pool.pool_size,
            "word_available": com_pool.word_pool.qsize(),
            "excel_available": com_pool.excel_pool.qsize(),
            "ppt_available": com_pool.ppt_pool.qsize(),
        }
        
        # 计算性能提升
        performance_improvements = []
        
        if cache_stats.get("total_cached_files", 0) > 0:
            avg_parse_time = cache_stats.get("average_parse_time", 0)
            cache_hit_benefit = f"缓存命中可节省平均 {avg_parse_time:.2f}s 解析时间"
            performance_improvements.append(cache_hit_benefit)
        
        if com_status["initialized"]:
            performance_improvements.append("COM对象池减少Office应用启动时间")
        
        return {
            "code": 200,
            "message": "性能优化总结获取成功",
            "data": {
                "cache_stats": cache_stats,
                "com_pool_status": com_status,
                "performance_improvements": performance_improvements,
                "optimization_tips": [
                    "定期清理过期缓存以节省存储空间",
                    "保持COM对象池活跃以提升解析速度",
                    "相同文件的重复上传会直接使用缓存结果",
                    "COM对象池在系统重启后需要重新初始化"
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"获取性能优化总结失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能优化总结失败: {str(e)}")

@router.post("/warmup")
async def warmup_system(current_user: dict = Depends(get_current_user)):
    """系统预热 - 初始化所有性能优化组件"""
    try:
        results = []
        
        # 初始化COM对象池
        try:
            com_pool = get_office_com_pool()
            if not com_pool.initialized:
                com_pool.initialize()
            results.append("✅ COM对象池初始化完成")
        except Exception as e:
            results.append(f"❌ COM对象池初始化失败: {str(e)}")
        
        # 检查缓存系统
        try:
            cache = get_document_cache()
            stats = cache.get_cache_stats()
            results.append(f"✅ 缓存系统正常 (已缓存 {stats.get('total_cached_files', 0)} 个文件)")
        except Exception as e:
            results.append(f"❌ 缓存系统异常: {str(e)}")
        
        # 检查文档解析器
        try:
            if hasattr(document_parser, 'cache') and document_parser.cache:
                results.append("✅ 文档解析器缓存集成正常")
            else:
                results.append("⚠️ 文档解析器缓存未集成")
                
            if hasattr(document_parser, 'com_pool') and document_parser.com_pool:
                results.append("✅ 文档解析器COM池集成正常")
            else:
                results.append("⚠️ 文档解析器COM池未集成")
        except Exception as e:
            results.append(f"❌ 文档解析器检查失败: {str(e)}")
        
        return {
            "code": 200,
            "message": "系统预热完成",
            "data": {
                "results": results,
                "timestamp": "2025-07-29T11:00:00Z"
            }
        }
        
    except Exception as e:
        logger.error(f"系统预热失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统预热失败: {str(e)}")
