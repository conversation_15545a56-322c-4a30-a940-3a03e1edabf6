import{_ as I,u as L,a as P,b as R,r as h,c as _,o as C,d as l,e as q,f as B,g as o,h as a,w as t,i as E,j as g,t as F,E as m}from"./index-76121fa4.js";import{_ as M}from"./logo-bcea583b.js";const N={class:"login-container"},z={class:"login-card"},K={class:"login-form"},j={class:"login-options"},D={__name:"Login",setup(T){const w=L(),y=P(),f=R(),s=h({username:"",password:""}),V={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},d=_(!1),u=_(!1),c=_(null);C(()=>{const r=localStorage.getItem("savedUsername"),e=localStorage.getItem("savedPassword");r&&e&&(s.username=r,s.password=e,u.value=!0)});const v=async()=>{c.value&&await c.value.validate(async r=>{if(r){d.value=!0;try{if(await f.loginUser(s.username,s.password)){u.value?(localStorage.setItem("savedUsername",s.username),localStorage.setItem("savedPassword",s.password)):(localStorage.removeItem("savedUsername"),localStorage.removeItem("savedPassword")),m({message:"登录成功",type:"success"});const i=y.query.redirect;w.push(i||"/")}else m({message:f.error||"登录失败，请检查用户名和密码",type:"error"})}catch(e){m({message:e.message||"登录时发生错误",type:"error"})}finally{d.value=!1}}})},b=()=>{m({message:"请联系系统管理员重置密码",type:"info"})};return(r,e)=>{const i=l("el-input"),p=l("el-form-item"),k=l("el-checkbox"),S=l("el-link"),U=l("el-button"),x=l("el-form");return q(),B("div",N,[o("div",z,[e[5]||(e[5]=o("div",{class:"login-header"},[o("img",{src:M,alt:"Logo",class:"logo"}),o("h1",{class:"title"},"条线项目管理系统"),o("p",{class:"subtitle"},"专业的项目管理平台")],-1)),o("div",K,[a(x,{ref_key:"loginFormRef",ref:c,model:s,rules:V,onKeyup:E(v,["enter"])},{default:t(()=>[a(p,{prop:"username"},{default:t(()=>[a(i,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=n=>s.username=n),placeholder:"请输入用户名",clearable:"",size:"large"},null,8,["modelValue"])]),_:1}),a(p,{prop:"password"},{default:t(()=>[a(i,{modelValue:s.password,"onUpdate:modelValue":e[1]||(e[1]=n=>s.password=n),type:"password",placeholder:"请输入密码","show-password":"",size:"large"},null,8,["modelValue"])]),_:1}),o("div",j,[a(k,{modelValue:u.value,"onUpdate:modelValue":e[2]||(e[2]=n=>u.value=n)},{default:t(()=>e[3]||(e[3]=[g("记住密码")])),_:1,__:[3]},8,["modelValue"]),a(S,{type:"primary",onClick:b},{default:t(()=>e[4]||(e[4]=[g("忘记密码?")])),_:1,__:[4]})]),a(p,null,{default:t(()=>[a(U,{type:"primary",loading:d.value,class:"login-button",onClick:v},{default:t(()=>[g(F(d.value?"登录中...":"登 录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),e[6]||(e[6]=o("div",{class:"login-footer"},[o("p",{class:"version"},"版本 1.0.0")],-1))])])}}},H=I(D,[["__scopeId","data-v-d1330ac5"]]);export{H as default};
