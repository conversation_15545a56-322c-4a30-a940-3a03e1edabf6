#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
动态知识库服务
实时监听数据库变化和文档更新，自动更新知识库
"""

import json
import time
import hashlib
import threading
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
from pathlib import Path
import os
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from app.core.logger import get_logger
from app.services.knowledge_service import PMOKnowledgeService

# 尝试导入向量知识库服务
try:
    from app.services.vector_knowledge_service import vector_knowledge_service
    VECTOR_AVAILABLE = True
except ImportError:
    VECTOR_AVAILABLE = False

logger = get_logger(__name__)

class DatabaseChangeDetector:
    """数据库变化检测器"""
    
    def __init__(self):
        self.table_checksums = {}
        self.last_check_time = {}
        self.check_interval = 60  # 增加到60秒检查一次，减少启动开销
        
    def get_table_checksum(self, table_name: str) -> str:
        """获取表的校验和"""
        try:
            from app.db.utils import execute_query
            
            # 获取表的记录数和最后更新时间
            if table_name in ['project_account_book', 'users', 'project_hours', 'change_log']:
                # 对于重要表，计算更详细的校验和
                rows = execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                count = rows[0]['count'] if rows else 0
                
                # 尝试获取最后更新时间
                try:
                    if table_name == 'change_log':
                        latest = execute_query(f"SELECT MAX(id) as latest FROM {table_name}")
                    else:
                        latest = execute_query(f"SELECT COUNT(*) as latest FROM {table_name}")
                    latest_value = latest[0]['latest'] if latest else 0
                except:
                    latest_value = count
                
                # 生成校验和
                checksum_data = f"{table_name}:{count}:{latest_value}:{datetime.now().strftime('%Y-%m-%d %H')}"
                return hashlib.md5(checksum_data.encode()).hexdigest()
            else:
                # 对于其他表，简单计算记录数
                rows = execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
                count = rows[0]['count'] if rows else 0
                checksum_data = f"{table_name}:{count}"
                return hashlib.md5(checksum_data.encode()).hexdigest()
                
        except Exception as e:
            logger.error(f"获取表 {table_name} 校验和失败: {str(e)}")
            return ""
    
    def check_table_changes(self, tables: List[str]) -> Set[str]:
        """检查哪些表发生了变化"""
        changed_tables = set()
        
        for table in tables:
            try:
                current_checksum = self.get_table_checksum(table)
                previous_checksum = self.table_checksums.get(table, "")
                
                if current_checksum != previous_checksum:
                    changed_tables.add(table)
                    self.table_checksums[table] = current_checksum
                    logger.info(f"检测到表 {table} 发生变化")
                
            except Exception as e:
                logger.error(f"检查表 {table} 变化失败: {str(e)}")
        
        return changed_tables

class FileChangeHandler(FileSystemEventHandler):
    """文件变化处理器"""
    
    def __init__(self, knowledge_service):
        self.knowledge_service = knowledge_service
        self.last_update = {}
        self.update_delay = 5  # 5秒延迟，避免频繁更新
        
    def on_modified(self, event):
        """文件修改事件"""
        if event.is_directory:
            return
            
        file_path = event.src_path
        current_time = time.time()
        
        # 只处理特定类型的文件
        if not any(file_path.endswith(ext) for ext in ['.pdf', '.docx', '.doc', '.md', '.txt']):
            return
            
        # 避免频繁更新
        if file_path in self.last_update:
            if current_time - self.last_update[file_path] < self.update_delay:
                return
        
        self.last_update[file_path] = current_time
        logger.info(f"检测到文件变化: {file_path}")
        
        # 异步更新知识库
        threading.Thread(
            target=self._update_knowledge_for_file,
            args=(file_path,),
            daemon=True
        ).start()
    
    def on_created(self, event):
        """文件创建事件"""
        if not event.is_directory:
            logger.info(f"检测到新文件: {event.src_path}")
            self.on_modified(event)
    
    def _update_knowledge_for_file(self, file_path: str):
        """为特定文件更新知识库"""
        try:
            # 触发知识库更新
            self.knowledge_service.invalidate_cache(['project_details', 'formatted_knowledge'])
            logger.info(f"已触发知识库更新，因文件变化: {file_path}")
        except Exception as e:
            logger.error(f"文件变化触发知识库更新失败: {str(e)}")

class DynamicKnowledgeService:
    """动态知识库服务"""
    
    def __init__(self):
        self.base_knowledge_service = PMOKnowledgeService()
        self.db_detector = DatabaseChangeDetector()
        self.file_observer = None
        self.monitoring_thread = None
        self.is_monitoring = False
        
        # 监控的数据库表（减少监控表数量）
        self.monitored_tables = [
            'project_account_book',  # 核心项目数据
            'users',                 # 用户数据
            'itbp'                   # 团队数据
        ]
        
        # 监控的文件目录
        self.monitored_directories = [
            'project_archive_materials',
            'temp_files',
            'knowledge_export'
        ]
        
    def start_monitoring(self):
        """开始监控数据库和文件变化 - 已禁用以提升启动速度"""
        logger.info("动态知识库监控已禁用（提升启动速度）")
        return
    
    def stop_monitoring(self):
        """停止监控 - 已禁用"""
        logger.info("动态知识库监控已禁用")
        return
    
    def _start_file_monitoring(self):
        """启动文件监控"""
        try:
            self.file_observer = Observer()
            handler = FileChangeHandler(self)
            
            for directory in self.monitored_directories:
                if os.path.exists(directory):
                    self.file_observer.schedule(handler, directory, recursive=True)
                    logger.info(f"📁 开始监控目录: {directory}")
            
            self.file_observer.start()
            
        except Exception as e:
            logger.error(f"启动文件监控失败: {str(e)}")
    
    def _database_monitoring_loop(self):
        """数据库监控循环"""
        logger.info("📊 数据库变化监控循环已启动")
        
        while self.is_monitoring:
            try:
                # 检查数据库表变化
                changed_tables = self.db_detector.check_table_changes(self.monitored_tables)
                
                if changed_tables:
                    logger.info(f"检测到数据库变化，涉及表: {', '.join(changed_tables)}")
                    self._handle_database_changes(changed_tables)
                
                # 等待下次检查
                time.sleep(self.db_detector.check_interval)
                
            except Exception as e:
                logger.error(f"数据库监控循环出错: {str(e)}")
                time.sleep(60)  # 出错时等待1分钟再重试
    
    def _handle_database_changes(self, changed_tables: Set[str]):
        """处理数据库变化"""
        try:
            # 根据变化的表确定需要更新的缓存部分
            cache_keys_to_invalidate = set()

            if any(table in changed_tables for table in ['project_account_book', 'project_hours']):
                cache_keys_to_invalidate.update(['red_black_board', 'entity_stats', 'project_summary'])

            if any(table in changed_tables for table in ['users', 'itbp']):
                cache_keys_to_invalidate.add('user_data')

            if any(table in changed_tables for table in ['project_hours', 'project_plan', 'weekly_reports']):
                cache_keys_to_invalidate.add('project_details')

            if any(table in changed_tables for table in ['supervision_items', 'progress_details', 'companies']):
                cache_keys_to_invalidate.add('supervision_data')

            if 'change_log' in changed_tables:
                cache_keys_to_invalidate.add('change_history')

            # 总是更新格式化的知识库
            cache_keys_to_invalidate.add('formatted_knowledge')

            # 使缓存失效
            self.invalidate_cache(list(cache_keys_to_invalidate))

            logger.info(f"已使缓存失效: {', '.join(cache_keys_to_invalidate)}")

            # 如果向量知识库可用，触发重新索引
            if VECTOR_AVAILABLE:
                self._trigger_vector_reindex(changed_tables)

        except Exception as e:
            logger.error(f"处理数据库变化失败: {str(e)}")

    def _trigger_vector_reindex(self, changed_tables: Set[str]):
        """触发向量知识库重新索引"""
        try:
            from app.services.vector_knowledge_service import vector_knowledge_service

            # 重要表变化时触发完整重建，其他表变化时增量更新
            important_tables = {'project_account_book', 'users', 'project_hours'}

            if any(table in changed_tables for table in important_tables):
                logger.info("🔄 触发向量知识库完整重建...")
                # 在后台线程中执行，避免阻塞
                threading.Thread(
                    target=vector_knowledge_service.index_knowledge_base,
                    args=(True,),  # force_rebuild=True
                    daemon=True
                ).start()
            else:
                logger.info("🔄 触发向量知识库增量更新...")
                threading.Thread(
                    target=vector_knowledge_service.index_knowledge_base,
                    args=(False,),  # force_rebuild=False
                    daemon=True
                ).start()

        except Exception as e:
            logger.error(f"触发向量知识库重新索引失败: {str(e)}")
    
    def invalidate_cache(self, cache_keys: List[str]):
        """使指定的缓存失效"""
        for key in cache_keys:
            if key in self.base_knowledge_service.knowledge_cache:
                del self.base_knowledge_service.knowledge_cache[key]
                logger.debug(f"已清除缓存: {key}")
    
    def get_knowledge_base(self, force_refresh: bool = False) -> str:
        """获取动态知识库"""
        return self.base_knowledge_service.get_knowledge_base(force_refresh)
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "monitored_tables": self.monitored_tables,
            "monitored_directories": self.monitored_directories,
            "table_checksums": self.db_detector.table_checksums,
            "cache_status": {
                "cached_items": list(self.base_knowledge_service.knowledge_cache.keys()),
                "last_updates": self.base_knowledge_service.last_update
            }
        }

# 全局动态知识库服务实例
dynamic_knowledge_service = DynamicKnowledgeService()
