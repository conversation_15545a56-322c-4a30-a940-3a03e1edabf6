from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Any
from datetime import timedelta

from app.core.security import create_access_token, get_current_user
from app.core.config import settings
from app.utils.response_utils import standard_response
from app.db.utils import execute_query
from app.core.logger import get_logger
from app.cloud_functions.getuser.index import main_handler as getuser_handler
from app.cloud_functions.getUserItbp.index import main_handler as get_user_itbp_handler

logger = get_logger(__name__)
router = APIRouter()

def get_user_service_lines(user_id: str) -> list:
    """
    获取用户的服务条线列表
    完全参考Python桌面版本的实现
    """
    try:
        logger.info(f"获取用户 {user_id} 的服务条线")

        # 调用getUserItbp云函数
        event = {
            'queryString': {
                'action': 'getUserServiceLines',
                'UserID': user_id
            }
        }

        response = get_user_itbp_handler(event, None)

        if response.get('statusCode') == 200:
            import json
            body = json.loads(response.get('body', '{}'))
            if body.get('code') == 200:
                service_lines = body.get('data', [])
                logger.info(f"用户 {user_id} 的服务条线: {service_lines}")
                return service_lines

        logger.warning(f"获取用户 {user_id} 服务条线失败: {response}")
        return []

    except Exception as e:
        logger.error(f"获取用户服务条线异常: {str(e)}")
        return []

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()) -> Dict[str, Any]:
    """
    用户登录
    """
    try:
        # 调用getuser云函数进行验证
        event = {
            "username": form_data.username,
            "password": form_data.password
        }
        
        result = getuser_handler(event, None)
        
        if result.get('code') != 200:
            logger.warning(f"登录失败: {result.get('message')}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result.get('message', "用户名或密码错误"),
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_data = result.get('data', {})
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user_data.get("username")},
            expires_delta=access_token_expires
        )
        
        logger.info(f"用户 {form_data.username} 登录成功")
        
        return standard_response({
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "username": user_data.get("username"),
                "display_name": user_data.get("name", user_data.get("username")),
                "role": user_data.get("role", "user"),
                "UserID": user_data.get("UserID"),
                "service_lines": user_data.get("service_lines", [])
            }
        })
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录处理错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误",
        )

@router.get("/me")
async def get_me(current_user = Depends(get_current_user)) -> Dict[str, Any]:
    """
    获取当前用户信息
    """
    try:
        import os

        # 测试模式：直接返回测试用户信息
        if os.getenv("PMO_TEST_MODE") == "true":
            logger.info("测试模式：返回测试用户信息")
            return standard_response({
                "username": current_user.get("username"),
                "name": current_user.get("name"),
                "UserID": current_user.get("UserID"),
                "role": current_user.get("role"),
                "department_name": current_user.get("department_name"),
                "company_name": current_user.get("company_name"),
                "LaborCost": current_user.get("LaborCost"),
                "display_name": current_user.get("name", current_user.get("username")),
                "service_lines": current_user.get("service_lines", [])
            })

        # 从token中只能获取username，需要查询数据库获取完整用户信息
        username = current_user.get("username")
        if not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的用户信息"
            )

        # 先尝试通过username查询用户信息（不需要密码验证）
        # 查询数据库获取用户信息
        query = """
            SELECT UserID, username, name, role, department_name, company_name, LaborCost
            FROM users
            WHERE username = %s AND (is_disabled IS NULL OR is_disabled = 'N')
        """

        try:
            user_result = execute_query(query, (username,))
            if not user_result:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户信息不存在"
                )

            user_data = user_result[0]
            logger.info(f"获取到用户信息: {user_data}")

        except Exception as e:
            logger.error(f"查询用户信息失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="查询用户信息失败"
            )

        # 返回完整的用户信息
        return standard_response({
            "username": user_data.get("username"),
            "name": user_data.get("name"),
            "UserID": user_data.get("UserID"),
            "role": user_data.get("role"),
            "department_name": user_data.get("department_name"),
            "company_name": user_data.get("company_name"),
            "LaborCost": user_data.get("LaborCost"),
            "display_name": user_data.get("name", user_data.get("username")),
            "service_lines": get_user_service_lines(user_data.get("UserID", ""))
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务器内部错误"
        )