#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计报表API - 第七个功能模块：统计报表和仪表板
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import date, datetime, timedelta
from app.models.user import User
from app.models.report import Report, Dashboard, StatService
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/report", tags=["统计报表"])

# 请求模型
class ReportCreateRequest(BaseModel):
    name: str
    code: str
    desc: str = ""
    domain: str = ""
    public: str = "0"

class DashboardCreateRequest(BaseModel):
    name: str
    desc: str = ""
    layout: str = ""
    public: str = "0"

class EffortQueryRequest(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None

# 权限检查
def check_report_permission(current_user: User = Depends(get_current_user)):
    """检查报表权限"""
    if current_user.role not in ["admin", "pm", "po"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员、项目经理或产品经理权限"
        )
    return current_user

# 报表管理API
@router.get("/reports")
async def get_reports(
    domain: str = "",
    current_user: User = Depends(get_current_user)
):
    """获取报表列表"""
    try:
        reports = Report.get_all_reports(domain=domain)
        return success_response(reports, "获取报表列表成功")
    except Exception as e:
        return error_response(f"获取报表列表失败: {str(e)}")

@router.get("/reports/{report_id}")
async def get_report(report_id: int, current_user: User = Depends(get_current_user)):
    """获取报表详情"""
    try:
        report = Report.get_by_id(report_id)
        if not report:
            return error_response("报表不存在", status_code=404)
        
        return success_response(report.to_dict(), "获取报表详情成功")
    except Exception as e:
        return error_response(f"获取报表详情失败: {str(e)}")

# 仪表板管理API
@router.get("/dashboards")
async def get_dashboards(current_user: User = Depends(get_current_user)):
    """获取仪表板列表"""
    try:
        dashboards = Dashboard.get_all_dashboards()
        return success_response(dashboards, "获取仪表板列表成功")
    except Exception as e:
        return error_response(f"获取仪表板列表失败: {str(e)}")

@router.get("/dashboards/{dashboard_id}")
async def get_dashboard(dashboard_id: int, current_user: User = Depends(get_current_user)):
    """获取仪表板详情"""
    try:
        dashboard = Dashboard.get_by_id(dashboard_id)
        if not dashboard:
            return error_response("仪表板不存在", status_code=404)
        
        return success_response(dashboard.to_dict(include_blocks=True), "获取仪表板详情成功")
    except Exception as e:
        return error_response(f"获取仪表板详情失败: {str(e)}")

# 统计数据API
@router.get("/stats/project-overview")
async def get_project_overview(current_user: User = Depends(get_current_user)):
    """获取项目概览统计"""
    try:
        stats = StatService.get_project_overview()
        return success_response(stats, "获取项目概览成功")
    except Exception as e:
        return error_response(f"获取项目概览失败: {str(e)}")

@router.get("/stats/story-statistics")
async def get_story_statistics(current_user: User = Depends(get_current_user)):
    """获取需求统计"""
    try:
        stats = StatService.get_story_statistics()
        return success_response(stats, "获取需求统计成功")
    except Exception as e:
        return error_response(f"获取需求统计失败: {str(e)}")

@router.get("/stats/bug-trend")
async def get_bug_trend(
    days: int = Query(30, description="统计天数", ge=1, le=365),
    current_user: User = Depends(get_current_user)
):
    """获取缺陷趋势"""
    try:
        stats = StatService.get_bug_trend(days=days)
        return success_response(stats, "获取缺陷趋势成功")
    except Exception as e:
        return error_response(f"获取缺陷趋势失败: {str(e)}")

@router.get("/stats/effort-summary")
async def get_effort_summary(
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user: User = Depends(get_current_user)
):
    """获取工时统计"""
    try:
        stats = StatService.get_effort_summary(start_date=start_date, end_date=end_date)
        return success_response(stats, "获取工时统计成功")
    except Exception as e:
        return error_response(f"获取工时统计失败: {str(e)}")

@router.get("/stats/test-execution")
async def get_test_execution(current_user: User = Depends(get_current_user)):
    """获取测试执行统计"""
    try:
        stats = StatService.get_test_execution()
        return success_response(stats, "获取测试执行统计成功")
    except Exception as e:
        return error_response(f"获取测试执行统计失败: {str(e)}")

# 综合统计API
@router.get("/stats/overview")
async def get_overview_stats(current_user: User = Depends(get_current_user)):
    """获取系统概览统计"""
    try:
        # 获取各模块的基础统计
        project_stats = StatService.get_project_overview()
        story_stats = StatService.get_story_statistics()
        bug_trend = StatService.get_bug_trend(days=7)  # 最近7天
        test_stats = StatService.get_test_execution()
        
        # 组合成概览数据
        overview = {
            "projects": {
                "total": project_stats.get("total", 0),
                "doing": project_stats.get("doing", 0),
                "completion_rate": project_stats.get("completion_rate", 0)
            },
            "stories": {
                "total": story_stats.get("total", 0),
                "status_distribution": story_stats.get("status_distribution", [])
            },
            "bugs": {
                "recent_opened": len(bug_trend.get("opened_trend", [])),
                "recent_resolved": len(bug_trend.get("resolved_trend", [])),
                "severity_distribution": bug_trend.get("severity_distribution", [])
            },
            "tests": {
                "total_cases": test_stats.get("total_cases", 0),
                "execution_results": test_stats.get("execution_results", [])
            }
        }
        
        return success_response(overview, "获取系统概览成功")
    except Exception as e:
        return error_response(f"获取系统概览失败: {str(e)}")

# 个人统计API
@router.get("/stats/my-work")
async def get_my_work_stats(current_user: User = Depends(get_current_user)):
    """获取个人工作统计"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 我的任务统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_task 
                WHERE assignedTo = %s AND deleted = '0'
                GROUP BY status
            """, (current_user.account,))
            my_tasks = cursor.fetchall()
            
            # 我的缺陷统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_bug 
                WHERE assignedTo = %s AND deleted = '0'
                GROUP BY status
            """, (current_user.account,))
            my_bugs = cursor.fetchall()
            
            # 我的需求统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_story 
                WHERE assignedTo = %s AND deleted = '0'
                GROUP BY status
            """, (current_user.account,))
            my_stories = cursor.fetchall()
            
            # 我的工时统计（最近30天）
            cursor.execute("""
                SELECT SUM(consumed) as total_hours
                FROM zt_effort 
                WHERE account = %s 
                AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            """, (current_user.account,))
            effort_result = cursor.fetchone()
            my_effort = effort_result['total_hours'] if effort_result['total_hours'] else 0
            
            stats = {
                "tasks": my_tasks,
                "bugs": my_bugs,
                "stories": my_stories,
                "effort_30days": float(my_effort)
            }
            
            return success_response(stats, "获取个人工作统计成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"获取个人工作统计失败: {str(e)}")

# 项目详细统计API
@router.get("/stats/project/{project_id}")
async def get_project_stats(
    project_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取项目详细统计"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 项目基本信息
            cursor.execute("""
                SELECT * FROM zt_project WHERE id = %s AND deleted = '0'
            """, (project_id,))
            project_info = cursor.fetchone()
            
            if not project_info:
                return error_response("项目不存在", status_code=404)
            
            # 项目任务统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_task 
                WHERE project = %s AND deleted = '0'
                GROUP BY status
            """, (project_id,))
            task_stats = cursor.fetchall()
            
            # 项目团队成员
            cursor.execute("""
                SELECT t.account, u.realname, t.role, t.days, t.hours
                FROM zt_team t
                LEFT JOIN zt_user u ON t.account = u.account
                WHERE t.root = %s AND t.type = 'project'
            """, (project_id,))
            team_members = cursor.fetchall()
            
            # 项目工时统计
            cursor.execute("""
                SELECT SUM(consumed) as total_hours, SUM(`left`) as left_hours
                FROM zt_task 
                WHERE project = %s AND deleted = '0'
            """, (project_id,))
            effort_result = cursor.fetchone()
            
            stats = {
                "project_info": project_info,
                "task_distribution": task_stats,
                "team_members": team_members,
                "effort_summary": {
                    "consumed_hours": float(effort_result['total_hours']) if effort_result['total_hours'] else 0,
                    "left_hours": float(effort_result['left_hours']) if effort_result['left_hours'] else 0
                }
            }
            
            return success_response(stats, "获取项目统计成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"获取项目统计失败: {str(e)}")

# 产品详细统计API
@router.get("/stats/product/{product_id}")
async def get_product_stats(
    product_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取产品详细统计"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 产品基本信息
            cursor.execute("""
                SELECT * FROM zt_product WHERE id = %s AND deleted = '0'
            """, (product_id,))
            product_info = cursor.fetchone()
            
            if not product_info:
                return error_response("产品不存在", status_code=404)
            
            # 产品需求统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_story 
                WHERE product = %s AND deleted = '0'
                GROUP BY status
            """, (product_id,))
            story_stats = cursor.fetchall()
            
            # 产品缺陷统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_bug 
                WHERE product = %s AND deleted = '0'
                GROUP BY status
            """, (product_id,))
            bug_stats = cursor.fetchall()
            
            # 产品测试用例统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_case 
                WHERE product = %s AND deleted = '0'
                GROUP BY status
            """, (product_id,))
            case_stats = cursor.fetchall()
            
            stats = {
                "product_info": product_info,
                "story_distribution": story_stats,
                "bug_distribution": bug_stats,
                "case_distribution": case_stats
            }
            
            return success_response(stats, "获取产品统计成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"获取产品统计失败: {str(e)}")
