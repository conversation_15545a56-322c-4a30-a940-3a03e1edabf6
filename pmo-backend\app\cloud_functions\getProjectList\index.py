#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取项目列表云函数

功能：获取指定投资主体的项目列表
接口：HTTP GET
参数：
  - entity: 投资主体
  - implementation: 是否只获取开工项目
  - delayed: 是否只获取逾期项目
  - project_id: 项目ID（如果提供，则返回单个项目详情）

返回数据：
{
  "code": 200,
  "message": "success",
  "data": [项目列表]
}
"""

import os
import json
import pymysql
import logging
from datetime import datetime, date

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 添加自定义JSON编码器处理date类型
class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

# 数据库配置
DB_CONFIG = {
    'host': os.environ['DB_HOST'],
    'port': int(os.environ['DB_PORT']),
    'user': os.environ['DB_USER'],
    'password': os.environ['DB_PASSWORD'],
    'database': os.environ['DB_NAME'],
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

def is_project_delayed(project, current_date=None):
    """
    判断任务是否逾期 - 与getRedBlackBoard保持一致的逻辑
    :param project: 项目数据
    :param current_date: 当前日期，默认为None则使用当前系统日期
    :return: 是否逾期，逾期天数
    """
    if current_date is None:
        current_date = datetime.now()
    
    # 如果任务没有设置当前进度，无法判断是否逾期
    if not project.get('current_progress'):
        return False, 0
    
    # 定义任务阶段及其先后顺序
    stage_order = [
        '未启动', '业务调研', '解决方案', '项目立项', '任务采购',
        '项目实施', '项目验收', '项目结项', '已完成'
    ]
    
    # 获取当前阶段在顺序中的索引
    current_stage = project['current_progress']
    current_stage_index = stage_order.index(current_stage) if current_stage in stage_order else -1
    
    # 如果当前阶段不在定义的顺序中，无法判断是否逾期
    if current_stage_index == -1:
        return False, 0
    
    # 阶段时间字段映射
    stage_time_fields = {
        '未启动': 'business_research_time',  # 未启动阶段，检查业务调研时间
        '业务调研': 'business_research_time',
        '解决方案': 'solution_time',
        '项目立项': 'project_establishment_time',
        '任务采购': 'project_procurement_time',
        '项目实施': 'project_implementation_time',
        '项目验收': 'project_acceptance_time',
        '项目结项': None,  # 结项阶段不再有后续时间点
        '已完成': None     # 已完成阶段不再有后续时间点
    }
    
    # 获取当前阶段对应的计划完成时间字段
    time_field = stage_time_fields.get(current_stage)
    
    # 如果是最终阶段或无对应时间字段，则不算逾期
    if not time_field:
        return False, 0
    
    # 获取计划完成时间
    planned_date_str = project.get(time_field)
    
    # 如果没有计划时间，则不算逾期
    if not planned_date_str:
        return False, 0
    
    # 转换为日期对象并验证
    try:
        # 如果已经是日期对象，直接使用
        if hasattr(planned_date_str, 'year'):
            planned_date = planned_date_str
        else:
            # 处理包含时间部分的日期字符串
            if isinstance(planned_date_str, str) and ' 00:00:00' in planned_date_str:
                planned_date_str = planned_date_str.split(' ')[0]  # 只保留日期部分
            
            # 确保是字符串格式
            if not isinstance(planned_date_str, str):
                return False, 0
                
            planned_date = datetime.strptime(planned_date_str, '%Y-%m-%d').date()
        
        # 判断是否逾期
        is_delayed = current_date.date() > planned_date
        
        delay_days = 0
        if is_delayed:
            delta = current_date.date() - planned_date
            delay_days = delta.days
            
        return is_delayed, delay_days
    except (ValueError, TypeError):
        return False, 0

def main_handler(event, context):
    """云函数入口函数"""
    logger.info(f"接收到请求: {event}")
    
    try:
        # 解析请求参数
        query_params = event.get('queryString', {})
        entity = query_params.get('entity')
        implementation = query_params.get('implementation') == 'true' or query_params.get('implementation') is True
        delayed = query_params.get('delayed') == 'true' or query_params.get('delayed') is True
        project_id = query_params.get('project_id')
        
        # 创建数据库连接
        conn = get_db_connection()
        
        try:
            with conn.cursor() as cursor:
                # 构建SQL查询
                sql = """
                    SELECT * FROM Project_Account_Book
                    WHERE 1=1
                """
                params = []
                
                # 根据参数添加条件
                if entity:
                    sql += " AND investment_entity = %s"
                    params.append(entity)
                
                if project_id:
                    sql += " AND project_code = %s"
                    params.append(project_id)
                
                # 执行查询
                cursor.execute(sql, params)
                projects = cursor.fetchall()
                
                # 获取当前日期，用于计算逾期状态
                current_date = datetime.now()
                
                # 处理结果
                result_projects = []
                for project in projects:
                    # 使用正确的逾期判断逻辑
                    is_delayed, delay_days = is_project_delayed(project, current_date)
                    project['delayed'] = is_delayed
                    project['delay_days'] = delay_days
                    
                    # 根据条件过滤
                    if implementation and project.get('current_progress') not in ['任务采购', '项目实施', '项目验收', '项目结项', '已完成']:
                        continue
                    
                    if delayed and not is_delayed:
                        continue
                    
                    result_projects.append(project)
                
                return {
                    'statusCode': 200,
                    'headers': {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    'body': json.dumps({
                        'code': 200,
                        'message': 'success',
                        'data': result_projects
                    }, ensure_ascii=False, cls=DateEncoder)
                }
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"获取项目列表失败: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'code': 500,
                'message': f'获取项目列表失败: {str(e)}',
                'error': str(e)
            }, ensure_ascii=False)
        } 