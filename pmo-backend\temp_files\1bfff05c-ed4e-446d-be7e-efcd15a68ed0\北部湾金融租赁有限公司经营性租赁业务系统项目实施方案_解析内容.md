# Word文档内容


北部湾金融租赁有限公司

经营性租赁业务系统项目
实施方案

2024年1月

目 录

一、项目背景	- 1 -

二、建设内容	- 1 -

三、功能明细	- 4 -

四、项目推进模式和进度计划	- 13 -

五、项目里程碑和交付物	- 14 -

六、项目投资预算与采购方式	- 15 -

## 一、项目背景

“十四五 ”期间，公司将重点围绕广投体系圈、自治区国企圈、地市实体圈、行业领先圈为业务发展方向，全面提升金融服务能力，聚焦医疗器械、新能源、环保再生资源、铝产业等重点产业，同时按照监管合规要求，提升直租和经营性租赁比例，开展经营性租赁业务。为加强数字信息化基础设施架构规划，加快信息系统建设，需要在当前公司业务中台系统的基础上建设经营性租赁系统，进行业务管理功能扩展，服务于公司业务部、授信管理部、合规部、财务部等部门，提升公司各条线的工作效率，加强公司对业务风险的管理，支持公司快速发展。

## 二、建设内容

建立一套全流程智能化的经营性租赁业务系统，该系统可面向供应商、渠道方和承租人客户开展经营性租赁业务，并能与现有的业务中台系统良好集成，建设内容主要包括云端支撑平台、经营性租赁/厂租业务管理、与现有中台业务系统、反洗钱系统集成、与第三方系统对接、微信公众号服务、移动端业务处理、监管报表辅助生成、数据报表分析等，实现多角色在线连接互动，增效降本、防范风险、更好的服务企业，助力公司业务的发展。

### 独立部署苍穹融资租赁业务系统软件，通过苍穹技术平台，实现技术底座的优化升级，打造管控容易、全面可视化管理的先进软件系统，保障系统的稳定运行及有效的业务支撑。

独立部署苍穹融资租赁业务系统软件，基于苍穹平台底座基础能力，提供智能的工作流引擎、报表设计工具、扩展开发工具及系统自动运维平台，支持公司业务敏捷性开发，并以模型沉淀企业的业务能力，灵活快速响应业务变化，支持个性化扩展开发，实现模型化、组件化、服务化，保障融资租赁系统的业务支撑及稳定运行，打造管控容易、全面可视化管理的先进融资租赁业务软件系统。

建设智能化的经营性租赁业务系统，实现经营性租赁业务租前、租中、租后全生命周期的全流程线上化、规范化的统一管理。

一是通过云端支撑平台，实现中小微经营性租赁业务线上化、规范化管理，贯穿客户管理、租前管理、租后管理、报表管理、档案管理等业务全流程，实现不同行业领域的经营性租赁业务统一管理，并可依据不同业务类型进行流程配置，以系统支撑和促进业务管理，并为业务提供分析指导支撑。二是通过人脸识别，人工智能，大数据，完成客户身份认证及资料审核环节，通过电子合同、电子签章等完成合同签订及资金投放环节，通过发票云实现在线的自动开票、收票、发票核验、税务管理，实现业财税一体化，通过物联网技术，完善客户回笼、客户后期管理、租后巡查及设备实时监控，最大化的降低人工参与环节，提高审核效率与准确度。

实现经营性租赁业务产品管理、营销管理、风控管理、移动端应用专项服务，实现产品数字化管理、风控智能化管理、业务数字化、移动化办理。

一是搭建智能化产品管理中心，支持通过多层次的组合设计，按照市场需求和公司策略灵活扩展，快速配置产品，并支持通过以产品特征进行综合收益测算，以支撑公司产品方案定制及决策。二是打造营销管理中心，链接客户和合作商，支持多类别、多级别的渠道准入，支持公司与多家厂商开展业务合作获取更多客户源，增加业务辐射范围。同时对业务经理营销记录进行管理，提高公司管理透明度，降低公司运营成本。三是对接第三方大数据，并根据企业规则搭建风控模型，实现智能化风控管理，提升客户质量，实现系统自动批量审批，提升人工审核及风控管理效率。四是集成移动端应用，包含租赁公司钉钉移动端、微信公众号渠道端及终端客户端应用，支持业务经理随时随地开展业务，快速进行业务推广，实现多角色全场景的移动化业务办理。

集成第三方外部系统、并与现有业务中台系统对接，实现经营性租赁业务系统内外部领域的集成需求，以及与现有业务中台系统的统一管理。

系统提供API接口，支持与相关系统的API对接，实现经营性租赁业务系统内外部领域、上下游应用环节的集成需求，具体包括：与征信系统、企查查、OCR识别技术、电子签约系统、中登网、资金系统、财务系统、短信系统、GPS监控系统等应用与数据集成。并与北金租现有业务中台系统对接，统一用户组织及权限管理，实现单点登录、统一待办，实现数据的统一管理，保持客户、供应商、黑名单等基础数据保持同步。

### 建设报表管理中心，提供各类业务报表管理，满足监管数据报送和报表辅助生成。

通过建设报表管理中心，实现多样化的报表类型，固定报表、自定义报表设计、查询和导出功能。同时，根据监管部门要求，生成监管部门需要的报表数据并对接业务中台系统，完成监管数据报送。

## 功能明细

## 四、项目推进模式和进度计划

### （一）项目推进计划

经营性租赁系统项目由公司业务经营部门和数字金服选派专员组成项目团队，共同推动项目的实施和成功落地。其中：数字金服负责项目的执行、实施落地、培训及运维保障，确保系统的稳定运行与高效服务；公司业务、评审、财务和合规部门负责明确需求、测试，以确保系统满足业务和功能需求，提高用户体验。

### （二）项目进度计划

本项目将按照标准信息化项目推进方式，分为调研、设计、开发、测试、验证、上线等几个主要阶段，项目建设周期预计为4个月左右，计划于一季度启动、第二季度完成开发并上线运行，第三季度完成项目验收，具体阶段如下：

## 五、项目里程碑和交付物

### （一）项目里程碑

### （二）项目交付物

## 六、项目投资预算与采购方式

### （一）项目投资预算

经营性租赁业务系统项目建设开发费用预计为180万元，已列入公司2024年度预算范围，项目总体建设费用预算明细如下：

### （二）项目采购方式

根据《北部湾金融租赁有限公司采购管理办法（2022年修订版）》第二十四条：“符合下列情形之一的可选择单元直接采购方式进行：（四）为保证项目一致性或者服务配套要求，需要向原供应商采购工程、货物或服务，否则将影响施工或者功能配套要求的。”，因本项目需要与原有租赁业务中台系统高度集成，原业务中台的承建方广西数字金服科技有限公司也具备经营性租赁业务系统的开发能力，业务中台顺利实现监管报送自动化、业财一体化和数据可视化也得益于数字金服全程提供优质的现场开发运维配套服务，在业务亟需系统的客观情况下，为保证新项目能保质保量完成建设，拟采用单源直接采购方式向广西数字金服科技有限公司采购经营性租赁系统项目建设服务。

综合管理部

2024年1月26日


| 模块 | 功能类别 | 功能模块 | 功能描述 |
| --- | --- | --- | --- |
| 1、基础资料 | 1.1 客户资料 | 客户报表项目、借款人性质、客户企业规模、所属行业门类、客户所有制性质区域等 | 基础信息维护，使得系统更具有灵活性、扩展性； |
| 1、基础资料 | 1.1 客户资料 | 客户报表项目、借款人性质、客户企业规模、所属行业门类、客户所有制性质区域等 | 同时系统分析数据可以依据不同纬度进行统计分析； |
| 1、基础资料 | 1.1 客户资料 | 客户报表项目、借款人性质、客户企业规模、所属行业门类、客户所有制性质区域等 | 便于用户快速操作。 |
| 1、基础资料 | 1.2 租赁类型 | 租赁业务类型设置 | 系统支持面向中小微客户业务的厂商租赁、经营性租赁等业务类型，可对业务类型进行参数配置，以适应不同的场景和流程。 |
| 1、基础资料 | 1.3 系统资料 | 利率参数、央行贷款利率幅度维护、租赁形式、保险公司、保险费率、央行存款利率幅度维护、罚息利率、费用类型、利率类型、设备类型、设备状态等 | 系统提供参数化设置，更灵活适用于不同业务场景。 |
| 1、基础资料 | 1.3 系统资料 | 利率参数、央行贷款利率幅度维护、租赁形式、保险公司、保险费率、央行存款利率幅度维护、罚息利率、费用类型、利率类型、设备类型、设备状态等 | 如：设置利率参数、央行贷款利率幅度维护等数据，系统可以自动进行单笔、批量调息处理；设备保险自动计算；费用类型、罚息利率、罚息宽限天数个性配置等 |
| 1、基础资料 | 1.4 项目资料 | 律师事务所、信用等级、担保保证类型、评分项、保险公司等 | 系统提供项目相关资料。可支持手工新增、修改各资料内容，是后续业务数据的基础信息。 |
| 2、客户管理 | 2.1 客户管理 | 法人客户 | 将客户分为法人客户登记、自然人登记、厂商登记、担保人，维护相应客户的基本信息，并记录业务员日常客户拜访的信息；支持对客户的名称及证照号码等进行修改；能够按提供的标准，对企业的规划分类（大中小型）进行自动划分。 |
| 2、客户管理 | 2.1 客户管理 | 自然人客户 | 将客户分为法人客户登记、自然人登记、厂商登记、担保人，维护相应客户的基本信息，并记录业务员日常客户拜访的信息；支持对客户的名称及证照号码等进行修改；能够按提供的标准，对企业的规划分类（大中小型）进行自动划分。 |
| 2、客户管理 | 2.2 报表管理 | 报表项目 | 系统支持自定义报表项目，以及财务指标分析计算逻辑。 |
| 2、客户管理 | 2.2 报表管理 | 报表模板 | 企业在尽调风控环节需要搜集客户的财务报表及统计分析报表，系统支持预设报表模板，便于灵活运用。 |
| 2、客户管理 | 2.2 报表管理 | 财务报表OCR识别 | 可OCR识别所搜索的PDF或JPG等格式的财务报表，提升业务人员的报表录入效率。 |
| 2、客户管理 | 2.3 供应商 | 供应商管理 | 记录租赁业务中的供应商，如直租或厂商租赁业务中设备的供应商。 |
| 2、客户管理 | 2.4 合作方管理 | 合作方准入 | 业务员可发起维护预合作的经销商信息、厂商信息、渠道信息，然后经过审批，审批通过后，合作方准入通过后，才能顺利维护合作方用户信息，有了合作方用户才能开展合作方的进件业务。 |
| 2、客户管理 | 2.4 合作方管理 | 合作协议管理 | 记录跟合作的厂商或经销商签署的合作协议，也可以用系统实现线上签署。 |
| 2、客户管理 | 2.4 合作方管理 | 合作方授信额度管理 | 对于业务管理有要求的合作方，需要在开展业务前进行担保或授信额度申请、审批和批复， 审批完成后以最终核定的授信额度进行后续的进件业务开展，整体进件过程都根据该授信额度进行控制和提示。 |
| 2、客户管理 | 2.4 合作方管理 | 合作方用户管理 | 针对已经准入审批通过的合作方进行该合作方的合作方用户的增删改查和启用、禁用、权限等维护。 |
| 2、客户管理 | 2.5 营销中心商机管理 | 商机登记 | 记录客户名称、联系人等信息 |
| 2、客户管理 | 2.5 营销中心商机管理 | 商机跟进（客户拜访） | 记录商机跟进的情况。支持移动端录入和填写拜访记录，支持资料、拍照及视频等文件上传。 |
| 2、客户管理 | 2.5 营销中心商机管理 | 商机跟进统计和提醒 | 对每人的跟进情况进行统计，能够准确的按项目按业务人员统计拜访次数、拜访间隔等，能按制度自动计算业务工作得分，并可对近期未按要求进行跟进反馈的商机，提醒相关人员及时介入处理。 |
| 2、客户管理 | 2.6 黑名单 | 黑名单管理 | 对信誉不良的客户可直接黑名单处理，后续不再发生相关业务，从而控制业务风险。 |
| 2、客户管理 | 2.6 黑名单 | 黑名单管理 | 支持批量导入、支持从客户名单中转入黑名单、移除黑名单等功能；控制强度支持多种：如预警、提醒、严格控制。 |
| 3、租前管理 | 3.1 进件管理 | 租赁产品方案 | 允许用户配置租赁业务金融产品方案，并设置编辑权限、启用或禁用等。 |
| 3、租前管理 | 3.1 进件管理 | 报价测算 | 根据录入的关键要素和选项，自动提供报价方案，并支持手工调整，满足个性化报价需求，支持租金表计算，支持等额，等差，等比，等本，一次性还本，利息前置，手工录入，特殊调整等多种租金计算报价和收益率指标计算。 |
| 3、租前管理 | 3.1 进件管理 | 租赁业务申请 | 可接收前端公众号等提供的项目申请信息，也可由业务人员接单后，手工录入申请。 |
| 3、租前管理 | 3.1 进件管理 | 租赁业务审批 | 将前端提交的租赁业务申请表，转化为可供融资租赁公司内部进行业务、风控及财务审核的内部单据，并进行流程审批。 |
| 3、租前管理 | 3.1 进件管理 | 系统自动审批 | 可根据租赁公司启用的风控模型和相关流程设置，对项目申请进行自动审批，发送审批结果通知。 |
| 3、租前管理 | 3.1 进件管理 | 人工调整 | 可配置系统，对系统进件审批的结果，进行有条件的确认和人工调整 |
| 3、租前管理 | 3.2 合同管理 | 融资租赁合同 | 通过尽职调查和风控审查完成且符合租赁审批条件，即可进入合同签订阶段，支持各类合同（如租赁合同、买卖合同、回租回购协议等）模板设计，打印，或套打输出为Word、PDF；支持电子签章；支持各种复杂流程审批配置。 |
| 3、租前管理 | 3.2 合同管理 | 经营性租赁合同 | 录入和维护经营性租赁合同，支持合同的审批和套打打印，支持合同的变更，可同步维护合同状态，或关闭合同。 |
| 3、租前管理 | 3.2 合同管理 | 合同综合查询 | 按合同纬度，进行查询合同相关的所有业务发生数据，如：合同后期的放款计划、投放单、偿还计划、回笼、其他收退款等。 |
| 3、租前管理 | 3.2 合同管理 | 担保合同登记 | 融资租赁业务中，需要提供抵押、质押、保证等多种担保类型，系统支持多种合同登记 |
| 3、租前管理 | 3.2 合同管理 | 抵质押物管理 | 抵质押物管理；系统支持批量导入、登记、解押管理 |
| 3、租前管理 | 3.3 合同签约 | 合同签约 | 所有签约文件都在此单据记录签约流程，以及最终版签约文件，支持线下签约和线上签约（使用电子签章功能）。 |
| 3、租前管理 | 3.3 合同签约 | 用印管理 | 可进行用印申请，登记印章类型，盖章分类以及用印审批过程等信息。 |
| 3、租前管理 | 3.4 合同变更 | 合同后期变更 | 合同后期的变更记录查询 |
| 3、租前管理 | 3.4 合同变更 | 合同后期变更 | 合同生效后，需要进行合同修改，需要进行合同修改申请，申请通过后，才允许进行合同内容变更。 |
| 3、租前管理 | 3.4 合同变更 | 合同变更记录查询 |  |
| 4、租后管理 | 4.1 租赁物管理 | 租赁物登记 | 租赁合同涉及的租赁物登记信息，可反写合同 |
| 4、租后管理 | 4.1 租赁物管理 | 租赁资产交付确认 | 可录入租赁交付确认单，按合同要求将租赁物资产交付客户，记录交付起租确认信息。 |
| 4、租后管理 | 4.1 租赁物管理 | 资产状态管理 | 经营租赁资产基本状态管理，如使用情况、即时、分段和累计使用量、在库待租、维修维护等。 |
| 4、租后管理 | 4.1 租赁物管理 | 设备保险 | 自动对设备进行首保、续保计算，并根据实际情况生成凭证。 |
| 4、租后管理 | 4.1 租赁物管理 | 租赁资产归还验收 | 录入租赁资产归还及验收情况，可根据之前的经营性租赁资产交付确认单生成，需要提供单据套打功能，以便可配置模板并打印归还验收单。 |
| 4、租后管理 | 4.1 租赁物管理 | 产权转移 | 产权转出，合同结束前设备产权转移的登记及产权转移单的打印 |
| 4、租后管理 | 4.1 租赁物管理 | 产权转移 | 回租业务中，由承租方向出租方销售租赁后，需要将设备产权转移到租赁公司 |
| 4、租后管理 | 4.2 收退款 | 保证金收款/退款 | 合同中涉及的保证金的收款管理 |
| 4、租后管理 | 4.2 收退款 | 保证金收款/退款 | 系统支持保证金内扣逻辑；支持保证金抵扣租金。 |
| 4、租后管理 | 4.2 收退款 | 保证金收款/退款 | 合同中涉及的保证金的退款管理； |
| 4、租后管理 | 4.2 收退款 | 保证金收款/退款 | 保证金支持自动生成财务凭证。 |
| 4、租后管理 | 4.2 收退款 | 其他收款 | 合同中涉及的咨询费、通道费、手续费等费用的收款管理 |
| 4、租后管理 | 4.2 收退款 | 其他退款 | 合同中涉及的咨询费、手续费等费用的退款管理 |
| 4、租后管理 | 4.3 资金投放 | 投放申请 | 由业务员根据合同约定编制投放计划，支持多次投放计划的编制 |
| 4、租后管理 | 4.3 资金投放 | 投放申请 | 支持投放前提条件自动生成，及系统控制 |
| 4、租后管理 | 4.3 资金投放 | 资金投放 | 审批通过的投放申请，为财务部可依据计划做资金投放；系统能自动根据费用是否为内扣而生成不同凭证 |
| 4、租后管理 | 4.4 偿还计划 | 偿还计划制作 | 根据合同约定或合同后期修改约定的内容自动生成偿还计划，系统支持分批投放分批起租；支持分批投放一次起租；支持租前结息等多种起租逻辑。 |
| 4、租后管理 | 4.4 偿还计划 | 偿还计划变更 | 偿还计划可以通过变更单进行变更处理。 |
| 4、租后管理 | 4.4 偿还计划 | 偿还计划调息单 | 依据央行利率等信息的维护，系统可以自动计算偿还计划调息浮动；支持单笔、批量两种方式 |
| 4、租后管理 | 4.5 核销管理 | 核销规则设置 | 按公司的财务管理要求，设置用于识别银行还款流水等对应的应收款的规则，以供系统自动执行。 |
| 4、租后管理 | 4.5 核销管理 | 核销认款工作台 | 将所有需要人工干预的暂时未明款项列出并展示给相关用户，以供人工进行认款匹配处理。 |
| 4、租后管理 | 4.5 核销管理 | 核销日志 | 记录系统自动核销的实际情况，以供事后查验核对。 |
| 4、租后管理 | 4.6 资金回笼 | 银行流水单 | 由银企直联、资金系统或手工导入成为银行流水单，作为重要的原始收款记录，以供回笼核销。 |
| 4、租后管理 | 4.6 资金回笼 | 回笼单 | 根据偿还计划进行资金回笼，并自动计算出余额。 |
| 4、租后管理 | 4.6 资金回笼 | 回笼单 | 如未按偿还计划约定时间回笼，系统根据合同约定罚息利率，自动计算出罚息；系统支持提前还款； |
| 4、租后管理 | 4.6 资金回笼 | 或有收入回笼 | 当租赁业务中存在或有收入时，可使用或有收入回笼进行登记； |
| 4、租后管理 | 4.6 资金回笼 | 逾期催收 | 对于存在逾期的租赁合同，记录相关的催收信息，并对催收过程进行记录； |
| 4、租后管理 | 4.6 资金回笼 | 逾期停息 | 针对已逾期的期项，系统可以设置停息日期，停息日期后系统将不再计算罚息； |
| 4、租后管理 | 4.6 资金回笼 | 到期租金通知单 | 系统可根据每期租金到期前N天，打印租金通知单给客户； |
| 4、租后管理 | 4.6 资金回笼 | 坏账处理 | 当逾期超过设定天数时，财务人员需要进行坏账处理。 |
| 4、租后管理 | 4.7 发票管理 | 收票管理 | 进行付款业务的收票登记、收票验证等处理，包括：发票真伪数据查验服务；票据图像自动识别OCR服务；票据全票面数据获取服务；拍照扫描发票图像服务。 |
| 4、租后管理 | 4.7 发票管理 | 开票申请 | 支持数电票开具；支持根据实际放款金额进行开票；支持通过邮件发送电票。 |
| 4、租后管理 | 4.8 计提管理 | 融资租赁收入计提 | 支持按照实际利率法，或者直线法来计提租息收入，支持初始化，支持每月批量计提，支持租金表变更后，计提轧差处理。 |
| 4、租后管理 | 4.8 计提管理 | 坏账比例配置 | 配置五级分类结果对应的坏账准备计提比例，支持按照合同配置坏账计提比例，支持调整坏账准备计提比例。 |
| 4、租后管理 | 4.8 计提管理 | 坏账准备计提 | 支持按照根据合同五级分类结果，查询坏账计提比例后，计提坏账准备，支持初始化，支持每月批量计提，支持租金回笼后坏账准备冲销。 |
| 4、租后管理 | 4.8 计提管理 | 坏账损失确认 | 当项目认定为损失后，需要进行坏账损失确认，用此单记录坏账损失确认金额。 |
| 4、租后管理 | 4.9 制裁诉讼 | 起诉管理 | 含：诉讼申请、财产保全、诉状、起诉委托代理、一审法院受理、二审法院受理 |
| 4、租后管理 | 4.9 制裁诉讼 | 应诉管理 | 含：应诉答辩、应诉委托代理 |
| 4、租后管理 | 4.9 制裁诉讼 | 诉讼费用 | 用于记录诉讼过程中产生的费用 |
| 4、租后管理 | 4.9 制裁诉讼 | 裁决管理 | 含：一审传票、二审传票、案件调解、案件撤诉、一审判决、二审判决 |
| 4、租后管理 | 4.9 制裁诉讼 | 执行管理 | 含：结案管理、自动履行、申请执行、执行情况 |
| 4、租后管理 | 4.10 五级分类 | 五级分类 | 系统可以对所有合同按五种分类归档，并记录归类原因。系统可以实现审批流及多级、批量审批功能。 |
| 4、租后管理 | 4.11 租后巡检 | 首次检查 | 针对投放后进行首次检查，核实投放资金情况及设备等情况 |
| 4、租后管理 | 4.11 租后巡检 | 租后巡检 | 常规检查，每季度或月进行租后巡检，针对租赁物、客户财务情况等信息进行记录，并出具报告。 |
| 4、租后管理 | 4.12 合同结清 | 产权转移 | 回租业务中，由承租方向出租方销售租赁后，需要将设备产权转移到租赁公司。或在正常直租的合同结束前，对设备产权转移的登记及产权转移单的打印。 |
| 4、租后管理 | 4.12 合同结清 | 合同结清 | 系统支持合同正常结束、提前结束、异常结束等多种情况的方式。可以依据不同进行审批流程设置。 |
| 5、档案管理 | 5.1 项目资料档案管理 | 业务清单配置 | 管理所有项目资料的移交、归档、借阅、归还、移出；以及项目合同变更后的资料移交、归档、借阅、归还。 |
| 5、档案管理 | 5.1 项目资料档案管理 | 资料归档 | 管理所有项目资料的移交、归档、借阅、归还、移出；以及项目合同变更后的资料移交、归档、借阅、归还。 |
| 5、档案管理 | 5.1 项目资料档案管理 | 资料借阅登记、归还、移出 | 管理所有项目资料的移交、归档、借阅、归还、移出；以及项目合同变更后的资料移交、归档、借阅、归还。 |
| 6、报表管理 | 6.1 各类报表 | 项目管理台账 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 合同管理报表 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 投放管理报表 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 收退款管理报表 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 偿还计划报表 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 逾期催收报表 | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.1 各类报表 | 其他重要业务报表（15张） | 系统内提供各类管理报表、业务分析报表、数据统计报表等，共大约21张。 |
| 6、报表管理 | 6.2 监管数据报送和报表辅助生成 | 辅助生成需要报送的监管报表 | 系统模块的数据标准，严格按照人行征信(融资租赁)、 金融基础数据、利率报备、银监 EAST 报表、1104 报表、人行大集中等监管要求严格执行，可通过集中的报表工具，输出需要报送监管的各类报表数据，辅助生成各类监管报表，以提升数据报送的效率。 |
| 7、微信公众号（渠道端） | 7.1 系统管理 | 个人设置 | 账号基本信息维护，实名认证，密码管理。 |
| 7、微信公众号（渠道端） | 7.1 系统管理 | 注册账户管理 | 可手工代客户设置基本信息和登录账户，禁用及维护账户。 |
| 7、微信公众号（渠道端） | 7.2 测试报价 | 报价器 | 提供简化的即时报价测算功能。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 首页统计 | 在首页界面显示重要的相关业务统计指标。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 我的待办 | 处理当前的待办任务，如：租赁申请、征信查询、补充资料及其他事项的审批处理，支持同意及驳回、终止等操作。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 我的已办 | 可查询已处理的待办情况，查询业务流程审批进度等。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 商机跟进 | 处理租赁公司指派过来的商机线索，可转化为租赁申请 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 客户管理 | 在线管理个人有权限的所有租赁客户。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 征信查询 | 依据征信查询授权书，发起针对指定客户的征信查询申请。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 补充资料 | 在租赁申请过程中，如果租赁公司审核时发现有资料需要补充，可通过此功能补充递交资料。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 租赁申请 | 根据客户信息及征信情况，填写和提交租赁业务申请。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 交货确认 | 与客户的现场交货签收确认，提供交货验收时间及照片，以供后面生成和通知客户签署确认起租。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 起租确认 | 查询了解有权限的客户的交货验收确认签署情况。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 逾期管理 | 查询了解有权限的客户的还款及逾期情况。 |
| 7、微信公众号（渠道端） | 7.3 业务办理 | 产权转移 | 在客户结清时，申请办理合格证转移等产权转移手续。 |
| 7、微信公众号（渠道端） | 7.4 业务报表 | 移动报表 | 可在手机端多维度查询合同数据及统计图表。 |
| 8、微信公众号（客户端） | 8.1 系统管理 | 个人设置 | 账号基本信息维护，实名认证，密码管理，联系手机、地址管理，开票信息维护。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 首页统计 | 在首页界面显示重要的相关业务统计指标。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 我的待办 | 处理当前的待办任务，如：租赁申请确认、征信查询授权签署确认、补充申请资料、合同签署、交货验收签署、在线还款、开票信息确认及其他事项的处理。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 我的已办 | 可查询已处理的待办情况，查询业务审批进度等。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 租赁申请 | 可查询个人提交的正式租赁申请，包括处理过程详情。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 合同管理 | 可查询已签署的征信授权书、合同等文件情况。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 文件签署 | 可核销需要个人签署的租赁业务相关文件，并在线签署。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 还款计划 | 列出合同相关的个人还款计划，且可导出。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 在线还款 | 可查询个人的近期待还款，并查询历史还款情况，可直接进行在线还款。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 提前结清申请 | 可发起提前结清申请，查询结清需要的相关待还费用。 |
| 8、微信公众号（客户端） | 8.2 业务办理 | 发票管理 | 可查询未开票和下载租赁公司给自己开具的发票情况。 |
| 9、系统接口及预警 | 9.1 查询数据 | 企业信息大数据查询抓取（启信宝、企查查等） | 从大数据接口读取客户动态信息 |
| 9、系统接口及预警 | 9.2 发送数据 | 手机短信 | 系统发送短信提醒到员工或客户手机 |
| 9、系统接口及预警 | 9.2 发送数据 | 电子邮件 | 系统发送邮件到指定人邮箱 |
| 9、系统接口及预警 | 9.3系统集成 | 与北金租现有业务中台系统对接 | 与北金租现有业务中台系统对接，统一用户组织及权限管理，实现单点登录，重要的客户、供应商、黑名单等基础数据保持同步。 |
| 9、系统接口及预警 | 9.3系统集成 | 与钉钉系统对接，实现移动审批 | 与北金租当前使用的钉钉进行移动端集成，在前端也实现单点登录、统一所有租赁业务的待办和已办任务，实现统一的业务审批处理。系统在前端即能够区分不同租赁审批任务对应的实际中后台，然后提交相应的中后台进行处理。 |
| 9、系统接口及预警 | 9.3系统集成 | 与资金系统对接 | 通过接口向资金系统提供应收款代扣数据，并查询获取实际代扣结果。或者获取收款账户的银行账户流水，即实际收款数据。 |
| 9、系统接口及预警 | 9.3系统集成 | 中登网对接 | 可按需要实现与中登网接口，实现在系统中直接进行动产租赁物登记，并可查询登记情况。 |
| 9、系统接口及预警 | 9.3系统集成 | 合同电子签署对接 | 对于需要电子签署的授权书、合同、承诺书等文件，可对接e签宝或法大大等电子签章系统，实现电子签署。 |
| 9、系统接口及预警 | 9.3系统集成 | 公众号或小程序 | 对接企业的微信公众号，开发提供微信公众号或小程序相关的业务功能，以服务外部的渠道服务商及终端客户。 |
| 9、系统接口及预警 | 9.3系统集成 | 与财务系统对接 | 与金蝶EAS系统对接，以实现生成凭证、传递凭证、查询凭证等业务财务一体化功能。 |
| 9、系统接口及预警 | 9.4 辅助服务 | 身份证件OCR识别 | 自动从身份证照片提取信息 |
| 9、系统接口及预警 | 9.4 辅助服务 | 身份证两要素验证 | 按名字和身份证号码对身份证真伪进行验证 |
| 9、系统接口及预警 | 9.4 辅助服务 | 手机号三要素验证 | 对姓名、身份证号、手机号三要素进行验证 |
| 9、系统接口及预警 | 9.4 辅助服务 | 银行卡号OCR识别 | 自动从银行卡照片提取信息 |
| 9、系统接口及预警 | 9.4 辅助服务 | 银行卡验证（三要素或四要素） | 对姓名、身份证号、银行卡号（手机号）三要素/四要素进行验证 |
| 10、自动任务和预警 | 10.1系统自动任务和预警 | 自动任务配置 | 根据用户的要求，预先设置好需要执行的自动任务，并指定其执行计划，以便让系统自动按计划执行 |
| 10、自动任务和预警 | 10.1系统自动任务和预警 | 待办任务提醒 | 对于每个用户的待办任务，系统根据预设的紧急及重要程度进行自动提醒。 |
| 10、自动任务和预警 | 10.1系统自动任务和预警 | 其它自动任务 | 可根据需要，配置提供如：档案归档、合同签署、起租提醒、还款提醒、逾期提醒、催收提醒等不超10项的自动预警任务。 |



| 实施阶段 | 计划时间 | 主要工作说明 |
| --- | --- | --- |
| 项目启动 | 第一季度 | 项目启动前需要完成项目立项、采购、合同签订等流程。 |
| 需求调研 | T+30 | 明确项目的具体业务、功能需求，明确功能边界。 |
| 分析设计 | T+30 | 对相关系统及数据情况进行梳理和明确，对系统相关功能进行设计。 |
| 开发测试 | T+100 | 按照需求与设计的要求，进行系统功能实现以及内部集成测试。 |
| 用户验证 | T+110 | 安排系统关键用户对系统整体功能进行全面验证，确保各项功能符合预期要求。 |
| 系统全面上线 | 第二季度 | 系统功能全面上线试运行 |



| 里程碑 | 任务名称 | 计划完成时间 |
| --- | --- | --- |
| 项目启动 | 确认项目实施工作范围 | 第一季度 |
| 需求确认 | 完成系统业务需求调研和系统对接方案设计 | T+10 |
| 设计完成 | 完成业务需求分析并通过确认
完成接口规范编制并通过确认 | T+30 |
| 功能实现 | 完成客户定制需求的开发
完成内部系统测试 | T+90 |
| 测试完成 | 完成软件功能测试工作 | T+100 |
| 用户验收 | 完成用户验收测试 | T+110 |
| 系统上线 | 完成生产环境上线 | T+120 |



| 项目阶段 | 主要交付成果 |
| --- | --- |
| 启动 | 《项目工作任务确认单》 |
| 需求 | 《需求调研汇报材料》 |
| 设计 | 《原型设计材料》 |
| 测试 | 《用户操作手册》、《测试报告》 |
| 上线 | 《系统上线确认单》 |
| 验收 | 《开发完成确认单》、《用户验收确认单》 |



| 预算项目 | 预算金额（万） | 主要内容说明 |
| --- | --- | --- |
| 苍穹产品 | 45 | 包括苍穹融资租赁业务系统，1、融资租赁产品；2、苍穹平台（开发设计器、ZooKeeper、RabbitMQ、Redis、Nginx、ELK、K8S容器(苍穹应用、文件服务、管理中心),苍穹工作流，苍穹发票云，支持80个系统用户权限。 |
| 云苍穹（经营性租赁/厂租）系统功能建设 | 135 | 包括云端支撑平台、中小微租赁业务管理、与现有中台业务系统集成、与第三方系统对接、微信公众号服务、移动端业务处理、监管报表辅助生成、数据报表分析。 |
| 总计 | 180 |  |
