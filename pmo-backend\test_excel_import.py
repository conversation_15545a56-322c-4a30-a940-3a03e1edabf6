#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导入功能
"""

import requests
import pandas as pd
import os
from datetime import datetime

def create_test_excel():
    """创建测试Excel文件"""
    print("📝 创建测试Excel文件...")
    
    # 创建测试数据
    data = [
        {
            '序号': 1,
            '工作维度': '科技条线',
            '工作主题': '测试导入系统升级',
            '督办来源': '集团科技部',
            '工作内容和完成标志': '测试Excel导入功能的系统升级工作',
            '是否年度绩效考核指标': '是',
            '完成时限': '12月末',
            '整体进度': 'O',
            '财险': '√',
            '寿险': 'O',
            '金租': '！',
            '资管': 'X',
            '广租': '—'
        },
        {
            '序号': 2,
            '工作维度': '科技条线',
            '工作主题': '测试导入数据安全',
            '督办来源': '集团科技部',
            '工作内容和完成标志': '测试Excel导入功能的数据安全检查',
            '是否年度绩效考核指标': '否',
            '完成时限': '11月末',
            '整体进度': '√',
            '财险': '√',
            '寿险': '√',
            '金租': '√',
            '资管': '√',
            '广租': 'O'
        },
        {
            '序号': 3,
            '工作维度': '风险管理',
            '工作主题': '测试导入风险评估',
            '督办来源': '集团风险部',
            '工作内容和完成标志': '测试Excel导入功能的风险评估工作',
            '是否年度绩效考核指标': '是',
            '完成时限': '10月末',
            '整体进度': '！',
            '财险': '！',
            '寿险': '！',
            '金租': 'O',
            '资管': 'X',
            '广租': 'X'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    excel_file = 'test_supervision_import.xlsx'
    df.to_excel(excel_file, index=False, sheet_name='督办事项')
    
    print(f"✅ 测试Excel文件已创建: {excel_file}")
    return excel_file

def test_excel_import(excel_file):
    """测试Excel导入API"""
    print(f"🧪 测试Excel导入: {excel_file}")
    
    try:
        # 准备文件上传
        with open(excel_file, 'rb') as f:
            files = {'file': (excel_file, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            # 调用导入API
            response = requests.post(
                'http://127.0.0.1:8001/api/v1/new-supervision/import-excel',
                files=files
            )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Excel导入成功")
            print(f"导入结果: {result}")
            return True
        else:
            print(f"❌ Excel导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 导入测试失败: {str(e)}")
        return False

def test_api_after_import():
    """导入后测试API数据"""
    print("🔍 测试导入后的API数据...")
    
    try:
        response = requests.get('http://127.0.0.1:8001/api/v1/new-supervision/items')
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"督办事项数量: {len(data.get('data', []))}")
            
            if data.get('data'):
                print("\n📋 督办事项列表:")
                for item in data['data']:
                    work_theme = item.get('work_theme', '')
                    deadline = item.get('completion_deadline', '')
                    overall_progress = item.get('overall_progress', '')
                    print(f"  - {work_theme} (截止: {deadline}, 进度: {overall_progress})")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_export_excel():
    """测试Excel导出功能"""
    print("📤 测试Excel导出...")
    
    try:
        response = requests.get('http://127.0.0.1:8001/api/v1/new-supervision/export-excel')
        
        if response.status_code == 200:
            # 保存导出的文件
            export_file = f'exported_supervision_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            with open(export_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Excel导出成功: {export_file}")
            
            # 验证导出文件
            try:
                df = pd.read_excel(export_file)
                print(f"导出文件包含 {len(df)} 行数据")
                print("导出文件列名:", list(df.columns))
                return True
            except Exception as e:
                print(f"❌ 导出文件验证失败: {str(e)}")
                return False
                
        else:
            print(f"❌ Excel导出失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 导出测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Excel导入导出功能测试...")
    print("=" * 50)
    
    # 1. 创建测试Excel文件
    excel_file = create_test_excel()
    
    # 2. 测试Excel导入
    if test_excel_import(excel_file):
        print("\n" + "=" * 50)
        
        # 3. 测试导入后的API数据
        test_api_after_import()
        
        print("\n" + "=" * 50)
        
        # 4. 测试Excel导出
        test_export_excel()
    
    # 5. 清理测试文件
    try:
        if os.path.exists(excel_file):
            os.remove(excel_file)
            print(f"\n🧹 已清理测试文件: {excel_file}")
    except:
        pass
    
    print("\n🎉 Excel导入导出功能测试完成！")

if __name__ == "__main__":
    main()
