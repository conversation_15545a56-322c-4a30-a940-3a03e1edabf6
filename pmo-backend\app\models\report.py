#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计报表模型 - 参考禅道zt_report表
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date, timedelta
import pymysql
import json
from app.core.database import get_db_connection, close_db_connection

class Report:
    """报表模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.code: str = ""
        self.desc: Optional[str] = None
        self.sql: Optional[str] = None
        self.vars: Optional[str] = None
        self.langs: Optional[str] = None
        self.domain: str = ""
        self.vision: str = "rnd"
        self.public: str = "0"
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, report_id: int) -> Optional['Report']:
        """根据ID获取报表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_report WHERE id = %s AND deleted = '0'
            """, (report_id,))
            
            row = cursor.fetchone()
            if row:
                report = cls()
                report._load_from_dict(row)
                return report
            return None
            
        except Exception as e:
            print(f"获取报表失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_reports(cls, domain: str = "") -> List[Dict[str, Any]]:
        """获取报表列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["deleted = '0'"]
            params = []
            
            if domain:
                where_conditions.append("domain = %s")
                params.append(domain)
            
            where_clause = " AND ".join(where_conditions)
            
            cursor.execute(f"""
                SELECT * FROM zt_report
                WHERE {where_clause}
                ORDER BY id ASC
            """, params)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取报表列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "code": self.code,
            "desc": self.desc,
            "sql": self.sql,
            "vars": self.vars,
            "langs": self.langs,
            "domain": self.domain,
            "vision": self.vision,
            "public": self.public,
            "createdBy": self.createdBy,
            "createdDate": self.createdDate.isoformat() if self.createdDate else None,
            "editedBy": self.editedBy,
            "editedDate": self.editedDate.isoformat() if self.editedDate else None,
            "deleted": self.deleted
        }


class Dashboard:
    """仪表板模型类"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.name: str = ""
        self.desc: Optional[str] = None
        self.layout: Optional[str] = None
        self.public: str = "0"
        self.createdBy: str = ""
        self.createdDate: Optional[datetime] = None
        self.editedBy: str = ""
        self.editedDate: Optional[datetime] = None
        self.deleted: str = "0"

    @classmethod
    def get_by_id(cls, dashboard_id: int) -> Optional['Dashboard']:
        """根据ID获取仪表板"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_dashboard WHERE id = %s AND deleted = '0'
            """, (dashboard_id,))
            
            row = cursor.fetchone()
            if row:
                dashboard = cls()
                dashboard._load_from_dict(row)
                return dashboard
            return None
            
        except Exception as e:
            print(f"获取仪表板失败: {e}")
            return None
        finally:
            if conn:
                close_db_connection(conn)

    @classmethod
    def get_all_dashboards(cls) -> List[Dict[str, Any]]:
        """获取仪表板列表"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_dashboard
                WHERE deleted = '0'
                ORDER BY id ASC
            """)
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取仪表板列表失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def get_blocks(self) -> List[Dict[str, Any]]:
        """获取仪表板组件"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM zt_block
                WHERE dashboard = %s
                ORDER BY `order` ASC
            """, (self.id,))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"获取仪表板组件失败: {e}")
            return []
        finally:
            if conn:
                close_db_connection(conn)

    def _load_from_dict(self, data: Dict[str, Any]):
        """从字典加载数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self, include_blocks: bool = False) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            "id": self.id,
            "name": self.name,
            "desc": self.desc,
            "layout": self.layout,
            "public": self.public,
            "createdBy": self.createdBy,
            "createdDate": self.createdDate.isoformat() if self.createdDate else None,
            "editedBy": self.editedBy,
            "editedDate": self.editedDate.isoformat() if self.editedDate else None,
            "deleted": self.deleted
        }
        
        if include_blocks:
            data["blocks"] = self.get_blocks()
        
        return data


class StatService:
    """统计服务类"""
    
    @staticmethod
    def get_project_overview() -> Dict[str, Any]:
        """获取项目概览统计"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 项目总数
            cursor.execute("SELECT COUNT(*) as total FROM zt_project WHERE deleted = '0'")
            total_projects = cursor.fetchone()['total']
            
            # 进行中的项目
            cursor.execute("SELECT COUNT(*) as doing FROM zt_project WHERE status = 'doing' AND deleted = '0'")
            doing_projects = cursor.fetchone()['doing']
            
            # 已完成的项目
            cursor.execute("SELECT COUNT(*) as done FROM zt_project WHERE status = 'done' AND deleted = '0'")
            done_projects = cursor.fetchone()['done']
            
            # 延期项目
            cursor.execute("""
                SELECT COUNT(*) as delayed 
                FROM zt_project 
                WHERE status = 'doing' AND `end` < CURDATE() AND deleted = '0'
            """)
            delayed_projects = cursor.fetchone()['delayed']
            
            return {
                "total": total_projects,
                "doing": doing_projects,
                "done": done_projects,
                "delayed": delayed_projects,
                "completion_rate": round(done_projects / total_projects * 100, 2) if total_projects > 0 else 0
            }
            
        except Exception as e:
            print(f"获取项目概览失败: {e}")
            return {"total": 0, "doing": 0, "done": 0, "delayed": 0, "completion_rate": 0}
        finally:
            if conn:
                close_db_connection(conn)

    @staticmethod
    def get_story_statistics() -> Dict[str, Any]:
        """获取需求统计"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 需求总数
            cursor.execute("SELECT COUNT(*) as total FROM zt_story WHERE deleted = '0'")
            total_stories = cursor.fetchone()['total']
            
            # 各状态需求数
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_story 
                WHERE deleted = '0' 
                GROUP BY status
            """)
            status_stats = cursor.fetchall()
            
            # 按优先级统计
            cursor.execute("""
                SELECT pri, COUNT(*) as count 
                FROM zt_story 
                WHERE deleted = '0' 
                GROUP BY pri
            """)
            priority_stats = cursor.fetchall()
            
            return {
                "total": total_stories,
                "status_distribution": status_stats,
                "priority_distribution": priority_stats
            }
            
        except Exception as e:
            print(f"获取需求统计失败: {e}")
            return {"total": 0, "status_distribution": [], "priority_distribution": []}
        finally:
            if conn:
                close_db_connection(conn)

    @staticmethod
    def get_bug_trend(days: int = 30) -> Dict[str, Any]:
        """获取缺陷趋势"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 最近N天的缺陷趋势
            cursor.execute("""
                SELECT DATE(openedDate) as date, COUNT(*) as opened
                FROM zt_bug 
                WHERE openedDate >= DATE_SUB(CURDATE(), INTERVAL %s DAY) 
                AND deleted = '0'
                GROUP BY DATE(openedDate)
                ORDER BY date
            """, (days,))
            opened_trend = cursor.fetchall()
            
            # 最近N天的解决趋势
            cursor.execute("""
                SELECT DATE(resolvedDate) as date, COUNT(*) as resolved
                FROM zt_bug 
                WHERE resolvedDate >= DATE_SUB(CURDATE(), INTERVAL %s DAY) 
                AND deleted = '0'
                GROUP BY DATE(resolvedDate)
                ORDER BY date
            """, (days,))
            resolved_trend = cursor.fetchall()
            
            # 缺陷严重程度分布
            cursor.execute("""
                SELECT severity, COUNT(*) as count 
                FROM zt_bug 
                WHERE deleted = '0' 
                GROUP BY severity
            """)
            severity_stats = cursor.fetchall()
            
            return {
                "opened_trend": opened_trend,
                "resolved_trend": resolved_trend,
                "severity_distribution": severity_stats
            }
            
        except Exception as e:
            print(f"获取缺陷趋势失败: {e}")
            return {"opened_trend": [], "resolved_trend": [], "severity_distribution": []}
        finally:
            if conn:
                close_db_connection(conn)

    @staticmethod
    def get_effort_summary(start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取工时统计"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 如果没有指定日期，默认查询最近30天
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            # 按用户统计工时
            cursor.execute("""
                SELECT account, SUM(consumed) as total_hours
                FROM zt_effort 
                WHERE date BETWEEN %s AND %s
                GROUP BY account
                ORDER BY total_hours DESC
            """, (start_date, end_date))
            user_effort = cursor.fetchall()
            
            # 按日期统计工时
            cursor.execute("""
                SELECT date, SUM(consumed) as daily_hours
                FROM zt_effort 
                WHERE date BETWEEN %s AND %s
                GROUP BY date
                ORDER BY date
            """, (start_date, end_date))
            daily_effort = cursor.fetchall()
            
            # 按项目统计工时
            cursor.execute("""
                SELECT e.project, p.name as project_name, SUM(e.consumed) as project_hours
                FROM zt_effort e
                LEFT JOIN zt_project p ON e.project = p.id
                WHERE e.date BETWEEN %s AND %s
                GROUP BY e.project
                ORDER BY project_hours DESC
            """, (start_date, end_date))
            project_effort = cursor.fetchall()
            
            return {
                "user_effort": user_effort,
                "daily_effort": daily_effort,
                "project_effort": project_effort,
                "period": {"start": start_date, "end": end_date}
            }
            
        except Exception as e:
            print(f"获取工时统计失败: {e}")
            return {"user_effort": [], "daily_effort": [], "project_effort": [], "period": {}}
        finally:
            if conn:
                close_db_connection(conn)

    @staticmethod
    def get_test_execution() -> Dict[str, Any]:
        """获取测试执行统计"""
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 测试用例总数
            cursor.execute("SELECT COUNT(*) as total FROM zt_case WHERE deleted = '0'")
            total_cases = cursor.fetchone()['total']
            
            # 测试用例状态分布
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_case 
                WHERE deleted = '0' 
                GROUP BY status
            """)
            case_status = cursor.fetchall()
            
            # 测试任务统计
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zt_testtask 
                WHERE deleted = '0' 
                GROUP BY status
            """)
            task_status = cursor.fetchall()
            
            # 测试执行结果统计
            cursor.execute("""
                SELECT caseResult, COUNT(*) as count 
                FROM zt_testresult 
                GROUP BY caseResult
            """)
            result_stats = cursor.fetchall()
            
            return {
                "total_cases": total_cases,
                "case_status_distribution": case_status,
                "task_status_distribution": task_status,
                "execution_results": result_stats
            }
            
        except Exception as e:
            print(f"获取测试执行统计失败: {e}")
            return {"total_cases": 0, "case_status_distribution": [], "task_status_distribution": [], "execution_results": []}
        finally:
            if conn:
                close_db_connection(conn)
