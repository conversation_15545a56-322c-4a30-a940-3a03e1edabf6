#!/usr/bin/env python3
"""
测试改进的文本清理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_parser import DocumentParser

def test_gibberish_detection():
    """测试乱码检测功能"""
    print("🔧 测试乱码检测功能...")
    
    parser = DocumentParser()
    
    test_cases = [
        ("鄥燆鵒", True, "纯乱码"),
        ("年科技条线半年工作报告发言稿", False, "正常中文"),
        ("各位领导", False, "正常中文"),
        ("縹剉錧", True, "纯乱码"),
        ("科技项目建设情况", False, "正常中文"),
        ("鷁緥臽礠茤釼褃", True, "纯乱码"),
        ("上半年集团科技项目", False, "正常中文"),
        ("大家好", False, "正常中文"),
        ("Root Entry", True, "OLE结构信息"),
    ]
    
    for text, expected_gibberish, description in test_cases:
        is_gibberish = parser._is_pure_gibberish(text)
        status = "✅" if is_gibberish == expected_gibberish else "❌"
        print(f"{status} {text} -> {description} (检测为乱码: {is_gibberish})")

def test_mixed_text_cleaning():
    """测试混合文本清理功能"""
    print("\n🔧 测试混合文本清理功能...")
    
    parser = DocumentParser()
    
    test_cases = [
        "鄥燆鵒年科技条线半年工作报告发言稿",
        "各位领导縹剉錧大家好",
        "科技项目建设鷁緥臽礠情况",
        "上半年集团科技项目茤釼褃的开工进度",
    ]
    
    for text in test_cases:
        cleaned = parser._clean_mixed_text(text)
        print(f"原文: {text}")
        print(f"清理后: {cleaned}")
        print("---")

def test_meaningful_chinese_extraction():
    """测试有意义中文提取功能"""
    print("\n🔧 测试有意义中文提取功能...")
    
    parser = DocumentParser()
    
    test_text = """
    鄥燆鵒2025年科技条线半年工作报告发言稿各位领导縹剉錧大家好下面我就2025年上半年科技条线的工作情况
    向大家做简要汇报第一部分科技项目建设鷁緥臽礠科技项目建设情况我们来看一下上半年科技项目的整体建设情况
    """
    
    meaningful_texts = parser._extract_meaningful_chinese(test_text)
    print("提取到的有意义中文:")
    for text in meaningful_texts:
        print(f"- {text}")

def test_intelligent_cleaning():
    """测试智能文本清理功能"""
    print("\n🔧 测试智能文本清理功能...")
    
    parser = DocumentParser()
    
    # 模拟从.doc文件提取的混合文本
    mixed_texts = [
        "鄥燆鵒",  # 纯乱码，应该被过滤
        "年科技条线半年工作报告发言稿",  # 正常文本，应该保留
        "各位领导",  # 正常文本，应该保留
        "Root Entry",  # OLE结构信息，应该被过滤
        "縹剉錧",  # 纯乱码，应该被过滤
        "科技项目建设情况",  # 正常文本，应该保留
        "SummaryInformation",  # OLE结构信息，应该被过滤
        "鷁緥臽礠茤釼褃",  # 纯乱码，应该被过滤
        "上半年集团科技项目的开工进度正常",  # 正常文本，应该保留
        "大家好",  # 正常文本，应该保留
    ]
    
    print("原始文本列表:")
    for i, text in enumerate(mixed_texts):
        print(f"{i+1}. {text}")
    
    cleaned_texts = parser._intelligent_text_cleaning(mixed_texts)
    
    print("\n清理后的文本列表:")
    for i, text in enumerate(cleaned_texts):
        print(f"{i+1}. {text}")
    
    print(f"\n统计: 原始{len(mixed_texts)}个 -> 清理后{len(cleaned_texts)}个")

def test_complete_extraction():
    """测试完整的文档内容提取"""
    print("\n🔧 测试完整的文档内容提取...")
    
    parser = DocumentParser()
    
    # 创建包含混合内容的模拟.doc文件
    ole_header = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
    
    # 混合的中文内容（包含乱码和正文）
    mixed_content = """
    鄥燆鵒2025年科技条线半年工作报告发言稿各位领导大家好下面我就2025年上半年科技条线的工作情况
    向大家做简要汇报第一部分科技项目建设縹剉錧科技项目建设情况我们来看一下上半年科技项目的整体建设情况
    整体情况总体来看上半年集团科技项目鷁緥臽礠茤釼褃上半年集团科技项目的开工进度正常但预算执行慢于时间进度
    请大家关注几个核心数据
    """.encode('utf-16le')
    
    mock_doc = ole_header + b'\x00' * 200 + mixed_content + b'\x00' * 500
    
    result = parser._extract_word_document_content_only(mock_doc)
    
    print("完整提取结果:")
    print(result)
    
    # 检查结果质量
    if "鄥燆鵒" not in result and "縹剉錧" not in result:
        print("✅ 成功过滤掉乱码字符")
    else:
        print("❌ 仍包含乱码字符")
    
    if "年科技条线" in result and "各位领导" in result:
        print("✅ 成功保留有意义的内容")
    else:
        print("❌ 丢失了有意义的内容")

if __name__ == "__main__":
    print("🔧 测试改进的文本清理功能")
    print("=" * 60)
    
    try:
        # 测试乱码检测
        test_gibberish_detection()
        
        # 测试混合文本清理
        test_mixed_text_cleaning()
        
        # 测试有意义中文提取
        test_meaningful_chinese_extraction()
        
        # 测试智能清理
        test_intelligent_cleaning()
        
        # 测试完整提取
        test_complete_extraction()
        
        print("\n✅ 所有测试完成")
        print("💡 现在可以重新启动后端并测试实际的.doc文件")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
