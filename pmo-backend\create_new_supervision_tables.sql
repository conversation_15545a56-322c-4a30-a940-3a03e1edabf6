-- 重新设计督办管理数据库表
-- 基于督办表文件的完整结构

-- 1. 督办事项主表
DROP TABLE IF EXISTS supervision_items;
CREATE TABLE supervision_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sequence_number INT NOT NULL COMMENT '序号',
    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
    is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
    progress_description TEXT COMMENT '7月28日进度情况',
    overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') DEFAULT 'X 未启动' COMMENT '整体进度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',
    INDEX idx_sequence_number (sequence_number),
    INDEX idx_work_dimension (work_dimension),
    INDEX idx_overall_progress (overall_progress)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项主表';

-- 2. 公司表
DROP TABLE IF EXISTS companies;
CREATE TABLE companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_code (company_code),
    INDEX idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表';

-- 3. 公司督办状态表
DROP TABLE IF EXISTS company_supervision_status;
CREATE TABLE company_supervision_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
    company_id INT NOT NULL COMMENT '公司ID',
    status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行',
    progress_description TEXT COMMENT '当前进展情况',
    existing_problems TEXT COMMENT '存在问题',
    next_plan TEXT COMMENT '下一步计划',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY uk_item_company (supervision_item_id, company_id),
    INDEX idx_supervision_item_id (supervision_item_id),
    INDEX idx_company_id (company_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司督办状态表';

-- 4. 督办状态变更历史表
DROP TABLE IF EXISTS supervision_status_history;
CREATE TABLE supervision_status_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
    company_id INT NOT NULL COMMENT '公司ID',
    old_status ENUM('√', 'O', '！', 'X', '—') COMMENT '原状态',
    new_status ENUM('√', 'O', '！', 'X', '—') COMMENT '新状态',
    old_progress_description TEXT COMMENT '原进展情况',
    new_progress_description TEXT COMMENT '新进展情况',
    old_existing_problems TEXT COMMENT '原存在问题',
    new_existing_problems TEXT COMMENT '新存在问题',
    old_next_plan TEXT COMMENT '原下一步计划',
    new_next_plan TEXT COMMENT '新下一步计划',
    change_reason VARCHAR(500) COMMENT '变更原因',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_supervision_item_id (supervision_item_id),
    INDEX idx_company_id (company_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办状态变更历史表';

-- 插入公司数据
INSERT INTO companies (company_code, company_name, display_order) VALUES
('CXBX', '财险', 1),
('SXBX', '寿险', 2),
('JINZU', '金租', 3),
('ZICHAN', '资管', 4),
('GUANGZU', '广租', 5),
('TONGSHENG', '通盛', 6),
('DANBAO', '担保', 7),
('XIAODAI', '小贷', 8),
('BAOLI', '保理', 9),
('BUDONGCHAN', '不动产', 10),
('ZHENGXIN', '征信', 11),
('JINFU', '金服', 12),
('BENBU', '本部', 13);

-- 插入督办事项数据（基于督办表文件）
INSERT INTO supervision_items (sequence_number, work_dimension, work_theme, supervision_source, work_content, is_annual_assessment, completion_deadline, progress_description, overall_progress) VALUES
(1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', '否', '5月末', '各部门及子公司已制定ITBP对接人，数字金服已按条线建立ITBP服务团队，对重点项目进行"周跟踪"。', '√ 已完成'),
(2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会', '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。', '否', '4月末', '已建立公共台账实时反应项目红绿灯，子公司每月根据科委会公布的红绿灯对项目进行"月复盘"。', '√ 已完成'),
(3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会', '各子公司参照集团印发的信息化管理制度，印发包括科技风险、项目管理等8个制度，并落地执行。完成标志：各单位对照集团信息化制度，检查和印发自身信息化管理制度清单。', '否', '8月末', '本部制度印发已较为完整；其他企业仍缺部分制度，8月底完成印发。', 'O进行中'),
(4, '一、监管和制度', '印发非信创采购管理制度', '7月科技例会', '科委办及各单位参照广投集团印发非信创采购管理制度，含含非信创设备评审流程，并落地执行。完成标志：科委办及各单位对照广投制度，印发非信创制度。', '否', '10月末', '广投集团的非信创采购管理制度未印发，待其印发后，科委办及各单位参照印发。', 'X 未启动'),
(5, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会', '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：各单位配合完成集团下达的财务，风控和经营18项指标数据治理，三率（准确、及时、T+1）90%。', '是', '4月末', '已完成第一批次，财务，风控和经营18项指标数据治理', '√ 已完成'),
(6, '二、数据治理和系统覆盖', '第二批次数据治理', '5月科技例会', '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：涉及经营、风控、人力的152项数据治理。三率（准确、及时、T+1）90%。', '是', '11月末', '第二批次涉及经营、风控、人力的152项指标，经营、风控的22项已基本完成，人力的130项正在进行中。', 'O进行中'),
(7, '二、数据治理和系统覆盖', '100%落实集团本部各管理条线要求', '3月科技例会', '以集团条线要求为基准，梳理本部各条线系统使用情况，各单位落实集团管理条线的覆盖度达到100%。完成标志：集团管理条线提出覆盖要求，科委办统筹推广，各单位负责落地，确保本部条线管理的系统在各单位100%覆盖使用。', '是', '10月末', '结合信创对124个系统排查，截至7月末，财务、风控等基本覆盖，审计、印控等未完全覆盖，各管理部门需明确覆盖要求，科委办统筹10月末落实。', 'O进行中'),
(8, '二、数据治理和系统覆盖', '业务中台接入', '5月科技例会', '重点租赁、通盛租赁、广西租赁、金控资管接入业务中台的事项需在6月启动接入工作，与科委办确认对接方案、实施路径、时间计划等，并纳入年终考核。完成标志：四家公司业务系统接入业务中台，统一集团的信用评级等风险管理。', '是', '11月末', '完成初步梳理，因工作量较大，需加快配合推进', 'O进行中'),
(9, '三、网络和数据安全', '完成数据防泄漏方案并实施', '4月科技例会', '制定和实施DLP数据防泄漏方案。完成标志：各单位配合DLP数据防泄漏方案的评审和实施。', '否', '6月末', '完成意见征集和方案定稿（北部湾保险和国富人寿参照实施），立项中', '！逾期'),
(10, '三、网络和数据安全', '集团金投云方案的意见征集', '4月科技例会', '建立"统一云平台"、"统一灾备体系"、"统一网络安全"完成标志：科委办统筹建设，各单位配合完成金投云方案的确定，完成第一轮意见征集。', '是', '5月末', '已完成第二轮意见征集', '√ 已完成');
