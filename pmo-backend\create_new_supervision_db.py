#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建新的督办管理数据库表
"""

import pymysql

def create_new_supervision_db():
    """创建新的督办管理数据库"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        # 读取SQL文件
        with open('create_new_supervision_tables.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("📄 SQL文件读取成功")
        
        # 分割SQL语句
        sql_statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue
                
            current_statement += line + '\n'
            
            if line.endswith(';'):
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        # 执行SQL语句
        with connection.cursor() as cursor:
            for i, statement in enumerate(sql_statements):
                if statement:
                    try:
                        print(f"📋 执行SQL语句 {i+1}/{len(sql_statements)}")
                        cursor.execute(statement)
                        connection.commit()
                        print(f"✅ SQL语句 {i+1} 执行成功")
                    except Exception as e:
                        print(f"❌ SQL语句 {i+1} 执行失败: {str(e)}")
                        print(f"语句内容: {statement[:100]}...")
                        # 继续执行其他语句
        
        # 为每个督办事项创建公司状态记录
        print("📊 创建公司状态记录...")
        with connection.cursor() as cursor:
            # 获取所有督办事项
            cursor.execute("SELECT id FROM supervision_items")
            items = cursor.fetchall()
            
            # 获取所有公司
            cursor.execute("SELECT id FROM companies WHERE is_active = 1")
            companies = cursor.fetchall()
            
            # 根据督办表文件的实际状态插入数据
            status_data = {
                # 第1项：建立条线ITBP团队管理办法 - 全部已完成
                1: {'CXBX': '√', 'SXBX': '√', 'JINZU': '√', 'ZICHAN': '√', 'GUANGZU': '√', 'TONGSHENG': '√', 'DANBAO': '√', 'XIAODAI': '√', 'BAOLI': '√', 'BUDONGCHAN': '√', 'ZHENGXIN': '√', 'JINFU': '√', 'BENBU': '√'},
                # 第2项：建立项目红绿灯管理办法 - 全部已完成
                2: {'CXBX': '√', 'SXBX': '√', 'JINZU': '√', 'ZICHAN': '√', 'GUANGZU': '√', 'TONGSHENG': '√', 'DANBAO': '√', 'XIAODAI': '√', 'BAOLI': '√', 'BUDONGCHAN': '√', 'ZHENGXIN': '√', 'JINFU': '√', 'BENBU': '√'},
                # 第3项：印发8个信息化管理制度 - 本部已完成，其他进行中
                3: {'CXBX': 'O', 'SXBX': 'O', 'JINZU': 'O', 'ZICHAN': 'O', 'GUANGZU': 'O', 'TONGSHENG': 'O', 'DANBAO': 'O', 'XIAODAI': 'O', 'BAOLI': 'O', 'BUDONGCHAN': 'O', 'ZHENGXIN': 'O', 'JINFU': 'O', 'BENBU': '√'},
                # 第4项：印发非信创采购管理制度 - 全部未启动
                4: {'CXBX': 'X', 'SXBX': 'X', 'JINZU': 'X', 'ZICHAN': 'X', 'GUANGZU': 'X', 'TONGSHENG': 'X', 'DANBAO': 'X', 'XIAODAI': 'X', 'BAOLI': 'X', 'BUDONGCHAN': 'X', 'ZHENGXIN': 'X', 'JINFU': 'X', 'BENBU': 'X'},
                # 第5项：第一批次数据治理 - 财险已完成，寿险不需要执行，其他已完成
                5: {'CXBX': '√', 'SXBX': '—', 'JINZU': '√', 'ZICHAN': '√', 'GUANGZU': '√', 'TONGSHENG': '√', 'DANBAO': '√', 'XIAODAI': '√', 'BAOLI': '√', 'BUDONGCHAN': '√', 'ZHENGXIN': '√', 'JINFU': '√', 'BENBU': '√'},
            }
            
            # 获取公司代码到ID的映射
            cursor.execute("SELECT id, company_code FROM companies")
            company_mapping = {row[1]: row[0] for row in cursor.fetchall()}
            
            for item in items:
                item_id = item[0]
                for company in companies:
                    company_id = company[0]
                    
                    # 获取公司代码
                    cursor.execute("SELECT company_code FROM companies WHERE id = %s", (company_id,))
                    company_code = cursor.fetchone()[0]
                    
                    # 获取状态
                    if item_id in status_data and company_code in status_data[item_id]:
                        status = status_data[item_id][company_code]
                    else:
                        status = 'X'  # 默认未启动
                    
                    cursor.execute("""
                        INSERT IGNORE INTO company_supervision_status 
                        (supervision_item_id, company_id, status)
                        VALUES (%s, %s, %s)
                    """, (item_id, company_id, status))
            
            connection.commit()
        
        # 检查创建结果
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            print(f"📊 创建结果:")
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   状态记录: {status_count} 条")
        
        print("🎉 新督办管理数据库创建完成！")
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    create_new_supervision_db()
