"""
智能上下文生成器
根据用户查询类型动态生成相关上下文
"""

import re
from typing import Dict, Any, List, Optional
from app.core.logger import get_logger
from app.services.knowledge_service import pmo_knowledge_service

# 尝试导入轻量级向量服务
try:
    from app.services.lightweight_vector_service import lightweight_vector_service
    VECTOR_SERVICE_AVAILABLE = True
except ImportError:
    VECTOR_SERVICE_AVAILABLE = False
    lightweight_vector_service = None

logger = get_logger(__name__)

class SmartContextGenerator:
    """智能上下文生成器"""
    
    def __init__(self):
        self.entity_keywords = [
            '汽租', '商租', '金租', '征信', '资管', '集团战略部', '集团财务部', 
            '小贷', '集团', '不动产', '担保', '集团保全部', '集团风控部', 
            '集团办公室', '集团人力部', '集团协同部', '金服', '外部主体', 
            '集团法规部', '集团审计部', '保理', '财险'
        ]
        
        self.query_patterns = {
            'summary': [
                r'总共|总数|多少个|统计|概览|汇总',
                r'各个.*数量|分布情况|整体情况'
            ],
            'entity_specific': [
                r'(' + '|'.join(self.entity_keywords) + r')',
                r'.*部门.*|.*主体.*'
            ],
            'overdue': [
                r'逾期|延期|滞后|超期',
                r'黑榜|延误'
            ],
            'progress': [
                r'进度|开工|实施|完成',
                r'红榜|推进'
            ],
            'budget': [
                r'预算|资金|投资|金额',
                r'费用|成本'
            ],
            'labor': [
                r'工时|人工费|人力|成本',
                r'工作量|人员'
            ],
            'ranking': [
                r'排名|排行|榜单',
                r'红榜|黑榜|排序'
            ],
            'itbp': [
                r'ITBP|itbp|IT.*?BP|it.*?bp',
                r'ITBP.*?团队|ITBP.*?人员|ITBP.*?成员',
                r'业务.*?伙伴|IT.*?业务.*?伙伴|团队.*?人员|团队.*?成员'
            ],
            'recent': [
                r'最新|最近|今天|昨天|本月|上月',
                r'近期|当前|现在'
            ]
        }
    
    def generate_context(self, user_query: str) -> str:
        """根据用户查询生成智能上下文（增强版）"""
        try:
            logger.info(f"为查询生成上下文: {user_query[:50]}...")

            # 1. 尝试使用向量搜索获取相关上下文
            vector_context = ""
            if VECTOR_SERVICE_AVAILABLE and lightweight_vector_service and lightweight_vector_service.is_available():
                logger.info("🔍 使用轻量级向量搜索...")
                vector_context = lightweight_vector_service.get_relevant_context(user_query, max_context_length=1000)
                if vector_context:
                    logger.info(f"✅ 向量搜索找到相关内容，长度: {len(vector_context)} 字符")
                else:
                    logger.info("⚠️ 向量搜索未找到相关内容")
            else:
                logger.info("⚠️ 向量服务不可用，使用传统方法")

            # 2. 获取基础知识库
            base_knowledge = pmo_knowledge_service.get_knowledge_base()

            # 3. 分析查询类型
            query_types = self._analyze_query_types(user_query)
            logger.info(f"查询类型分析结果: {query_types}")

            # 4. 提取提及的投资主体
            mentioned_entities = self._extract_mentioned_entities(user_query)
            logger.info(f"提及的投资主体: {mentioned_entities}")

            # 5. 根据查询类型生成专门的上下文
            specialized_context = self._generate_specialized_context(query_types, mentioned_entities)

            # 6. 组合最终上下文（优先使用向量搜索结果）
            if vector_context:
                final_context = self._combine_enhanced_context(vector_context, specialized_context, user_query)
            else:
                final_context = self._combine_context(base_knowledge, specialized_context, user_query)

            # 7. 控制上下文长度
            final_context = self._limit_context_length(final_context)

            logger.info(f"上下文生成完成，长度: {len(final_context)} 字符")
            return final_context
            
        except Exception as e:
            logger.error(f"生成上下文失败: {str(e)}")
            # 返回基础知识库作为备用
            return pmo_knowledge_service.get_knowledge_base() + f"\n\n用户问题：{user_query}\n请基于上述PMO系统数据回答。"
    
    def _analyze_query_types(self, query: str) -> List[str]:
        """分析查询类型"""
        query_lower = query.lower()
        detected_types = []
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    detected_types.append(query_type)
                    break
        
        # 如果没有检测到特定类型，默认为汇总查询
        if not detected_types:
            detected_types.append('summary')
        
        return list(set(detected_types))  # 去重
    
    def _extract_mentioned_entities(self, query: str) -> List[str]:
        """提取查询中提及的投资主体"""
        mentioned = []
        
        for entity in self.entity_keywords:
            if entity in query:
                mentioned.append(entity)
        
        return mentioned
    
    def _generate_specialized_context(self, query_types: List[str], mentioned_entities: List[str]) -> str:
        """生成专门的上下文信息"""
        specialized_parts = []
        
        try:
            # 获取缓存的知识库数据
            entity_stats = pmo_knowledge_service.knowledge_cache.get('entity_stats', {})
            red_black_data = pmo_knowledge_service.knowledge_cache.get('red_black_board', {})
            
            # 如果提及了特定投资主体，添加详细信息
            if mentioned_entities and entity_stats:
                specialized_parts.append("## 相关投资主体详细信息")
                for entity in mentioned_entities:
                    if entity in entity_stats:
                        stats = entity_stats[entity]
                        specialized_parts.append(f"""
### {entity}详细数据
- 项目总数：{stats['total_projects']}个
- 总预算：{stats['total_budget']}万元
- 实施项目：{stats['implementation_projects']}个
- 实施预算：{stats['implementation_budget']}万元
- 逾期项目：{stats['delayed_projects']}个
- 逾期预算：{stats['delayed_budget']}万元
- 开工率：{stats['completion_rate']}%
- 工时：{stats['working_hours']}人次
- 人工费：{stats['labor_cost']}万元
- 当前状态：{stats['status']}""")
            
            # 如果查询逾期相关，添加逾期详情
            if 'overdue' in query_types and red_black_data:
                delayed_list = red_black_data.get('delayedList', [])
                if delayed_list:
                    specialized_parts.append("## 逾期项目详情")
                    for entity in delayed_list[:5]:  # 只显示前5个
                        specialized_parts.append(f"- {entity.get('entity', '')}: {entity.get('delayedCount', 0)}个逾期项目，逾期预算{entity.get('delayedBudget', 0)}万元")
            
            # 如果查询排名相关，添加排名详情
            if 'ranking' in query_types and red_black_data:
                red_entities = red_black_data.get('redEntities', [])
                black_entities = red_black_data.get('blackEntities', [])
                
                specialized_parts.append("## 红黑榜详细排名")
                if red_entities:
                    specialized_parts.append(f"红榜（无逾期，按开工金额排序）：{', '.join(red_entities)}")
                if black_entities:
                    specialized_parts.append(f"黑榜（有逾期，按逾期金额排序）：{', '.join(black_entities)}")
            
            # 如果查询预算相关，添加预算分析
            if 'budget' in query_types and entity_stats:
                specialized_parts.append("## 预算分析")
                total_budget = sum(stats['total_budget'] for stats in entity_stats.values())
                total_impl_budget = sum(stats['implementation_budget'] for stats in entity_stats.values())
                specialized_parts.append(f"- 总预算：{total_budget}万元")
                specialized_parts.append(f"- 实施预算：{total_impl_budget}万元")
                specialized_parts.append(f"- 整体开工率：{(total_impl_budget/total_budget*100) if total_budget > 0 else 0:.1f}%")
            
            # 如果查询人工费相关，添加人工费分析
            if 'labor' in query_types and entity_stats:
                specialized_parts.append("## 人工费分析")
                total_labor_cost = sum(stats['labor_cost'] for stats in entity_stats.values())
                total_working_hours = sum(stats['working_hours'] for stats in entity_stats.values())
                specialized_parts.append(f"- 总人工费：{total_labor_cost}万元")
                specialized_parts.append(f"- 总工时：{total_working_hours}人次")
                if total_working_hours > 0:
                    avg_cost_per_hour = total_labor_cost * 10000 / total_working_hours
                    specialized_parts.append(f"- 平均人工成本：{avg_cost_per_hour:.0f}元/人次")

            # 如果查询ITBP相关，添加ITBP团队详细信息
            if 'itbp' in query_types:
                itbp_data = pmo_knowledge_service.knowledge_cache.get('itbp_teams', {})
                if itbp_data:
                    specialized_parts.append("## ITBP团队详细信息")
                    specialized_parts.append("### 各投资主体ITBP团队成员")

                    for entity, members in itbp_data.items():
                        if members:
                            specialized_parts.append(f"\n**{entity}** ({len(members)}人):")
                            for member in members:
                                specialized_parts.append(f"- {member.get('name', '未知')} (用户ID: {member.get('user_id', '未知')})")

                    # 添加ITBP团队统计
                    total_itbp_members = sum(len(members) for members in itbp_data.values())
                    total_entities_with_itbp = len([entity for entity, members in itbp_data.items() if members])
                    specialized_parts.append(f"\n### ITBP团队统计")
                    specialized_parts.append(f"- 总ITBP成员数：{total_itbp_members}人")
                    specialized_parts.append(f"- 有ITBP团队的投资主体：{total_entities_with_itbp}个")

                    # 按团队规模排序
                    sorted_teams = sorted(itbp_data.items(), key=lambda x: len(x[1]), reverse=True)
                    if sorted_teams:
                        specialized_parts.append(f"- 最大ITBP团队：{sorted_teams[0][0]} ({len(sorted_teams[0][1])}人)")
                        if len(sorted_teams) > 1:
                            specialized_parts.append(f"- 最小ITBP团队：{sorted_teams[-1][0]} ({len(sorted_teams[-1][1])}人)")
                else:
                    specialized_parts.append("## ITBP团队信息")
                    specialized_parts.append("暂无ITBP团队详细数据，请检查数据源。")
            
        except Exception as e:
            logger.error(f"生成专门上下文失败: {str(e)}")
        
        return "\n".join(specialized_parts)
    
    def _combine_context(self, base_knowledge: str, specialized_context: str, user_query: str) -> str:
        """组合最终上下文"""
        context_parts = [base_knowledge]
        
        if specialized_context.strip():
            context_parts.append(specialized_context)
        
        context_parts.append(f"""
---
用户问题：{user_query}

请基于上述PMO系统的实时数据准确回答用户问题。回答要求：
1. 使用Markdown格式输出
2. 数据要准确，直接引用上述统计信息
3. 如果涉及具体数字，请提供准确的数值
4. 语言要专业、友好、易懂
5. 如果用户问题涉及多个投资主体，请提供对比分析
""")
        
        return "\n".join(context_parts)
    
    def _limit_context_length(self, context: str, max_length: int = 20000) -> str:
        """限制上下文长度"""
        if len(context) <= max_length:
            return context
        
        # 如果超长，保留前面的重要信息，截断后面的详细信息
        truncated = context[:max_length]
        
        # 找到最后一个完整的段落
        last_section = truncated.rfind('\n## ')
        if last_section > max_length * 0.8:  # 如果截断点在后80%，就在那里截断
            truncated = truncated[:last_section]
        
        truncated += "\n\n...(内容已截断，如需更详细信息请具体询问)"
        
        return truncated

    def _combine_enhanced_context(self, vector_context: str, specialized_context: str, user_query: str) -> str:
        """组合增强版上下文（使用向量搜索结果）"""
        context_parts = []

        # 1. 添加向量搜索的相关内容
        if vector_context.strip():
            context_parts.append("# 相关知识（语义搜索结果）")
            context_parts.append(vector_context)

        # 2. 添加专门的上下文
        if specialized_context.strip():
            context_parts.append("\n# 专项数据分析")
            context_parts.append(specialized_context)

        # 3. 添加回答指导
        context_parts.append(f"""
---
用户问题：{user_query}

请基于上述PMO系统的实时数据准确回答用户问题。回答要求：
1. 优先使用语义搜索找到的相关知识
2. 结合专项数据分析提供准确答案
3. 使用Markdown格式输出
4. 数据要准确，直接引用上述统计信息
5. 如果涉及具体数字，请提供准确的数值
6. 语言要专业、友好、易懂
7. 如果用户问题涉及多个投资主体，请提供对比分析
8. 避免推测，只基于已有数据回答
""")

        return "\n".join(context_parts)

# 全局上下文生成器实例
smart_context_generator = SmartContextGenerator()
