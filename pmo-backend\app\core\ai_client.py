#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI客户端 - 支持多种AI模型的统一接口
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import logging

# AI模型客户端
try:
    import openai
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import dashscope
    from dashscope import Generation
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False

logger = logging.getLogger(__name__)

class AIClient:
    """AI客户端 - 统一的AI模型接口"""
    
    def __init__(self):
        self.provider = os.getenv("AI_PROVIDER", "mock")  # qwen, openai, mock
        self.api_key = os.getenv("AI_API_KEY", "sk-test-key")
        self.base_url = os.getenv("AI_BASE_URL", "")
        self.model = os.getenv("AI_MODEL", "qwen-turbo")

        # 如果没有配置有效的API密钥，使用模拟模式
        if not self.api_key or self.api_key == "sk-test-key":
            self.provider = "mock"
            logger.info("🎭 使用模拟AI客户端")

        # 初始化客户端
        self._init_client()
    
    def _init_client(self):
        """初始化AI客户端"""
        try:
            if self.provider == "qwen" and DASHSCOPE_AVAILABLE and self.api_key != "sk-test-key":
                dashscope.api_key = self.api_key
                logger.info("✅ 通义千问客户端初始化成功")

            elif self.provider == "openai" and OPENAI_AVAILABLE and self.api_key != "sk-test-key":
                self.openai_client = AsyncOpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url if self.base_url else None
                )
                logger.info("✅ OpenAI客户端初始化成功")

            else:
                self.provider = "mock"
                logger.info("🎭 使用模拟AI响应")
                
        except Exception as e:
            logger.error(f"❌ AI客户端初始化失败: {str(e)}")
    
    async def generate_response(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """生成AI响应"""
        try:
            if self.provider == "qwen":
                return await self._qwen_generate(prompt, system_prompt, **kwargs)
            elif self.provider == "openai":
                return await self._openai_generate(prompt, system_prompt, **kwargs)
            else:
                return await self._mock_generate(prompt, system_prompt, **kwargs)
                
        except Exception as e:
            logger.error(f"❌ AI响应生成失败: {str(e)}")
            return self._get_fallback_response(prompt)
    
    async def _qwen_generate(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """通义千问生成响应"""
        if not DASHSCOPE_AVAILABLE:
            return await self._mock_generate(prompt, system_prompt, **kwargs)
        
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = Generation.call(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", 0.7),
                max_tokens=kwargs.get("max_tokens", 2000),
                result_format='message'
            )
            
            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                logger.error(f"通义千问API错误: {response.message}")
                return self._get_fallback_response(prompt)
                
        except Exception as e:
            logger.error(f"通义千问调用失败: {str(e)}")
            return self._get_fallback_response(prompt)
    
    async def _openai_generate(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """OpenAI生成响应"""
        if not OPENAI_AVAILABLE or not hasattr(self, 'openai_client'):
            return await self._mock_generate(prompt, system_prompt, **kwargs)
        
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=kwargs.get("temperature", 0.7),
                max_tokens=kwargs.get("max_tokens", 2000)
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI调用失败: {str(e)}")
            return self._get_fallback_response(prompt)
    
    async def _mock_generate(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """模拟AI响应 - 用于测试和演示"""
        await asyncio.sleep(0.5)  # 模拟网络延迟

        # 检查是否是SQL分析请求 - 更宽泛的条件判断
        if ("JSON格式返回" in system_prompt or "生成SQL查询" in prompt or "SQL查询" in prompt):
            # 这是SQL分析请求，返回JSON格式，基于真实数据库表结构
            if "小贷" in prompt or "小额贷款" in prompt:
                return json.dumps({
                    "sql": "SELECT project_id, project_name, summary, category_level1, category_level2 FROM project_plan WHERE project_name LIKE '%小贷%' OR project_name LIKE '%小额贷款%' OR summary LIKE '%小贷%' OR summary LIKE '%小额贷款%'",
                    "explanation": "查询项目计划表中包含小贷或小额贷款相关的项目信息",
                    "chart_suggestion": {
                        "type": "table",
                        "title": "小贷相关项目列表",
                        "x_axis": "project_name",
                        "y_axis": "category_level1"
                    }
                }, ensure_ascii=False)
            elif "AI" in prompt and "项目" in prompt:
                return json.dumps({
                    "sql": "SELECT project_id, project_name, summary, category_level1, category_level2, priority FROM project_plan WHERE project_name LIKE '%AI%' OR summary LIKE '%AI%' OR category_level1 LIKE '%AI%'",
                    "explanation": "查询项目计划表中包含AI相关的项目信息",
                    "chart_suggestion": {
                        "type": "bar",
                        "title": "AI相关项目分布",
                        "x_axis": "category_level2",
                        "y_axis": "count"
                    }
                }, ensure_ascii=False)
            elif "项目" in prompt:
                return json.dumps({
                    "sql": "SELECT project_id, project_name, category_level1, category_level2, priority, total_budget FROM project_plan ORDER BY total_budget DESC LIMIT 10",
                    "explanation": "查询项目计划表中的项目基本信息，按预算排序",
                    "chart_suggestion": {
                        "type": "bar",
                        "title": "项目预算情况",
                        "x_axis": "project_name",
                        "y_axis": "total_budget"
                    }
                }, ensure_ascii=False)

        # 检查是否是自然语言回答生成请求
        elif "用户问题" in prompt and "查询结果摘要" in prompt:
            # 这是生成自然语言回答的请求
            if "小贷" in prompt or "小额贷款" in prompt:
                return "根据查询结果，系统中目前有3个小贷相关项目：包括个人消费贷、小微企业贷和农户贷项目。这些项目总投资额约2.5亿元，主要服务于不同的客户群体。"
            elif "项目" in prompt:
                return "根据查询结果，系统中当前有多个活跃项目，投资规模从几百万到数亿元不等。主要集中在基础设施建设、科技创新和民生改善等领域。"
            else:
                return "根据查询结果，已为您找到相关数据，请查看详细信息。"

        # 其他类型的请求
        elif "洞察" in prompt or "分析" in prompt:
            return "基于当前数据分析，建议关注项目进度和投资效率，确保按计划推进。"
        elif "图表" in prompt or "可视化" in prompt:
            return json.dumps({
                "chart_type": "bar",
                "title": "数据分析图表",
                "data": {
                    "labels": ["类别A", "类别B", "类别C"],
                    "values": [75, 60, 90]
                }
            })
        else:
            return "已收到您的请求，正在处理中..."
    
    def _get_fallback_response(self, prompt: str) -> str:
        """获取备用响应"""
        return f"抱歉，AI服务暂时不可用。您的问题是：{prompt[:100]}..."
    
    async def analyze_sql_query(self, question: str, database_schema: Dict[str, Any]) -> Dict[str, Any]:
        """分析自然语言问题并生成SQL查询"""
        
        schema_info = self._format_schema_info(database_schema)
        
        system_prompt = f"""
        你是一个专业的SQL查询生成助手。根据用户的自然语言问题，生成准确的SQL查询。
        
        数据库结构信息：
        {schema_info}
        
        请严格按照以下JSON格式返回：
        {{
            "sql": "生成的SQL查询语句",
            "explanation": "查询逻辑说明",
            "chart_suggestion": {{
                "type": "图表类型(bar/line/pie/table)",
                "title": "图表标题",
                "x_axis": "X轴字段",
                "y_axis": "Y轴字段"
            }}
        }}
        """
        
        user_prompt = f"请为以下问题生成SQL查询：{question}"
        
        try:
            response = await self.generate_response(user_prompt, system_prompt)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                return result
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取SQL
                return {
                    "sql": self._extract_sql_from_text(response),
                    "explanation": "AI生成的查询",
                    "chart_suggestion": {
                        "type": "table",
                        "title": "查询结果",
                        "x_axis": "",
                        "y_axis": ""
                    }
                }
                
        except Exception as e:
            logger.error(f"SQL分析失败: {str(e)}")
            # 根据问题关键词返回默认查询
            if "小贷" in question or "小额贷款" in question:
                return {
                    "sql": "SELECT project_id, project_name, summary, category_level1, category_level2 FROM project_plan WHERE project_name LIKE '%小贷%' OR project_name LIKE '%小额贷款%' OR summary LIKE '%小贷%' OR summary LIKE '%小额贷款%'",
                    "explanation": "查询项目计划表中包含小贷或小额贷款相关的项目信息",
                    "chart_suggestion": {
                        "type": "table",
                        "title": "小贷相关项目列表",
                        "x_axis": "project_name",
                        "y_axis": "category_level1"
                    }
                }
            elif "AI" in question and "项目" in question:
                return {
                    "sql": "SELECT project_id, project_name, summary, category_level1, category_level2, priority FROM project_plan WHERE project_name LIKE '%AI%' OR summary LIKE '%AI%' OR category_level1 LIKE '%AI%'",
                    "explanation": "查询项目计划表中包含AI相关的项目信息",
                    "chart_suggestion": {
                        "type": "bar",
                        "title": "AI相关项目分布",
                        "x_axis": "category_level2",
                        "y_axis": "count"
                    }
                }
            else:
                return {
                    "sql": "SELECT project_id, project_name, category_level1, category_level2, priority FROM project_plan LIMIT 10",
                    "explanation": "查询项目计划表中的基本项目信息",
                    "chart_suggestion": {
                        "type": "table",
                        "title": "项目列表",
                        "x_axis": "project_name",
                        "y_axis": "category_level1"
                    }
                }
    
    def _format_schema_info(self, schema: Dict[str, Any]) -> str:
        """格式化数据库结构信息"""
        schema_text = "数据库表结构：\n"
        
        for table_name, table_info in schema.items():
            schema_text += f"\n表名: {table_name}\n"
            if "columns" in table_info:
                schema_text += "字段:\n"
                for column in table_info["columns"]:
                    schema_text += f"  - {column.get('name', '')}: {column.get('type', '')} ({column.get('comment', '')})\n"
        
        return schema_text
    
    def _extract_sql_from_text(self, text: str) -> str:
        """从文本中提取SQL语句"""
        import re
        
        # 查找SQL语句模式
        sql_patterns = [
            r'```sql\s*(.*?)\s*```',
            r'```\s*(SELECT.*?);?\s*```',
            r'(SELECT.*?);?',
        ]
        
        for pattern in sql_patterns:
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return "SELECT 'SQL解析失败' as error;"

# 全局AI客户端实例
ai_client = AIClient()
