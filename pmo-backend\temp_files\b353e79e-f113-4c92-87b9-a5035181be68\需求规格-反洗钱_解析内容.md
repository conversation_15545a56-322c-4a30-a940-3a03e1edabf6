# Word文档内容


## 反洗钱单据字段改造（实时接口）

### 行政组织-部门编码

#### 关键功能说明

行政组织部门增加或替换编码，码值和部门对照关系如下：

9110001	北部湾金融租赁有限公司

9110002	北金租-公司领导

9110003	北金租-综合管理部

9110004	北金租-财务管理部

9110005	北金租-授信管理部

9110006	北金租-合规与风险管理部

9110007	北金租-审计纪检部

9110008	北金租-业务一部

9110009	北金租-业务二部

9110010	北金租-业务三部

9110011	北金租-金融市场部

9110012	北金租-其他

### 基础资料-所属行业（调整）

#### 关键功能说明

见SVN：\100金融租赁\116北部湾金租\02调研资料\客户资料\监管数据字段及编号规则\数据字典-EAST    国标行业GB4754-2017.xlsx

### 基础资料-客户经济成分（监）（新增）

#### 关键功能说明

不允许填A、B，只允许填A01，或A0101

### 数据字典-客户来源（调整）

#### 关键功能说明

### 基础资料-行政区划-国籍

### 基础资料-行政区划-省市县代码

### 法人登记（反洗钱改造）

#### 关键功能说明

当客户经济成分（监）填写的大类为B时，收益所有人分录至少填写一条记录，否则提交时给与提示：请填写收益所有人信息

所属区域根据填写的国家、省、市、区县对照基础资料的国籍、省市县代码映射出对应的国籍、省、市、区县的编码值，可采用隐藏字段存储对应的编码值

所属行业共四级，行业小类只允许选所属行业大类下的最末级节点，并能在页面展示从行业大类至最小类的路径，类似如下，用隐藏字段存储每一层级的码值

### 客户财报

#### 关键功能说明

客户财报审核后，将客户财报的报表日期反写至法人登记-报表日期（监）；客户财报的资产负债表-资产总计反写至法人登记-总资产（监）；损益表-营业总收入-上年同期数反写至法人登记-上年营业收入（监）；资产负债表-所有者权益合计-期末数反写至法人登记-净资产（监）；资产负债表-负债合计-期末数反写至法人登记-总负债（监）；

### 经营性租赁-还款计划制作

#### 关键功能说明

1.非标、扩展开发

2.还款计划制作审核通过时，如该还款计划是法人客户的第一笔还款计划且该法人客户的首次建立租赁关系年月（监）字段值为空，则将还款计划单据的租赁开始日期反写至法人登记的首次建立租赁关系年月（监）字段；

### 融资性租赁-还款计划制作

#### 关键功能说明

1.非标、扩展开发

2.还款计划制作审核通过时，如该还款计划是法人客户的第一笔还款计划且该法人客户的首次建立租赁关系年月（监）字段值不为空，则将还款计划单据的计息开始日期反写至法人登记的首次建立租赁关系年月（监）字段；

### 融资性租赁-项目立项、项目申报、放款申请、租赁合同

#### 字段说明

1.如现在环境有雷同字段按上述字段为准调整

### 经营性租赁-项目立项、项目申报、交付确认单、租赁合同

#### 字段说明

#### 关键功能说明

1.如现在环境有雷同字段按上述字段为准调整

## 反洗钱接口调用（实时接口）

### 融资性租赁-实时接口调用

#### 关键功能说明

实时接口取值规则见表 《反洗钱实时接口字段取值说明.xls》

接口调用后，接收字段：返回码、整体状态、客户号、确认等级cfmRank、确认时间cfmTime、下次评级日期nextDate；若返回码returnCode为10000且整体状态status为2，则表示调用成功，将确认等级、确认时间、下次评级日期根据客户号分别更新至法人登记对应承租方的反洗钱等级、本次评定日期、下次评定日期字段中；否则表示调用失败，在调用结束后在业务单据中给与提示：返回码（   ）：未获取反洗钱等级信息，请稍后再试！且不允许提交单据。接口返回的等级代码及其等级描述对照如下：

项目立项，在选择承租方之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目立项的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在立项单中点击调用反洗钱按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目立项对应的反洗钱等级、本次评定日期、下次评定日期字段中；并作为风险拦截的依据，风险拦截规则见风险拦截的需求说明。若调用成功，且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若调用成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

项目申报，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目申报的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

项目申报单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

放款申请，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目立项的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

放款申请单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

6.租赁合同，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目立项的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

租赁合同单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

### 经营性性租赁-实时接口调用

#### 关键功能说明

实时接口取值规则见表 《反洗钱实时接口字段取值说明.xls》

2.接口调用后，接收字段：返回码、整体状态、客户号、确认等级cfmRank、确认时间cfmTime、下次评级日期nextDate；若返回码returnCode为10000且整体状态status为2，则表示调用成功，将确认等级、确认时间、下次评级日期根据客户号分别更新至法人登记对应承租方的反洗钱等级、本次评定日期、下次评定日期字段中；否则表示调用失败，在调用结束后在业务单据中给与提示：返回码（   ）：未获取反洗钱等级信息，请稍后再试！且不允许提交单据。接口返回的等级代码及其等级描述对照如下：

3.项目立项，在选择承租方之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目立项的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在立项单中点击调用反洗钱按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目立项对应的反洗钱等级、本次评定日期、下次评定日期字段中；并作为风险拦截的依据，风险拦截规则见风险拦截的需求说明。若调用成功，且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若调用成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

4.项目申报，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目申报的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

项目申报单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

5.交付确认单，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目申报的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

交付确认单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。

6.租赁合同，在选单之后

先在法人登记中查询承租方的反洗钱等级、本次评定日期、下次评定日期，将法人登记该承租方的反洗钱等级、本次评定日期、下次评定日期携带至项目申报的反洗钱等级、本次评定日期、下次评定日期字段中

增加隐藏字段：反洗钱调用结果、反洗钱调用返回码、反洗钱调用整体状态；打开单据时，反洗钱调用结果默认为“未调用”。每次调用结束后，若按第2点规则调用成功，则反洗钱调用结果为“成功”，若失败，则反洗钱调用结果为“失败”，无论调用结果是否成功，均将接口返回的返回码和整体状态分别更新至反洗钱调用返回码、反洗钱调用整体状态字段中；

在单据中点击调用反洗钱接口按钮，按第2点规则调用反洗钱实时接口，调用成功后，除按第2点更新法人登记单据对应的字段外，还将接口返回的确认等级、确认时间、下次评级日期更新至项目申报对应的反洗钱等级、本次评定日期、下次评定日期字段中；

租赁合同单据提交时，若隐藏字段-反洗钱调用结果为“未调用”或“失败”或（反洗钱等级、本次评定日期、下次评级日期任一一项为空），则提示“请进行反洗钱评定后再提交”；若隐藏字段-反洗钱调用结果为“成功”且系统当前日期仍然>接口返回的下次评定日期，则给与提示：超过反洗钱评定日期，请重新评定反洗钱风险等级！且不允许提交。若隐藏字段-反洗钱调用结果为成功，且系统当前日期<=接口返回的下次评定日期，则允许提交。


| 一级编码 | 一级名称 | 二级编码 | 二级名称 | 三级编码 | 三级名称 |
| --- | --- | --- | --- | --- | --- |
| A | 公有控股经济 | A01 | 国有控股 | A0101 | 国有相对控股 |
| A | 公有控股经济 | A01 | 国有控股 | A0102 | 国有绝对控股 |
| A | 公有控股经济 | A02 | 集体控股 | A0201 | 集体相对控股 |
| A | 公有控股经济 | A02 | 集体控股 | A0202 | 集体绝对控股 |
| B | 私人控股 | B01 | 私人控股 | B0101 | 私人相对控股 |
| B | 私人控股 | B01 | 私人控股 | B0102 | 私人绝对控股 |
| B | 私人控股 | B02 | 港澳台控股 | B0201 | 港澳台相对控股 |
| B | 私人控股 | B02 | 港澳台控股 | B0202 | 港澳台绝对控股 |
| B | 私人控股 | B03 | 外商控股 | B0301 | 外商相对控股 |
| B | 私人控股 | B03 | 外商控股 | B0302 | 外商绝对控股 |



| 码值 | 名称 |
| --- | --- |
| 1 | 面对面渠道 |
| 2 | 网络渠道-pc注册 |
| 3 | 网络渠道-手机注册 |
| 4 | 代理渠道 |
| 9 | 其他渠道 |



| 字段 | 字段类型 | 字段说明 | 列表展示 | 必录 | 锁定 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 客户基本信息 | 客户基本信息 | 客户基本信息 | 客户基本信息 | 客户基本信息 | 客户基本信息 | 客户基本信息 |
| 客户来源 | F7 | 数据字典-客户来源 | N | Y | N | 调整：改必填 |
| 证件有效期 | 日期 |  | N | Y | N | 新增：必填，在“证件号码”和“所有制性质”之间添加 |
| 所属行业 | F7 | 基础资料-行业分类 | N | Y | N | 调整：改为必填，从企查查取值 |
| 行业小类 | F7 | 基础资料-行业分类 | N | Y | N | 新增，必填，只允许在所属行业所选的层级下，填写行业分类基础资料中的最末级节点 |
| 法定代表人身份证件类型 | 下拉框 | 身份证、军人证、护照、港澳身份证、台湾回乡证、其他；默认身份证 | N | Y | N | 新增，必填 |
| 法定代表人证件号 | 文本 |  | N | Y | N | 调整：改必填 |
| 法定代表人证件有效期 | 日期 |  | N | Y | N | 新增 |
| 实际控制人类型 | 下拉框 | 选项：企业、个人 | N | Y | N | 新增，必填 |
| 实际控制人证件类型 | 下拉框 | 如实际控制人类型为企业，则选项为：
统一信用代码、组织机构代码、工商注册号、纳税识别号、机关和其他事业单位登记号

如实际控制人类型为个人，则选项为：
身份证、军人证、护照、港澳身份证、台湾回乡证、其他；默认身份证 | N | Y | N | 新增，必填 |
| 实际控制人证件号码 | 文本 |  | N | Y | N | 调整：改必填 |
| 实际控制人证件有效期限 | 日期 |  | N | Y | N | 新增 |
| 受益所有人（新增分录） | 受益所有人（新增分录） | 受益所有人（新增分录） | 受益所有人（新增分录） | 受益所有人（新增分录） | 受益所有人（新增分录） | 受益所有人（新增分录） |
| 受益人名称 | 文本 |  | N | Y | N | 必填 |
| 证件类型 | 下拉框 | 身份证、军人证、护照、港澳身份证、台湾回乡证、其他；默认身份证 | N | Y | N | 必填 |
| 证件号码 | 文本 |  | N | Y | N | 必填 |
| 证件有效期 | 日期 |  | N | Y | N | 必填 |
| 主要管理团队-分录 | 主要管理团队-分录 | 主要管理团队-分录 | 主要管理团队-分录 | 主要管理团队-分录 | 主要管理团队-分录 | 主要管理团队-分录 |
| 是否联系人 | 布尔 |  | N | Y | N | 新增，在“是否股东”之后添加，分录中至少有一条记录填写为“是”联系人 |
| 职务 |  |  | N | N | N | 调整：当该条记录为联系人时，必填 |
| 联系电话 |  |  | N | N | N | 调整：当该条记录为联系人时，必填 |
| 邮箱 |  |  | N | N | N | 调整：当该条记录为联系人时，必填 |
| 监管报送（新增页签） | 监管报送（新增页签） | 监管报送（新增页签） | 监管报送（新增页签） | 监管报送（新增页签） | 监管报送（新增页签） | 监管报送（新增页签） |
| EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） | EAST（新增子页签，属于监管报送的子页签） |
| 总资产 | 数值 | 两位小数 | N | N | N | 新增，默认为0.00 |
| 净资产 | 数值 | 两位小数 | N | N | N | 新增，默认为0.00 |
| 上年营业收入 | 数值 | 两位小数 | N | N | N | 新增，默认为0.00 |
| 总负债 | 数值 | 两位小数 | N | N | N | 新增，默认为0.00 |
| 报表日期 | 日期 | 精确到月，后台格式为YYYYMM | N | N | N | 新增 |
| 首次建立租赁关系年月 | 日期 |  | N | N | N |  |
| 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） | 金融基础数据（新增子页签，属于监管报送的子页签） |
| 客户经济成分 | F7 | 基础资料-客户经济成分（监），不允许填A、B，只允许填A01，或A0101 | N | Y | N | 新增 |



| 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） |
| --- | --- | --- | --- | --- | --- | --- |
| 反洗钱等级 | 下拉列表 | 携带法人登记反洗钱等级 | N | N | Y | 新增 |
| 本次评定日期 | 日期 | 携带法人登记本次评定日期 | N | N | Y | 新增 |
| 下次评定日期 | 日期 | 携带法人登记下次评定日期 | N | N | Y | 新增 |



| 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） | 反洗钱信息（新增页签，在项目要素之后，如无项目要素，则放在基本信息之后） |
| --- | --- | --- | --- | --- | --- | --- |
| 反洗钱等级 | 下拉列表 | 携带法人登记反洗钱等级 | N | N | Y | 新增 |
| 本次评定日期 | 日期 | 携带法人登记本次评定日期 | N | N | Y | 新增 |
| 下次评定日期 | 日期 | 携带法人登记下次评定日期 | N | N | Y | 新增 |
