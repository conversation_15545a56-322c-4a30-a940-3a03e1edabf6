#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
商用版本配置
用于客户交付时的配置管理
"""

import os
from typing import Dict, Any

class CommercialConfig:
    """商用版本配置管理"""
    
    def __init__(self):
        self.version = "1.0.0"
        self.build_date = "2025-07-27"
        self.license_type = "Commercial"
        
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "product_name": "PMO项目管理系统",
            "version": self.version,
            "build_date": self.build_date,
            "license_type": self.license_type,
            "vendor": "您的公司名称",
            "support_email": "<EMAIL>",
            "support_phone": "400-xxx-xxxx",
            "features": {
                "project_management": True,
                "user_management": True,
                "timesheet": True,
                "reports": True,
                "ai_assistant": True,
                "vector_knowledge": True,
                "archive_management": True,
                "supervision": True
            },
            "open_source_components": [
                {
                    "name": "Qdrant",
                    "version": "1.7.0",
                    "license": "Apache 2.0",
                    "purpose": "向量数据库"
                },
                {
                    "name": "sentence-transformers",
                    "version": "2.2.2",
                    "license": "Apache 2.0",
                    "purpose": "文本向量化"
                },
                {
                    "name": "FastAPI",
                    "version": "0.104.1",
                    "license": "MIT",
                    "purpose": "Web框架"
                },
                {
                    "name": "Vue.js",
                    "version": "3.3.0",
                    "license": "MIT",
                    "purpose": "前端框架"
                }
            ]
        }
    
    def get_deployment_config(self, environment: str = "production") -> Dict[str, Any]:
        """获取部署配置"""
        base_config = {
            "database": {
                "host": os.getenv("DB_HOST", "localhost"),
                "port": int(os.getenv("DB_PORT", "3306")),
                "name": os.getenv("DB_NAME", "pmo_db"),
                "ssl_mode": "required" if environment == "production" else "disabled"
            },
            "redis": {
                "host": os.getenv("REDIS_HOST", "localhost"),
                "port": int(os.getenv("REDIS_PORT", "6379")),
                "ssl": environment == "production"
            },
            "qdrant": {
                "host": os.getenv("QDRANT_HOST", "localhost"),
                "port": int(os.getenv("QDRANT_PORT", "6333")),
                "collection_name": "pmo_knowledge"
            },
            "security": {
                "jwt_secret": os.getenv("JWT_SECRET", "your-secret-key"),
                "jwt_expire_hours": 24,
                "password_min_length": 8,
                "max_login_attempts": 5,
                "session_timeout": 3600
            },
            "logging": {
                "level": "INFO" if environment == "production" else "DEBUG",
                "file_path": "./logs/pmo.log",
                "max_file_size": "10MB",
                "backup_count": 5
            }
        }
        
        if environment == "production":
            base_config.update({
                "ssl": {
                    "enabled": True,
                    "cert_file": "/path/to/cert.pem",
                    "key_file": "/path/to/key.pem"
                },
                "cors": {
                    "allowed_origins": ["https://yourdomain.com"],
                    "allowed_methods": ["GET", "POST", "PUT", "DELETE"],
                    "allowed_headers": ["*"]
                }
            })
        
        return base_config
    
    def generate_license_info(self) -> str:
        """生成许可证信息"""
        return f"""
# PMO项目管理系统 - 商用许可证

## 产品信息
- 产品名称: PMO项目管理系统
- 版本: {self.version}
- 构建日期: {self.build_date}
- 许可类型: {self.license_type}

## 开源组件声明
本产品使用了以下开源组件，均为商业友好许可证：

1. Qdrant (Apache 2.0) - 向量数据库
2. sentence-transformers (Apache 2.0) - 文本向量化
3. FastAPI (MIT) - Web框架
4. Vue.js (MIT) - 前端框架
5. Element Plus (MIT) - UI组件库

## 使用条款
1. 本软件按"现状"提供，不提供任何明示或暗示的保证
2. 使用者需遵守相关开源组件的许可证条款
3. 建议在生产环境中进行充分测试
4. 如需技术支持，请联系供应商

## 技术支持
- 邮箱: <EMAIL>
- 电话: 400-xxx-xxxx
- 网站: https://yourcompany.com

---
生成时间: {self.build_date}
"""

def create_deployment_package():
    """创建部署包"""
    import shutil
    import zipfile
    from datetime import datetime
    
    config = CommercialConfig()
    
    # 创建部署目录
    deploy_dir = f"pmo_deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(deploy_dir, exist_ok=True)
    
    # 复制必要文件
    files_to_copy = [
        "app/",
        "main.py",
        "requirements.txt",
        "docker-compose.qdrant.yml",
        "start_qdrant.bat",
        "start_qdrant.sh",
        "install_vector_knowledge.py",
        "LICENSES.md",
        "商用部署指南.md",
        "向量知识库安装指南.md"
    ]
    
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            if os.path.isdir(file_path):
                shutil.copytree(file_path, os.path.join(deploy_dir, file_path))
            else:
                shutil.copy2(file_path, deploy_dir)
    
    # 生成许可证文件
    with open(os.path.join(deploy_dir, "LICENSE_COMMERCIAL.txt"), "w", encoding="utf-8") as f:
        f.write(config.generate_license_info())
    
    # 生成系统信息文件
    import json
    with open(os.path.join(deploy_dir, "system_info.json"), "w", encoding="utf-8") as f:
        json.dump(config.get_system_info(), f, ensure_ascii=False, indent=2)
    
    # 创建ZIP包
    zip_filename = f"{deploy_dir}.zip"
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deploy_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, deploy_dir)
                zipf.write(file_path, arcname)
    
    print(f"✅ 部署包已创建: {zip_filename}")
    print(f"📦 包含文件: {len(files_to_copy)} 个主要组件")
    print(f"📄 许可证文件: LICENSE_COMMERCIAL.txt")
    print(f"ℹ️ 系统信息: system_info.json")
    
    return zip_filename

if __name__ == "__main__":
    # 创建商用部署包
    create_deployment_package()
