from fastapi import APIRouter
from .endpoints.auth import router as auth_router
from .endpoints.project import router as projects_router
from .endpoints.users import router as users_router
from .endpoints.dashboard import router as dashboard_router

# 导入工时管理路由，添加调试信息
try:
    from .endpoints.timesheet import router as timesheet_router
    print("工时管理路由导入成功")
    print(f"工时管理路由包含的端点: {[route.path for route in timesheet_router.routes]}")
except ImportError as e:
    print(f"工时管理路由导入失败: {e}")
    timesheet_router = APIRouter()

from .endpoints.reports import router as reports_router
from .endpoints.options import router as options_router
from .endpoints.team import router as teams_router
from .planning import router as planning_router  # 直接导入planning.py
from .endpoints.project_archive import router as archive_router  # 新增项目档案路由
from .endpoints.archive_management import router as archive_management_router  # 新增档案管理路由

api_router = APIRouter()

# 注册所有路由
api_router.include_router(auth_router, tags=["认证"])
api_router.include_router(projects_router, tags=["项目管理"])
api_router.include_router(users_router, tags=["用户管理"])
api_router.include_router(dashboard_router, tags=["仪表盘"])

# 注册工时管理路由，添加调试信息
print(f"🔧 注册工时管理路由，前缀: /timesheet")
api_router.include_router(timesheet_router, prefix="/timesheet", tags=["工时管理"])
print(f"✅ 工时管理路由注册完成")

api_router.include_router(reports_router, tags=["报告管理"])
api_router.include_router(options_router, tags=["选项管理"])
api_router.include_router(teams_router, tags=["团队管理"])
api_router.include_router(planning_router, tags=["规划管理"])  # 新增
api_router.include_router(archive_router, prefix="/archive", tags=["项目档案管理"])  # 新增项目档案路由
api_router.include_router(archive_management_router, prefix="/archive-management", tags=["档案清单管理"])  # 新增档案管理路由

# 打印所有注册的路由
print("📋 所有注册的路由:")
for route in api_router.routes:
    if hasattr(route, 'path') and hasattr(route, 'methods'):
        print(f"  {list(route.methods)[0] if route.methods else 'GET'} {route.path}")
    elif hasattr(route, 'path_regex'):
        print(f"  MOUNT {route.path_regex.pattern}")
