#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建督办管理相关数据库表
"""

import pymysql
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_supervision_tables():
    """创建督办管理相关表"""
    try:
        # 数据库连接配置
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='pmo_system',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with connection.cursor() as cursor:
            print("🚀 开始创建督办管理相关表...")
            
            # 1. 创建督办事项表
            print("📋 创建 supervision_items 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS supervision_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sequence_number INT NOT NULL COMMENT '序号',
                    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                    completion_date VARCHAR(50) NULL COMMENT '完成日期',
                    overall_progress VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '整体进度',
                    is_overdue TINYINT DEFAULT 0 COMMENT '是否逾期',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_sequence_number (sequence_number),
                    INDEX idx_work_dimension (work_dimension),
                    INDEX idx_overall_progress (overall_progress)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
            """)
            
            # 2. 创建公司表
            print("🏢 创建 companies 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS companies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_company_code (company_code),
                    INDEX idx_display_order (display_order),
                    INDEX idx_is_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
            """)
            
            # 3. 创建公司进度表
            print("📊 创建 company_progress 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS company_progress (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    status VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '状态',
                    progress_description TEXT COMMENT '进展描述',
                    existing_problems TEXT COMMENT '存在问题',
                    next_plan TEXT COMMENT '下步计划',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
                    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
                    UNIQUE KEY uk_item_company (supervision_item_id, company_id),
                    INDEX idx_supervision_item_id (supervision_item_id),
                    INDEX idx_company_id (company_id),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司进度表'
            """)
            
            # 4. 插入默认公司数据
            print("🏢 插入默认公司数据...")
            companies_data = [
                ('CXBX', '财险', 1),
                ('SXBX', '寿险', 2),
                ('JINZU', '金租', 3),
                ('ZICHAN', '资管', 4),
                ('GUANGZU', '广租', 5),
                ('TONGSHENG', '通盛', 6),
                ('DANBAO', '担保', 7),
                ('XIAODAI', '小贷', 8),
                ('BAOLI', '保理', 9),
                ('BUDONGCHAN', '不动产', 10),
                ('ZHENGXIN', '征信', 11),
                ('JINFU', '金服', 12),
                ('BENBU', '本部', 13)
            ]
            
            for company_code, company_name, display_order in companies_data:
                cursor.execute("""
                    INSERT IGNORE INTO companies (company_code, company_name, display_order)
                    VALUES (%s, %s, %s)
                """, (company_code, company_name, display_order))
            
            # 5. 插入示例督办事项数据
            print("📋 插入示例督办事项数据...")
            sample_items = [
                (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
                 '5月末', '已完成'),
                (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                 '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
                 '4月末', '已完成'),
                (3, '一、监管和制度', '印发数据分级分类制度', '3月科技例会',
                 '各单位需对数据进行分级分类管理，保障基础数据的准确性和安全性。完成标志：各单位印发分级分类制度。',
                 '7月末', '进行中')
            ]
            
            for item_data in sample_items:
                cursor.execute("""
                    INSERT IGNORE INTO supervision_items 
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content, completion_deadline, overall_progress)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, item_data)
            
            # 6. 为示例督办事项创建公司进度记录
            print("📊 创建公司进度记录...")
            cursor.execute("SELECT id FROM supervision_items")
            items = cursor.fetchall()
            
            cursor.execute("SELECT id FROM companies WHERE is_active = 1")
            companies = cursor.fetchall()
            
            for item in items:
                for company in companies:
                    cursor.execute("""
                        INSERT IGNORE INTO company_progress (supervision_item_id, company_id, status)
                        VALUES (%s, %s, '未开始')
                    """, (item['id'], company['id']))
            
            # 提交事务
            connection.commit()
            print("✅ 督办管理表创建完成！")
            
            # 显示创建结果
            cursor.execute("SELECT COUNT(*) as count FROM supervision_items")
            items_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM companies")
            companies_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM company_progress")
            progress_count = cursor.fetchone()['count']
            
            print(f"📊 创建结果统计:")
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   进度记录: {progress_count} 条")
            
    except Exception as e:
        print(f"错误详情: {str(e)}")
        print(f"❌ 创建督办管理表失败: {str(e)}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    create_supervision_tables()
