#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import os
from dotenv import load_dotenv

def create_archive_tables():
    """创建项目档案管理相关表"""
    
    load_dotenv()
    
    config = {
        'host': os.getenv('DB_HOST'),
        'port': int(os.getenv('DB_PORT', '3306')),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME'),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    print('正在连接数据库并创建表...')
    
    try:
        conn = pymysql.connect(**config)
        
        with conn.cursor() as cursor:
            # 1. 创建档案清单表
            print('创建 project_archive_checklist 表...')
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_archive_checklist (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    main_process VARCHAR(100) NOT NULL COMMENT '主流程',
                    sub_process VARCHAR(100) NOT NULL COMMENT '子流程',
                    output_name VARCHAR(200) NOT NULL COMMENT '输出物名称',
                    content_description TEXT COMMENT '内容描述',
                    keywords TEXT COMMENT '关键词（逗号分隔）',
                    is_required TINYINT DEFAULT 1 COMMENT '是否必需（1必需，0可选）',
                    sort_order INT DEFAULT 0 COMMENT '排序',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_by VARCHAR(50) COMMENT '创建人',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    INDEX idx_main_process (main_process),
                    INDEX idx_sub_process (sub_process),
                    INDEX idx_sort_order (sort_order)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目档案清单表'
            """)
            
            # 2. 创建项目档案文件表
            print('创建 project_archive_files 表...')
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_archive_files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_code VARCHAR(50) NOT NULL COMMENT '项目编号',
                    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
                    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
                    file_size BIGINT COMMENT '文件大小（字节）',
                    file_type VARCHAR(50) COMMENT '文件类型',
                    main_process VARCHAR(100) COMMENT '主流程',
                    sub_process VARCHAR(100) COMMENT '子流程',
                    output_name VARCHAR(200) COMMENT '输出物名称',
                    classification_confidence DECIMAL(5,2) COMMENT '分类置信度',
                    is_confirmed TINYINT DEFAULT 0 COMMENT '是否已确认分类（0未确认，1已确认）',
                    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uploaded_by VARCHAR(50) COMMENT '上传人',
                    confirmed_by VARCHAR(50) COMMENT '确认人',
                    confirmed_time TIMESTAMP NULL COMMENT '确认时间',
                    INDEX idx_project_code (project_code),
                    INDEX idx_main_process (main_process),
                    INDEX idx_upload_time (upload_time),
                    INDEX idx_is_confirmed (is_confirmed)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目档案文件表'
            """)
            
            # 3. 检查是否已有数据
            cursor.execute("SELECT COUNT(*) as count FROM project_archive_checklist")
            count = cursor.fetchone()['count']
            
            if count == 0:
                print('插入预定义档案清单数据...')
                # 插入预定义数据
                checklist_data = [
                    ('项目立项', '立项报告', '立项申请书', '项目立项申请文件', '立项申请,项目申请,立项报告', 1),
                    ('项目立项', '立项报告', '可行性研究报告', '项目可行性分析文件', '可行性研究,可行性分析,研究报告', 2),
                    ('项目立项', '立项报告', '项目建议书', '项目建议文件', '项目建议,建议书,项目提案', 3),
                    ('项目设计', '需求分析', '需求规格说明书', '项目需求分析文件', '需求规格,需求分析,需求说明', 4),
                    ('项目设计', '系统设计', '系统设计方案', '系统设计文件', '系统设计,设计方案,架构设计', 5),
                    ('项目设计', '技术方案', '技术实施方案', '技术实施文件', '技术方案,实施方案,技术设计', 6),
                    ('项目采购', '采购管理', '采购合同', '采购合同文件', '采购合同,合同,采购协议', 7),
                    ('项目采购', '招标管理', '招标文件', '招标相关文件', '招标文件,招标,投标', 8),
                    ('项目实施', '开发管理', '开发计划', '项目开发计划文件', '开发计划,项目计划,实施计划', 9),
                    ('项目实施', '测试管理', '测试报告', '项目测试报告文件', '测试报告,测试,质量报告', 10),
                    ('项目验收', '验收管理', '验收报告', '项目验收报告文件', '验收报告,验收,项目验收', 11),
                    ('项目验收', '文档交付', '用户手册', '用户使用手册文件', '用户手册,使用手册,操作手册', 12),
                    ('项目运维', '运维管理', '运维手册', '系统运维手册文件', '运维手册,维护手册,运维文档', 13),
                    ('项目运维', '维护记录', '维护记录', '系统维护记录文件', '维护记录,运维记录,维护日志', 14)
                ]
                
                for data in checklist_data:
                    cursor.execute("""
                        INSERT INTO project_archive_checklist 
                        (main_process, sub_process, output_name, content_description, keywords, sort_order) 
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, data)
                
                print(f'✅ 插入了 {len(checklist_data)} 条档案清单数据')
            else:
                print(f'档案清单表已有 {count} 条数据，跳过插入')
        
        conn.commit()
        conn.close()
        
        print('✅ 数据库表创建成功')
        
        # 验证创建结果
        print('\n验证创建结果:')
        conn = pymysql.connect(**config)
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES LIKE '%archive%'")
            tables = cursor.fetchall()
            for table in tables:
                table_name = list(table.values())[0]
                print(f'  ✓ {table_name}')
                
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                print(f'    数据量: {count} 条')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 创建失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_archive_tables()
