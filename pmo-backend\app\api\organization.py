#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组织管理API - 第二个功能模块：部门和用户组管理
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.models.user import User
from app.models.dept import Dept
from app.models.group import Group
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/organization", tags=["组织管理"])

# 请求模型
class DeptCreateRequest(BaseModel):
    name: str
    parent: int = 0
    position: str = ""
    function: str = ""
    manager: str = ""
    order_num: int = 0

class DeptUpdateRequest(BaseModel):
    name: Optional[str] = None
    position: Optional[str] = None
    function: Optional[str] = None
    manager: Optional[str] = None
    order_num: Optional[int] = None

class GroupCreateRequest(BaseModel):
    name: str
    role: str = ""
    desc: str = ""

class GroupUpdateRequest(BaseModel):
    name: Optional[str] = None
    role: Optional[str] = None
    desc: Optional[str] = None

class UserGroupRequest(BaseModel):
    account: str
    group_id: int

class PermissionRequest(BaseModel):
    permissions: List[Dict[str, str]]

# 权限检查装饰器
def require_admin(current_user: User = Depends(get_current_user)):
    """需要管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user

# 部门管理API
@router.get("/depts")
async def get_departments(current_user: User = Depends(get_current_user)):
    """获取部门列表"""
    try:
        depts = Dept.get_all_depts()
        return success_response([dept.to_dict() for dept in depts], "获取部门列表成功")
    except Exception as e:
        return error_response(f"获取部门列表失败: {str(e)}")

@router.get("/depts/tree")
async def get_department_tree(current_user: User = Depends(get_current_user)):
    """获取部门树形结构"""
    try:
        tree = Dept.get_dept_tree()
        return success_response(tree, "获取部门树形结构成功")
    except Exception as e:
        return error_response(f"获取部门树形结构失败: {str(e)}")

@router.get("/depts/{dept_id}")
async def get_department(dept_id: int, current_user: User = Depends(get_current_user)):
    """获取部门详情"""
    try:
        dept = Dept.get_by_id(dept_id)
        if not dept:
            return error_response("部门不存在", status_code=404)
        
        return success_response(dept.to_dict(), "获取部门详情成功")
    except Exception as e:
        return error_response(f"获取部门详情失败: {str(e)}")

@router.post("/depts")
async def create_department(
    request: DeptCreateRequest,
    current_user: User = Depends(require_admin)
):
    """创建部门"""
    try:
        dept = Dept()
        dept.name = request.name
        dept.parent = request.parent
        dept.position = request.position
        dept.function = request.function
        dept.manager = request.manager
        dept.order_num = request.order_num
        
        if dept.create():
            return success_response(dept.to_dict(), "创建部门成功")
        else:
            return error_response("创建部门失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"创建部门失败: {str(e)}")

@router.put("/depts/{dept_id}")
async def update_department(
    dept_id: int,
    request: DeptUpdateRequest,
    current_user: User = Depends(require_admin)
):
    """更新部门信息"""
    try:
        dept = Dept.get_by_id(dept_id)
        if not dept:
            return error_response("部门不存在", status_code=404)
        
        # 更新部门信息
        if request.name is not None:
            dept.name = request.name
        if request.position is not None:
            dept.position = request.position
        if request.function is not None:
            dept.function = request.function
        if request.manager is not None:
            dept.manager = request.manager
        if request.order_num is not None:
            dept.order_num = request.order_num
        
        if dept.update():
            return success_response(dept.to_dict(), "更新部门信息成功")
        else:
            return error_response("更新部门信息失败")
            
    except Exception as e:
        return error_response(f"更新部门信息失败: {str(e)}")

@router.delete("/depts/{dept_id}")
async def delete_department(
    dept_id: int,
    current_user: User = Depends(require_admin)
):
    """删除部门"""
    try:
        dept = Dept.get_by_id(dept_id)
        if not dept:
            return error_response("部门不存在", status_code=404)
        
        if dept.delete():
            return success_response(None, "删除部门成功")
        else:
            return error_response("删除部门失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"删除部门失败: {str(e)}")

# 用户组管理API
@router.get("/groups")
async def get_groups(current_user: User = Depends(get_current_user)):
    """获取用户组列表"""
    try:
        groups = Group.get_all_groups()
        return success_response([group.to_dict() for group in groups], "获取用户组列表成功")
    except Exception as e:
        return error_response(f"获取用户组列表失败: {str(e)}")

@router.get("/groups/{group_id}")
async def get_group(group_id: int, current_user: User = Depends(get_current_user)):
    """获取用户组详情"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        # 获取用户组的用户和权限
        group_data = group.to_dict()
        group_data['users'] = group.get_users()
        group_data['permissions'] = group.get_permissions()
        
        return success_response(group_data, "获取用户组详情成功")
    except Exception as e:
        return error_response(f"获取用户组详情失败: {str(e)}")

@router.post("/groups")
async def create_group(
    request: GroupCreateRequest,
    current_user: User = Depends(require_admin)
):
    """创建用户组"""
    try:
        group = Group()
        group.name = request.name
        group.role = request.role
        group.desc = request.desc
        
        if group.create():
            return success_response(group.to_dict(), "创建用户组成功")
        else:
            return error_response("创建用户组失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"创建用户组失败: {str(e)}")

@router.put("/groups/{group_id}")
async def update_group(
    group_id: int,
    request: GroupUpdateRequest,
    current_user: User = Depends(require_admin)
):
    """更新用户组信息"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        # 更新用户组信息
        if request.name is not None:
            group.name = request.name
        if request.role is not None:
            group.role = request.role
        if request.desc is not None:
            group.desc = request.desc
        
        if group.update():
            return success_response(group.to_dict(), "更新用户组信息成功")
        else:
            return error_response("更新用户组信息失败")
            
    except Exception as e:
        return error_response(f"更新用户组信息失败: {str(e)}")

@router.delete("/groups/{group_id}")
async def delete_group(
    group_id: int,
    current_user: User = Depends(require_admin)
):
    """删除用户组"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        if group.delete():
            return success_response(None, "删除用户组成功")
        else:
            return error_response("删除用户组失败")
            
    except ValueError as e:
        return error_response(str(e), status_code=400)
    except Exception as e:
        return error_response(f"删除用户组失败: {str(e)}")

# 用户组成员管理
@router.post("/groups/{group_id}/users")
async def add_user_to_group(
    group_id: int,
    request: UserGroupRequest,
    current_user: User = Depends(require_admin)
):
    """添加用户到用户组"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        # 检查用户是否存在
        user = User.get_by_account(request.account)
        if not user:
            return error_response("用户不存在", status_code=404)
        
        if group.add_user(request.account):
            return success_response(None, "添加用户到用户组成功")
        else:
            return error_response("添加用户到用户组失败")
            
    except Exception as e:
        return error_response(f"添加用户到用户组失败: {str(e)}")

@router.delete("/groups/{group_id}/users/{account}")
async def remove_user_from_group(
    group_id: int,
    account: str,
    current_user: User = Depends(require_admin)
):
    """从用户组移除用户"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        if group.remove_user(account):
            return success_response(None, "从用户组移除用户成功")
        else:
            return error_response("从用户组移除用户失败")
            
    except Exception as e:
        return error_response(f"从用户组移除用户失败: {str(e)}")

# 权限管理
@router.put("/groups/{group_id}/permissions")
async def set_group_permissions(
    group_id: int,
    request: PermissionRequest,
    current_user: User = Depends(require_admin)
):
    """设置用户组权限"""
    try:
        group = Group.get_by_id(group_id)
        if not group:
            return error_response("用户组不存在", status_code=404)
        
        if group.set_permissions(request.permissions):
            return success_response(None, "设置用户组权限成功")
        else:
            return error_response("设置用户组权限失败")
            
    except Exception as e:
        return error_response(f"设置用户组权限失败: {str(e)}")

@router.get("/users/{account}/groups")
async def get_user_groups(
    account: str,
    current_user: User = Depends(get_current_user)
):
    """获取用户所属的用户组"""
    try:
        # 检查权限：只能查看自己的或管理员可以查看所有
        if current_user.account != account and current_user.role != "admin":
            return error_response("权限不足", status_code=403)
        
        groups = Group.get_user_groups(account)
        return success_response([group.to_dict() for group in groups], "获取用户组成功")
    except Exception as e:
        return error_response(f"获取用户组失败: {str(e)}")
