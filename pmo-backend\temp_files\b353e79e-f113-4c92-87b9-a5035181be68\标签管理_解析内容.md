# Word文档内容


## 项目立项&项目申报

增加“业务标签”F7字段，取业务标签基础资料，可多选，只能选择最末级；

监管相关的字段需要根据所选的业务标签取值，规则如下：

## 标签管理

### 业务场景

可多选项目申报单，对客户标签及业务标签进行批量认定。

### 字段说明

### 关键功能说明

不允许反审核；

单据审核后，若客户标签字段不为空，则需要将客户标签反写到法人登记单客户标签字段中（不要做覆盖更新，只需要将新增的值累加上去即可，例如原法人登记单上的值为1，标签认定时客户标签选择的值为2，则单据审核后，法人登记单客户标签字段值为1；2两个值）；为空或者为否则不反写

单据审核后，若业务标签字段不为空，则需要将所选的业务标签字段反写到项目立项单&项目申报单业务标签字段（不要做覆盖更新，只需要将新增的值累加上去即可）和监管相关字段（根据业务标签的“标识”映射到单据中的对应字段，具体反写规则如下）；为空则不反写；

映射关系如下：


| 所选业务标签标识 | 对应单据字段 | 取值规则 |
| --- | --- | --- |
| 1 | 是否个体工商户贷款（监） | 若选择的业务标签存在标识为1的值，则“是否个体工商户贷款（监）”取是 |
| 2 | 是否小微企业主贷款（监） | 若选择的业务标签存在标识为2的值，则“是否小微企业主贷款（监）”取是 |
| 3 | 是否涉农贷款（监） | 若选择的业务标签存在标识为3的值，则“是否涉农贷款（监）”取是 |
| 3 | 涉农贷款类型（监） | 若选择的业务标签存在标识为3的值，则根据业务标签的名称去映射涉农贷款类型（监）基础资料中的名称去取值 |
| 4 | 是否精准扶贫贷款（监） | 若选择的业务标签存在标识为4的值，则“是否精准扶贫贷款（监）”取是 |
| 5 | 是否建档立卡贫困人口贷款（监） | 若选择的业务标签存在标识为5的值，则“是否建档立卡贫困人口贷款（监）”取是 |
| 6/7/8 | 是否地方政府融资平台贷款（监） | 若选择的业务标签存在标识为6/7/8的值，则“是否地方政府融资平台贷款（监）”取是 |
| 6 | 地方融资平台按法律性质分类类型（监） | 若选择的业务标签存在标识为6的值，则根据业务标签的名称去映射地方融资平台按法律性质分类类型（监）基础资料中的名称去取值 |
| 7 | 地方融资平台按隶属关系分类类型（监） | 若选择的业务标签存在标识为7的值，则根据业务标签的名称去映射地方融资平台按隶属关系分类类型（监）基础资料中的名称去取值 |
| 8 | 地方融资平台偿债资金来源分类（监） | 若选择的业务标签存在标识为8的值，则根据业务标签的名称去映射地方融资平台偿债资金来源分类（监）基础资料中的名称去取值 |
| 9 | 是否保障性安居工程贷款（监） | 若选择的业务标签存在标识为9的值，则“是否保障性安居工程贷款（监）”取是 |
| 9 | 保障性安居工程贷款类型（监） | 若选择的业务标签存在标识为9的值，则根据业务标签的名称去映射保障性安居工程贷款类型（监）基础资料中的名称去取值 |
| 10 | 是否绿色贷款（监） | 若选择的业务标签存在标识为10的值，则“是否绿色贷款（监）”取是 |
| 11 | 是否创业担保贷款（监） | 若选择的业务标签存在标识为11的值，则“是否创业担保贷款（监）”取是 |
| 11 | 创业担保贷款类型（监） | 若选择的业务标签存在标识为11的值，则根据业务标签的名称去映射创业担保贷款类型（监）基础资料中的名称去取值 |



| 字段 | 字段类型 | 字段说明 | 列表展示 | 必录 | 锁定 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 基本信息 | 基本信息 | 基本信息 | 基本信息 | 基本信息 | 基本信息 | 基本信息 |
| 单据编号 | 文本 | 编码规则自动生成 | Y | N | Y |  |
| 业务日期 | 日期 | 默认当前日期 | Y | Y | N |  |
| 备注 | 文本 | 200字 | N | N | N |  |
| 查询条件 | 查询条件 | 查询条件 | 查询条件 | 查询条件 | 查询条件 | 查询条件 |
| 项目申报 | F7 | 取已审核的项目申报单，可多选 | N | N | N |  |
| 承租方 | F7 | 取已审核的法人登记单，可多选 | N | N | N |  |
| 项目申报列表（分录） | 项目申报列表（分录） | 项目申报列表（分录） | 项目申报列表（分录） | 项目申报列表（分录） | 项目申报列表（分录） | 项目申报列表（分录） |
| 项目申报 | F7 | 根据查询条件，查询出满足条件的项目申报单，取单据编号，超链接到项目申报单 | N | N | Y |  |
| 项目立项 | F7 | 取项目申报单-项目立项单，超链接到项目立项单 | N | N | Y |  |
| 承租方 | F7 | 取项目申报单-承租方，超链接到法人登记单 | N | N | Y |  |
| 主办人 | F7 | 取项目申报单-主办人 | N | N | Y |  |
| 协办人 | F7 | 取项目申报单-协办人 | N | N | Y |  |
| 业务部门 | F7 | 取项目申报单-业务部门 | N | N | Y |  |
| 租赁类型 | F7 | 取项目申报单-租赁类型 | N | N | Y |  |
| 租赁形式 | F7 | 取项目申报单-租赁形式 | N | N | Y |  |
| 客户标签认定 | 客户标签认定 | 客户标签认定 | 客户标签认定 | 客户标签认定 | 客户标签认定 | 客户标签认定 |
| 客户标签 | F7 | 取客户标签基础资料，可多选，只允许选到最末级（同法人登记单客户标签字段） | N | N | N |  |
| 业务标签认定 | 业务标签认定 | 业务标签认定 | 业务标签认定 | 业务标签认定 | 业务标签认定 | 业务标签认定 |
| 业务标签 | F7 | 取业务标签基础资料，可多选，只允许选到最末级（同项目申报单业务标签字段） | N | N | N |  |



| 业务标签的标识 | 对应单据字段 | 反写规则 |
| --- | --- | --- |
| 1 | 是否个体工商户贷款（监） | 若选择的业务标签存在标识为1的值，则反写“是否个体工商户贷款（监）”为是，若不存在则不反写 |
| 2 | 是否小微企业主贷款（监） | 若选择的业务标签存在标识为2的值，则反写“是否小微企业主贷款（监）”为是，若不存在则不反写 |
| 3 | 是否涉农贷款（监） | 若选择的业务标签存在标识为3的值，则反写“是否涉农贷款（监）”为是，若不存在则不反写 |
| 3 | 涉农贷款类型（监） | 若选择的业务标签存在标识为3的值，则将所选的值反写回“涉农贷款类型（监）” |
| 4 | 是否精准扶贫贷款（监） | 若选择的业务标签存在标识为4的值，则反写“是否精准扶贫贷款（监）”为是，若不存在则不反写 |
| 5 | 是否建档立卡贫困人口贷款（监） | 若选择的业务标签存在标识为5的值，则反写“是否建档立卡贫困人口贷款（监）”为是，若不存在则不反写 |
| 6/7/8 | 是否地方政府融资平台贷款（监） | 若选择的业务标签存在标识为6/7/8的值，则反写“是否地方政府融资平台贷款（监）”为是，若不存在则不反写 |
| 6 | 地方融资平台按法律性质分类类型（监） | 若选择的业务标签存在标识为6的值，则将所选的值反写回“地方融资平台按法律性质分类类型（监）” |
| 7 | 地方融资平台按隶属关系分类类型（监） | 若选择的业务标签存在标识为7的值，则将所选的值反写回“地方融资平台按隶属关系分类类型（监）” |
| 8 | 地方融资平台偿债资金来源分类（监） | 若选择的业务标签存在标识为8的值，则将所选的值反写回“地方融资平台偿债资金来源分类（监）” |
| 9 | 是否保障性安居工程贷款（监） | 若选择的业务标签存在标识为9的值，则反写“是否保障性安居工程贷款（监）”为是，若不存在则不反写 |
| 9 | 保障性安居工程贷款类型（监） | 若选择的业务标签存在标识为9的值，则将所选的值反写回“保障性安居工程贷款类型（监）” |
| 10 | 是否绿色贷款（监） | 若选择的业务标签存在标识为10的值，则反写“是否绿色贷款（监）”为是，若不存在则不反写 |
| 11 | 是否创业担保贷款（监） | 若选择的业务标签存在标识为11的值，则反写“是否创业担保贷款（监）”为是，若不存在则不反写 |
| 11 | 创业担保贷款类型（监） | 若选择的业务标签存在标识为11的值，则将所选的值反写回“创业担保贷款类型（监）” |
