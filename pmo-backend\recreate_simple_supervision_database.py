#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建简化的督办管理数据库
按照人员管理页面的模式，使用两个表实现
"""

import pymysql
import os
from dotenv import load_dotenv

def recreate_database():
    """重新创建数据库表结构"""
    print("🚀 开始重新创建督办管理数据库...")
    
    # 加载环境变量
    load_dotenv()
    
    config = {
        'host': os.getenv('DB_HOST'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME'),
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 数据库连接成功")
        
        # 1. 删除旧表
        print("\n🗑️ 删除旧表...")
        old_tables = [
            'supervision_status_history',
            'company_progress',
            'companies',
            'supervision_items',
            'company_progress_details'
        ]
        
        for table in old_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✅ 删除表 {table}")
            except Exception as e:
                print(f"  ⚠️ 删除表 {table} 失败: {e}")
        
        # 2. 创建主表：supervision_items（督办事项表）
        print("\n📋 创建主表 supervision_items...")
        
        create_main_table = """
        CREATE TABLE supervision_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sequence_number INT NOT NULL COMMENT '序号',
            work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
            work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
            supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
            work_content TEXT NOT NULL COMMENT '工作内容',
            is_annual_assessment VARCHAR(10) DEFAULT '否' COMMENT '是否年度考核',
            completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
            overall_progress VARCHAR(20) DEFAULT 'X 未启动' COMMENT '整体进度',
            财险_status VARCHAR(10) DEFAULT 'X' COMMENT '财险状态',
            寿险_status VARCHAR(10) DEFAULT 'X' COMMENT '寿险状态',
            金租_status VARCHAR(10) DEFAULT 'X' COMMENT '金租状态',
            资管_status VARCHAR(10) DEFAULT 'X' COMMENT '资管状态',
            广租_status VARCHAR(10) DEFAULT 'X' COMMENT '广租状态',
            通盛_status VARCHAR(10) DEFAULT 'X' COMMENT '通盛状态',
            担保_status VARCHAR(10) DEFAULT 'X' COMMENT '担保状态',
            小贷_status VARCHAR(10) DEFAULT 'X' COMMENT '小贷状态',
            保理_status VARCHAR(10) DEFAULT 'X' COMMENT '保理状态',
            不动产_status VARCHAR(10) DEFAULT 'X' COMMENT '不动产状态',
            征信_status VARCHAR(10) DEFAULT 'X' COMMENT '征信状态',
            金服_status VARCHAR(10) DEFAULT 'X' COMMENT '金服状态',
            本部_status VARCHAR(10) DEFAULT 'X' COMMENT '本部状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_work_theme (work_theme)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='督办事项主表'
        """
        
        cursor.execute(create_main_table)
        print("  ✅ 创建主表 supervision_items 成功")
        
        # 3. 创建详情表：company_progress_details（公司进度详情表）
        print("\n📝 创建详情表 company_progress_details...")
        
        create_detail_table = """
        CREATE TABLE company_progress_details (
            id INT AUTO_INCREMENT PRIMARY KEY,
            work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
            company_name VARCHAR(50) NOT NULL COMMENT '公司名称',
            progress_description TEXT COMMENT '项目进度情况',
            existing_problems TEXT COMMENT '存在问题',
            next_plan TEXT COMMENT '下一步计划',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_work_company (work_theme, company_name),
            INDEX idx_work_theme (work_theme),
            INDEX idx_company_name (company_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司进度详情表'
        """
        
        cursor.execute(create_detail_table)
        print("  ✅ 创建详情表 company_progress_details 成功")
        
        # 4. 插入测试数据
        print("\n📊 插入测试数据...")
        
        # 插入主表测试数据
        test_items = [
            (1, '数字化转型', '核心系统升级改造', '集团总部', '完成核心业务系统的升级改造工作', '是', '2024年12月底', 'O 进行中'),
            (2, '风险管控', '风险管理体系建设', '风控部门', '建立完善的风险管理体系和制度', '是', '2024年11月底', '！ 延期'),
            (3, '业务拓展', '新产品开发', '产品部门', '开发适应市场需求的新产品', '否', '2024年10月底', 'X 未启动'),
            (4, '合规管理', '合规制度完善', '合规部门', '完善各项合规管理制度', '是', '2024年9月底', '√ 已完成'),
            (5, '人才培养', '员工技能提升', '人力资源', '开展员工技能培训和提升计划', '否', '2024年12月底', 'O 进行中')
        ]
        
        insert_main_sql = """
        INSERT INTO supervision_items 
        (sequence_number, work_dimension, work_theme, supervision_source, work_content, 
         is_annual_assessment, completion_deadline, overall_progress)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.executemany(insert_main_sql, test_items)
        print(f"  ✅ 插入 {len(test_items)} 条主表测试数据")
        
        # 插入详情表测试数据
        companies = ['财险', '寿险', '金租', '资管', '广租']
        work_themes = ['核心系统升级改造', '风险管理体系建设', '新产品开发']
        
        detail_data = []
        for theme in work_themes:
            for company in companies:
                detail_data.append((
                    theme,
                    company,
                    f'{company}公司{theme}项目进展顺利，当前完成度约70%，预计按时完成。',
                    f'{company}在{theme}过程中遇到的主要问题是技术人员不足。',
                    f'{company}下一步计划加强人员配备，加快项目进度。'
                ))
        
        insert_detail_sql = """
        INSERT INTO company_progress_details 
        (work_theme, company_name, progress_description, existing_problems, next_plan)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        cursor.executemany(insert_detail_sql, detail_data)
        print(f"  ✅ 插入 {len(detail_data)} 条详情表测试数据")
        
        # 5. 提交事务
        connection.commit()
        
        # 6. 验证数据
        print("\n🔍 验证数据...")
        
        cursor.execute("SELECT COUNT(*) as count FROM supervision_items")
        main_count = cursor.fetchone()[0]
        print(f"  📋 主表记录数: {main_count}")

        cursor.execute("SELECT COUNT(*) as count FROM company_progress_details")
        detail_count = cursor.fetchone()[0]
        print(f"  📝 详情表记录数: {detail_count}")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 数据库重建完成！")
        print("\n📋 表结构说明:")
        print("  1. supervision_items: 主表，对应前端显示的表格")
        print("  2. company_progress_details: 详情表，存储详细进度信息")
        print("\n💡 操作方式:")
        print("  - 单击状态单元格：快速修改状态")
        print("  - 双击状态单元格：编辑详细进度信息")
        print("  - 支持动态添加/删除公司列")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库重建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    recreate_database()
