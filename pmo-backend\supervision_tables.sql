-- 创建督办管理相关数据库表

-- 1. 创建督办事项表
CREATE TABLE IF NOT EXISTS supervision_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sequence_number INT NOT NULL COMMENT '序号',
    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
    completion_date VARCHAR(50) NULL COMMENT '完成日期',
    overall_progress VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '整体进度',
    is_overdue TINYINT DEFAULT 0 COMMENT '是否逾期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sequence_number (sequence_number),
    INDEX idx_work_dimension (work_dimension),
    INDEX idx_overall_progress (overall_progress)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表';

-- 2. 创建公司表
CREATE TABLE IF NOT EXISTS companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_code (company_code),
    INDEX idx_display_order (display_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表';

-- 3. 创建公司进度表
CREATE TABLE IF NOT EXISTS company_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
    company_id INT NOT NULL COMMENT '公司ID',
    status VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '状态',
    progress_description TEXT COMMENT '进展描述',
    existing_problems TEXT COMMENT '存在问题',
    next_plan TEXT COMMENT '下步计划',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_item_company (supervision_item_id, company_id),
    INDEX idx_supervision_item_id (supervision_item_id),
    INDEX idx_company_id (company_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司进度表';

-- 4. 插入默认公司数据
INSERT IGNORE INTO companies (company_code, company_name, display_order) VALUES
('CXBX', '财险', 1),
('SXBX', '寿险', 2),
('JINZU', '金租', 3),
('ZICHAN', '资管', 4),
('GUANGZU', '广租', 5),
('TONGSHENG', '通盛', 6),
('DANBAO', '担保', 7),
('XIAODAI', '小贷', 8),
('BAOLI', '保理', 9),
('BUDONGCHAN', '不动产', 10),
('ZHENGXIN', '征信', 11),
('JINFU', '金服', 12),
('BENBU', '本部', 13);

-- 5. 插入示例督办事项数据
INSERT IGNORE INTO supervision_items 
(sequence_number, work_dimension, work_theme, supervision_source, work_content, completion_deadline, overall_progress) VALUES
(1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
 '5月末', '已完成'),
(2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
 '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
 '4月末', '已完成'),
(3, '一、监管和制度', '印发数据分级分类制度', '3月科技例会',
 '各单位需对数据进行分级分类管理，保障基础数据的准确性和安全性。完成标志：各单位印发分级分类制度。',
 '7月末', '进行中');

-- 6. 为督办事项创建公司进度记录
-- 这部分需要在有数据后执行，所以先创建一个存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CreateCompanyProgress()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE item_id INT;
    DECLARE company_id INT;
    DECLARE item_cursor CURSOR FOR SELECT id FROM supervision_items;
    DECLARE company_cursor CURSOR FOR SELECT id FROM companies WHERE is_active = 1;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 为每个督办事项创建所有公司的进度记录
    OPEN item_cursor;
    item_loop: LOOP
        FETCH item_cursor INTO item_id;
        IF done THEN
            LEAVE item_loop;
        END IF;
        
        -- 重置company cursor
        SET done = FALSE;
        OPEN company_cursor;
        company_loop: LOOP
            FETCH company_cursor INTO company_id;
            IF done THEN
                LEAVE company_loop;
            END IF;
            
            INSERT IGNORE INTO company_progress (supervision_item_id, company_id, status)
            VALUES (item_id, company_id, '未开始');
        END LOOP;
        CLOSE company_cursor;
        SET done = FALSE;
    END LOOP;
    CLOSE item_cursor;
END //
DELIMITER ;

-- 执行存储过程
CALL CreateCompanyProgress();

-- 删除存储过程
DROP PROCEDURE IF EXISTS CreateCompanyProgress;
