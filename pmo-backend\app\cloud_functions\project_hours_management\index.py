#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
project_hours_management 云函数
统一处理项目工时数据的获取和保存
包含getProjectHoursData和saveProjectHoursData的功能
"""

import json
import os
import pymysql
from datetime import datetime

# 配置 PyMySQL 以避免 cryptography 依赖
pymysql.install_as_MySQLdb()
# 禁用 ssl 验证，避免需要 cryptography
pymysql._auth.sha256_password = None 
pymysql._auth.caching_sha2_password = None
print("已禁用 PyMySQL 中的 cryptography 依赖")

# 数据库配置 - 使用统一配置
try:
    from app.core.config import settings
    DB_CONFIG = {
        'host': settings.DB_HOST,
        'port': settings.DB_PORT,
        'user': settings.DB_USER,
        'password': settings.DB_PASSWORD,
        'database': settings.DB_NAME,
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
except ImportError:
    # 兼容原有环境变量方式
    import os
    DB_CONFIG = {
        'host': os.environ['DB_HOST'],
        'port': int(os.environ['DB_PORT']),
        'user': os.environ['DB_USER'],
        'password': os.environ['DB_PASSWORD'],
        'database': os.environ['DB_NAME'],
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

#===========================
# 获取工时数据相关函数
#===========================

def get_project_hours_for_employee(params):
    """
    获取员工工时数据，支持显示当前月和前五个月的数据
    
    Args:
        params: 包含以下参数：
            - UserID: 员工ID
            - year: 当前年份
            - month: 当前月份
            - prevYear1: 上个月年份
            - prevMonth1: 上个月月份
            - prevYear2: 上上个月年份
            - prevMonth2: 上上个月月份
            - prevYear3: 第三个月年份(可选)
            - prevMonth3: 第三个月月份(可选)
            - prevYear4: 第四个月年份(可选)
            - prevMonth4: 第四个月月份(可选)
            - prevYear5: 第五个月年份(可选)
            - prevMonth5: 第五个月月份(可选)
            - showAllProjects: 是否显示所有项目（即使没有工时记录）
            
    Returns:
        dict: 包含工时数据的结果
    """
    user_id = params.get('UserID')
    year = params.get('year')
    month = params.get('month')
    
    # 获取所有月份参数
    prev_year1 = params.get('prevYear1')
    prev_month1 = params.get('prevMonth1')
    prev_year2 = params.get('prevYear2')
    prev_month2 = params.get('prevMonth2')
    prev_year3 = params.get('prevYear3')
    prev_month3 = params.get('prevMonth3')
    prev_year4 = params.get('prevYear4')
    prev_month4 = params.get('prevMonth4')
    prev_year5 = params.get('prevYear5')
    prev_month5 = params.get('prevMonth5')
    
    show_all_projects = params.get('showAllProjects', False)  # 默认值改为False
    
    if not user_id or not year or not month:
        return {
            'code': 400,
            'message': '缺少必要参数：UserID、year或month'
        }
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        # 格式化当前月的日期
        current_month = f"{year}-{str(month).zfill(2)}-01"  # 添加日部分
        print(f"格式化后的当前月: {current_month}")
        
        # 查询用户在最近6个月内的所有工时记录
        hours_sql = """
            SELECT 
                project_code, 
                project_name, 
                month, 
                SUM(working_hours) as working_hours,
                COUNT(*) as record_count
            FROM project_hours 
            WHERE UserID = %s 
            AND month >= DATE_SUB(%s, INTERVAL 5 MONTH)
            AND month <= %s
            GROUP BY project_code, project_name, month
            ORDER BY month
        """
        cursor.execute(hours_sql, [user_id, current_month, current_month])
        hours_data = cursor.fetchall()
        
        print(f"获取到 {len(hours_data)} 条工时记录")
        if hours_data:
            print(f"工时记录样例: {hours_data[0]}")
            
        # 单独查询当前月的数据，确保不会遗漏
        current_month_sql = """
            SELECT 
                project_code, 
                project_name, 
                month, 
                SUM(working_hours) as working_hours
            FROM project_hours 
            WHERE UserID = %s 
            AND DATE_FORMAT(month, '%%Y-%%m') = %s
            GROUP BY project_code, project_name, month
        """
        current_month_str = f"{year}-{str(month).zfill(2)}"  # 年-月格式，不包含日
        cursor.execute(current_month_sql, [user_id, current_month_str])
        current_month_data = cursor.fetchall()
        print(f"当前月({current_month_str})数据: {current_month_data}")
        
        # 获取项目数据
        all_projects = []
        if show_all_projects:
            # 显示所有项目
            project_sql = """
                SELECT project_code, project_name, investment_entity 
                FROM Project_Account_Book 
                ORDER BY investment_entity DESC, project_name
            """
            cursor.execute(project_sql)
            all_projects = cursor.fetchall()
        else:
            # 只获取用户在最近6个月内有工时记录的项目
            project_sql = """
                SELECT DISTINCT p.project_code, p.project_name, p.investment_entity
                FROM project_hours h
                JOIN Project_Account_Book p ON h.project_code = p.project_code
                WHERE h.UserID = %s 
                AND h.month >= DATE_SUB(%s, INTERVAL 5 MONTH)
                AND h.month <= %s
                ORDER BY p.investment_entity DESC, p.project_name
            """
            cursor.execute(project_sql, [user_id, current_month, current_month])
            all_projects = cursor.fetchall()
        
        print(f"获取到 {len(all_projects)} 个项目")
        
        # 如果没有项目数据，直接返回空列表
        if not all_projects:
            return {
                'code': 200,
                'data': [],
                'message': '没有找到符合条件的项目工时记录'
            }
        
        # 组织工时数据，按项目编码分组
        project_hours_map = {}
        
        # 首先初始化所有项目的数据结构
        for project in all_projects:
            project_code = project['project_code']
            if project_code not in project_hours_map:
                project_data = {
                    'project_code': project_code,
                    'project_name': project['project_name'],
                    'investment_entity': project.get('investment_entity', ''),
                    'hours_current': 0,
                    'current_record_exists': False,
                    'has_hours_records': False  # 新增标志，用于标记是否有工时记录
                }
                
                # 初始化前几个月的数据
                for i in range(1, 6):
                    project_data[f'hours_prev{i}'] = 0
                    project_data[f'prev{i}_record_exists'] = False
                
                project_hours_map[project_code] = project_data
        
        # 获取当前月和前5个月的日期字符串
        month_dates = []
        current_year = int(year)
        current_month = int(month)
        
        # 从最远的月份到最近的月份
        for i in range(5, -1, -1):
            # 计算月份和年份
            target_month = current_month - i
            target_year = current_year
            
            # 处理月份小于1的情况
            while target_month <= 0:
                target_month += 12
                target_year -= 1
                
            month_date = f"{target_year}-{str(target_month).zfill(2)}-01"
            month_dates.append(month_date)
        
        print(f"计算的月份列表（从最远到最近）: {month_dates}")
        
        # 填充工时数据
        for hour in hours_data:
            project_code = hour['project_code']
            working_hours = float(hour['working_hours'])
            
            # 处理日期格式，确保我们可以比较年-月
            if isinstance(hour['month'], datetime):
                month_str = hour['month'].strftime('%Y-%m-%d')
            elif isinstance(hour['month'], str):
                # 尝试解析日期字符串
                try:
                    # 如果是YYYY-MM-DD格式
                    if '-' in hour['month']:
                        parts = hour['month'].split('-')
                        if len(parts) >= 3:
                            month_str = f"{parts[0]}-{parts[1].zfill(2)}-{parts[2].zfill(2)}"
                        else:
                            month_str = hour['month']
                    else:
                        month_str = hour['month']
                except Exception:
                    month_str = hour['month']
            else:
                # 对于datetime.date对象
                try:
                    month_str = hour['month'].strftime('%Y-%m-%d')
                except Exception:
                    month_str = str(hour['month'])
            
            print(f"处理工时记录: {project_code}, 月份: {month_str}, 原始月份: {hour['month']}, 格式: {type(hour['month'])}")
            
            if project_code in project_hours_map:
                # 标记该项目有工时记录
                project_hours_map[project_code]['has_hours_records'] = True
                
                # 根据月份设置对应的工时数据
                if month_str == current_month:
                    project_hours_map[project_code]['hours_current'] = working_hours
                    project_hours_map[project_code]['hours_current_original'] = working_hours
                    project_hours_map[project_code]['current_record_exists'] = True
                    print(f"设置当前月 {current_month} 工时: {working_hours} (精确匹配)")
                else:
                    # 查找月份在month_dates中的位置
                    try:
                        month_index = month_dates.index(month_str)
                        # 根据索引设置对应的prev月份
                        prev_index = 5 - month_index  # 将索引转换为prev1-prev5
                        if 1 <= prev_index <= 5:
                            hours_key = f'hours_prev{prev_index}'
                            exists_key = f'prev{prev_index}_record_exists'
                            original_key = f'hours_prev{prev_index}_original'
                            
                            project_hours_map[project_code][hours_key] = working_hours
                            project_hours_map[project_code][original_key] = working_hours
                            project_hours_map[project_code][exists_key] = True
                            print(f"设置prev{prev_index}月 {month_str} 工时: {working_hours} (精确匹配)")
                    except ValueError:
                        # 如果月份不在预计算的列表中，尝试精确匹配
                        for i, calc_month in enumerate(month_dates):
                            if calc_month[0:7] == month_str[0:7]:  # 只比较年月部分
                                # 如果是当前月
                                if i == len(month_dates) - 1:
                                    project_hours_map[project_code]['hours_current'] = working_hours
                                    project_hours_map[project_code]['hours_current_original'] = working_hours
                                    project_hours_map[project_code]['current_record_exists'] = True
                                    print(f"设置当前月 {month_str} 工时 (年月匹配): {working_hours}")
                                else:
                                    prev_index = 5 - i
                                    if 1 <= prev_index <= 5:
                                        hours_key = f'hours_prev{prev_index}'
                                        exists_key = f'prev{prev_index}_record_exists'
                                        original_key = f'hours_prev{prev_index}_original'
                                        
                                        project_hours_map[project_code][hours_key] = working_hours
                                        project_hours_map[project_code][original_key] = working_hours
                                        project_hours_map[project_code][exists_key] = True
                                        print(f"设置prev{prev_index}月 {month_str} 工时 (年月匹配): {working_hours}")
                                break
                        else:
                            # 最后一次尝试，直接比较月份的年月部分
                            month_year_month = month_str[0:7]  # 获取年月部分
                            current_year_month = current_month[0:7]  # 获取当前月的年月部分
                            
                            print(f"最后尝试匹配: 记录月份={month_year_month}, 当前月={current_year_month}")
                            
                            if month_year_month == current_year_month:
                                project_hours_map[project_code]['hours_current'] = working_hours
                                project_hours_map[project_code]['hours_current_original'] = working_hours
                                project_hours_map[project_code]['current_record_exists'] = True
                                print(f"设置当前月 {month_str} 工时 (最终年月匹配): {working_hours}")
                            else:
                                print(f"月份 {month_str} 不匹配任何预计算的月份，跳过")
        
        # 将字典转换为列表，并过滤掉无工时记录的项目（如果不显示所有项目）
        if show_all_projects:
            result = list(project_hours_map.values())
        else:
            result = [item for item in project_hours_map.values() if item['has_hours_records']]
        
        # 确保当前月的数据被正确设置
        for item in result:
            project_code = item['project_code']
            # 检查是否有当前月的数据
            for current_data in current_month_data:
                if current_data['project_code'] == project_code:
                    working_hours = float(current_data['working_hours'])
                    item['hours_current'] = working_hours
                    item['hours_current_original'] = working_hours
                    item['current_record_exists'] = True
                    print(f"最终确认: 为项目 {project_code} 设置当前月 {current_month_str} 工时: {working_hours}")
                    break
        
        # 按工时降序排序
        result.sort(key=lambda x: (-float(x.get('hours_current', 0)), -float(x.get('hours_prev1', 0))))
        
        # 按投资主体倒序排序（安全处理None值）
        def safe_entity_key(x):
            entity = x.get('investment_entity')
            return '' if entity is None else str(entity)
            
        result.sort(key=safe_entity_key, reverse=True)
        
        # 确保所有工时数据都保留两位小数
        for item in result:
            item['hours_current'] = round(item['hours_current'], 2)
            
            # 处理前几个月的工时数据
            for i in range(1, 6):
                hours_key = f'hours_prev{i}'
                if hours_key in item:
                    item[hours_key] = round(item[hours_key], 2)
            
            # 添加唯一标识符，用于前端识别记录
            item['record_id'] = f"{item['project_code']}_{user_id}"
        
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'data': result,
            'message': '获取工时数据成功'
        }
        
    except Exception as e:
        print(f"获取工时数据失败: {str(e)}")
        return {
            'code': 500,
            'message': f'获取工时数据失败: {str(e)}'
        }

def get_projects_count():
    """
    获取项目总数，用于生成项目编码
    
    Returns:
        dict: 包含项目总数的结果
    """
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        # 获取项目总数
        sql = "SELECT COUNT(*) as count FROM Project_Account_Book"
        cursor.execute(sql)
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'count': result['count'] if result else 0
        }
        
    except Exception as e:
        print(f"获取项目总数失败: {str(e)}")
        return {
            'code': 500,
            'message': f'获取项目总数失败: {str(e)}'
        }

#===========================
# 保存工时数据相关函数
#===========================

def save_project_hours(params):
    """
    保存员工工时数据
    
    Args:
        params: 包含以下参数：
            - UserID: 员工ID
            - name: 员工姓名
            - year: 当前年份
            - month: 当前月份
            - prevYear1: 上个月年份
            - prevMonth1: 上个月月份
            - prevYear2: 上上个月年份
            - prevMonth2: 上上个月月份
            - prevYear3: 第三个月年份(可选)
            - prevMonth3: 第三个月月份(可选)
            - prevYear4: 第四个月年份(可选)
            - prevMonth4: 第四个月月份(可选)
            - prevYear5: 第五个月年份(可选)
            - prevMonth5: 第五个月月份(可选)
            - hoursData: 工时数据列表
            - department: 部门
            - updatedBy: 更新者ID
            
    Returns:
        dict: 保存结果
    """
    user_id = params.get('UserID')
    name = params.get('name')
    year = params.get('year')
    month = params.get('month')
    hours_data = params.get('hoursData')
    updated_by = params.get('updatedBy')
    department = params.get('department', '')
    
    # 获取所有月份参数
    prev_year1 = params.get('prevYear1')
    prev_month1 = params.get('prevMonth1')
    prev_year2 = params.get('prevYear2')
    prev_month2 = params.get('prevMonth2')
    prev_year3 = params.get('prevYear3')
    prev_month3 = params.get('prevMonth3')
    prev_year4 = params.get('prevYear4')
    prev_month4 = params.get('prevMonth4')
    prev_year5 = params.get('prevYear5')
    prev_month5 = params.get('prevMonth5')
    
    # 参数校验
    if not user_id or not name or not year or not month or not hours_data or not updated_by:
        return {
            'code': 400,
            'message': '参数不完整，请检查'
        }
    
    if not isinstance(hours_data, list) or not hours_data:
        return {
            'code': 400,
            'message': '工时数据格式错误或为空'
        }
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        # 添加权限验证
        # 获取更新者信息
        cursor.execute(
            "SELECT role, department_name FROM users WHERE UserID = %s",
            [updated_by]
        )
        updater = cursor.fetchone()
        
        if not updater:
            return {'code': 403, 'message': '无效的用户ID'}
            
        updater_role = updater.get('role')
        
        # 非系统管理员
        if updater_role > 1:
            # 部门管理员只能修改本部门员工
            if updater_role == 2:
                # 获取目标用户部门
                cursor.execute(
                    "SELECT department_name FROM users WHERE UserID = %s",
                    [user_id]
                )
                target = cursor.fetchone()
                
                if target and updater.get('department_name') != target.get('department_name'):
                    return {'code': 403, 'message': '部门管理员只能修改本部门员工工时'}
            
            # 普通用户/只读用户/外部用户只能修改自己
            elif updated_by != user_id:
                return {'code': 403, 'message': '您只能修改自己的工时数据'}
        
        # 开始事务
        connection.begin()
        
        # 计算多个月的日期格式，确保使用完整的日期（年-月-日）
        months_to_save = [f"{year}-{str(month).zfill(2)}-01"]  # 当前月第一天
        
        # 添加前几个月的日期格式
        if prev_year1 and prev_month1:
            months_to_save.append(f"{prev_year1}-{str(prev_month1).zfill(2)}-01")
        if prev_year2 and prev_month2:
            months_to_save.append(f"{prev_year2}-{str(prev_month2).zfill(2)}-01")
        if prev_year3 and prev_month3:
            months_to_save.append(f"{prev_year3}-{str(prev_month3).zfill(2)}-01")
        if prev_year4 and prev_month4:
            months_to_save.append(f"{prev_year4}-{str(prev_month4).zfill(2)}-01")
        if prev_year5 and prev_month5:
            months_to_save.append(f"{prev_year5}-{str(prev_month5).zfill(2)}-01")
        
        print(f"保存月份: {months_to_save}")
        
        # 处理工时数据
        for data in hours_data:
            project_code = data.get('project_code')
            project_name = data.get('project_name')
            investment_entity = data.get('investment_entity', '')
            
            if not project_code or not project_name:
                print(f"跳过无效项目: {json.dumps(data)}")
                continue
                
            # 删除用户在所有指定月份的该项目记录（先删除再插入）
            delete_conditions = []
            delete_params = [user_id, project_code]
            
            # 为每个月份创建精确匹配条件
            for month_date in months_to_save:
                month_prefix = month_date[0:7]  # 只取年-月部分 YYYY-MM
                delete_conditions.append("DATE_FORMAT(month, '%%Y-%%m') = %s")
                delete_params.append(month_prefix)
            
            delete_sql = f"""
                DELETE FROM project_hours 
                WHERE UserID = %s AND project_code = %s 
                AND ({' OR '.join(delete_conditions)})
            """
            cursor.execute(delete_sql, delete_params)
            
            # 插入当前月数据
            current_hours = float(data.get('hours_current', 0))
            if current_hours > 0 or (data.get('current_record_exists') and data.get('hours_current_original') != current_hours):
                insert_sql = """
                    INSERT INTO project_hours 
                    (project_code, project_name, department, name, UserID, month, working_hours) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_sql, [
                    project_code,
                    project_name,
                    department,
                    name,
                    user_id,
                    months_to_save[0],  # 当前月
                    round(current_hours, 2)
                ])
            
            # 插入前几个月数据
            for i in range(1, 6):
                if i < len(months_to_save):  # 确保月份可用
                    hours_key = f'hours_prev{i}'
                    hours_original_key = f'hours_prev{i}_original'
                    exists_key = f'prev{i}_record_exists'
                    
                    prev_hours = float(data.get(hours_key, 0))
                    prev_hours_original = float(data.get(hours_original_key, 0)) if hours_original_key in data else 0
                    prev_exists = data.get(exists_key, False)
                    
                    if prev_hours > 0 or (prev_exists and prev_hours != prev_hours_original):
                        insert_sql = """
                            INSERT INTO project_hours 
                            (project_code, project_name, department, name, UserID, month, working_hours) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_sql, [
                            project_code,
                            project_name,
                            department,
                            name,
                            user_id,
                            months_to_save[i],
                            round(prev_hours, 2)
                        ])
        
        # 提交事务
        connection.commit()
        
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'message': '工时数据保存成功'
        }
        
    except Exception as e:
        # 回滚事务
        if connection:
            connection.rollback()
            
        print(f"保存工时数据失败: {str(e)}")
        return {
            'code': 500,
            'message': f'保存工时数据失败: {str(e)}'
        }

def save_project(params):
    """
    保存项目信息
    
    Args:
        params: 包含以下参数：
            - projectData: 项目数据
            - updatedBy: 更新者ID
            
    Returns:
        dict: 保存结果
    """
    project_data = params.get('projectData')
    updated_by = params.get('updatedBy')
    
    if not project_data:
        return {
            'code': 400,
            'message': '缺少项目数据'
        }
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        # 开始事务
        connection.begin()
        
        # 添加项目
        insert_sql = """
            INSERT INTO Project_Account_Book 
            (project_code, investment_entity, project_name, responsible_department,
             business_research_time, solution_time, project_establishment_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(insert_sql, [
            project_data.get('project_code'),
            project_data.get('investment_entity'),
            project_data.get('project_name'),
            project_data.get('responsible_department'),
            project_data.get('business_research_time'),
            project_data.get('solution_time'),
            project_data.get('project_establishment_time')
        ])
        
        # 提交事务
        connection.commit()
        
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'message': '项目创建成功'
        }
        
    except Exception as e:
        # 回滚事务
        if connection:
            connection.rollback()
            
        print(f"保存项目信息失败: {str(e)}")
        return {
            'code': 500,
            'message': f'保存项目信息失败: {str(e)}'
        }

def get_all_projects():
    """
    获取所有项目列表
    
    Returns:
        dict: 包含所有项目的结果
    """
    try:
        connection = get_connection()
        cursor = connection.cursor()

        # 查询所有项目
        sql = """
            SELECT project_code, project_name, investment_entity
            FROM Project_Account_Book
            ORDER BY project_name
        """
        cursor.execute(sql)
        projects = cursor.fetchall()
        
        print(f"获取到 {len(projects)} 个项目")
        
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'data': projects,
            'message': '获取项目列表成功'
        }
        
    except Exception as e:
        print(f"获取项目列表失败: {str(e)}")
        return {
            'code': 500,
            'message': f'获取项目列表失败: {str(e)}'
        }

def get_project_options(params):
    """
    根据投资主体获取项目选项
    
    Args:
        params: 包含以下参数：
            - investment_entity: 可选，投资主体
            
    Returns:
        dict: 包含项目选项的结果
    """
    # 处理可能嵌套的投资主体参数
    investment_entity = params.get('investment_entity')
    print(f"获取项目选项 - 收到参数: {params}")
    
    # 检查参数是否为嵌套字典，统一处理为字符串格式
    if isinstance(investment_entity, dict) and 'investment_entity' in investment_entity:
        investment_entity = investment_entity.get('investment_entity')
    
    print(f"处理后的投资主体参数: '{investment_entity}'")
    
    try:
        connection = get_connection()
        cursor = connection.cursor()

        # 如果没有指定投资主体，获取所有不同的投资主体
        if not investment_entity:
            sql = """
                SELECT DISTINCT investment_entity 
                FROM Project_Account_Book 
                WHERE investment_entity IS NOT NULL AND investment_entity != ''
                ORDER BY investment_entity
            """
            cursor.execute(sql)
            entities = cursor.fetchall()
            
            print(f"查询投资主体列表 - 获取到 {len(entities)} 个投资主体")
            if entities:
                print(f"投资主体示例: {entities[:3]}")
            
            cursor.close()
            connection.close()
            
            return {
                'code': 200,
                'data': {
                    'entities': [e['investment_entity'] for e in entities if e['investment_entity']]
                },
                'message': '获取投资主体列表成功'
            }
        
        # 如果指定了投资主体，获取该投资主体下的所有项目
        else:
            # 首先尝试精确匹配
            sql = """
                SELECT project_code, project_name, responsible_department, investment_entity
                FROM Project_Account_Book
                WHERE investment_entity = %s
                ORDER BY project_code
            """
            print(f"执行SQL: {sql} 参数: [{investment_entity}]")
            cursor.execute(sql, [investment_entity])
            projects = cursor.fetchall()
            
            # 如果精确匹配没有结果，尝试模糊匹配
            if not projects:
                print(f"精确匹配未找到项目，尝试模糊匹配...")
                fuzzy_sql = """
                    SELECT project_code, project_name, responsible_department, investment_entity
                    FROM Project_Account_Book
                    WHERE investment_entity LIKE %s
                    ORDER BY project_code
                """
                cursor.execute(fuzzy_sql, [f"%{investment_entity}%"])
                projects = cursor.fetchall()
            
            print(f"查询项目 - 投资主体 '{investment_entity}' - 获取到 {len(projects)} 个项目")
            if projects:
                print(f"项目示例: {projects[:3]}")
            else:
                # 如果没有找到项目，检查投资主体是否存在
                check_sql = "SELECT COUNT(*) as count FROM Project_Account_Book WHERE investment_entity = %s"
                cursor.execute(check_sql, [investment_entity])
                count = cursor.fetchone()
                print(f"检查投资主体 '{investment_entity}' 是否存在: {count}")
                
                # 查找所有投资主体以进行比较
                cursor.execute("SELECT DISTINCT investment_entity FROM Project_Account_Book LIMIT 10")
                existing_entities = cursor.fetchall()
                print(f"数据库中存在的投资主体示例: {existing_entities}")
                
                # 检查是否有类似的投资主体
                cursor.execute("SELECT DISTINCT investment_entity FROM Project_Account_Book WHERE investment_entity LIKE %s LIMIT 5", [f"%{investment_entity}%"])
                similar_entities = cursor.fetchall()
                print(f"类似投资主体: {similar_entities}")
            
            cursor.close()
            connection.close()
            
            return {
                'code': 200,
                'data': {
                    'projects': projects
                },
                'message': '获取项目列表成功'
            }
        
    except Exception as e:
        print(f"获取项目选项失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return {
            'code': 500,
            'message': f'获取项目选项失败: {str(e)}'
        }

def get_employee_monthly_totals(params):
    """
    获取员工按月工时合计
    
    Args:
        params: 包含以下参数：
            - months: 需要查询的月份列表，例如 ["2025-01", "2025-02"]
            - userID: 可选，指定用户ID
            - includeProjectDetails: 可选，是否包含项目详情
            
    Returns:
        dict: 包含员工月度工时合计的结果
    """
    months = params.get('months', [])
    user_id = params.get('userID')
    include_project_details = params.get('includeProjectDetails', False)
    
    if not months:
        return {
            'code': 400,
            'message': '缺少必要参数：months'
        }
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        # 构建SQL查询条件
        where_conditions = []
        query_params = []
        
        # 构建月份条件，确保精确匹配年月
        month_conditions = []
        for month_str in months:
            print(f"处理统计月份: {month_str}")
            month_conditions.append("DATE_FORMAT(month, '%%Y-%%m') = %s")
            query_params.append(month_str)
        
        if month_conditions:
            where_conditions.append(f"({' OR '.join(month_conditions)})")
        
        # 如果指定了用户，添加用户条件
        if user_id:
            where_conditions.append("UserID = %s")
            query_params.append(user_id)
        
        # 构建完整SQL
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 修改SQL查询：先获取基础数据，然后才聚合
        # 查询每个员工每个项目每个月的工时记录
        base_sql = f"""
            SELECT 
                UserID, 
                name,
                department,
                project_code,
                project_name,
                month,
                working_hours
            FROM project_hours
            WHERE {where_clause}
            ORDER BY UserID, month
        """
        
        cursor.execute(base_sql, query_params)
        base_results = cursor.fetchall()
        
        print(f"获取到 {len(base_results)} 条基础工时记录")
        
        # 手动聚合数据，确保与get_project_hours_for_employee函数使用相同的逻辑
        user_monthly_data = {}
        
        # 处理每条工时记录
        for record in base_results:
            user_id = record['UserID']
            
            # 处理日期格式，确保一致性
            if isinstance(record['month'], datetime):
                month_str = record['month'].strftime('%Y-%m')
            elif isinstance(record['month'], str):
                # 尝试解析日期字符串
                try:
                    # 如果是YYYY-MM-DD格式，只取年月部分
                    if '-' in record['month']:
                        parts = record['month'].split('-')
                        if len(parts) >= 2:
                            month_str = f"{parts[0]}-{parts[1].zfill(2)}"
                        else:
                            month_str = record['month']
                    else:
                        month_str = record['month']
                except Exception:
                    month_str = record['month']
            else:
                # 对于其他类型
                try:
                    month_str = record['month'].strftime('%Y-%m')
                except Exception:
                    month_str = str(record['month'])
                    
            working_hours = float(record['working_hours'])
            project_code = record['project_code']
            project_name = record['project_name']
            
            print(f"处理工时记录: 用户={user_id}, 月份={month_str}, 项目={project_code}, 工时={working_hours}")
            
            # 初始化用户数据
            if user_id not in user_monthly_data:
                user_monthly_data[user_id] = {
                    'UserID': user_id,
                    'name': record['name'],
                    'department': record['department'],
                    'monthly_hours': {},
                    'project_details': {} if include_project_details else None
                }
            
            # 添加到月度汇总
            if month_str not in user_monthly_data[user_id]['monthly_hours']:
                user_monthly_data[user_id]['monthly_hours'][month_str] = 0
            
            user_monthly_data[user_id]['monthly_hours'][month_str] += working_hours
            
            # 如果需要项目详情
            if include_project_details:
                if month_str not in user_monthly_data[user_id]['project_details']:
                    user_monthly_data[user_id]['project_details'][month_str] = []
                
                # 检查是否已存在相同项目记录
                project_found = False
                for p in user_monthly_data[user_id]['project_details'][month_str]:
                    if p['project_code'] == project_code:
                        p['working_hours'] += working_hours
                        project_found = True
                        break
                
                # 如果没有找到相同项目，添加新记录
                if not project_found:
                    project_detail = {
                        'project_code': project_code,
                        'project_name': project_name,
                        'working_hours': working_hours
                    }
                    user_monthly_data[user_id]['project_details'][month_str].append(project_detail)
        
        # 进行四舍五入处理，确保小数点后保留3位
        for user_id, user_data in user_monthly_data.items():
            for month, hours in user_data['monthly_hours'].items():
                user_data['monthly_hours'][month] = round(hours, 3)
                print(f"月度总计: 用户={user_id}, 月份={month}, 总工时={user_data['monthly_hours'][month]}")
            
            # 同样处理项目详情
            if include_project_details and user_data['project_details']:
                for month, projects in user_data['project_details'].items():
                    for project in projects:
                        project['working_hours'] = round(project['working_hours'], 3)
        
        # 将字典转换为列表
        result_list = list(user_monthly_data.values())
        
        # 关闭数据库连接
        cursor.close()
        connection.close()
        
        return {
            'code': 200,
            'data': result_list,
            'message': '获取员工月度工时合计成功'
        }
        
    except Exception as e:
        print(f"获取员工月度工时合计失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return {
            'code': 500,
            'message': f'获取员工月度工时合计失败: {str(e)}'
        }

#===========================
# 云函数入口
#===========================

def main_handler(event_data):
    """主处理函数 - 适配后的版本"""
    print("project_hours_management 函数被调用")

    # 打印接收到的参数
    print(f"接收到的参数: {event_data}")

    # 获取函数名和操作
    function_name = event_data.get('function')
    action = event_data.get('action')
    params = event_data.get('params', {})

    # 构建响应数据
    result = {
        'code': 400,
        'message': '无效的请求'
    }

    # 根据不同的函数名调用不同的处理函数
    if function_name == 'get_project_hours_for_employee' or action == 'getProjectHoursForEmployee':
        result = get_project_hours_for_employee(params)
    elif function_name == 'save_project_hours' or action == 'saveProjectHours':
        result = save_project_hours(params)
    elif function_name == 'save_project' or action == 'saveProject':
        result = save_project(params)
    elif function_name == 'get_projects_count' or action == 'getProjectsCount':
        result = get_projects_count()
    elif function_name == 'get_all_projects' or action == 'getAllProjects':
        result = get_all_projects()
    elif function_name == 'get_project_options' or action == 'getProjectOptions':
        result = get_project_options(params)
    elif function_name == 'get_employee_monthly_totals' or action == 'getEmployeeMonthlyTotals':
        result = get_employee_monthly_totals(params)
    else:
        result = {
            'code': 400,
            'message': f'不支持的函数: {function_name or action}'
        }

    return result

def legacy_main_handler(event, context):
    """原有的云函数入口，保持兼容性"""
    print("project_hours_management 函数被调用 (legacy)")

    # 打印接收到的参数
    print(f"接收到的参数: {event}")

    # 解析请求体
    body = {}
    if 'body' in event:
        try:
            body = json.loads(event['body'])
        except Exception as e:
            print(f"解析请求体失败: {str(e)}")
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'code': 400,
                    'message': f'无效的请求体: {str(e)}'
                })
            }

    # 调用新的处理函数
    result = main_handler(body)

    # 返回响应
    return {
        'statusCode': 200,
        'body': json.dumps(result)
    }