#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI集成功能测试脚本
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_ai_client():
    """测试AI客户端"""
    print("🧪 测试AI客户端...")
    
    try:
        from app.core.ai_client import ai_client
        
        # 测试基本响应
        response = await ai_client.generate_response("你好，请简单回复")
        print(f"✅ AI客户端响应: {response[:100]}...")
        
        # 测试SQL分析
        schema = {
            "projects": {
                "columns": [
                    {"name": "project_name", "type": "VARCHAR", "comment": "项目名称"},
                    {"name": "status", "type": "VARCHAR", "comment": "项目状态"}
                ]
            }
        }
        
        sql_result = await ai_client.analyze_sql_query("查询所有项目", schema)
        print(f"✅ SQL分析结果: {sql_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI客户端测试失败: {str(e)}")
        return False

async def test_wren_service():
    """测试WrenAI服务"""
    print("🧪 测试WrenAI服务...")
    
    try:
        from app.services.ai_wren_service import wren_ai_service
        
        # 测试快捷问题
        questions = wren_ai_service.get_quick_questions()
        print(f"✅ 快捷问题数量: {len(questions)}")
        
        # 测试数据库摘要
        summary = await wren_ai_service.get_database_summary()
        print(f"✅ 数据库摘要: {summary}")
        
        # 测试简单问答
        result = await wren_ai_service.ask_question("测试问题")
        print(f"✅ 问答结果: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ WrenAI服务测试失败: {str(e)}")
        return False

async def test_database_connection():
    """测试数据库连接"""
    print("🧪 测试数据库连接...")
    
    try:
        from app.core.database import get_db_connection
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 测试简单查询
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        print(f"✅ 数据库连接成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始AI集成功能测试\n")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("AI客户端", test_ai_client),
        ("WrenAI服务", test_wren_service),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI集成功能正常")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
