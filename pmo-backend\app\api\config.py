#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置API - 第八个功能模块：系统配置和管理
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.user import User
from app.models.config import Config, Lang, SystemLog, SystemCache
from app.utils.response_utils import success_response, error_response
from app.api.auth import get_current_user

# 创建路由器
router = APIRouter(prefix="/config", tags=["系统配置"])

# 请求模型
class ConfigSetRequest(BaseModel):
    owner: str = "system"
    module: str
    section: str
    key: str
    value: str

class LangSetRequest(BaseModel):
    lang: str = "zh-cn"
    module: str
    section: str
    key: str
    value: str
    system: str = "0"

class CacheSetRequest(BaseModel):
    key: str
    value: str
    expired_hours: Optional[int] = None

# 权限检查
def check_admin_permission(current_user: User = Depends(get_current_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user

# 系统配置API
@router.get("/system")
async def get_system_configs(
    module: str = Query("", description="模块名"),
    current_user: User = Depends(get_current_user)
):
    """获取系统配置"""
    try:
        if module:
            configs = Config.get_configs_by_module(module=module)
        else:
            configs = Config.get_all_configs()
        
        return success_response(configs, "获取系统配置成功")
    except Exception as e:
        return error_response(f"获取系统配置失败: {str(e)}")

@router.get("/system/{module}/{section}/{key}")
async def get_config_value(
    module: str,
    section: str,
    key: str,
    current_user: User = Depends(get_current_user)
):
    """获取单个配置值"""
    try:
        value = Config.get_config(module=module, section=section, key=key)
        if value is None:
            return error_response("配置不存在", status_code=404)
        
        return success_response({"value": value}, "获取配置值成功")
    except Exception as e:
        return error_response(f"获取配置值失败: {str(e)}")

@router.post("/system")
async def set_config(
    request: ConfigSetRequest,
    current_user: User = Depends(check_admin_permission)
):
    """设置系统配置"""
    try:
        success = Config.set_config(
            owner=request.owner,
            module=request.module,
            section=request.section,
            key=request.key,
            value=request.value
        )
        
        if success:
            # 记录操作日志
            SystemLog.add_log(
                objectType="config",
                objectID=0,
                action="edit",
                actor=current_user.account,
                comment=f"修改配置 {request.module}.{request.section}.{request.key}"
            )
            return success_response(None, "设置配置成功")
        else:
            return error_response("设置配置失败")
    except Exception as e:
        return error_response(f"设置配置失败: {str(e)}")

@router.delete("/system/{module}/{section}/{key}")
async def delete_config(
    module: str,
    section: str,
    key: str,
    current_user: User = Depends(check_admin_permission)
):
    """删除系统配置"""
    try:
        success = Config.delete_config(module=module, section=section, key=key)
        
        if success:
            # 记录操作日志
            SystemLog.add_log(
                objectType="config",
                objectID=0,
                action="delete",
                actor=current_user.account,
                comment=f"删除配置 {module}.{section}.{key}"
            )
            return success_response(None, "删除配置成功")
        else:
            return error_response("删除配置失败")
    except Exception as e:
        return error_response(f"删除配置失败: {str(e)}")

# 数据字典API
@router.get("/lang")
async def get_lang_data(
    lang: str = Query("zh-cn", description="语言"),
    module: str = Query("", description="模块名"),
    section: str = Query("", description="配置段"),
    current_user: User = Depends(get_current_user)
):
    """获取数据字典"""
    try:
        if module or section:
            data = Lang.get_lang_data(lang=lang, module=module, section=section)
        else:
            data = Lang.get_all_lang_data(lang=lang)
        
        return success_response(data, "获取数据字典成功")
    except Exception as e:
        return error_response(f"获取数据字典失败: {str(e)}")

@router.post("/lang")
async def set_lang_data(
    request: LangSetRequest,
    current_user: User = Depends(check_admin_permission)
):
    """设置数据字典"""
    try:
        success = Lang.set_lang_data(
            lang=request.lang,
            module=request.module,
            section=request.section,
            key=request.key,
            value=request.value,
            system=request.system
        )
        
        if success:
            # 记录操作日志
            SystemLog.add_log(
                objectType="lang",
                objectID=0,
                action="edit",
                actor=current_user.account,
                comment=f"修改数据字典 {request.module}.{request.section}.{request.key}"
            )
            return success_response(None, "设置数据字典成功")
        else:
            return error_response("设置数据字典失败")
    except Exception as e:
        return error_response(f"设置数据字典失败: {str(e)}")

# 系统日志API
@router.get("/logs")
async def get_system_logs(
    objectType: str = Query("", description="对象类型"),
    actor: str = Query("", description="操作者"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    current_user: User = Depends(check_admin_permission)
):
    """获取系统日志"""
    try:
        offset = (page - 1) * page_size
        logs = SystemLog.get_logs(
            objectType=objectType,
            actor=actor,
            limit=page_size,
            offset=offset
        )
        
        return success_response({
            "logs": logs,
            "page": page,
            "page_size": page_size,
            "total": len(logs)
        }, "获取系统日志成功")
    except Exception as e:
        return error_response(f"获取系统日志失败: {str(e)}")

@router.post("/logs")
async def add_system_log(
    objectType: str,
    objectID: int,
    action: str,
    comment: str = "",
    extra: str = "",
    current_user: User = Depends(get_current_user)
):
    """添加系统日志"""
    try:
        success = SystemLog.add_log(
            objectType=objectType,
            objectID=objectID,
            action=action,
            actor=current_user.account,
            comment=comment,
            extra=extra
        )
        
        if success:
            return success_response(None, "添加系统日志成功")
        else:
            return error_response("添加系统日志失败")
    except Exception as e:
        return error_response(f"添加系统日志失败: {str(e)}")

# 系统缓存API
@router.get("/cache/{key}")
async def get_cache(
    key: str,
    current_user: User = Depends(check_admin_permission)
):
    """获取缓存"""
    try:
        value = SystemCache.get_cache(key)
        if value is None:
            return error_response("缓存不存在或已过期", status_code=404)
        
        return success_response({"value": value}, "获取缓存成功")
    except Exception as e:
        return error_response(f"获取缓存失败: {str(e)}")

@router.post("/cache")
async def set_cache(
    request: CacheSetRequest,
    current_user: User = Depends(check_admin_permission)
):
    """设置缓存"""
    try:
        expired_time = None
        if request.expired_hours:
            from datetime import timedelta
            expired_time = datetime.now() + timedelta(hours=request.expired_hours)
        
        success = SystemCache.set_cache(
            key=request.key,
            value=request.value,
            expired_time=expired_time
        )
        
        if success:
            return success_response(None, "设置缓存成功")
        else:
            return error_response("设置缓存失败")
    except Exception as e:
        return error_response(f"设置缓存失败: {str(e)}")

@router.delete("/cache/{key}")
async def delete_cache(
    key: str,
    current_user: User = Depends(check_admin_permission)
):
    """删除缓存"""
    try:
        success = SystemCache.delete_cache(key)
        
        if success:
            return success_response(None, "删除缓存成功")
        else:
            return error_response("删除缓存失败")
    except Exception as e:
        return error_response(f"删除缓存失败: {str(e)}")

@router.post("/cache/clear-expired")
async def clear_expired_cache(
    current_user: User = Depends(check_admin_permission)
):
    """清理过期缓存"""
    try:
        success = SystemCache.clear_expired_cache()
        
        if success:
            return success_response(None, "清理过期缓存成功")
        else:
            return error_response("清理过期缓存失败")
    except Exception as e:
        return error_response(f"清理过期缓存失败: {str(e)}")

# 系统信息API
@router.get("/system-info")
async def get_system_info(current_user: User = Depends(get_current_user)):
    """获取系统信息"""
    try:
        # 获取基本系统信息
        version = Config.get_config(module="common", section="global", key="version") or "1.0.0"
        company = Config.get_config(module="common", section="global", key="company") or "PMO管理系统"
        timezone = Config.get_config(module="common", section="global", key="timezone") or "Asia/Shanghai"
        lang = Config.get_config(module="common", section="global", key="lang") or "zh-cn"
        
        system_info = {
            "version": version,
            "company": company,
            "timezone": timezone,
            "lang": lang,
            "current_time": datetime.now().isoformat(),
            "server_info": {
                "python_version": "3.10+",
                "framework": "FastAPI",
                "database": "MySQL"
            }
        }
        
        return success_response(system_info, "获取系统信息成功")
    except Exception as e:
        return error_response(f"获取系统信息失败: {str(e)}")

# 系统状态API
@router.get("/system-status")
async def get_system_status(current_user: User = Depends(check_admin_permission)):
    """获取系统状态"""
    try:
        from app.core.database import get_db_connection, close_db_connection
        
        # 检查数据库连接
        db_status = "正常"
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            close_db_connection(conn)
        except Exception as e:
            db_status = f"异常: {str(e)}"
        
        # 获取系统统计
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 用户数量
            cursor.execute("SELECT COUNT(*) as count FROM zt_user WHERE deleted = '0'")
            user_count = cursor.fetchone()['count']
            
            # 项目数量
            cursor.execute("SELECT COUNT(*) as count FROM zt_project WHERE deleted = '0'")
            project_count = cursor.fetchone()['count']
            
            # 任务数量
            cursor.execute("SELECT COUNT(*) as count FROM zt_task WHERE deleted = '0'")
            task_count = cursor.fetchone()['count']
            
            # 缺陷数量
            cursor.execute("SELECT COUNT(*) as count FROM zt_bug WHERE deleted = '0'")
            bug_count = cursor.fetchone()['count']
            
            system_status = {
                "database": db_status,
                "statistics": {
                    "users": user_count,
                    "projects": project_count,
                    "tasks": task_count,
                    "bugs": bug_count
                },
                "uptime": "系统运行正常",
                "last_check": datetime.now().isoformat()
            }
            
            return success_response(system_status, "获取系统状态成功")
            
        finally:
            if conn:
                close_db_connection(conn)
                
    except Exception as e:
        return error_response(f"获取系统状态失败: {str(e)}")
