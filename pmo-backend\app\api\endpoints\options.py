from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional, List
import json

from app.core.security import get_current_user
from app.core.logger import get_logger

# 导入云函数
from app.cloud_functions.getDatabaseOptions.index import main_handler as get_database_options_handler

logger = get_logger(__name__)
router = APIRouter()

# 删除默认选项数据，始终使用数据库中的真实数据

@router.get("/")
async def get_options(
    option_type: str = Query(..., description="选项类型"),
    action: str = Query(None, description="操作类型"),
    current_user: Dict = None
) -> Dict[str, Any]:
    """
    获取数据库选项
    """
    # 不再根据认证状态返回默认数据，始终从数据库获取真实数据
    try:
        # 确保action参数有值
        if not action:
            if option_type == 'investment_entity':
                action = 'getInvestmentEntities'
            elif option_type == 'responsible_department':
                action = 'getResponsibleDepartments'
            else:
                action = f'get{option_type.capitalize()}'
        
        # 构造云函数需要的event对象
        event = {
            "queryString": {
                "option_type": option_type,
                "action": action
            }
        }
        
        logger.info(f"调用云函数获取{option_type}数据，action={action}")
        
        # 调用云函数
        result = get_database_options_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            logger.info(f"成功获取{option_type}数据: {body}")
            return body
        else:
            logger.error(f"获取数据库选项失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取数据库选项失败")
    except Exception as e:
        logger.error(f"获取数据库选项异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 